/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import io.vertx.core.Future;

/**
 * Transport server interface for the Motadata event bus system.
 *
 * <p>This interface defines the contract for all transport server implementations
 * in the Motadata event bus architecture. It provides a standardized abstraction
 * layer that allows different transport protocols (ZMQ, TCP, UDP) to be used
 * interchangeably without affecting the application logic.</p>
 *
 * <p>The interface supports both synchronous and asynchronous operations:</p>
 * <ul>
 *   <li><b>Binding</b>: Asynchronous server binding to network interfaces</li>
 *   <li><b>Message Sending</b>: Synchronous message transmission to connected clients</li>
 *   <li><b>Message Receiving</b>: Asynchronous message reception from clients</li>
 *   <li><b>Resource Management</b>: Proper cleanup and resource release</li>
 * </ul>
 *
 * <p>Typical usage pattern:</p>
 * <pre>{@code
 * // Create and configure transport server
 * TransportServer server = TransportProvider.getServer(config);
 *
 * // Bind to network interface
 * server.bind().onSuccess(v -> {
 *     // Start receiving messages
 *     server.receive();
 *
 *     // Send messages to clients
 *     server.send("topic", "Hello World".getBytes());
 * }).onFailure(error -> {
 *     // Handle binding failure
 *     handleError(error);
 * });
 *
 * // Cleanup when done
 * server.close();
 * }</pre>
 *
 * <p><b>Implementation Requirements:</b></p>
 * <ul>
 *   <li>Implementations must be thread-safe for concurrent access</li>
 *   <li>Server state must be properly managed</li>
 *   <li>Resources must be properly released in the close() method</li>
 *   <li>Error conditions should be handled gracefully</li>
 * </ul>
 *
 * <p><b>Supported Transport Protocols:</b></p>
 * <ul>
 *   <li>{@link ZMQServer} - ZeroMQ transport implementation</li>
 * </ul>
 *
 * <AUTHOR> Engineering Team
 * @version 1.0
 * @since 1.0
 * @see TransportClient
 * @see TransportProvider
 * @see ZMQServer
 */
public interface TransportServer
{
    /**
     * Binds the server to the configured network interface and port.
     *
     * <p>This method initiates an asynchronous binding operation that prepares the server
     * to accept incoming connections or messages. The binding process varies depending on
     * the transport protocol:</p>
     * <ul>
     *   <li><b>ZMQ</b>: Creates socket, configures parameters, and binds to specified address</li>
     *   <li><b>TCP</b>: Binds TCP socket to specified host and port</li>
     *   <li><b>UDP</b>: Binds UDP socket to specified host and port</li>
     * </ul>
     *
     * <p>The binding process typically includes:</p>
     * <ol>
     *   <li>Socket creation and configuration</li>
     *   <li>Network interface binding</li>
     *   <li>Protocol-specific initialization</li>
     *   <li>Server state validation</li>
     * </ol>
     *
     * <p><b>Server State:</b></p>
     * <ul>
     *   <li>After successful binding, the server is ready to send and receive messages</li>
     *   <li>Binding failures should be handled gracefully with appropriate error reporting</li>
     * </ul>
     *
     * <p><b>Configuration Requirements:</b></p>
     * <ul>
     *   <li>Host address and port must be specified in server configuration</li>
     *   <li>Transport-specific parameters (socket type, buffer sizes) should be configured</li>
     *   <li>Security settings (if applicable) must be properly configured</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * server.bind()
     *     .onSuccess(v -> {
     *         logger.info("Server bound successfully");
     *         // Start receiving messages
     *         server.receive();
     *     })
     *     .onFailure(error -> {
     *         logger.error("Binding failed", error);
     *         // Handle binding failure
     *         handleBindingError(error);
     *     });
     * }</pre>
     *
     * @return a Future that completes successfully when the server is bound,
     *         or fails with an exception if binding cannot be completed
     * @throws IllegalStateException if the server is already bound
     * @throws IllegalArgumentException if the server configuration is invalid
     * @see #send(String, byte[])
     * @see #receive()
     * @see #close()
     */
    Future<Void> bind();

    /**
     * Sends a message to connected clients with the specified topic.
     *
     * <p>This method transmits the provided byte array as a message to connected clients,
     * using the specified topic for message routing. The sending behavior depends on the
     * transport protocol and socket mode:</p>
     * <ul>
     *   <li><b>ZMQ PUB</b>: Publishes message with topic to all subscribed clients</li>
     *   <li><b>ZMQ PUSH</b>: Sends message to connected PULL clients in load-balanced fashion</li>
     *   <li><b>TCP/UDP</b>: Sends message to all connected clients</li>
     * </ul>
     *
     * <p><b>Synchronous Operation:</b></p>
     * <ul>
     *   <li>This method is synchronous and may block until the message is queued for sending</li>
     *   <li>Actual network transmission may be asynchronous depending on the transport</li>
     *   <li>For high-throughput scenarios, consider the transport's buffering behavior</li>
     * </ul>
     *
     * <p><b>Message Delivery:</b></p>
     * <ul>
     *   <li>Message delivery guarantees depend on the underlying transport protocol</li>
     *   <li>ZMQ provides different delivery patterns (pub-sub, push-pull, etc.)</li>
     *   <li>Some transports may drop messages if clients are disconnected or slow</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * // Send a message with topic "metrics" to all connected clients
     * byte[] payload = serializeMetricData(metricData);
     * server.send("metrics", payload);
     *
     * // Send a control message
     * server.send("control", "{\"command\":\"restart\"}".getBytes());
     * }</pre>
     *
     * @param topic the message topic used for routing or filtering
     * @param data the message payload as a byte array
     * @throws IllegalStateException if the server is not bound
     * @throws NullPointerException if topic or data is null
     * @see #bind()
     * @see #receive()
     */
    void send(String topic, byte[] data);

    /**
     * Starts asynchronous message reception from connected clients.
     *
     * <p>This method initiates the message receiving process, which typically runs
     * in a separate thread or event loop. Received messages are processed according
     * to the server implementation, which may include:</p>
     * <ul>
     *   <li>Forwarding messages to the Vert.x event bus</li>
     *   <li>Invoking registered message handlers</li>
     *   <li>Storing messages for later processing</li>
     *   <li>Filtering or transforming messages</li>
     * </ul>
     *
     * <p><b>Asynchronous Operation:</b></p>
     * <ul>
     *   <li>This method returns immediately after starting the receive operation</li>
     *   <li>Message reception continues until the server is closed</li>
     *   <li>Received messages are processed asynchronously</li>
     * </ul>
     *
     * <p><b>Error Handling:</b></p>
     * <ul>
     *   <li>Reception errors should be logged but should not stop the receive loop</li>
     *   <li>Critical errors may be reported through the returned Future</li>
     *   <li>Connection failures should trigger appropriate recovery mechanisms</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * server.bind().onSuccess(v -> {
     *     // Start receiving messages
     *     server.receive().onSuccess(v2 -> {
     *         logger.info("Message reception started");
     *     }).onFailure(error -> {
     *         logger.error("Failed to start message reception", error);
     *     });
     * });
     * }</pre>
     *
     * @return a Future that completes successfully when message reception is started,
     *         or fails with an exception if reception cannot be started
     * @throws IllegalStateException if the server is not bound or reception is already active
     * @see #bind()
     * @see #send(String, byte[])
     * @see #close()
     */
    Future<Void> receive();

    /**
     * Closes the server and releases all associated resources.
     *
     * <p>This method performs a clean shutdown of the server, which typically includes:</p>
     * <ul>
     *   <li>Stopping message reception</li>
     *   <li>Closing network connections</li>
     *   <li>Releasing socket and other system resources</li>
     *   <li>Terminating background threads</li>
     * </ul>
     *
     * <p><b>Synchronous Operation:</b></p>
     * <ul>
     *   <li>This method is synchronous and blocks until the server is fully closed</li>
     *   <li>All pending operations should be completed or aborted</li>
     *   <li>Resources should be released even if errors occur during shutdown</li>
     * </ul>
     *
     * <p><b>Thread Safety:</b></p>
     * <ul>
     *   <li>This method can be called from any thread</li>
     *   <li>Multiple concurrent calls should be handled safely (idempotent operation)</li>
     *   <li>After closing, the server cannot be reused; a new instance must be created</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * // Shutdown hook or application termination
     * Runtime.getRuntime().addShutdownHook(new Thread(() -> {
     *     logger.info("Shutting down server");
     *     server.close();
     * }));
     *
     * // Explicit shutdown
     * void shutdown() {
     *     server.close();
     *     logger.info("Server closed");
     * }
     * }</pre>
     *
     * @throws IllegalStateException if the server is already closed
     * @see #bind()
     * @see #receive()
     */
    Future<Void> close();
}