/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.notification;

import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Promise;
import io.vertx.core.datagram.DatagramSocket;
import io.vertx.core.json.JsonObject;

import static com.mindarray.GlobalConstants.ERROR_CODE;

/**
 * Implementation of the Notification abstract class for sending syslog notifications.
 * <p>
 * This class handles syslog notifications by sending UDP datagrams to specified
 * syslog servers. Syslog is a standard protocol used for system logging and is
 * commonly used for log management and security auditing.
 * <p>
 * The notification process:
 * <ol>
 *   <li>Validates that the target host, port, and message are specified</li>
 *   <li>Sends the message as a UDP datagram to the specified target</li>
 *   <li>Reports success or failure of the syslog message delivery</li>
 * </ol>
 * <p>
 * The notification event should contain:
 * <ul>
 *   <li>{@link GlobalConstants#TARGET}: The target syslog server hostname or IP address</li>
 *   <li>{@link GlobalConstants#PORT}: The UDP port number of the syslog server</li>
 *   <li>{@link GlobalConstants#MESSAGE}: The syslog message content to send</li>
 * </ul>
 * <p>
 * This implementation uses Vert.x's DatagramSocket to perform non-blocking UDP
 * communication with the syslog server.
 */
class SyslogNotification extends Notification
{
    /**
     * Logger for syslog notification operations
     */
    private static final Logger LOGGER = new Logger(SyslogNotification.class, GlobalConstants.MOTADATA_NOTIFICATION, "Syslog Notification");

    /**
     * Socket for sending UDP datagrams to syslog servers
     */
    private final DatagramSocket socket;

    /**
     * Constructs a SyslogNotification with the specified datagram socket.
     *
     * @param socket The DatagramSocket instance to use for sending syslog messages
     */
    public SyslogNotification(DatagramSocket socket)
    {
        this.socket = socket;
    }

    /**
     * Processes and sends a syslog notification based on the provided event data.
     * <p>
     * This method:
     * <ul>
     *   <li>Validates that the target host, port, and message are specified</li>
     *   <li>Sends the message as a UDP datagram to the specified target</li>
     *   <li>Reports success or failure of the syslog message delivery</li>
     * </ul>
     *
     * @param event A JsonObject containing the syslog notification parameters
     *              (target host, port, message)
     * @return A Promise that resolves to a JsonObject containing the result of the
     * syslog notification attempt, including success/failure status and any error details
     */
    @Override
    public Promise<JsonObject> notify(JsonObject event)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            // Validate that the target host and port are specified
            if (CommonUtil.isNotNullOrEmpty(event.getString(GlobalConstants.TARGET)) && event.getInteger(GlobalConstants.PORT) != null)
            {
                // Validate that the message is specified
                if (CommonUtil.isNotNullOrEmpty(event.getString(GlobalConstants.MESSAGE)))
                {
                    // Send the message as a UDP datagram to the specified target and port
                    socket.send(event.getString(GlobalConstants.MESSAGE),
                            event.getInteger(GlobalConstants.PORT),
                            event.getString(GlobalConstants.TARGET),
                            result ->
                            {
                                // If the datagram was sent successfully
                                if (result.succeeded())
                                {
                                    // Mark the notification as successful
                                    promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS));
                                }
                                // If the datagram could not be sent
                                else
                                {
                                    // Log the error
                                    LOGGER.error(result.cause());

                                    // Mark the notification as failed and include error details
                                    promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                            .put(GlobalConstants.MESSAGE, result.cause().getMessage())
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                                }
                            });
                }
                // If no message was specified, complete the promise with the original event
                else
                {
                    promise.complete(event);
                }
            }
            // If no target host or port was specified, complete the promise with the original event
            else
            {
                promise.complete(event);
            }
        }
        // Handle any exceptions that occur during syslog notification processing
        catch (Exception exception)
        {
            // Log the exception
            LOGGER.error(exception);

            // Mark the notification as failed and include error details
            promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(GlobalConstants.MESSAGE, exception.getMessage())
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
        }

        return promise;
    }
}
