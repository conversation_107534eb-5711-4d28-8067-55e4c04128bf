/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.metricscheduler;

import com.mindarray.Bootstrap;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.MetricScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_NAME;
import static com.mindarray.api.AIOpsObject.OBJECT_TARGET;
import static com.mindarray.eventbus.EventBusConstants.EVENT_METRIC_POLL_RESPONSE;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(120 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestMetricScheduler
{
    private static final Logger LOGGER = new Logger(TestMetricScheduler.class, MOTADATA_NMS, "Test Metric Scheduler");

    private static final JsonObject METRICS = new JsonObject();

    public static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        Bootstrap.restartVerticle(MetricScheduler.class).onComplete(restart ->
        {
            if (restart.succeeded())
            {
                METRICS.put(NMSConstants.MetricPlugin.LINUX.getName(), "172.16.9.98").put(NMSConstants.MetricPlugin.HYPER_V.getName(), "172.16.10.10")
                        .put(NMSConstants.MetricPlugin.PRISM.getName(), "172.16.10.212").put(NMSConstants.MetricPlugin.CISCO_VBOND.getName(), "10.10.1.3")
                        .put(NMSConstants.MetricPlugin.VMWARE_ESXI.getName(), "172.16.11.11").put(NMSConstants.MetricPlugin.CITRIX_XEN.getName(), "172.16.8.52")
                        .put(NMSConstants.MetricPlugin.CITRIX_XEN.getName(), "172.16.8.52").put(NMSConstants.MetricPlugin.VCENTER.getName(), "172.16.10.180")
                        .put(NMSConstants.MetricPlugin.RUCKUS_WIRELESS.getName(), "10.20.40.4").put(NMSConstants.MetricPlugin.ARUBA_WIRELESS.getName(), "172.16.10.242")
                        .put(NMSConstants.MetricPlugin.AMAZON_EC2.getName(), "ServiceOps_Saas_AMI(ap-south-1)").put(NMSConstants.MetricPlugin.CISCO_MERAKI.getName(), "n149.meraki.com");


                LoggerCacheStore.getStore().update(JsonArray.of(
                        MOTADATA_SYSTEM, MOTADATA_OBSERVER, MOTADATA_PLUGIN_ENGINE, MOTADATA_LOG, MOTADATA_FLOW,
                        MOTADATA_NMS, MOTADATA_STREAMING, MOTADATA_RUNBOOK, MOTADATA_DB, MOTADATA_DATASTORE,
                        MOTADATA_CACHE, MOTADATA_STORE, MOTADATA_POLICY, MOTADATA_UTIL, MOTADATA_AIOPS,
                        MOTADATA_PATCH, MOTADATA_TRACE, MOTADATA_NOTIFICATION, MOTADATA_JOB, MOTADATA_CONFIGURATION,
                        MOTADATA_EVENT_BUS, MOTADATA_API, MOTADATA_AGENT, MOTADATA_APP_MANAGER, MOTADATA_AUDIT,
                        MOTADATA_HA, MOTADATA_VISUALIZATION, MOTADATA_REPORTING, MOTADATA_COMPLIANCE
                ));

                testContext.completeNow();
            }
            else
            {
                LOGGER.debug(restart.cause().getMessage());

                testContext.failNow("Failed to restart Metric Scheduler...");
            }
        });
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        try
        {
            var devMode = MotadataConfigUtil.class.getDeclaredField("devMode");

            devMode.setAccessible(true);

            devMode.set(devMode, true);

            Assertions.assertTrue(MotadataConfigUtil.devMode());

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var now = LocalDateTime.now();

        var seconds = ChronoUnit.SECONDS.between(now, now.plusHours(1).truncatedTo(ChronoUnit.HOURS));

        if (seconds <= 60)
        {
            testContext.awaitCompletion(seconds + 2, TimeUnit.SECONDS);
        }

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Timeout(150 * 1000)
    @Order(1)
    void testValidateMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var responses = new HashMap<String, Boolean>();

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_METRIC_POLL_RESPONSE, message ->
            {
                try
                {
                    var event = message.body();

                    if (event.containsKey(Metric.METRIC_PLUGIN))
                    {
                        LOGGER.trace("polling event : " + event);

                        var key = event.getString(Metric.METRIC_PLUGIN) + "=" + event.getString(OBJECT_TARGET);

                        if (responses.containsKey(key))
                        {
                            responses.put(key, Boolean.TRUE);

                            TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + event.getLong(Metric.METRIC_OBJECT),
                                    new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(MetricConfigStore.getStore().getItem(event.getLong(ID)).put(Metric.METRIC_POLLING_TIME, 50000))),
                                    testContext.succeeding(response -> testContext.verify(() -> TestAPIUtil.assertValidResponseTestResult(response, LOGGER, EMPTY_VALUE))));
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            METRICS.forEach(entry ->
            {
                try
                {
                    var id = NMSConstants.getCategory(NMSConstants.Type.valueOfName(ObjectManagerCacheStore.getStore().getItemByMetricPlugin(entry.getKey()).getString(Metric.METRIC_TYPE))) == NMSConstants.Category.CLOUD ? ObjectConfigStore.getStore().getItemByTarget(entry.getValue().toString()) : ObjectConfigStore.getStore().getItemByIP(entry.getValue().toString());

                    assertNotNull(id);

                    if (MetricConfigStore.getStore().getItemByMetricPlugin(id, entry.getKey()) != NOT_AVAILABLE)
                    {
                        var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, entry.getKey()));

                        item.put(Metric.METRIC_POLLING_TIME, 10);

                        item.put(Metric.METRIC_STATE, NMSConstants.State.ENABLE.name());

                        ObjectStatusCacheStore.getStore().updateItem(id, STATUS_UP, DateTimeUtil.currentSeconds());

                        TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + item.getLong(Metric.METRIC_OBJECT),
                                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(item)),
                                testContext.succeeding(response -> testContext.verify(() ->
                                {
                                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                                    responses.put(entry.toString(), false);
                                })));
                    }
                    else
                    {
                        testContext.failNow(String.format("Metric %s not found for target %s", entry.getKey(), entry.getValue()));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });

            var retries = new AtomicInteger();

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(10), timer ->
            {
                try
                {
                    LOGGER.info(responses.toString());

                    if (responses.values().stream().noneMatch(Boolean.FALSE::equals))
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                    else if (retries.get() > 14)
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        testContext.failNow("all the expected metrics are not scheduled");
                    }
                    else
                    {
                        retries.incrementAndGet();
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCheckDownMonitorUnreachableInstances(VertxTestContext testContext)
    {
        try
        {
            var id = ObjectConfigStore.getStore().getItemByIP("**********");

            var field = ObjectStatusCacheStore.class.getDeclaredField("items");

            field.setAccessible(true);

            var provisionedObjects = (Map<Long, String>) field.get(ObjectStatusCacheStore.getStore());

            provisionedObjects.put(id, STATUS_DOWN);

            TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_METRIC_POLL_RESPONSE, message ->
            {

                var metric = message.body();

                if (metric != null && metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()) && "**********".equalsIgnoreCase(metric.getString(AIOpsObject.OBJECT_IP)))
                {
                    var result = metric.getJsonObject(RESULT);

                    Assertions.assertNotNull(result);

                    Assertions.assertEquals(STATUS_SUCCEED, metric.getString(STATUS));

                    Assertions.assertTrue(result.containsKey(NMSConstants.DISCOVERABLE_INSTANCES.get(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())));

                    var objects = result.getJsonArray(NMSConstants.DISCOVERABLE_INSTANCES.get(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

                    Assertions.assertTrue(objects != null && !objects.isEmpty());

                    for (var index = 0; index < objects.size(); index++)
                    {
                        var object = objects.getJsonObject(index);

                        Assertions.assertEquals(STATUS_UNREACHABLE, object.getString(STATUS));
                    }

                    //setting monitor status Up, after the test-case
                    provisionedObjects.put(id, STATUS_UP);

                    testContext.completeNow();
                }

            });

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_POLL_SCHEDULE, id);

            testContext.awaitCompletion(30, TimeUnit.SECONDS);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testScheduleServerDownMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var devMode = MotadataConfigUtil.class.getDeclaredField("devMode");

            devMode.setAccessible(true);

            devMode.set(devMode, false);

            Assertions.assertFalse(MotadataConfigUtil.devMode());

            var id = ObjectConfigStore.getStore().getItemByIP(METRICS.getString(NMSConstants.MetricPlugin.LINUX.getName()));

            assertNotNull(id);

            assertMetricSchedulerDownMetrics(testContext, testInfo, id, ObjectConfigStore.getStore().getItem(id).getString(OBJECT_NAME), false);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testScheduleHCIDownMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP(METRICS.getString(NMSConstants.MetricPlugin.PRISM.getName()));

        assertNotNull(id);

        assertMetricSchedulerDownMetrics(testContext, testInfo, id, ObjectConfigStore.getStore().getItem(id).getString(OBJECT_NAME), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testScheduleSDNDownMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP(METRICS.getString(NMSConstants.MetricPlugin.CISCO_VBOND.getName()));

        assertNotNull(id);

        assertMetricSchedulerDownMetrics(testContext, testInfo, id, ObjectConfigStore.getStore().getItem(id).getString(OBJECT_NAME), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testScheduleVirtualizationDownMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP(METRICS.getString(NMSConstants.MetricPlugin.VMWARE_ESXI.getName()));

        assertNotNull(id);

        assertMetricSchedulerDownMetrics(testContext, testInfo, id, ObjectConfigStore.getStore().getItem(id).getString(OBJECT_NAME), true);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testScheduleWirelessDownMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP(METRICS.getString(NMSConstants.MetricPlugin.RUCKUS_WIRELESS.getName()));

        assertNotNull(id);

        assertMetricSchedulerDownMetrics(testContext, testInfo, id, ObjectConfigStore.getStore().getItem(id).getString(OBJECT_NAME), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    public void testObjectStatusDownFlapCount(VertxTestContext testContext) throws Exception
    {
        var field = ObjectStatusCacheStore.class.getDeclaredField("STATUS_DOWN_FLAPS");

        field.setAccessible(true);

        try
        {
            field.setInt(ObjectStatusCacheStore.getStore(), 2);

            var id = ObjectConfigStore.getStore().getItemByIP(METRICS.getString(NMSConstants.MetricPlugin.LINUX.getName()));

            var store = ObjectStatusCacheStore.getStore();

            var timestamp = System.currentTimeMillis();

            store.updateItem(id, STATUS_UP, timestamp);

            assertEquals(STATUS_UP, store.getItem(id));

            // First DOWN update - should not change status yet
            store.updateItem(id, EMPTY_VALUE, STATUS_DOWN, timestamp + 5000);

            assertFalse(store.eligible(id, EMPTY_VALUE)); // must not be eligible

            assertEquals(STATUS_UP, store.getItem(id)); // Status must be Up

            // Second DOWN update - should change status to DOWN
            store.updateItem(id, EMPTY_VALUE, STATUS_DOWN, timestamp + 10000);

            assertFalse(store.eligible(id, EMPTY_VALUE)); // must not be eligible

            assertEquals(STATUS_DOWN, store.getItem(id)); // Status must be Down

            // Update to UP - should reset flap count and change status
            store.updateItem(id, EMPTY_VALUE, STATUS_UP, timestamp + 20000);

            assertTrue(store.eligible(id, EMPTY_VALUE)); // Eligible again

            assertEquals(STATUS_UP, store.getItem(id)); // Status must be UP now

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
        finally
        {
            // Restore original value
            field.setInt(ObjectStatusCacheStore.getStore(), 1);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    public void testInterfaceStatusDownFlapCount(VertxTestContext testContext) throws Exception
    {
        var field = ObjectStatusCacheStore.class.getDeclaredField("STATUS_DOWN_FLAPS");

        field.setAccessible(true);

        try
        {
            // Set OBJECT_STATUS_DOWN_FLAP_COUNT to 2 via reflection
            field.setInt(ObjectStatusCacheStore.getStore(), 2);

            var id = ObjectConfigStore.getStore().getItemByIP("*************");

            assertNotNull(id);

            var store = ObjectStatusCacheStore.getStore();

            var interfaceName = store.getInstanceItems(id, true).entrySet().iterator().next().getKey();

            var timestamp = System.currentTimeMillis();

            store.updateItem(id, STATUS_UP, timestamp);

            store.updateItem(id, interfaceName, STATUS_UP, timestamp + 2000);

            assertEquals(STATUS_UP, store.getInstanceItems(id, false).get(interfaceName));

            // First DOWN update - should not change status yet
            store.updateItem(id, interfaceName, STATUS_DOWN, timestamp + 4000);

            assertEquals(STATUS_UP, store.getInstanceItems(id, false).get(interfaceName)); // Status must be Up

            // Second DOWN update - should change status to DOWN
            store.updateItem(id, interfaceName, STATUS_DOWN, timestamp + 10000);

            assertEquals(STATUS_DOWN, store.getInstanceItems(id, false).get(interfaceName)); // Status must be Down

            // Update to UP - should reset flap count and change status
            store.updateItem(id, interfaceName, STATUS_UP, timestamp + 20000);

            assertEquals(STATUS_UP, store.getInstanceItems(id, false).get(interfaceName)); // Status must be UP now

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
        finally
        {
            // Restore original value
            field.setInt(ObjectStatusCacheStore.getStore(), 1);
        }
    }

    private void assertMetricSchedulerDownMetrics(VertxTestContext testContext, TestInfo testInfo, long id, String objectName, boolean availability)
    {
        try
        {
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + "nms" + PATH_SEPARATOR + "$$$-Metric Scheduler.log".replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy HH").format(new Date())));

            if (file.exists())
            {
                FileUtils.moveFile(file, new File(CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + "nms" + PATH_SEPARATOR + "$$$-Metric Scheduler($$).log".replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy HH").format(new Date())).replace("$$", String.valueOf(System.currentTimeMillis()))));
            }

            ObjectStatusCacheStore.getStore().updateItem(id, EMPTY_VALUE, STATUS_DOWN, DateTimeUtil.currentSeconds());

            testContext.awaitCompletion(2, TimeUnit.SECONDS);

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_POLL_SCHEDULE, id);

            TestUtil.vertx().setTimer(10000, timer ->
            {
                try
                {
                    if (file.exists())
                    {
                        var buffer = FileUtils.readFileToString(file, StandardCharsets.UTF_8);

                        LOGGER.info(testInfo.getTestMethod().get().getName() + " and buffer is : " + buffer);

                        if (buffer.contains(String.format("manual poll event received for object %s", objectName)) && buffer.split(NEW_LINE).length >= 1)
                        {
                            try
                            {
                                var dirty = false;

                                for (var line : buffer.split(NEW_LINE))
                                {
                                    if (line.contains(String.format("object %s is scheduled to poll", objectName)) && !line.contains(!availability ? "Object Status" : "Availability"))
                                    {
                                        dirty = true;

                                        LOGGER.debug(String.format("Unwanted metric is scheduled to poll : line is : %s", line));
                                    }
                                }

                                if (dirty)
                                {
                                    testContext.failNow("Unwanted metric is scheduled to poll");
                                }
                                else if (buffer.contains(String.format("metric %s of object %s is scheduled to poll", !availability ? "Object Status" : "Availability", objectName)))
                                {
                                    FileUtils.moveFile(file, new File(CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + "nms" + PATH_SEPARATOR + "$$$-Metric Scheduler($$).log".replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy HH").format(new Date())).replace("$$", String.valueOf(System.currentTimeMillis()))));

                                    ObjectStatusCacheStore.getStore().updateItem(id, EMPTY_VALUE, STATUS_UP, DateTimeUtil.currentSeconds());

                                    testContext.awaitCompletion(2, TimeUnit.SECONDS);

                                    TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_POLL_SCHEDULE, id);

                                    TestUtil.vertx().setTimer(10000, timerId ->
                                    {
                                        try
                                        {
                                            if (file.exists())
                                            {
                                                var content = FileUtils.readFileToString(file, StandardCharsets.UTF_8);

                                                LOGGER.info(testInfo.getTestMethod().get().getName() + " and logs are  : " + content);

                                                if (content.contains(String.format("manual poll event received for object %s", objectName)) && content.split(NEW_LINE).length >= 1)
                                                {
                                                    for (var line : content.split(NEW_LINE))
                                                    {
                                                        if (line.contains(String.format("object %s is scheduled to poll", objectName)) && !line.contains(!availability ? "Object Status" : "Availability")
                                                                && content.contains(String.format("metric %s of object %s is scheduled to poll", !availability ? "Object Status" : "Availability", objectName)))
                                                        {
                                                            testContext.completeNow();

                                                            break;
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    TestUtil.vertx().cancelTimer(timer);

                                                    testContext.failNow("expected logs not found for metric scheduler");
                                                }
                                            }
                                            else
                                            {
                                                testContext.failNow("metric scheduler file doesn't exists while status is up");
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            testContext.failNow(exception);
                                        }
                                    });
                                }
                                else
                                {
                                    testContext.failNow(!availability ? "Object Status" : "Availability" + " is not polled when monitor is down");
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }
                        else
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            testContext.failNow("expected logs not found for metric scheduler");
                        }
                    }
                    else
                    {
                        testContext.failNow("metric scheduler file doesn't exists while status is down");
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
