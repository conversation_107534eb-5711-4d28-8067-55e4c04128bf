/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIUtil;
import com.mindarray.api.FlowGeolocationMapper;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FlowGeolocationMapperConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(FlowGeolocationMapperConfigStore.class, GlobalConstants.MOTADATA_STORE, "Flow Geolocation Mapper Config Store");

    private static final FlowGeolocationMapperConfigStore STORE = new FlowGeolocationMapperConfigStore();

    private final Map<String, List<String>> mappings = new ConcurrentHashMap<>();  //{"127.0.0.1" : city#country}

    private FlowGeolocationMapperConfigStore()
    {
        super(DBConstants.TBL_FLOW_GEOLOCATION_MAPPER, LOGGER, true);
    }

    public static FlowGeolocationMapperConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_FLOW_GEOLOCATION_MAPPER, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    mappings.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            update(item.getLong(GlobalConstants.ID), true);
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                else
                {
                    promise.fail(result.cause());

                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));
                }

            });
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }

        return promise.future();
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                update(id, true);
            }
        });
    }

    @Override
    public void addItem(long id, JsonObject item)
    {
        if (item != null)
        {
            this.items.put(id, item);

            update(id, true);
        }
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        update(id, false);        // delete item first from Cache as we don't know what user has deleted from range

        return super.updateItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                update(id, true);
            }
        });
    }

    @Override
    public void deleteItem(long id)
    {
        update(id, false);

        super.deleteItem(id);

    }

    @Override
    public void deleteItems(JsonArray ids)
    {
        if (ids != null && !ids.isEmpty())
        {
            for (var index = 0; index < ids.size(); index++)
            {
                this.deleteItem(ids.getLong(index));
            }
        }
    }

    /**
     * update cache
     * if IP update directly
     * if Range - Calculate Range and update
     *
     * @param id
     */
    private void update(long id, boolean add)
    {
        try
        {
            var item = items.get(id);

            if (item != null)
            {
                var ipAddresses = item.getJsonArray(FlowGeolocationMapper.FLOW_GEOLOCATION_MAPPER_GROUP);

                for (var index = 0; index < ipAddresses.size(); index++)
                {
                    var ipGroup = ipAddresses.getString(index); //Could be in single ip / Range of Ips

                    if (ipGroup.contains("-"))
                    {
                        var tokens = ipGroup.split("-");

                        var start = tokens[0].trim();

                        if (tokens[1].trim().matches("\\d+"))//in case user provides range like ***********-55
                        {
                            update(start, start.substring(0, start.lastIndexOf('.')) + "." + tokens[1].trim(), item, add);
                        }
                        else
                        {
                            //in case user provides range like ***********-*********** or xx.xx.10.1-xx.1.2.5
                            update(start, ipGroup.split("-")[1].trim(), item, add);
                        }
                    }
                    else if (ipGroup.contains("xx"))
                    {
                        update(ipGroup, ipGroup, item, add);
                    }
                    else
                    {
                        if (add)
                        {
                            mappings.put(ipGroup, new ArrayList<>(List.of(
                                    item.getString(FlowGeolocationMapper.FLOW_GEOLOCATION_MAPPER_COUNTRY),         // index 0
                                    item.getString(FlowGeolocationMapper.FLOW_GEOLOCATION_MAPPER_CITY, GlobalConstants.EMPTY_VALUE)  // index 1
                            )));
                        }
                        else
                        {
                            mappings.remove(ipGroup);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void update(String start, String end, JsonObject item, boolean add)
    {
        try
        {
            if (start.contains("xx"))
            {
                start = start.replaceAll("xx", "0");
            }
            if (end.contains("xx"))
            {
                end = end.replaceAll("xx", "255");
            }

            if (APIUtil.validateRange(start, end))
            {
                if (add)
                {
                    var country = item.getString(FlowGeolocationMapper.FLOW_GEOLOCATION_MAPPER_COUNTRY);

                    var city = item.getString(FlowGeolocationMapper.FLOW_GEOLOCATION_MAPPER_CITY, GlobalConstants.EMPTY_VALUE);

                    CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end))
                            .forEach(ip -> mappings.put(ip, new ArrayList<>(List.of(
                                    country,    // index 0
                                    city       // index 1
                            ))));
                }
                else
                {
                    CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).forEach(mappings::remove);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /*
        Set City and Country in flow as per ip, flow and flowType.
     */
    public void setGeoLocation(String ip, JsonObject flow, String flowType)
    {
        var mapping = mappings.get(ip);

        if (mapping != null)
        {

            flow.put(flowType + ".country", mapping.get(0));

            /*
               As City is not a required field, if there is any data mapped to it, it will map it in flow.
             */
            if (!mapping.get(0).isEmpty())
            {
                flow.put(flowType + ".city", mapping.get(1));
            }
        }
    }

}
