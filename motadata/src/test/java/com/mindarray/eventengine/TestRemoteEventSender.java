/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventengine;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.RemoteEventSender;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.lang.reflect.Field;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.HOST;
import static com.mindarray.GlobalConstants.PORT;
import static com.mindarray.eventbus.EventBusConstants.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(30 * 1000)

public class TestRemoteEventSender
{
    private static final Logger LOGGER = new Logger(TestRemoteEventSender.class, GlobalConstants.MOTADATA_EVENT_BUS, "Remote Event Engine Test");

    private static final AtomicBoolean HAS_MORE_EVENTS = new AtomicBoolean();

    @BeforeAll
    static void setup(VertxTestContext testContext)
    {
        var config = new JsonObject("{\"system.boot.sequence\":\"COLLECTOR\",\"event.subscriber.host\":\"localhost\",\"event.publisher.host\":\"localhost\",\"event.subscriber.port\":50001}");

        config.put("event.remote.socket.reconnect.interval.seconds", 1);

        MotadataConfigUtil.loadConfigs(config);

        Field field;

        try
        {
            field = Bootstrap.class.getDeclaredField("bootstrapType");

            field.setAccessible(true);

            field.set(new Bootstrap(), GlobalConstants.BootstrapType.COLLECTOR);

            LOGGER.info(CommonUtil.getEventReceiverPort());

            testContext.completeNow();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    private static void subscribe(ZMQ.Socket forwarderReceiver, int port, String filter, Promise<Void> promise)
    {
        LOGGER.info(String.format("starting thread : %s at port  : %s ",filter,port));

        forwarderReceiver.setHWM(MotadataConfigUtil.getEventBacklogSize());

        forwarderReceiver.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

        forwarderReceiver.bind("tcp://*:" + port);

        var retries = new AtomicInteger();

        new Thread(() ->
        {
            while (HAS_MORE_EVENTS.get())
            {
                try
                {
                    if (!forwarderReceiver.recvStr().isEmpty())
                    {
                        LOGGER.info("Event received :" + retries.get());

                        retries.getAndIncrement();

                        if (retries.get() == 3)
                        {
                            promise.complete();
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        }, filter).start();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testRemoteEventSenderCollector(VertxTestContext testContext)
    {
        try
        {
            var future = Promise.<Void>promise();

            HAS_MORE_EVENTS.set(true);

            Bootstrap.undeployVerticle(RemoteEventSender.class.getSimpleName()).onComplete(undeploy -> future.complete());

            testContext.awaitCompletion(2, TimeUnit.SECONDS);

            future.future().onComplete(asyncResult ->
                    Bootstrap.startEngine(new RemoteEventSender(new JsonObject().put(AUTO_RECONNECT,true).put(TRANSPORT_PROTOCOL, TransportProtocol.ZMQ.getValue()).put(SOCKET_MODE, EventBusConstants.SocketMode.PUSH)
                                    .put(PORT,CommonUtil.getEventReceiverPort()).put(HOST,CommonUtil.getRemoteEventSender())), RemoteEventSender.class.getSimpleName(), null)
                            .onComplete(result -> testContext.completeNow()));

            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            var promise = Promise.<Void>promise();

            var remoteSender = Bootstrap.zcontext().socket(SocketType.PULL);

            subscribe(remoteSender, CommonUtil.getEventReceiverPort(), "Remote Event Sender Collector", promise);

            for (var index = 0; index < 10; index++)
            {
                TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_REMOTE, new JsonObject().put("message", "device poll " + index + "@" + System.currentTimeMillis()));
            }

            TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_REMOTE, new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.EVENT_METRIC_POLL).put("message", "device poll " + 23 + "@" + System.currentTimeMillis()));

            LOGGER.info(String.format("events sent for testing , port : %s ",CommonUtil.getEventReceiverPort()));

            promise.future().onComplete(result ->
            {
                HAS_MORE_EVENTS.set(false);

                if (result.succeeded())
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testRemoteEventSenderAgent(VertxTestContext testContext)
    {
        try
        {

            var config = new JsonObject("{\"system.boot.sequence\":\"AGENT\",\"event.subscriber.host\":\"localhost\",\"event.subscriber.port\":2355}");

            config.put("event.remote.socket.reconnect.interval.seconds", 1);

            MotadataConfigUtil.loadConfigs(config);

            var field = Bootstrap.class.getDeclaredField("bootstrapType");

            field.setAccessible(true);

            field.set(new Bootstrap(), GlobalConstants.BootstrapType.AGENT);

            var future = Promise.<Void>promise();

            HAS_MORE_EVENTS.set(true);

            Bootstrap.undeployVerticle(RemoteEventSender.class.getSimpleName()).onComplete(undeploy -> future.complete());

            future.future().onComplete(asyncResult ->
                    Bootstrap.startEngine(new RemoteEventSender(new JsonObject().put(AUTO_RECONNECT,true).put(TRANSPORT_PROTOCOL, TransportProtocol.ZMQ.getValue()).put(SOCKET_MODE, EventBusConstants.SocketMode.PUSH)
                                    .put(PORT,CommonUtil.getEventSenderPort()).put(HOST,CommonUtil.getRemoteEventSender())), RemoteEventSender.class.getSimpleName(), null)
                            .onComplete(result -> testContext.completeNow()));

            var remotePublisher = Bootstrap.zcontext().socket(SocketType.PULL);

            var promise = Promise.<Void>promise();

            subscribe(remotePublisher, 2355, "Remote Event Forwarder Agent", promise);

            for (var index = 0; index < 3; index++)
            {
                TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_AGENT, new JsonObject().put("message", "device poll " + index + "@" + System.currentTimeMillis()));
            }

            promise.future().onComplete(result ->
            {
                HAS_MORE_EVENTS.set(false);

                if (result.succeeded())
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
