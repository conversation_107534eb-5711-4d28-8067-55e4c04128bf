/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * The EventTracker class tracks the status and progress of events as they flow through the system.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Adding events to a tracking queue with timestamps and initial state</li>
 *   <li>Updating event progress and state as they move through the system</li>
 *   <li>Completing events with success, failure, or abort status</li>
 *   <li>Maintaining a limited-size cache of recent events for monitoring and debugging</li>
 *   <li>Providing event context information to requesting components</li>
 *   <li>Streaming event updates to clients in real-time</li>
 *   <li>Logging event details when logging is enabled</li>
 * </ul>
 * <p>
 * The EventTracker maintains a fixed-size cache of events, removing the oldest events when the
 * cache reaches its capacity limit. Each event is tracked with its state, progress log, context
 * information, and other metadata.
 * <p>
 * The class supports real-time streaming of event updates to clients, which can be enabled or
 * disabled through change notification events. When streaming is enabled, all event updates are
 * broadcast to subscribed clients.
 * <p>
 * Example usage:
 * <pre>
 * // Add a new event to the tracker
 * JsonObject event = new JsonObject()
 *     .put(EVENT_ID, CommonUtil.newEventId())
 *     .put(EVENT_TYPE, "discovery")
 *     .put(EVENT_CONTEXT, new JsonObject()
 *         .put(OBJECT_IP, "***********")
 *         .put(OBJECT_NAME, "router-1"));
 *
 * vertx.eventBus().send(EVENT_ADD, event);
 *
 * // Update event progress
 * JsonObject update = new JsonObject()
 *     .put(EVENT_ID, event.getLong(EVENT_ID))
 *     .put(EVENT_PROGRESS_LOG, "Discovered 5 interfaces");
 *
 * vertx.eventBus().send(EVENT_PROGRESS_UPDATE, update);
 *
 * // Complete the event with success
 * JsonObject complete = new JsonObject()
 *     .put(EVENT_ID, event.getLong(EVENT_ID))
 *     .put(EVENT_CONTEXT, new JsonObject()
 *         .put("discoveredDevices", 10));
 *
 * vertx.eventBus().send(EVENT_SUCCEED, complete);
 * </pre>
 */
public class EventTracker extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(EventTracker.class, GlobalConstants.MOTADATA_EVENT_BUS, "Event Tracker");
    private static final int MAX_EVENTS = MotadataConfigUtil.getEventCacheEntries();
    private final Map<Long, JsonObject> events = new HashMap<>();
    private final LinkedList<Long> eventIds = new LinkedList<>();
    private final AtomicBoolean loggingEnabled = new AtomicBoolean(MotadataConfigUtil.eventLoggingEnabled()); // if yes log event tracker event with status

    private final AtomicLong count = new AtomicLong(0);

    private boolean streaming = false;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>localConsumer(EVENT_ADD, message ->
        {
            try
            {
                var event = message.body();

                if (!event.isEmpty() && event.getLong(EVENT_ID) != null)
                {
                    var millis = System.currentTimeMillis();

                    var context = event.getJsonObject(EVENT_CONTEXT);

                    if (eventIds.size() == MAX_EVENTS)
                    {
                        events.remove(eventIds.removeLast()); // remove last event from queue
                    }

                    eventIds.addFirst(event.getLong(EVENT_ID)); // add new event on first index

                    if (context != null)
                    {
                        event.remove(EVENT_CONTEXT);

                        NMSConstants.removeGarbageFields(context);

                        event.put(EVENT_CONTEXT, CommonUtil.removeSensitiveFields(context, false)).put(AIOpsObject.OBJECT_TARGET, context.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE)).put(AIOpsObject.OBJECT_IP, context.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE));
                    }

                    event.put(EVENT_PROGRESS_LOG, new JsonArray()).put(EVENT_TIMESTAMP, millis);

                    if (event.containsKey(EVENT_STATE) && event.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_QUEUED))
                    {
                        events.put(event.getLong(EVENT_ID), event);

                        event.getJsonArray(EVENT_PROGRESS_LOG).add(String.format(EVENT_TRACKER_EVENT_QUEUED, DateTimeUtil.timestamp(millis)));
                    }

                    else
                    {
                        events.put(event.getLong(EVENT_ID), event.put(EVENT_STATE, EVENT_STATE_RUNNING));

                        event.getJsonArray(EVENT_PROGRESS_LOG).add(String.format(EVENT_TRACKER_EVENT_STARTED, DateTimeUtil.timestamp(millis)));

                        if (streaming)
                        {
                            vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_TRACKER.getName()).put(EVENT_CONTEXT, events.get(event.getLong(EVENT_ID))));
                        }
                    }

                    count.getAndIncrement();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // reply requested event context
        vertx.eventBus().<JsonObject>localConsumer(EVENT_TRACKER, message ->
        {
            var event = message.body();

            if (!event.isEmpty() && event.getLong(EVENT_ID) != null && events.containsKey(event.getLong(EVENT_ID)))
            {
                message.reply(events.get(event.getLong(EVENT_ID)));
            }

            else
            {
                message.fail(GlobalConstants.NOT_AVAILABLE, UNKNOWN);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_SUCCEED, message -> complete(message.body().put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS), STATUS_SUCCEED)).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_ABORT, message -> complete(message.body(), STATUS_ABORT)).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_PROGRESS_UPDATE, message ->
        {
            var event = message.body();

            if (!event.isEmpty() && event.getLong(EVENT_ID) != null && events.containsKey(event.getLong(EVENT_ID)))
            {
                var object = events.get(event.getLong(EVENT_ID));

                var context = event.getJsonObject(EVENT_CONTEXT);

                if (event.containsKey(EVENT_PROGRESS_LOG))
                {
                    object.getJsonArray(EVENT_PROGRESS_LOG).add(event.getString(EVENT_PROGRESS_LOG));
                }

                if (context != null && object.containsKey(EVENT_CONTEXT))
                {
                    NMSConstants.removeGarbageFields(context);

                    object.getJsonObject(EVENT_CONTEXT).mergeIn(CommonUtil.removeSensitiveFields(context, false));
                }

                if (event.containsKey(EVENT_STATE))
                {
                    object.put(EVENT_STATE, event.getString(EVENT_STATE));

                    if (event.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_RUNNING) && streaming)
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_TRACKER.getName()).put(EVENT_CONTEXT, object));
                    }
                }
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_FAIL, message -> complete(message.body(), STATUS_FAIL)).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_STREAM_CONTEXT, message ->
        {
            var event = message.body();

            EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), UI_ACTION_TASK_STREAM_CONTEXT, events.get(event.getLong(ID)) != null ? events.get(event.getLong(ID)).mergeIn(event) : event);

        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            switch (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
            {
                case START_EVENT_TRACKER_STREAMING ->
                {
                    streaming = true;

                    eventIds.forEach(id -> vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_TRACKER.getName()).put(EVENT_CONTEXT, events.get(id))));
                }

                case STOP_EVENT_TRACKER_STREAMING -> streaming = false;
            }

        }).exceptionHandler(LOGGER::error);

        promise.complete();
    }

    private void complete(JsonObject event, String status)
    {
        try
        {
            if (!event.isEmpty() && event.getLong(EVENT_ID) != null && events.containsKey(event.getLong(EVENT_ID)))
            {
                var context = events.get(event.getLong(EVENT_ID));

                if (!context.containsKey(STATUS))
                {
                    context.put(STATUS, status);
                }

                context.put(EVENT_STATE, EVENT_STATE_COMPLETED).getJsonArray(EVENT_PROGRESS_LOG).add(String.format(EVENT_TRACKER_EVENT_COMPLETED, DateTimeUtil.timestamp(), status));

                if (event.containsKey(EVENT_CONTEXT) && context.containsKey(EVENT_CONTEXT))
                {
                    context.getJsonObject(EVENT_CONTEXT).mergeIn(CommonUtil.removeSensitiveFields(event.getJsonObject(EVENT_CONTEXT), false));
                }

                if (event.containsKey(MESSAGE))
                {
                    context.put(MESSAGE, event.getString(MESSAGE));
                }

                if (event.containsKey(ERROR) && !event.getString(ERROR).isEmpty())
                {
                    context.put(ERROR, event.getString(ERROR));
                }

                if (event.containsKey(ERROR_CODE))
                {
                    context.put(ERROR_CODE, event.getString(ERROR_CODE));
                }

                if (loggingEnabled.get())
                {
                    LOGGER.logEvent(status, context.encodePrettily());
                }

                if (streaming)
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_TRACKER.getName()).put(EVENT_CONTEXT, context));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
