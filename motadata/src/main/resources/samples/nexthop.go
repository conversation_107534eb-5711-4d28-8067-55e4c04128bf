/*
 * Copyright (c) Motadata 2025. All rights reserved.
 */

package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"motadatasdk/consts"
	"motadatasdk/motadatatypes"
	"os"
)

const (
	IPRouteOIDs = "ip.route.oids"

	IPForwardRouteOIDs = "ip.forward.route.oids"

	IPRouteNextHop = "ip.route.next.hop"

	InvalidHops = "0.0.0.0"
)

func main() {

	decodedContext, _ := base64.StdEncoding.DecodeString(os.Args[2:][0])

	context := make(motadatatypes.MotadataMap)

	_ = json.Unmarshal(decodedContext, &context)

	bytes, _ := json.Marshal(Run(context))

	fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)
}

func Run(context motadatatypes.MotadataMap) motadatatypes.MotadataMap {

	result := motadatatypes.MotadataMap{
		consts.Status: consts.StatusFail,
	}

	hops := motadatatypes.MotadataStringList{}

	for _, value := range context.GetSliceValue(consts.Result) {

		if motadatatypes.ToMap(value)[IPRouteOIDs] != nil {

			for _, token := range motadatatypes.ToMap(value).GetSliceValue(IPRouteOIDs) {

				if len(motadatatypes.ToMap(token).GetStringValue(IPRouteNextHop)) > 0 && motadatatypes.ToMap(token).GetStringValue(IPRouteNextHop) != InvalidHops {

					hops = append(hops, motadatatypes.ToMap(token).GetStringValue(IPRouteNextHop))
				}
			}

		} else if motadatatypes.ToMap(value)[IPForwardRouteOIDs] != nil {

			for _, token := range motadatatypes.ToMap(value).GetSliceValue(IPForwardRouteOIDs) {

				if len(motadatatypes.ToMap(token).GetStringValue(IPRouteNextHop)) > 0 && motadatatypes.ToMap(token).GetStringValue(IPRouteNextHop) != InvalidHops {

					hops = append(hops, motadatatypes.ToMap(token).GetStringValue(IPRouteNextHop))
				}
			}
		}
	}

	if len(hops) > 0 {

		result[consts.Objects] = hops

		result[consts.Status] = consts.StatusSucceed
	}

	return result
}
