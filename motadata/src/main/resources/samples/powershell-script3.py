#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode

from motadatasdk.constants import Constant
from motadatasdk.util import Util

# COMMAND="get-adforest -Current LocalComputer |select DomainNamingMaster,SchemaMaster,Name |fl"

if __name__ == '__main__':

    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    output = Util.load_plugin_context(str(args.context)).get(Constant.RESULT)

    result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

    if output is not None and len(output) > 0:

        domains = []

        forests = {}

        tokens = output.strip().split("\r\n\r\n")

        for token in tokens:

            if len(token) > 0 and token.strip().split(":", 1)[0].strip() == "DomainNamingMaster":

                domain_naming_master = Constant.BLANK_STRING

                schema_master = Constant.BLANK_STRING

                for counter_value in token.split("\r\n"):

                    counter_value = counter_value.split(":", 1)

                    if counter_value[0].strip() == "DomainNamingMaster":

                        domain_naming_master = counter_value[1].strip().lower()

                    elif counter_value[0].strip() == "SchemaMaster":

                        schema_master = counter_value[1].strip().lower()

                    elif counter_value[0].strip() == "Name":

                        forests[counter_value[1].strip()] = {
                            'ad.role.forest': counter_value[1].strip(),
                            'ad.role.domain.naming.master.role': 'Found' if "HOST_NAME" in domain_naming_master else 'Not Found',
                            'ad.role.schema.master.role': 'Found' if "HOST_NAME" in schema_master else 'Not Found'
                        }

        if len(forests) > 0:

            result[Constant.STATUS] = Constant.STATUS_SUCCEED

            result[Constant.RESULT]['ad.role.forest'] = forests

    print(b64encode(json.dumps(result).encode()).decode(), end=Constant.BLANK_STRING)