/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.notification;

import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import static com.mindarray.GlobalConstants.ERROR_CODE;

/**
 * Implementation of the Notification abstract class for playing sound notifications.
 * <p>
 * This class handles sound notifications by publishing events to the UI layer,
 * which is responsible for actually playing the sound. This approach allows the
 * server-side notification system to trigger audio alerts in the user interface
 * without having to directly interact with audio hardware.
 * <p>
 * The notification process:
 * <ol>
 *   <li>Publishes the notification event to the UI notification channel</li>
 *   <li>The UI layer receives the event and plays the appropriate sound</li>
 *   <li>Reports success or failure of the notification attempt</li>
 * </ol>
 * <p>
 * The notification event may contain parameters that control which sound to play,
 * volume level, repetition count, etc., depending on what the UI layer supports.
 * <p>
 * This implementation uses Vert.x's event bus to communicate with the UI layer.
 */
public class SoundNotification extends Notification
{
    /**
     * Logger for sound notification operations
     */
    private static final Logger LOGGER = new Logger(SoundNotification.class, GlobalConstants.MOTADATA_NOTIFICATION, "Sound Notification");

    /**
     * Processes and sends a sound notification based on the provided event data.
     * <p>
     * This method:
     * <ul>
     *   <li>Publishes the notification event to the UI notification channel</li>
     *   <li>Marks the notification as successful (unless an exception occurs)</li>
     *   <li>Handles any exceptions that might occur during the process</li>
     * </ul>
     * <p>
     * Note that this method does not validate the event contents or wait for
     * confirmation that the sound was actually played. It simply forwards the
     * event to the UI layer and assumes success.
     *
     * @param event A JsonObject containing the sound notification parameters
     * @return A Promise that resolves to a JsonObject containing the result of the
     * sound notification attempt, including success/failure status and any error details
     */
    @Override
    public Promise<JsonObject> notify(JsonObject event)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            // Publish the notification event to the UI notification channel
            EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_SOUND_NOTIFICATION, event);

            // Mark the notification as successful
            promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS));
        }
        // Handle any exceptions that occur during sound notification processing
        catch (Exception exception)
        {
            // Log the exception
            LOGGER.error(exception);

            // Mark the notification as failed and include error details
            promise.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(GlobalConstants.MESSAGE, exception.getMessage())
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
        }

        return promise;
    }
}
