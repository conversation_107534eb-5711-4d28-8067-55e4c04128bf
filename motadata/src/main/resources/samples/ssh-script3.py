#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode

from motadatasdk.constants import Constant
from motadatasdk.util import Util

# Command: "uname -n"

if __name__ == '__main__':

    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    output = Util.load_plugin_context(str(args.context)).get(Constant.RESULT)

    result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

    if output and len(output) > 0 and output != '\n':

        result[Constant.STATUS] = Constant.STATUS_SUCCEED

        result[Constant.RESULT]["username"] = output.strip()

    print(b64encode(json.dumps(result).encode()).decode(), end=Constant.BLANK_STRING)