/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.aiops;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.LocalEventRouter;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Handler;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.METRIC_API_ENDPOINT;
import static com.mindarray.aiops.AIOpsConstants.DEPENDENCY_PARENT;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Test class for the DependencyManager and DependencyQueryProcessor.
 * <p>
 * This class contains tests for various dependency scenarios:
 * <ul>
 *   <li>Cloud dependencies (AWS, Azure, Office 365)</li>
 *   <li>Network device dependencies</li>
 *   <li>Virtualization dependencies (vCenter, Hyper-V, Citrix Xen)</li>
 *   <li>HCI dependencies (Prism, Nutanix)</li>
 *   <li>SDN dependencies (Cisco vManage, vEdge)</li>
 *   <li>Process dependencies (Windows, Linux, Solaris)</li>
 *   <li>Application dependencies</li>
 *   <li>Manual link creation and management</li>
 * </ul>
 * <p>
 * These tests verify that dependencies are correctly created, queried, and managed
 * in different formats (flat, hierarchical) and for different types of IT components.
 */
@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(100 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestDependency
{

    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(40 * 1000L);

    private static final Logger LOGGER = new Logger(TestDependency.class, MOTADATA_AIOPS, "Dependency Test");

    private static final JsonArray NETWORK_TOPOLOGY_DEPENDENT_LEVELS = new JsonArray(new ArrayList<>(1)).add(AIOpsConstants.DependencyLevel.FOUR.getName());

    private static JsonObject metricContext = null;

    private static MessageConsumer<JsonObject> messageConsumer = null;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testAWSCloudDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AWS_CLOUD.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD.name());

        assertCloudDependenciesTestResult(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testAzureCloudDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AZURE_CLOUD.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD.name());

        assertCloudDependenciesTestResult(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testOffice365Dependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.OFFICE_365.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD.name());

        query(eventContext, testContext, false, items -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateManualLink(VertxTestContext testContext)
    {
        LOGGER.info("running : testCreateManualLink");

        var item = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(item);

        var context = new JsonObject("{\"dependency.mapper.parent\":\"**********\",\"dependency.mapper.child\":\"************\",\"dependency.mapper.type\":\"Network\",\"dependency.mapper.archived\":\"no\",\"dependency.mapper.context\":{\"dependency.mapper.parent.interface\":\"58\",\"dependency.mapper.parent.interface.name\":\"Vl40\",\"dependency.mapper.child.interface\":\"100\",\"dependency.mapper.child.interface.name\":\"Vl100\",\"dependency.mapper.link.layer\":\"L2\"}}");

        TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, context, response -> testContext.verify(() ->
        {
            var body = response.result().bodyAsJsonObject();

            LOGGER.info(String.format("response : %s ", body.encode()));

            assertEquals(SC_OK, response.result().statusCode());

            Assertions.assertNotNull(body);

            Assertions.assertTrue(body.containsKey(STATUS));

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testNetworkDeviceDependencies(VertxTestContext testContext) throws Exception
    {
        LOGGER.info("running : testNetworkDeviceDependencies");

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        query(eventContext, testContext, true, items ->
        {
            LOGGER.info(String.format("neighbors : %s ", items));

            // means neighbor is connected
            Assertions.assertFalse(items.isEmpty());

            var levels = items.stream().filter(item -> JsonObject.mapFrom(item).containsKey(AIOpsConstants.DEPENDENCY_LEVEL) && CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL)) != null)
                    .map(item -> CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL))).distinct().collect(Collectors.toCollection(ArrayList::new));

            LOGGER.info(String.format("levels : %s ", items));

            Assertions.assertFalse(levels.isEmpty());

            levels.remove(AIOpsConstants.DependencyLevel.MINUS_ONE.getName());

            queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
            {
                LOGGER.info(String.format("result : %s ", result));

                Assertions.assertNotNull(result);

                Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

                Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

                var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

                Assertions.assertNotNull(child);

                Assertions.assertFalse(child.isEmpty());

                testContext.completeNow();
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testNetworkDeviceInterfaceDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        assertObjectDependenciesTestResult(object, true, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), NMSConstants.INTERFACE_NAME, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateNetworkDeviceDependencies(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(metricContext);

        var interfaces = metricContext.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

        interfaces.remove(0);

        interfaces.remove(1);

        TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + metricContext.getLong(ID),
                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var retries = new AtomicInteger();

                            TestUtil.vertx().setPeriodic(2000, timer ->
                            {
                                retries.getAndIncrement();

                                var context = new JsonObject().put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.FLAT.getName())
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, "**********").put(AIOpsConstants.DEPENDENCY_LEVEL, NETWORK_TOPOLOGY_DEPENDENT_LEVELS);

                                query(context, testContext, true, AIOpsConstants.DependencyLevel.FOUR.getName(), timer, objects ->
                                {
                                    if (interfaces.size() == objects.size())
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                    else if (retries.get() == 10)
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        testContext.failNow(new Exception("failed to update dependency"));
                                    }
                                });
                            });
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testSolarisProcessDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        assertObjectDependenciesTestResult(object, false, NMSConstants.MetricPlugin.SOLARIS_PROCESS.getName(), AIOpsObject.OBJECT_NAME, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testLinuxProcessDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        assertObjectDependenciesTestResult(object, false, NMSConstants.MetricPlugin.LINUX_PROCESS.getName(), AIOpsObject.OBJECT_NAME, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testApplicationDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        query(eventContext, testContext, true, items ->
        {
            // means application is connected
            Assertions.assertFalse(items.isEmpty());

            var valid = false;

            for (var index = 0; index < items.size(); index++)
            {
                var dependency = items.getJsonObject(index);

                if (dependency.containsKey(AIOpsConstants.DEPENDENCY_CONNECTED_LINK) && !dependency.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK).equalsIgnoreCase(UNKNOWN))
                {
                    Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_SOURCE));

                    assertEquals(object.getLong(ID), dependency.getLong(AIOpsConstants.DEPENDENCY_SOURCE));

                    Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_LEVEL));

                    var level = CommonUtil.getByteValue(dependency.getValue(AIOpsConstants.DEPENDENCY_LEVEL));

                    if (level.equals(AIOpsConstants.DependencyLevel.ZERO.getName()) || level.equals(AIOpsConstants.DependencyLevel.ONE.getName()) || level.equals(AIOpsConstants.DependencyLevel.TWO.getName()) || level.equals(AIOpsConstants.DependencyLevel.THREE.getName()))
                    {
                        Assertions.assertTrue(true);
                    }

                    Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_CONNECTED_LINK));

                    var connectedLink = dependency.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK);

                    Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION));

                    Assertions.assertFalse(dependency.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

                    Assertions.assertTrue(NMSConstants.APPLICATION_TYPES.contains(dependency.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).getString(0)));

                    Assertions.assertNotNull(metricContext);

                    Assertions.assertNotNull(metricContext.getJsonObject(Metric.METRIC_CONTEXT));

                    Assertions.assertNotNull(metricContext.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS));

                    var objects = metricContext.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).stream().map(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_NAME).trim())
                            .distinct().toList();

                    Assertions.assertFalse(object.isEmpty());

                    Assertions.assertTrue(objects.contains(connectedLink));

                    valid = true;

                    break;
                }
            }

            if (!valid)
            {
                testContext.failNow(new Exception("failed to find application dependency..."));
            }
            else
            {
                queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
                {
                    Assertions.assertNotNull(result);

                    Assertions.assertFalse(result.isEmpty());

                    var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

                    Assertions.assertNotNull(children);

                    Assertions.assertFalse(children.isEmpty());

                    var child = children.getJsonObject(0);

                    Assertions.assertNotNull(child);

                    Assertions.assertTrue(child.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION));

                    Assertions.assertTrue(NMSConstants.APPLICATION_TYPES.contains(child.getString(AIOpsConstants.DEPENDENCY_DESTINATION)));

                    Assertions.assertTrue(child.containsKey(AIOpsConstants.DEPENDENCY_LEVEL));

                    var level = CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL));

                    if (level.equals(AIOpsConstants.DependencyLevel.ZERO.getName()) || level.equals(AIOpsConstants.DependencyLevel.ONE.getName()) || level.equals(AIOpsConstants.DependencyLevel.TWO.getName()) || level.equals(AIOpsConstants.DependencyLevel.THREE.getName()))
                    {
                        Assertions.assertTrue(true);
                    }

                    testContext.completeNow();
                });
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testWindowsProcessDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_IP, "************").stream()
                .filter(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName()))
                .map(JsonObject::mapFrom)
                .findFirst().orElse(null);

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        assertObjectDependenciesTestResult(object, false, NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName(), AIOpsObject.OBJECT_NAME, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    @Disabled
    void testCiscoUCSDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CISCO_UCS.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.OTHER.name());

        var dependencyLevels = new JsonArray().add(AIOpsConstants.DependencyLevel.TWELVE.getName())
                .add(AIOpsConstants.DependencyLevel.TEN.getName())
                .add(AIOpsConstants.DependencyLevel.NINE.getName())
                .add(AIOpsConstants.DependencyLevel.EIGHT.getName());

        query(eventContext, testContext, true, items ->
        {
            // means cisco ucs dependencies available
            Assertions.assertFalse(items.isEmpty());

            var levels = items.stream().map(item -> CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL)))
                    .distinct().toList();

            Assertions.assertFalse(levels.isEmpty());

            var size = dependencyLevels.size();

            levels.stream().filter(dependencyLevels::contains).forEach(dependencyLevels::remove);

            Assertions.assertNotEquals(dependencyLevels.size(), size);

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testvCenterDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.VCENTER.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items ->
        {
            // means vcenter dependencies available (datacenter -> vcenter -> vm)
            Assertions.assertFalse(items.isEmpty());

            var dataCenter = false;

            var cluster = false;

            var esxi = false;

            for (var index = 0; index < items.size(); index++)
            {
                var result = items.getJsonObject(index);

                if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.TWELVE.getName()))
                {
                    dataCenter = true;

                    assertEquals(object.getLong(ID), result.getLong(AIOpsConstants.DEPENDENCY_SOURCE));

                    Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());
                }
                else if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                {
                    Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());
                }
                else if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.TEN.getName()))
                {
                    cluster = true;
                }
                else if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.NINE.getName()))
                {
                    esxi = true;
                }
            }

            Assertions.assertTrue(dataCenter);

            Assertions.assertTrue(cluster);

            Assertions.assertTrue(esxi);

            assertParentChildVirtualizationClusterDependency(eventContext, testContext);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testvCenterClearDependencies(VertxTestContext testContext) throws Exception
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********80");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var context = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        // sending new poll result with vm(**********12) moved from esxi (************) to esxi (************)
        var event = new JsonObject("{\n  \"_type\": \"1\",\n  \"authentication.type\": \"basic\",\n  \"credential.profile.name\": \"<EMAIL>\",\n  \"credential.profile.protocol\": \"HTTP/HTTPS\",\n  \"errors\": [],\n  \"event.id\": 75986072202,\n  \"event.timestamp\": 1748521067,\n  \"event.topic\": \"remote.event.processor \",\n  \"id\": ***********,\n  \"metric.category\": \"Virtualization\",\n  \"metric.credential.profile\": ***********,\n  \"metric.credential.profile.protocol\": \"HTTP/HTTPS\",\n  \"metric.discovery.method\": \"REMOTE\",\n  \"metric.name\": \"VMware vCenter Data Center\",\n  \"metric.object\": ***********,\n  \"metric.plugin\": \"vcenter\",\n  \"metric.polling.min.time\": 300,\n  \"metric.polling.time\": 300,\n  \"metric.state\": \"ENABLE\",\n  \"metric.type\": \"vCenter\",\n  \"object.business.hour.profile\": 10000000000001,\n  \"object.category\": \"Virtualization\",\n  \"object.creation.time\": \"2025/05/29 16:17:13\",\n  \"object.creation.time.seconds\": 1748515633,\n  \"object.discovery.method\": \"REMOTE\",\n  \"object.event.processors\": [],\n  \"object.groups\": [\n    10000000000026,\n    10000000000027\n  ],\n  \"object.host\": \"***********80\",\n  \"object.id\": 1,\n  \"object.ip\": \"***********80\",\n  \"object.name\": \"***********80\",\n  \"object.state\": \"ENABLE\",\n  \"object.tags\": [],\n  \"object.target\": \"***********80\",\n  \"object.type\": \"vCenter\",\n  \"password\": \"Mind#@$341\",\n  \"ping.check.status\": \"yes\",\n  \"plugin.engine\": \"go\",\n  \"plugin.id\": 175,\n  \"port\": 443,\n  \"remote.address\": \"0:0:0:0:0:0:0:1\",\n  \"remote.event.processor.uuid\": \"sankalp\",\n  \"result\": {\n    \"vcenter.datastores\": 4,\n    \"vcenter.offline.datastores\": 0,\n    \"vcenter.datacenters\": 1,\n    \"vcenter.memory.installed.bytes\": ************,\n    \"vcenter.memory.used.bytes\": ************,\n    \"vcenter.cpu.hz\": 167864000000,\n    \"vcenter.cpu.used.hz\": 28865000000,\n    \"vcenter.clusters\": 3,\n    \"vcenter.resource.pools\": 3,\n    \"vcenter.nodes\": 4,\n    \"vcenter.connected.nodes\": 4,\n    \"vcenter.disconnected.nodes\": 0,\n    \"vcenter.node\": [\n      {\n        \"vcenter.node\": \"************\",\n        \"vcenter.node.ip\": \"************\",\n        \"vcenter.node.cluster.name\": \"SupportCluster\",\n        \"vcenter.node.memory.bytes\": 68582551552,\n        \"vcenter.node.memory.used.bytes\": 50100961280,\n        \"vcenter.node.memory.free.bytes\": 18481590272,\n        \"vcenter.node.cpu.used.hz\": 12741000000,\n        \"vcenter.node.power.state\": \"poweredOn\",\n        \"vcenter.node.virtual.machines\": 4,\n        \"vcenter.node.running.virtual.machines\": 4\n      },\n      {\n        \"vcenter.node\": \"************\",\n        \"vcenter.node.ip\": \"************\",\n        \"vcenter.node.cluster.name\": \"QACluster\",\n        \"vcenter.node.memory.bytes\": 77298647040,\n        \"vcenter.node.memory.used.bytes\": 43707793408,\n        \"vcenter.node.memory.free.bytes\": 33590853632,\n        \"vcenter.node.cpu.used.hz\": 2090000000,\n        \"vcenter.node.power.state\": \"poweredOn\",\n        \"vcenter.node.virtual.machines\": 13,\n        \"vcenter.node.running.virtual.machines\": 7\n      },\n      {\n        \"vcenter.node\": \"***********9\",\n        \"vcenter.node.ip\": \"***********9\",\n        \"vcenter.node.cluster.name\": \"ProductionCluster\",\n        \"vcenter.node.memory.bytes\": 137368485888,\n        \"vcenter.node.memory.used.bytes\": 98462334976,\n        \"vcenter.node.memory.free.bytes\": 38906150912,\n        \"vcenter.node.cpu.used.hz\": 8051000000,\n        \"vcenter.node.power.state\": \"poweredOn\",\n        \"vcenter.node.virtual.machines\": 32,\n        \"vcenter.node.running.virtual.machines\": 23\n      },\n      {\n        \"vcenter.node\": \"************\",\n        \"vcenter.node.ip\": \"************\",\n        \"vcenter.node.cluster.name\": \"ProductionCluster\",\n        \"vcenter.node.memory.bytes\": 137402470400,\n        \"vcenter.node.memory.used.bytes\": 106728259584,\n        \"vcenter.node.memory.free.bytes\": 30674210816,\n        \"vcenter.node.cpu.used.hz\": 5983000000,\n        \"vcenter.node.power.state\": \"poweredOn\",\n        \"vcenter.node.virtual.machines\": 18,\n        \"vcenter.node.running.virtual.machines\": 12\n      }\n    ],\n    \"vcenter.virtual.machines\": 67,\n    \"vcenter.running.virtual.machines\": 46,\n    \"vcenter.vm\": [\n      {\n        \"vcenter.vm\": \"GNS3_VM-2.2.32_10.120\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"***********20\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 18.99,\n        \"vcenter.vm.cpu.percent\": 34.06\n      },\n      {\n        \"vcenter.vm\": \"GNS3 VM-ESXI_10.120-old-14.111\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"*************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 25.0,\n        \"vcenter.vm.cpu.percent\": 35.69\n      },\n      {\n        \"vcenter.vm\": \"GNS3 Windows Client_10.121\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 0.99,\n        \"vcenter.vm.cpu.percent\": 2.16\n      },\n      {\n        \"vcenter.vm\": \"GNS-windows-client-2-14.110\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"*************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 6.98,\n        \"vcenter.vm.cpu.percent\": 2.24\n      },\n      {\n        \"vcenter.vm\": \"radwin-utility-ubuntu-14-vm\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"Motadata-Master-7.6.900\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"Shailendra-chips-rpe-8.221\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"Delivery_deepak_CHiPS_7.3.047_10.237\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"*************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 10.53,\n        \"vcenter.vm.cpu.percent\": 21.32\n      },\n      {\n        \"vcenter.vm\": \"Delivery_Deepak_Observer_9.32_9.134\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 0.98,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"Motadata_RPE_7.6.400_9.87\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"KVM test-11.247\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"**********\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 5.0,\n        \"vcenter.vm.cpu.percent\": 2.25\n      },\n      {\n        \"vcenter.vm\": \"maharshi-radwin-7.3-8.153\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"Motadata-Datanode-7.6.900\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"Motadata-RPE-7.6.900\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"ubuntu-20-Tar-test-\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"**********07\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 11.99,\n        \"vcenter.vm.cpu.percent\": 1.25\n      },\n      {\n        \"vcenter.vm\": \"Delivery_Kalpesh_WinManage_Master_7.4.000_8.216\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"**********16\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 12.99,\n        \"vcenter.vm.cpu.percent\": 1.75\n      },\n      {\n        \"vcenter.vm\": \"Arpit_Radwin_7.5_RPE_8.212\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"**********12\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 23.0,\n        \"vcenter.vm.cpu.percent\": 3.74\n      },\n      {\n        \"vcenter.vm\": \"radwin-upgrade-iso\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"Ubuntu14-10.183\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 2.99,\n        \"vcenter.vm.cpu.percent\": 0.12\n      },\n      {\n        \"vcenter.vm\": \"sterlite-751_11.238\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"*************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 3.98,\n        \"vcenter.vm.cpu.percent\": 0.49\n      },\n      {\n        \"vcenter.vm\": \"Vivek_Sahu_Radwin_rpe_8.177\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 9.0,\n        \"vcenter.vm.cpu.percent\": 1.25\n      },\n      {\n        \"vcenter.vm\": \"Support_umang_vulnerabilities assessment_16.04.4_9.1\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 8.0,\n        \"vcenter.vm.cpu.percent\": 0.87\n      },\n      {\n        \"vcenter.vm\": \"Devops_Siddharth-Ubuntu-Jenkins-9.42\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 9.99,\n        \"vcenter.vm.cpu.percent\": 0.49\n      },\n      {\n        \"vcenter.vm\": \"Kube-master-node\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"kube-worker-2\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"kube-worker-1\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"Motadata ITSM Internal_8.94\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"QA_Radwin_Performance_master_8.182\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"Pratik_UBI_Performance_RPE3_9.116_9.122\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 29.0,\n        \"vcenter.vm.cpu.percent\": 5.83\n      },\n      {\n        \"vcenter.vm\": \"motadata IPAM (internal monitor)_9.52\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 38.66,\n        \"vcenter.vm.cpu.percent\": 22.47\n      },\n      {\n        \"vcenter.vm\": \"Latest-Windows-10-11.216\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 15.99,\n        \"vcenter.vm.cpu.percent\": 0.24\n      },\n      {\n        \"vcenter.vm\": \"motadata_Internal_monitoring_11.166\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"*************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 22.0,\n        \"vcenter.vm.cpu.percent\": 1.66\n      },\n      {\n        \"vcenter.vm\": \"8.0_QA_Scalibility_master-1_11.224\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"*************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 37.0,\n        \"vcenter.vm.cpu.percent\": 4.12\n      },\n      {\n        \"vcenter.vm\": \"Krunal_windows 10-11.246\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 53.99,\n        \"vcenter.vm.cpu.percent\": 5.75\n      },\n      {\n        \"vcenter.vm\": \"Devops_Siddharth_Ubuntu_Test_9.83\",\n        \"vcenter.vm.server\": \"************\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 4.98,\n        \"vcenter.vm.cpu.percent\": 0.96\n      },\n      {\n        \"vcenter.vm\": \"8.0_windows_2K8_Cluster_2_8.240\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 11.98,\n        \"vcenter.vm.cpu.percent\": 28.5\n      },\n      {\n        \"vcenter.vm\": \"8.0_ranjit_openbsd_8.208\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"8.0_hxehost_8.227_clone\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_ranjit_fedora_8.200_clone\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"8.0-Exchange server 2010_ip_8.202_clone_8.99\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"8.0_plugin_SLES_12_9.98\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"***********\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 9.96,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_Twinkle_.Net-8.80\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 13.99,\n        \"vcenter.vm.cpu.percent\": 1.75\n      },\n      {\n        \"vcenter.vm\": \"8.0_soalris_Oracle_Db2_8.222\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 14.99,\n        \"vcenter.vm.cpu.percent\": 6.25\n      },\n      {\n        \"vcenter.vm\": \"8.0_plugin_Centos_7.6_8.237\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 16.99,\n        \"vcenter.vm.cpu.percent\": 3.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_DB2_solaris-11.4-template_8.244\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 8.98,\n        \"vcenter.vm.cpu.percent\": 2.33\n      },\n      {\n        \"vcenter.vm\": \"8.0 exchange-edge-role2013_8.81\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 8.98,\n        \"vcenter.vm.cpu.percent\": 1.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_Xen_7_server_1_10.231\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 10.97,\n        \"vcenter.vm.cpu.percent\": 2.5\n      },\n      {\n        \"vcenter.vm\": \"8.0_ranjit_debian9_8.171_clone\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"8.0_plugin_ubuntu-14_9.94\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 0.98,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_Utsav_Bind-8.84\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"***********\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 2.98,\n        \"vcenter.vm.cpu.percent\": 1.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_solaris_8.48\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"8.0_plugin_SLES_11_9.96\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 61.91,\n        \"vcenter.vm.cpu.percent\": 2.0\n      },\n      {\n        \"vcenter.vm\": \"8.0SharepointExchange8.130\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 62.99,\n        \"vcenter.vm.cpu.percent\": 8.17\n      },\n      {\n        \"vcenter.vm\": \"8.0_Exchange2016_10.164_clone_8.86\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"***********\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 36.99,\n        \"vcenter.vm.cpu.percent\": 10.12\n      },\n      {\n        \"vcenter.vm\": \"8.0_windows_2019_8.100\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 29.98,\n        \"vcenter.vm.cpu.percent\": 7.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_Monitor Inventory_windows_8.145\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 0.99,\n        \"vcenter.vm.cpu.percent\": 0.25\n      },\n      {\n        \"vcenter.vm\": \"8.0_ranjit_windows8_8.168_clone\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"8.0_Edge_ExchangeServer16_8.104\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 17.99,\n        \"vcenter.vm.cpu.percent\": 3.33\n      },\n      {\n        \"vcenter.vm\": \"8.0_hyperv-2k8_9.51\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"***********\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 12.99,\n        \"vcenter.vm.cpu.percent\": 3.25\n      },\n      {\n        \"vcenter.vm\": \"8.02010sharepoint_8.112>>>8.250\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 5.99,\n        \"vcenter.vm.cpu.percent\": 0.67\n      },\n      {\n        \"vcenter.vm\": \"8.0_Xen_7_server_2_10.232\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 5.99,\n        \"vcenter.vm.cpu.percent\": 2.67\n      },\n      {\n        \"vcenter.vm\": \"8.0_ranjit_windows7_clone\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\"\n      },\n      {\n        \"vcenter.vm\": \"8.0_windows_2K8_Cluster_1_8.98\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 27.98,\n        \"vcenter.vm.cpu.percent\": 7.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_utsav-bind9.14_8.69\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"***********\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 0.98,\n        \"vcenter.vm.cpu.percent\": 0.0\n      },\n      {\n        \"vcenter.vm\": \"8.0_plugin_Centos_7.5_8.214\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 10.99,\n        \"vcenter.vm.cpu.percent\": 1.5\n      },\n      {\n        \"vcenter.vm\": \"8_0_UCSPE_8.110_new\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.ip\": \"************\",\n        \"vcenter.vm.power.state\": \"poweredOn\",\n        \"vcenter.vm.memory.used.percent\": 8.98,\n        \"vcenter.vm.cpu.percent\": 1.25\n      },\n      {\n        \"vcenter.vm\": \"Uday ITSM  Silver_8.156\",\n        \"vcenter.vm.server\": \"***********9\",\n        \"vcenter.vm.power.state\": \"poweredOff\",\n        \"vcenter.vm.memory.used.percent\": 0.0,\n        \"vcenter.vm.cpu.percent\": 0.0\n      }\n    ],\n    \"vcenter.networks\": 0\n  },\n  \"status\": \"succeed\",\n  \"timeout\": 60,\n  \"ui.event.uuid\": \"f65a39af-c6b2-4363-88a6-5ddfb9b79d9c\",\n  \"url.protocol\": \"http\",\n  \"user.name\": \"admin\",\n  \"username\": \"<EMAIL>\",\n  \"latency.ms\": 1161.0,\n  \"error.code\": \"MD000\",\n  \"event.type\": \"metric.poll\"\n}");

        TestUtil.vertx().eventBus().send(EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), new JsonObject().mergeIn(event)
                .put(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_IP)).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                .put(AIOpsConstants.DEPENDENCY_SOURCE, event.getString(AIOpsObject.OBJECT_IP))
                .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD_MAP.getName()));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        // fetching and verifying existing dependencies
        query(context, testContext, true, items ->
        {
            Assertions.assertFalse(items.isEmpty());

            var vm = "************";

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.getString(AIOpsConstants.DEPENDENCY_SOURCE) != null && item.getString(AIOpsConstants.DEPENDENCY_SOURCE).equalsIgnoreCase("***********9") && item.getLong(AIOpsConstants.DEPENDENCY_LEVEL) != -1)
                {
                    Assertions.assertTrue(item.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).contains(vm));
                }
                else if (item.getString(AIOpsConstants.DEPENDENCY_SOURCE) != null && item.getString(AIOpsConstants.DEPENDENCY_SOURCE).equalsIgnoreCase("************") && item.getLong(AIOpsConstants.DEPENDENCY_LEVEL) != -1)
                {
                    Assertions.assertFalse(item.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).contains(vm));
                }
            }

            // sending new poll result with vm(**********12) moved from esxi (************) to esxi (************)
            event.clear().mergeIn(new JsonObject("{ \"_type\": \"1\", \"authentication.type\": \"basic\", \"credential.profile.name\": \"<EMAIL>\", \"credential.profile.protocol\": \"HTTP/HTTPS\", \"errors\": [ ], \"event.id\": 75986072202, \"event.timestamp\": 1748521067, \"event.topic\": \"remote.event.processor \", \"id\": ***********, \"metric.category\": \"Virtualization\", \"metric.credential.profile\": ***********, \"metric.credential.profile.protocol\": \"HTTP/HTTPS\", \"metric.discovery.method\": \"REMOTE\", \"metric.name\": \"VMware vCenter Data Center\", \"metric.object\": ***********, \"metric.plugin\": \"vcenter\", \"metric.polling.min.time\": 300, \"metric.polling.time\": 300, \"metric.state\": \"ENABLE\", \"metric.type\": \"vCenter\", \"object.business.hour.profile\": 10000000000001, \"object.category\": \"Virtualization\", \"object.creation.time\": \"2025/05/29 16:17:13\", \"object.creation.time.seconds\": 1748515633, \"object.discovery.method\": \"REMOTE\", \"object.event.processors\": [ ], \"object.groups\": [ 10000000000026, 10000000000027 ], \"object.host\": \"***********80\", \"object.id\": 1, \"object.ip\": \"***********80\", \"object.name\": \"***********80\", \"object.state\": \"ENABLE\", \"object.tags\": [ ], \"object.target\": \"***********80\", \"object.type\": \"vCenter\", \"password\": \"Mind#@$341\", \"ping.check.status\": \"yes\", \"plugin.engine\": \"go\", \"plugin.id\": 175, \"port\": 443, \"remote.address\": \"0:0:0:0:0:0:0:1\", \"remote.event.processor.uuid\": \"sankalp\", \"result\": { \"vcenter.datastores\": 4, \"vcenter.offline.datastores\": 0, \"vcenter.datacenters\": 1, \"vcenter.memory.installed.bytes\": ************, \"vcenter.memory.used.bytes\": ************, \"vcenter.cpu.hz\": 167864000000, \"vcenter.cpu.used.hz\": 28865000000, \"vcenter.clusters\": 3, \"vcenter.resource.pools\": 3, \"vcenter.nodes\": 4, \"vcenter.connected.nodes\": 4, \"vcenter.disconnected.nodes\": 0, \"vcenter.node\": [ { \"vcenter.node\": \"************\", \"vcenter.node.ip\": \"************\", \"vcenter.node.cluster.name\": \"SupportCluster\", \"vcenter.node.memory.bytes\": 68582551552, \"vcenter.node.memory.used.bytes\": 50100961280, \"vcenter.node.memory.free.bytes\": 18481590272, \"vcenter.node.cpu.used.hz\": 12741000000, \"vcenter.node.power.state\": \"poweredOn\", \"vcenter.node.virtual.machines\": 4, \"vcenter.node.running.virtual.machines\": 4 }, { \"vcenter.node\": \"************\", \"vcenter.node.ip\": \"************\", \"vcenter.node.cluster.name\": \"QACluster\", \"vcenter.node.memory.bytes\": 77298647040, \"vcenter.node.memory.used.bytes\": 43707793408, \"vcenter.node.memory.free.bytes\": 33590853632, \"vcenter.node.cpu.used.hz\": 2090000000, \"vcenter.node.power.state\": \"poweredOn\", \"vcenter.node.virtual.machines\": 13, \"vcenter.node.running.virtual.machines\": 7 }, { \"vcenter.node\": \"***********9\", \"vcenter.node.ip\": \"***********9\", \"vcenter.node.cluster.name\": \"ProductionCluster\", \"vcenter.node.memory.bytes\": 137368485888, \"vcenter.node.memory.used.bytes\": 98462334976, \"vcenter.node.memory.free.bytes\": 38906150912, \"vcenter.node.cpu.used.hz\": 8051000000, \"vcenter.node.power.state\": \"poweredOn\", \"vcenter.node.virtual.machines\": 32, \"vcenter.node.running.virtual.machines\": 23 }, { \"vcenter.node\": \"************\", \"vcenter.node.ip\": \"************\", \"vcenter.node.cluster.name\": \"ProductionCluster\", \"vcenter.node.memory.bytes\": 137402470400, \"vcenter.node.memory.used.bytes\": 106728259584, \"vcenter.node.memory.free.bytes\": 30674210816, \"vcenter.node.cpu.used.hz\": 5983000000, \"vcenter.node.power.state\": \"poweredOn\", \"vcenter.node.virtual.machines\": 18, \"vcenter.node.running.virtual.machines\": 12 } ], \"vcenter.virtual.machines\": 67, \"vcenter.running.virtual.machines\": 46, \"vcenter.vm\": [ { \"vcenter.vm\": \"GNS3_VM-2.2.32_10.120\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"***********20\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 18.99, \"vcenter.vm.cpu.percent\": 34.06 }, { \"vcenter.vm\": \"GNS3 VM-ESXI_10.120-old-14.111\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"*************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 25.0, \"vcenter.vm.cpu.percent\": 35.69 }, { \"vcenter.vm\": \"GNS3 Windows Client_10.121\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 0.99, \"vcenter.vm.cpu.percent\": 2.16 }, { \"vcenter.vm\": \"GNS-windows-client-2-14.110\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"*************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 6.98, \"vcenter.vm.cpu.percent\": 2.24 }, { \"vcenter.vm\": \"radwin-utility-ubuntu-14-vm\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"Motadata-Master-7.6.900\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"Shailendra-chips-rpe-8.221\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"Delivery_deepak_CHiPS_7.3.047_10.237\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"*************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 10.53, \"vcenter.vm.cpu.percent\": 21.32 }, { \"vcenter.vm\": \"Delivery_Deepak_Observer_9.32_9.134\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 0.98, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"Motadata_RPE_7.6.400_9.87\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"KVM test-11.247\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"**********\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 5.0, \"vcenter.vm.cpu.percent\": 2.25 }, { \"vcenter.vm\": \"maharshi-radwin-7.3-8.153\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"Motadata-Datanode-7.6.900\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"Motadata-RPE-7.6.900\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"ubuntu-20-Tar-test-\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"**********07\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 11.99, \"vcenter.vm.cpu.percent\": 1.25 }, { \"vcenter.vm\": \"Delivery_Kalpesh_WinManage_Master_7.4.000_8.216\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"**********16\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 12.99, \"vcenter.vm.cpu.percent\": 1.75 }, { \"vcenter.vm\": \"Arpit_Radwin_7.5_RPE_8.212\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"**********12\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 23.0, \"vcenter.vm.cpu.percent\": 3.74 }, { \"vcenter.vm\": \"radwin-upgrade-iso\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"Ubuntu14-10.183\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 2.99, \"vcenter.vm.cpu.percent\": 0.12 }, { \"vcenter.vm\": \"sterlite-751_11.238\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"*************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 3.98, \"vcenter.vm.cpu.percent\": 0.49 }, { \"vcenter.vm\": \"Vivek_Sahu_Radwin_rpe_8.177\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 9.0, \"vcenter.vm.cpu.percent\": 1.25 }, { \"vcenter.vm\": \"Support_umang_vulnerabilities assessment_16.04.4_9.1\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 8.0, \"vcenter.vm.cpu.percent\": 0.87 }, { \"vcenter.vm\": \"Devops_Siddharth-Ubuntu-Jenkins-9.42\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 9.99, \"vcenter.vm.cpu.percent\": 0.49 }, { \"vcenter.vm\": \"Kube-master-node\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"kube-worker-2\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"kube-worker-1\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"Motadata ITSM Internal_8.94\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"QA_Radwin_Performance_master_8.182\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"Pratik_UBI_Performance_RPE3_9.116_9.122\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 29.0, \"vcenter.vm.cpu.percent\": 5.83 }, { \"vcenter.vm\": \"motadata IPAM (internal monitor)_9.52\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 38.66, \"vcenter.vm.cpu.percent\": 22.47 }, { \"vcenter.vm\": \"Latest-Windows-10-11.216\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 15.99, \"vcenter.vm.cpu.percent\": 0.24 }, { \"vcenter.vm\": \"motadata_Internal_monitoring_11.166\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"*************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 22.0, \"vcenter.vm.cpu.percent\": 1.66 }, { \"vcenter.vm\": \"8.0_QA_Scalibility_master-1_11.224\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"*************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 37.0, \"vcenter.vm.cpu.percent\": 4.12 }, { \"vcenter.vm\": \"Krunal_windows 10-11.246\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 53.99, \"vcenter.vm.cpu.percent\": 5.75 }, { \"vcenter.vm\": \"Devops_Siddharth_Ubuntu_Test_9.83\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 4.98, \"vcenter.vm.cpu.percent\": 0.96 }, { \"vcenter.vm\": \"8.0_windows_2K8_Cluster_2_8.240\", \"vcenter.vm.server\": \"************\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 11.98, \"vcenter.vm.cpu.percent\": 28.5 }, { \"vcenter.vm\": \"8.0_ranjit_openbsd_8.208\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"8.0_hxehost_8.227_clone\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"8.0_ranjit_fedora_8.200_clone\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"8.0-Exchange server 2010_ip_8.202_clone_8.99\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"8.0_plugin_SLES_12_9.98\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.ip\": \"***********\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 9.96, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"8.0_Twinkle_.Net-8.80\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 13.99, \"vcenter.vm.cpu.percent\": 1.75 }, { \"vcenter.vm\": \"8.0_soalris_Oracle_Db2_8.222\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 14.99, \"vcenter.vm.cpu.percent\": 6.25 }, { \"vcenter.vm\": \"8.0_plugin_Centos_7.6_8.237\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 16.99, \"vcenter.vm.cpu.percent\": 3.0 }, { \"vcenter.vm\": \"8.0_DB2_solaris-11.4-template_8.244\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 8.98, \"vcenter.vm.cpu.percent\": 2.33 }, { \"vcenter.vm\": \"8.0 exchange-edge-role2013_8.81\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 8.98, \"vcenter.vm.cpu.percent\": 1.0 }, { \"vcenter.vm\": \"8.0_Xen_7_server_1_10.231\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 10.97, \"vcenter.vm.cpu.percent\": 2.5 }, { \"vcenter.vm\": \"8.0_ranjit_debian9_8.171_clone\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"8.0_plugin_ubuntu-14_9.94\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 0.98, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"8.0_Utsav_Bind-8.84\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.ip\": \"***********\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 2.98, \"vcenter.vm.cpu.percent\": 1.0 }, { \"vcenter.vm\": \"8.0_solaris_8.48\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"8.0_plugin_SLES_11_9.96\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 61.91, \"vcenter.vm.cpu.percent\": 2.0 }, { \"vcenter.vm\": \"8.0SharepointExchange8.130\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 62.99, \"vcenter.vm.cpu.percent\": 8.17 }, { \"vcenter.vm\": \"8.0_Exchange2016_10.164_clone_8.86\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.ip\": \"***********\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 36.99, \"vcenter.vm.cpu.percent\": 10.12 }, { \"vcenter.vm\": \"8.0_windows_2019_8.100\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 29.98, \"vcenter.vm.cpu.percent\": 7.0 }, { \"vcenter.vm\": \"8.0_Monitor Inventory_windows_8.145\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 0.99, \"vcenter.vm.cpu.percent\": 0.25 }, { \"vcenter.vm\": \"8.0_ranjit_windows8_8.168_clone\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"8.0_Edge_ExchangeServer16_8.104\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 17.99, \"vcenter.vm.cpu.percent\": 3.33 }, { \"vcenter.vm\": \"8.0_hyperv-2k8_9.51\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.ip\": \"***********\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 12.99, \"vcenter.vm.cpu.percent\": 3.25 }, { \"vcenter.vm\": \"8.02010sharepoint_8.112>>>8.250\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 5.99, \"vcenter.vm.cpu.percent\": 0.67 }, { \"vcenter.vm\": \"8.0_Xen_7_server_2_10.232\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 5.99, \"vcenter.vm.cpu.percent\": 2.67 }, { \"vcenter.vm\": \"8.0_ranjit_windows7_clone\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\" }, { \"vcenter.vm\": \"8.0_windows_2K8_Cluster_1_8.98\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 27.98, \"vcenter.vm.cpu.percent\": 7.0 }, { \"vcenter.vm\": \"8.0_utsav-bind9.14_8.69\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.ip\": \"***********\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 0.98, \"vcenter.vm.cpu.percent\": 0.0 }, { \"vcenter.vm\": \"8.0_plugin_Centos_7.5_8.214\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 10.99, \"vcenter.vm.cpu.percent\": 1.5 }, { \"vcenter.vm\": \"8_0_UCSPE_8.110_new\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.ip\": \"************\", \"vcenter.vm.power.state\": \"poweredOn\", \"vcenter.vm.memory.used.percent\": 8.98, \"vcenter.vm.cpu.percent\": 1.25 }, { \"vcenter.vm\": \"Uday ITSM  Silver_8.156\", \"vcenter.vm.server\": \"***********9\", \"vcenter.vm.power.state\": \"poweredOff\", \"vcenter.vm.memory.used.percent\": 0.0, \"vcenter.vm.cpu.percent\": 0.0 } ], \"vcenter.networks\": 0 }, \"status\": \"succeed\", \"timeout\": 60, \"ui.event.uuid\": \"f65a39af-c6b2-4363-88a6-5ddfb9b79d9c\", \"url.protocol\": \"http\", \"user.name\": \"admin\", \"username\": \"<EMAIL>\", \"latency.ms\": 1161.0, \"error.code\": \"MD000\", \"event.type\": \"metric.poll\" }"));

            TestUtil.vertx().eventBus().send(EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), new JsonObject().mergeIn(event)
                    .put(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_IP)).put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                    .put(AIOpsConstants.DEPENDENCY_SOURCE, event.getString(AIOpsObject.OBJECT_IP))
                    .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD_MAP.getName()));

            var retries = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(5000, timer ->
            {
                // fetching and verifying new dependencies
                query(context, testContext, true, dependencies ->
                {
                    Assertions.assertFalse(dependencies.isEmpty());

                    var cleared = false;

                    var added = false;

                    for (var index = 0; index < dependencies.size(); index++)
                    {
                        var item = dependencies.getJsonObject(index);

                        if (item.getString(AIOpsConstants.DEPENDENCY_SOURCE) != null && item.getString(AIOpsConstants.DEPENDENCY_SOURCE).equalsIgnoreCase("***********9") && item.getLong(AIOpsConstants.DEPENDENCY_LEVEL) != -1)
                        {
                            if (!item.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).contains(vm))
                            {
                                cleared = true;
                            }
                        }
                        else if (item.getString(AIOpsConstants.DEPENDENCY_SOURCE) != null && item.getString(AIOpsConstants.DEPENDENCY_SOURCE).equalsIgnoreCase("************") && item.getLong(AIOpsConstants.DEPENDENCY_LEVEL) != -1)
                        {
                            if (item.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).contains(vm))
                            {
                                added = true;
                            }
                        }
                    }

                    if (cleared && added)
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        testContext.completeNow();

                    }
                    else if (retries.get() >= 5)
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        testContext.failNow("Maximum retries exceeded");
                    }
                    else
                    {
                        retries.getAndIncrement();
                    }
                });
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testHyperVDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.HYPER_V.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items -> assertVMDependenciesTestResult(items, testContext, object.getLong(ID), eventContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testHyperVClusterDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemsByType(NMSConstants.Type.HYPER_V_CLUSTER).getLong(0);

        Assertions.assertNotNull(object);

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object)
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items -> assertVirtualizationClusterDependencyTestResult(items, testContext, eventContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testCitrixXenDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CITRIX_XEN.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items -> assertVMDependenciesTestResult(items, testContext, object.getLong(ID), eventContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testCitrixXenClusterDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CITRIX_XEN_CLUSTER.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items -> assertVirtualizationClusterDependencyTestResult(items, testContext, eventContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testWindowsClusterDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS_CLUSTER.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        query(eventContext, testContext, true, items ->
        {
            Assertions.assertFalse(items.isEmpty());

            var os = false;

            for (var index = 0; index < items.size(); index++)
            {
                var result = items.getJsonObject(index);

                if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.SEVEN.getName()))
                {
                    os = true;

                    Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

                    break;
                }
            }

            Assertions.assertTrue(os);

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testWindowsClusterDependenciesWithTopBottomHierarchy(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS_CLUSTER.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertNotNull(result);

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

            Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

            var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

            Assertions.assertNotNull(child);

            Assertions.assertFalse(child.isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testChildParentDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.BOTTOM_TOP_HIERARCHY.getName(), result ->
        {
            Assertions.assertFalse(result.isEmpty());

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_PARENT));

            Assertions.assertFalse(result.getJsonArray(DEPENDENCY_PARENT).isEmpty());

            var parent = result.getJsonArray(AIOpsConstants.DEPENDENCY_PARENT).getJsonObject(0);

            Assertions.assertNotNull(parent);

            Assertions.assertTrue(parent.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION));

            assertEquals(ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "**********", ID).getLong(0), parent.getLong(AIOpsConstants.DEPENDENCY_DESTINATION));

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testCreateMultipleDependencies(VertxTestContext testContext)
    {
        var item = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********");

        Assertions.assertNotNull(item);

        var context = new JsonObject("{\"dependency.mapper.parent\":\"***********\",\"dependency.mapper.child\":\"**********\",\"dependency.mapper.type\":\"Network\",\"dependency.mapper.archived\":\"no\",\"dependency.mapper.context\":{\"dependency.mapper.parent.interface\":\"15\",\"dependency.mapper.parent.interface.name\":\"motadata-DMZ\",\"dependency.mapper.child.interface\":\"63\",\"dependency.mapper.child.interface.name\":\"Vl500\",\"dependency.mapper.link.layer\":\"L2\"}}");

        TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, context, httpResponse ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, httpResponse.result().statusCode());

                    var body = httpResponse.result().bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.containsKey(STATUS));

                    TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, new JsonObject("{\"dependency.mapper.parent\":\"***********\",\"dependency.mapper.child\":\"**********\",\"dependency.mapper.type\":\"Network\",\"dependency.mapper.archived\":\"no\",\"dependency.mapper.context\":{\"dependency.mapper.parent.interface\":\"11\",\"dependency.mapper.parent.interface.name\":\"motadata-LAN\",\"dependency.mapper.child.interface\":\"8\",\"dependency.mapper.child.interface.name\":\"Gi1/0/1\",\"dependency.mapper.link.layer\":\"L2\"}}"), response ->
                            testContext.verify(() ->
                            {
                                assertEquals(SC_OK, response.result().statusCode());

                                var result = response.result().bodyAsJsonObject();

                                Assertions.assertNotNull(result);

                                Assertions.assertTrue(result.containsKey(STATUS));

                                testContext.completeNow();
                            }));
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    @Disabled
    void testMultipleDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        query(eventContext, testContext, true, items ->
        {
            // means neighbor is connected
            Assertions.assertFalse(items.isEmpty());

            var levels = items.stream().filter(item -> JsonObject.mapFrom(item).containsKey(AIOpsConstants.DEPENDENCY_LEVEL) && CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL)) != null)
                    .map(item -> CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL))).distinct().collect(Collectors.toCollection(ArrayList::new));

            Assertions.assertFalse(levels.isEmpty());

            levels.remove(AIOpsConstants.DependencyLevel.MINUS_ONE.getName());

            queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
            {
                Assertions.assertNotNull(result);

                Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

                Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

                var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

                Assertions.assertNotNull(child);

                Assertions.assertFalse(child.isEmpty());

                testContext.completeNow();
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
        //#24868
    void testDuplicateChildManualLinkDependency(VertxTestContext testContext)
    {

        var item = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********");

        Assertions.assertNotNull(item);

        var context = new JsonObject("{\"dependency.mapper.parent\":\"**********\",\"dependency.mapper.child\":\"***********\",\"dependency.mapper.type\":\"Network\",\"dependency.mapper.archived\":\"no\",\"dependency.mapper.context\":{\"dependency.mapper.parent.interface\":\"62\",\"dependency.mapper.parent.interface.name\":\"vl100\",\"dependency.mapper.child.interface\":\"15\",\"dependency.mapper.child.interface.name\":\"motadata-DMZ\",\"dependency.mapper.link.layer\":\"L2\"}}");

        TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, context, response ->
                testContext.verify(() ->
                {
                    var result = response.result().bodyAsJsonObject();

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.containsKey(STATUS));

                    Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                    Assertions.assertEquals(String.format(ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED, "All entries contains cyclic or duplicate dependencies"), result.getString(MESSAGE));

                    testContext.completeNow();
                }));
    }

    @Test
    @Order(24)
    void testTopologyParentChildDependency(VertxTestContext testContext)
    {
        var eventContext = new JsonObject().put(ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********"))
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK)
                .put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.FLAT.getName());

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                Assertions.assertNotNull(context);

                if (context.containsKey(EVENT_TYPE) && context.getString(EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_ACTION_TOPOLOGY_HIERARCHY_FETCH))
                {
                    var result = context.getJsonArray(RESULT);

                    Assertions.assertNotNull(result);

                    Assertions.assertFalse(result.isEmpty());

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister();

                testContext.failNow(exception);
            }
        });

        TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_HIERARCHY_FETCH, eventContext.put(SESSION_ID, TestUtil.getSessionId()));
    }

    @Test
    @Order(25)
    void testTopologyParentFetchNetwork(VertxTestContext testContext)
    {
        try
        {
            var eventContext = new JsonObject().put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK);

            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    Assertions.assertTrue(context.containsKey(DEPENDENCY_PARENT));

                    var result = context.getJsonObject(DEPENDENCY_PARENT);

                    Assertions.assertFalse(result.isEmpty());

                    Assertions.assertTrue(result.containsKey(CommonUtil.getString(ObjectConfigStore.getStore().getItemByIP("***********"))));

                    Assertions.assertFalse(result.getJsonArray(CommonUtil.getString(ObjectConfigStore.getStore().getItemByIP("***********"))).isEmpty());

                    Assertions.assertTrue(result.getJsonArray(CommonUtil.getString(ObjectConfigStore.getStore().getItemByIP("***********"))).contains(ObjectConfigStore.getStore().getItemByIP("**********")));

                    testContext.completeNow();

                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
                finally
                {
                    messageConsumer.unregister();
                }
            });

            TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_PARENT_FETCH, eventContext.put(SESSION_ID, TestUtil.getSessionId()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @Test
    @Order(26)
    void testTopologyParentFetchVirtualization(VertxTestContext testContext)
    {
        var eventContext = new JsonObject().put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.VCENTER.getName());

        assertParentChildDependencyTestResult(testContext, object.getLong(ID));

        TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_PARENT_FETCH, eventContext.put(SESSION_ID, TestUtil.getSessionId()));
    }

    @Test
    @Order(27)
    void testTopologyParentFetchCloud(VertxTestContext testContext)
    {
        var eventContext = new JsonObject().put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AWS_CLOUD.getName());

        assertParentChildDependencyTestResult(testContext, object.getLong(ID));

        TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_PARENT_FETCH, eventContext.put(SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testPrismDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.PRISM.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var context = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.HCI.name());

        query(context, testContext, true, items ->
        {
            // means prism dependencies available ( prism -> cluster -> nutanix)
            Assertions.assertFalse(items.isEmpty());

            var cluster = false;

            var nutanix = false;

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                // todo ; for flat , why odd condition in DQP?

                if (CommonUtil.getByteValue(item.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                {
                    Assertions.assertFalse(item.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

                    nutanix = true;
                }
                else if (CommonUtil.getByteValue(item.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.NINE.getName()))
                {
                    cluster = true;
                }
            }

            Assertions.assertTrue(cluster);

            Assertions.assertTrue(nutanix);

            assertParentChildHCIDependency(context, testContext, true);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testNutanixDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.NUTANIX.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.HCI.name());

        query(eventContext, testContext, true, items ->
        {
            Assertions.assertFalse(items.isEmpty());

            var nutanix = false;

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (CommonUtil.getByteValue(item.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                {
                    Assertions.assertFalse(item.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

                    nutanix = true;
                }
            }

            Assertions.assertTrue(nutanix);

            assertParentChildHCIDependency(eventContext, testContext, false);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testCiscovManageDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CISCO_VMANAGE.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.SDN.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertNotNull(result);

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

            Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

            var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

            Assertions.assertNotNull(child);

            Assertions.assertFalse(child.isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testCiscovEdgeDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("**********"));

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.SDN.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertNotNull(result);

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

            Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

            var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

            Assertions.assertNotNull(child);

            Assertions.assertFalse(child.isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testCiscoACIDependencies(VertxTestContext testContext)
    {
        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("***********"));

        Assertions.assertNotNull(item);

        Assertions.assertFalse(item.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, item.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.SDN.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertNotNull(result);

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

            Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

            var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

            Assertions.assertNotNull(child);

            Assertions.assertFalse(child.isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testCreateDependencyMappings(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<JsonArray>request(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".1.read",
                new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".1"),
                DELIVERY_OPTIONS,
                reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else if (reply.result().body().isEmpty())
                    {
                        testContext.failNow(new Exception("failed to read dependency query..."));
                    }
                    else
                    {
                        Assertions.assertFalse(reply.result().body().isEmpty());

                        testContext.completeNow();
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    @Timeout(value = 30, timeUnit = TimeUnit.SECONDS)
    void testAddNewLocalEventRouter(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<String>request(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".add.router",
                new JsonObject(), reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else
                    {
                        Assertions.assertNotNull(reply.result().body());

                        Assertions.assertEquals(reply.result().body(), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + "." + (MotadataConfigUtil.getDependencyLocalDomainInstances() + 1));

                        testContext.completeNow();
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    @Timeout(value = 30, timeUnit = TimeUnit.SECONDS)
    void testRemoveLocalEventRouter(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<String>request(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".remove.router",
                new JsonObject(), reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else
                    {
                        Assertions.assertNotNull(reply.result().body());

                        Assertions.assertEquals(reply.result().body(), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + "." + (MotadataConfigUtil.getDependencyLocalDomainInstances() + 1));

                        testContext.completeNow();
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    @Timeout(value = 45, timeUnit = TimeUnit.SECONDS)
    void testUndeployDependencyManager(VertxTestContext testContext)
    {
        var engine = Bootstrap.getDeployedVerticles().get(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName());

        Assertions.assertNotNull(engine);

        Bootstrap.undeployVerticle(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).onComplete(asyncResult ->
        {
            if (asyncResult.failed())
            {
                testContext.failNow(asyncResult.cause());
            }
            else
            {
                Assertions.assertNull(Bootstrap.getDeployedVerticles().get(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()));

                Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), AIOpsConstants.DEPENDENCY_SOURCE,
                                MotadataConfigUtil.getDependencyCrossDomainInstances(), "com.mindarray.aiops.DependencyManager", false), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), null)
                        .onComplete(result ->
                        {
                            if (result.failed())
                            {
                                testContext.failNow(result.cause());
                            }
                            else
                            {
                                Assertions.assertNotNull(Bootstrap.getDeployedVerticles().get(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()));

                                testContext.completeNow();
                            }
                        });
            }
        });
    }

    private void assertVirtualizationClusterDependencyTestResult(JsonArray items, VertxTestContext testContext, JsonObject eventContext)
    {
        Assertions.assertFalse(items.isEmpty());

        var vms = false;

        var esxi = false;

        for (var index = 0; index < items.size(); index++)
        {
            var result = items.getJsonObject(index);

            if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
            {
                vms = true;

                Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());
            }
            else if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.NINE.getName()))
            {
                esxi = true;

                Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());
            }
        }

        Assertions.assertTrue(esxi);

        Assertions.assertTrue(vms);

        assertParentChildVirtualizationClusterDependency(eventContext, testContext);
    }

    private void assertParentChildVirtualizationClusterDependency(JsonObject eventContext, VertxTestContext testContext)
    {
        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {

            Assertions.assertFalse(result.isEmpty());

            var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

            Assertions.assertNotNull(children);

            Assertions.assertFalse(children.isEmpty());

            if (ObjectConfigStore.getStore().getItem(result.getLong(ID)).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.VCENTER.getName()))
            {

                var response = new JsonObject(CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "test-plugin-metrics" + GlobalConstants.PATH_SEPARATOR + "vcenter_poll.json")));

                Assertions.assertNotNull(response);

                assertEquals(response.getJsonObject(ObjectConfigStore.getStore().getItem(result.getLong(ID)).getString(AIOpsObject.OBJECT_IP)).getJsonObject(RESULT).getInteger("vcenter.nodes"), children.size());

            }

            var levels = new HashSet<Byte>();

            for (var index = 0; index < children.size(); index++)
            {
                var child = children.getJsonObject(0);

                if (child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN) != null && !child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty())
                {
                    levels.add(CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                    var nestedChild = child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

                    if (nestedChild != null && nestedChild.containsKey(AIOpsConstants.DEPENDENCY_LEVEL))
                    {
                        levels.add(CommonUtil.getByteValue(nestedChild.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));
                    }
                }
            }

            var valid = true;

            if (!levels.contains(AIOpsConstants.DependencyLevel.NINE.getName()))
            {
                valid = false;

                testContext.failNow(new Exception("failed to find esxi..."));
            }

            if (!levels.contains(AIOpsConstants.DependencyLevel.EIGHT.getName()))
            {
                valid = false;

                testContext.failNow(new Exception("failed to find vms..."));
            }

            if (valid)
            {
                testContext.completeNow();
            }
        });
    }

    private void assertParentChildHCIDependency(JsonObject eventContext, VertxTestContext testContext, boolean prism)
    {
        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertFalse(result.isEmpty());

            var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

            Assertions.assertNotNull(children);

            Assertions.assertFalse(children.isEmpty());

            var levels = new HashSet<Byte>();

            for (var index = 0; index < children.size(); index++)
            {
                var child = children.getJsonObject(0);

                levels.add(CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                if (child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN) != null && !child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty())
                {
                    var nestedChild = child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

                    if (nestedChild != null && nestedChild.containsKey(AIOpsConstants.DEPENDENCY_LEVEL))
                    {
                        levels.add(CommonUtil.getByteValue(nestedChild.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));
                    }
                }
            }

            var valid = true;

            if (prism && !levels.contains(AIOpsConstants.DependencyLevel.NINE.getName()))
            {
                valid = false;

                testContext.failNow(new Exception("failed to find nutanix..."));
            }

            if (!levels.contains(AIOpsConstants.DependencyLevel.EIGHT.getName()))
            {
                valid = false;

                testContext.failNow(new Exception("failed to find vms..."));
            }

            if (valid)
            {
                testContext.completeNow();
            }
        });

    }

    private void assertCloudDependenciesTestResult(JsonObject eventContext, VertxTestContext testContext, String hierarchy)
    {
        query(eventContext, testContext, true, items ->
        {
            Assertions.assertFalse(items.isEmpty());

            assertCloudConnectedServices(new JsonArray(items.stream().filter(item -> JsonObject.mapFrom(item).getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION) != null)
                    .flatMap(item -> JsonObject.mapFrom(item).getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION)
                            .stream().map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                    .map(item -> CommonUtil.getLong(item.getValue()))
                    .collect(Collectors.toList())));

            queryHierarchy(eventContext, testContext, hierarchy, result ->
            {
                Assertions.assertFalse(result.isEmpty());

                var destinations = new JsonArray();

                qualifyChildren(result, destinations);

                assertCloudConnectedServices(destinations);

                testContext.completeNow();
            });

        });
    }

    private void assertCloudConnectedServices(JsonArray destinations)
    {
        Assertions.assertFalse(destinations.isEmpty());

        var connectedServices = ObjectConfigStore.getStore().getItems(destinations).stream().map(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_TYPE)).distinct().collect(Collectors.toList());

        Assertions.assertNotNull(connectedServices);

        Assertions.assertFalse(connectedServices.isEmpty());
    }

    private void qualifyChildren(JsonObject result, JsonArray qualifiedChildren)
    {
        var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

        if (children != null && !children.isEmpty())
        {
            for (var index = 0; index < children.size(); index++)
            {
                var child = children.getJsonObject(index);

                var destination = child.getValue(AIOpsConstants.DEPENDENCY_DESTINATION);

                if (destination instanceof Long && CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                {
                    qualifiedChildren.add(destination);
                }

                var connections = child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

                if (connections != null && !connections.isEmpty())
                {
                    qualifyChildren(child, qualifiedChildren);
                }
            }
        }
    }

    private void query(JsonObject context, VertxTestContext testContext, boolean connectedDependency, Byte level, Long timer, Handler<JsonArray> event)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + context.getString(AIOpsConstants.DEPENDENCY_TYPE) + ".query",
                context,
                new DeliveryOptions().setSendTimeout(120000L),
                reply ->
                {
                    if (reply.failed())
                    {
                        if (timer != null)
                        {
                            TestUtil.vertx().cancelTimer(timer);
                        }

                        testContext.failNow(reply.cause());
                    }

                    Assertions.assertTrue(reply.result().body().containsKey(AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()));

                    var result = reply.result().body().getJsonObject(AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName());

                    LOGGER.info(String.format("result from query : %s ", result));

                    //means connected neighbors
                    if (connectedDependency)
                    {
                        var valid = false;

                        for (var item : result.getMap().keySet())
                        {
                            if (item.contains(VALUE_SEPARATOR))
                            {
                                valid = true;

                                break;
                            }
                        }

                        Assertions.assertTrue(valid);
                    }

                    var dependencies = result.getJsonObject(context.getString(AIOpsConstants.DEPENDENCY_SOURCE));

                    Assertions.assertNotNull(dependencies);

                    Assertions.assertTrue(dependencies.containsKey(level.toString()));

                    var objects = dependencies.getJsonArray(level.toString());

                    Assertions.assertNotNull(objects);

                    Assertions.assertFalse(objects.isEmpty());

                    event.handle(objects);
                });
    }

    private void query(JsonObject eventContext, VertxTestContext testContext, boolean checkResult, Handler<JsonArray> event)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                eventContext.put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.FLAT.getName()),
                DELIVERY_OPTIONS,
                reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }

                    Assertions.assertNotNull(reply.result().body());

                    Assertions.assertFalse(reply.result().body().isEmpty());

                    var items = reply.result().body().getJsonArray(RESULT);

                    LOGGER.info(String.format("result from dependency query : %s ", items));

                    if (checkResult)
                    {
                        Assertions.assertNotNull(items);
                    }

                    event.handle(items);
                });
    }

    private void queryHierarchy(JsonObject eventContext, VertxTestContext testContext, String resultType, Handler<JsonObject> event)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                eventContext.put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_FORMAT, resultType),
                DELIVERY_OPTIONS,
                reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }

                    Assertions.assertNotNull(reply.result().body());

                    Assertions.assertFalse(reply.result().body().isEmpty());

                    var result = reply.result().body().getJsonObject(RESULT);

                    Assertions.assertNotNull(result);

                    event.handle(result);
                });
    }

    private void assertVMDependenciesTestResult(JsonArray items, VertxTestContext testContext, long id, JsonObject eventContext)
    {
        Assertions.assertFalse(items.isEmpty());

        var dependency = items.getJsonObject(0);

        Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_CONNECTED_LINK));

        Assertions.assertTrue(dependency.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK).equalsIgnoreCase(UNKNOWN));

        assertEquals(id, dependency.getLong(AIOpsConstants.DEPENDENCY_SOURCE));

        assertEquals(AIOpsConstants.DependencyLevel.EIGHT.getName(), CommonUtil.getByteValue(dependency.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

        Assertions.assertFalse(dependency.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertFalse(result.isEmpty());

            var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

            Assertions.assertNotNull(children);

            Assertions.assertFalse(children.isEmpty());

            var child = children.getJsonObject(0);

            Assertions.assertTrue(child.containsKey(AIOpsConstants.DEPENDENCY_CONNECTED_LINK));

            Assertions.assertTrue(child.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK).equalsIgnoreCase(UNKNOWN));

            assertEquals(id, child.getLong(AIOpsConstants.DEPENDENCY_SOURCE));

            assertEquals(AIOpsConstants.DependencyLevel.EIGHT.getName(), CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

            Assertions.assertTrue(child.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION));

            testContext.completeNow();
        });
    }

    private void assertObjectDependenciesTestResult(JsonObject object, boolean connectedDependency, String metricPlugin, String objectName, VertxTestContext testContext)
    {
        var context = new JsonObject().put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP)).put(AIOpsConstants.DEPENDENCY_LEVEL, NETWORK_TOPOLOGY_DEPENDENT_LEVELS);

        query(context, testContext, connectedDependency, AIOpsConstants.DependencyLevel.FOUR.getName(), null, objects ->
        {
            var metric = MetricConfigStore.getStore().getItemsByObject(object.getLong(ID)).stream().filter(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(metricPlugin))
                    .map(JsonObject::copy).findFirst().orElse(null);

            LOGGER.info(String.format("metric %s for object %s ", metric, object.getString(AIOpsObject.OBJECT_NAME)));

            Assertions.assertNotNull(metric);

            Assertions.assertNotNull(metric.getJsonObject(Metric.METRIC_CONTEXT));

            Assertions.assertNotNull(metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS));

            metricContext = metric;

            var interfaces = metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS)
                    .stream().map(item -> JsonObject.mapFrom(item).getString(objectName)).distinct().toList();

            Assertions.assertFalse(interfaces.isEmpty());

            assertEquals(objects.size(), interfaces.size());

            testContext.completeNow();
        });
    }

    void assertParentChildDependencyTestResult(VertxTestContext testContext, long id)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                Assertions.assertTrue(context.containsKey(DEPENDENCY_PARENT));

                Assertions.assertFalse(context.getJsonArray(DEPENDENCY_PARENT).isEmpty());

                Assertions.assertTrue(context.getJsonArray(DEPENDENCY_PARENT).contains(id));

                messageConsumer.unregister();

                testContext.completeNow();

            }
            catch (Exception exception)
            {
                messageConsumer.unregister();

                testContext.failNow(exception);
            }
        });
    }
}
