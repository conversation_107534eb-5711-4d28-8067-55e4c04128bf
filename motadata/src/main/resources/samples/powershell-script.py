#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode

from motadatasdk.constants import Constant
from motadatasdk.util import Util

# COMMAND="Get-WmiObject -Class Win32_Service | Select-Object -Property Name,State, Description"

if __name__ == '__main__':

    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    output = Util.load_plugin_context(str(args.context)).get(Constant.RESULT)

    result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

    if output is not None and len(output) > 0:

        services = []

        for tokens in output.strip().split("\r\n\r\n"):

            service = {}

            for token in str(tokens).strip().split("\r\n"):

                token = token.split(":", 1)

                if "Name" in token[0].strip(" "):

                    service["system.service"] = token[1].strip()

                elif "State" in token[0].strip(" "):

                    service["status"] = "up" if token[1].strip() is not None and token[1].strip() == "Running" else "down"

                elif "Description" in token[0].strip(" "):

                    service["system.service.description"] = token[1].strip()

            if len(service) > 0:

                services.append(service)

        if len(services) > 0:

            result[Constant.RESULT]["system.service"] = services

            result[Constant.STATUS] = Constant.STATUS_SUCCEED

    print(b64encode(json.dumps(result).encode()).decode(), end=Constant.BLANK_STRING)