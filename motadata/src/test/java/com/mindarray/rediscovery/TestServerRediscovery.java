/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.rediscovery;

import com.mindarray.*;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Metric;
import com.mindarray.api.Scheduler;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.TestConstants.prepareParams;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.SystemFile.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@Timeout(180 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestServerRediscovery
{
    public static final JsonObject METRICS = new JsonObject();
    public static final JsonArray PROCESSES = new JsonArray();
    public static final JsonArray SERVICES = new JsonArray();
    public static final Map<Long, JsonObject> REDISCOVERY_ITEMS = new ConcurrentHashMap<>();
    public static final JsonObject PARAMETERS = new JsonObject();
    private static final Logger LOGGER = new Logger(TestServerRediscovery.class, MOTADATA_NMS, "Server Rediscovery Test");

    public static MessageConsumer<JsonObject> messageConsumer = null;
    public static MessageConsumer<JsonObject> messageHandler = null;
    public static long windowsId = -1L;
    public static long linuxId = -1L;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            testContext.awaitCompletion(10, TimeUnit.SECONDS);

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "rediscover-parameters.json");

            Assertions.assertTrue(file.exists());

            PARAMETERS.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

            Assertions.assertFalse(PARAMETERS.isEmpty());

            var context = PARAMETERS.getJsonObject(testInfo.getDisplayName());

            Assertions.assertNotNull(context);

            windowsId = ObjectConfigStore.getStore().getItemByIP(context.getString("windows"), NMSConstants.Type.WINDOWS).getLong(ID);

            linuxId = ObjectConfigStore.getStore().getItemByIP(context.getString("linux"), NMSConstants.Type.LINUX).getLong(ID);

            Assertions.assertNotEquals(-1, windowsId);

            Assertions.assertNotEquals(-1, linuxId);

            assertRediscoveryConsumerSetup();

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (messageHandler != null)
        {
            messageHandler.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }

    }

    public static void testRediscover(VertxTestContext testContext, JsonObject rediscoveryParameters, JsonObject object, boolean abort)
    {
        var objects = rediscoveryParameters.getJsonArray(NMSConstants.OBJECTS);

        rediscoveryParameters.remove(NMSConstants.OBJECTS);

        var autoProvision = object.containsKey(NMSConstants.AUTO_PROVISION_STATUS) && object.getString(NMSConstants.AUTO_PROVISION_STATUS).equalsIgnoreCase(YES);

        TestAPIUtil.post(TestAPIConstants.SCHEDULER_API_ENDPOINT, rediscoveryParameters, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    try
                    {
                        LOGGER.info(String.format("scheduler API response : %s ", response.bodyAsJsonObject().encode()));

                        Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                        var retries = new AtomicInteger();

                        var schedulerId = response.bodyAsJsonObject().getJsonArray(ID).getLong(0);

                        TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                        {
                            if (SchedulerConfigStore.getStore().getItem(schedulerId) != null)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                TestNMSUtil.setSchedulerId(response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                objects.forEach(rediscoverObject -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(rediscoverObject), STATUS_UP, DateTimeUtil.currentSeconds()));

                                LOGGER.trace(String.format("cloud rediscovery request sent for: %s", ObjectConfigStore.getStore().getObjectNames(response.bodyAsJsonObject().getJsonArray(ID))));

                                TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                                if (abort)
                                {
                                    TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_STOP, new JsonObject().put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0))
                                            .put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER));

                                    for (var index = 0; index < 5; index++)
                                    {
                                        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                                        TestUtil.vertx().setTimer(1000, id ->
                                                TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_STOP, new JsonObject().put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0))
                                                        .put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)));
                                    }

                                    testContext.completeNow();
                                }
                                else
                                {
                                    REDISCOVERY_ITEMS.put(response.bodyAsJsonObject().getJsonArray(ID).getLong(0), new JsonObject().put("object", object).put("auto.provision", autoProvision).put("metric.type", rediscoveryParameters.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getString(NMSConstants.REDISCOVER_JOB)).put("context", testContext));
                                }
                            }
                            else if (retries.get() > 5)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                testContext.failNow("scheduler id not found in scheduler config store");
                            }
                            else
                            {
                                retries.incrementAndGet();
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        testContext.failNow(exception);
                    }
                })));
    }

    private static void assertRediscoveryConsumerSetup()
    {
        messageHandler = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var event = message.body();

                var eventContext = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                if (event.getString(EVENT_TYPE) != null && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_STREAMING_BROADCAST) && (eventContext.getString(EVENT_STATE) != null && eventContext.getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED) && eventContext.getJsonObject(EVENT_CONTEXT) != null
                        && eventContext.getJsonObject(EVENT_CONTEXT).containsKey(EVENT_SCHEDULER) && REDISCOVERY_ITEMS.containsKey(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER))))
                {
                    var schedulerContext = REDISCOVERY_ITEMS.get(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER));

                    var testContext = (VertxTestContext) schedulerContext.getValue("context");

                    var object = schedulerContext.getJsonObject("object");

                    var autoProvision = schedulerContext.getBoolean("auto.provision");

                    LOGGER.info("Received rediscovery event : " + event.encode());

                    if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        LOGGER.info(String.format("Scheduler context info received: %s and auto provision is %s", object == null ? "object is null" : object.encode(), autoProvision));

                        Assertions.assertTrue(Boolean.TRUE);

                        if ((object != null && object.isEmpty()) || autoProvision)
                        {
                            REDISCOVERY_ITEMS.remove(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER));

                            testContext.completeNow();
                        }
                    }
                    else if (eventContext.getJsonObject(EVENT_CONTEXT) == null || eventContext.getJsonObject(EVENT_CONTEXT).getString(NMSConstants.REDISCOVER_JOB) == null)
                    {
                        testContext.failNow(new Exception("Rediscover failed"));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testRediscoverApps(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var objects = new JsonArray().add(linuxId).add(windowsId);

        testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).put(NMSConstants.OBJECTS, objects), new JsonObject(), false);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetWindowsProcess(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + windowsId, testContext.succeeding(response ->
        {
            assertEquals(HttpStatus.SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

            var result = JsonObject.mapFrom(body.getValue(RESULT));

            assertTrue(result != null && !result.isEmpty());

            METRICS.put(RESULT, result);

            PROCESSES.add(METRICS.getJsonObject(RESULT).getJsonArray("Windows Process").getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).getJsonObject(0));

            var metric = JsonObject.mapFrom(body.getValue(RESULT));

            metric.getJsonArray("Windows Process").getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).remove(0);

            TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + windowsId, new JsonObject().put(REQUEST_PARAMS, new JsonArray().add(metric.getJsonArray("Windows Process").getJsonObject(0))), testContext.succeeding(urlResult ->
            {
                assertEquals(HttpStatus.SC_OK, urlResult.statusCode());

                Assertions.assertNotNull(urlResult.bodyAsJsonObject());

                assertEquals(HttpStatus.SC_OK, urlResult.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + windowsId, testContext.succeeding(httpResponse ->
                {
                    assertEquals(HttpStatus.SC_OK, httpResponse.statusCode());

                    var output = httpResponse.bodyAsJsonObject();

                    Assertions.assertNotNull(output);

                    assertEquals(HttpStatus.SC_OK, output.getInteger(RESPONSE_CODE));

                    output = JsonObject.mapFrom(output.getValue(RESULT));

                    assertTrue(output != null && !output.isEmpty());

                    testContext.completeNow();

                }));

            }));

        }));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testRediscoverProcess(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var context = prepareParams(testInfo.getTestMethod().get().getName()).getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray().add(windowsId));

        testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).mergeIn(context), PROCESSES.getJsonObject(0), false);

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {

                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_REDISCOVER_STOP))
                {
                    var params = new JsonArray();

                    params.add(new JsonObject().put(ID, eventContext.getLong(ID))
                            .put(NMSConstants.REDISCOVER_JOB, eventContext.getString(NMSConstants.REDISCOVER_JOB))
                            .put(SESSION_ID, TestUtil.getSessionId())
                            .put(NMSConstants.OBJECT, PROCESSES.getJsonObject(0)).put(NMSConstants.OBJECTS, new JsonArray().add(PROCESSES.getJsonObject(0)))
                            .put(EVENT_SCHEDULER, TestNMSUtil.getSchedulerId()));

                    TestAPIUtil.post(TestAPIConstants.OBJECT_PROVISION_API_ENDPOINT, new JsonObject().put(REQUEST_PARAMS, params), testContext.succeeding(response ->
                    {
                        Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                        var result = response.bodyAsJsonObject();

                        Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                        Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                        Assertions.assertEquals(InfoMessageConstants.OBJECT_PROVISION_START_SUCCEEDED, result.getString(MESSAGE));

                        messageConsumer.unregister();

                        testContext.completeNow();
                    }));
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testValidateServerMetricsAfterRediscoverWindowsProcess(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + windowsId, testContext.succeeding(response ->
        {
            assertEquals(HttpStatus.SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

            body = JsonObject.mapFrom(body.getValue(RESULT));

            assertTrue(body != null && !body.isEmpty());

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetWindowsService(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + windowsId, testContext.succeeding(response ->
        {
            assertEquals(HttpStatus.SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

            var result = JsonObject.mapFrom(body.getValue(RESULT));

            assertTrue(result != null && !result.isEmpty());

            METRICS.put(RESULT, result);

            var metric = JsonObject.mapFrom(body.getValue(RESULT));

            LOGGER.info("Metric : " + metric.encode());

            var items = metric.getJsonArray(NMSConstants.RediscoverJob.WINDOWS_SERVICE.getName()).getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

            for (var index = 0; index < items.size(); index++)
            {
                if (items.getJsonObject(index).getString(STATUS).equals(STATUS_UP))
                {
                    SERVICES.add(JsonObject.mapFrom(metric.getJsonArray(NMSConstants.RediscoverJob.WINDOWS_SERVICE.getName()).getJsonObject(0).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).remove(index)));

                    LOGGER.info("Services : " + SERVICES.encode());

                    break;
                }
            }

            TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + windowsId, new JsonObject().put(REQUEST_PARAMS, new JsonArray().add(metric.getJsonArray(NMSConstants.RediscoverJob.WINDOWS_SERVICE.getName()).getJsonObject(0))), testContext.succeeding(httpResponse ->
            {
                assertEquals(HttpStatus.SC_OK, httpResponse.statusCode());

                Assertions.assertNotNull(httpResponse.bodyAsJsonObject());

                assertEquals(HttpStatus.SC_OK, httpResponse.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + windowsId, testContext.succeeding(urlResult ->
                {
                    assertEquals(HttpStatus.SC_OK, urlResult.statusCode());

                    var output = urlResult.bodyAsJsonObject();

                    Assertions.assertNotNull(output);

                    assertEquals(HttpStatus.SC_OK, output.getInteger(RESPONSE_CODE));

                    output = JsonObject.mapFrom(output.getValue(RESULT));

                    assertTrue(output != null && !output.isEmpty());

                    testContext.completeNow();

                }));

            }));

        }));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testRediscoverWindowsService(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var context = prepareParams(testInfo.getTestMethod().get().getName()).getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray().add(windowsId));

        testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).mergeIn(context), SERVICES.getJsonObject(0), false);

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {

                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_REDISCOVER_STOP))
                {
                    var params = new JsonArray();

                    params.add(new JsonObject().put(ID, eventContext.getLong(ID))
                            .put(NMSConstants.REDISCOVER_JOB, eventContext.getString(NMSConstants.REDISCOVER_JOB))
                            .put(SESSION_ID, TestUtil.getSessionId())
                            .put(NMSConstants.OBJECT, SERVICES.getJsonObject(0)).put(NMSConstants.OBJECTS, new JsonArray().add(SERVICES.getJsonObject(0)))
                            .put(EVENT_SCHEDULER, TestNMSUtil.getSchedulerId()));

                    TestAPIUtil.post(TestAPIConstants.OBJECT_PROVISION_API_ENDPOINT, new JsonObject().put(REQUEST_PARAMS, params), testContext.succeeding(response ->
                    {
                        Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                        var result = response.bodyAsJsonObject();

                        Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                        Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                        Assertions.assertEquals(InfoMessageConstants.OBJECT_PROVISION_START_SUCCEEDED, result.getString(MESSAGE));

                        var count = new AtomicInteger();

                        TestUtil.vertx().setPeriodic(5 * 1000L, timer ->
                        {
                            try
                            {
                                if (count.incrementAndGet() < 5)
                                {
                                    var scheduler = SchedulerConfigStore.getStore().getItem(TestNMSUtil.getSchedulerId());

                                    if (scheduler != null && scheduler.containsKey(Scheduler.SCHEDULER_STATE)
                                            && !scheduler.getString(Scheduler.SCHEDULER_STATE).equalsIgnoreCase(NMSConstants.STATE_RUNNING))
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        messageConsumer.unregister();

                                        testContext.completeNow();
                                    }
                                }
                                else
                                {
                                    TestUtil.vertx().cancelTimer(timer);

                                    messageConsumer.unregister();

                                    testContext.failNow("failed to update scheduler running state");
                                }
                            }
                            catch (Exception exception)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                testContext.failNow(exception.getMessage());

                                LOGGER.error(exception);
                            }
                        });
                    }));
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

    // #3604
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDeleteWindowsServiceMonitor(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.delete(OBJECT_API_ENDPOINT + "/" + windowsId, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, "Monitor"), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            assertEquals(HttpStatus.SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testValidateServerMetricsAfterRediscoverWindowsService(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + windowsId, testContext.succeeding(response ->
        {
            assertEquals(HttpStatus.SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

            var result = JsonObject.mapFrom(body.getValue(RESULT));

            assertTrue(result != null && !result.isEmpty());

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testRediscoverDirectory(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.post(SYSTEM_FILE_API_ENDPOINT, new JsonObject()
                        .put(SYSTEM_FILE, "test.txt").put(SYSTEM_FILE_OS, "Linux").put(SYSTEM_FILE_TYPE, "File"),
                testContext.succeeding(response ->
                {
                    var context = prepareParams(testInfo.getTestMethod().get().getName()).getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray().add(linuxId));

                    testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).mergeIn(context), new JsonObject(), false);
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testValidateServerMetricsAfterRediscoverDirectory(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + linuxId, testContext.succeeding(response ->
        {
            assertEquals(HttpStatus.SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

            body = JsonObject.mapFrom(body.getValue(RESULT));

            assertTrue(body != null && !body.isEmpty());

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testRediscoverWindowsServiceAutoProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var context = prepareParams(testInfo.getTestMethod().get().getName()).getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray().add(windowsId));

        testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).mergeIn(context), new JsonObject(), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testRediscoverDirectoryAutoProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var metrics = MetricConfigStore.getStore().getItemsByObject(linuxId);

        Assertions.assertFalse(metrics.isEmpty());

        var fileMetric = metrics.stream().filter(entry -> entry.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.LINUX_FILE.getName()))
                .map(JsonObject::mapFrom).findFirst().orElse(null);

        Assertions.assertNotNull(fileMetric);

        fileMetric.getJsonObject(Metric.METRIC_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray());

        Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, fileMetric.getLong(ID)),
                fileMetric, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                result -> MetricConfigStore.getStore().updateItem(fileMetric.getLong(ID)).onComplete(asyncResult ->
                {
                    var item = metrics.stream().filter(entry -> entry.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.LINUX_DIR.getName()))
                            .map(JsonObject::mapFrom).findFirst().orElse(null);

                    Assertions.assertNotNull(item);

                    item.getJsonObject(Metric.METRIC_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray());

                    Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                            item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                            response -> MetricConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(futureResult ->
                            {
                                var context = prepareParams(testInfo.getTestMethod().get().getName()).getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray().add(linuxId));

                                testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).mergeIn(context), new JsonObject(), false);
                            }));
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(72)
        //bug - 3618
    void testGetCredentialProfileAfterProvisionRediscoverMonitorNoCredential(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.get(CREDENTIAL_PROFILE_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, CredentialProfileConfigStore.getStore(), null);

                            Assertions.assertFalse(response.bodyAsJsonObject().getJsonArray(RESULT).isEmpty());

                            testContext.completeNow();
                        })));
    }
}
