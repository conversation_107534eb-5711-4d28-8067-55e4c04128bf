package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.SLOCycle;
import com.mindarray.api.SLOProfile;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.NO;
import static com.mindarray.GlobalConstants.YES;
import static com.mindarray.api.APIConstants.*;

public class SLOProfileConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(SLOProfileConfigStore.class, GlobalConstants.MOTADATA_STORE, "SLO Profile Config Store");

    private static final SLOProfileConfigStore STORE = new SLOProfileConfigStore();

    private final Map<Long, JsonObject> activeSLOProfiles = new ConcurrentHashMap<>();

    private final AtomicLong COUNTER = new AtomicLong(0);

    private SLOProfileConfigStore()
    {
        super(DBConstants.TBL_SLO_PROFILE, LOGGER, false);
    }

    public static SLOProfileConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                int maximum = 0;

                try
                {
                    for (var item : items.values())
                    {
                        if (item.getInteger(GlobalConstants.ID) > maximum)
                        {
                            maximum = item.getInteger(GlobalConstants.ID);
                        }
                        if (item.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(YES)
                                && item.containsKey(SLOProfile.SLO_CYCLE_ID))
                        {
                            activeSLOProfiles.put(item.getLong(GlobalConstants.ID), item);
                        }
                    }

                    COUNTER.set(maximum);

                    promise.complete();
                }
                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init SLO profile config store...");

                    LOGGER.error(exception);
                }
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    public long newId()
    {
        return COUNTER.incrementAndGet();
    }

    public JsonArray getActiveSLOProfiles()
    {
        return new JsonArray(this.activeSLOProfiles.values().parallelStream().collect(Collectors.toList()));
    }

    public JsonArray getActiveSLOProfileIds()
    {
        return new JsonArray(activeSLOProfiles.keySet().parallelStream().collect(Collectors.toList()));
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        var oldItem = items.get(id);

        return super.updateItem(id).onComplete(result ->
        {
            var item = items.get(id);

            if (item.containsKey(SLOProfile.SLO_CYCLE_ID))
            {
                if (oldItem.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(NO) && item.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(YES))
                {
                    activeSLOProfiles.put(item.getLong(GlobalConstants.ID), item);
                }
                else if (oldItem.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(YES) && item.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(NO))
                {
                    activeSLOProfiles.remove(item.getLong(GlobalConstants.ID));
                }
                else if (oldItem.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(YES) && item.getString(SLOProfile.SLO_PROFILE_STATE).equalsIgnoreCase(YES))
                {
                    activeSLOProfiles.put(item.getLong(GlobalConstants.ID), item);
                }
            }
        });
    }
}
