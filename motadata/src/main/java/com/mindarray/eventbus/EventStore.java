/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.StandardOpenOption;
import java.util.Arrays;

import static com.mindarray.GlobalConstants.*;

/**
 * The EventStore class provides persistent storage for events with support for segmentation and offset tracking.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Creating and managing event storage segments on disk</li>
 *   <li>Writing events to segments with proper offset tracking</li>
 *   <li>Reading events from segments in a sequential manner</li>
 *   <li>Handling segment rollover when a segment is full</li>
 *   <li>Persisting and loading bookmarks to resume processing after a restart</li>
 *   <li>Managing pending and dropped events statistics</li>
 * </ul>
 * <p>
 * The EventStore uses memory-mapped files for efficient I/O operations, dividing the storage into
 * multiple segments to improve performance and manage large volumes of events. It maintains separate
 * reading and writing positions to allow concurrent event production and consumption.
 * <p>
 * The store automatically handles segment rollover when a segment becomes full, and it can recover
 * its state after a system restart by loading bookmarks that track the reading and writing positions.
 * This ensures that no events are lost during system restarts or failures.
 * <p>
 * The size of each segment is determined by the deployment type configuration, with larger segments
 * used for higher-tier deployments to handle greater event volumes.
 * <p>
 * Example usage:
 * <pre>
 * // Create an EventStore with a specific path
 * EventStore store = new EventStore("/path/to/events")
 *     .setStoreName("metric-events")
 *     .setMaxSegments(4);
 *
 * // Initialize the store
 * store.init();
 *
 * // Store an event
 * JsonObject event = new JsonObject()
 *     .put("type", "cpu-usage")
 *     .put("value", 75.5)
 *     .put("timestamp", System.currentTimeMillis());
 * store.put(event);
 *
 * // Retrieve an event
 * byte[] eventData = store.get();
 * JsonObject retrievedEvent = new JsonObject(Buffer.buffer(eventData));
 *
 * // Save bookmarks for recovery
 * store.save();
 *
 * // Close the store
 * store.close();
 * </pre>
 */
public class EventStore
{
    private static final Logger LOGGER = new Logger(EventStore.class, GlobalConstants.MOTADATA_EVENT_BUS, "Event Store");
    private final int segmentSize;
    private final String path;
    private int segments;
    private String name;
    private MappedByteBuffer reader;
    private MappedByteBuffer writer;
    private int readingOffset;
    private int writingOffset;
    private int writingSegment;
    private int readingSegment;
    private FileChannel readerChannel;
    private FileChannel writerChannel;
    private int[] pendingEvents;
    private int[] droppedEvents;

    protected EventStore(String path)
    {
        this.path = path;

        switch (MotadataConfigUtil.getDeploymentType())
        {
            case 0:
            {
                this.segmentSize = 32 * 1024 * 1024;

                break;
            }
            case 1:
            {
                this.segmentSize = 64 * 1024 * 1024;

                break;
            }
            case 2:
            {
                this.segmentSize = 128 * 1024 * 1024;

                break;
            }
            case 3:
            {
                this.segmentSize = 256 * 1024 * 1024;

                break;
            }
            default:
            {
                this.segmentSize = 32 * 1024 * 1024;
            }
        }
    }

    protected void put(JsonObject event) throws Exception
    {
        put(CodecUtil.compress(event.encode()));
    }

    protected void put(byte[] bytes) throws Exception
    {
        try
        {
            if (writer != null)
            {
                if (writingOffset + bytes.length + 4 <= segmentSize)
                {
                    if (writingSegment != readingSegment)
                    {
                        writer.putInt(writingOffset - 4, bytes.length);

                        writer.put(writingOffset, bytes);

                        writingOffset += bytes.length;

                        writer.putInt(writingOffset, Integer.MIN_VALUE); //EOF

                        writingOffset += 4;

                        pendingEvents[writingSegment] += 1;

                    }
                    else
                    {
                        if (writingOffset < readingOffset)
                        {
                            droppedEvents[writingSegment] += pendingEvents[writingSegment];

                            pendingEvents[writingSegment] = 0;

                            rolloverReadingSegment();

                            if (CommonUtil.debugEnabled())
                            {

                                LOGGER.debug(String.format("rollover reading segment %s due to slow reading...", name));
                            }

                        }

                        writer.putInt(writingOffset - 4, bytes.length);

                        writer.put(writingOffset, bytes);

                        writingOffset += bytes.length;

                        writer.putInt(writingOffset, Integer.MIN_VALUE); //EOF

                        writingOffset += 4;

                        pendingEvents[writingSegment] += 1;
                    }
                }
                else
                {
                    writer.clear();

                    writerChannel.close();

                    droppedEvents[writingSegment] += pendingEvents[writingSegment];

                    writingSegment += 1;

                    if (writingSegment == segments)
                    {
                        writingSegment = 0;
                    }

                    if (writingSegment == readingSegment)
                    {
                        rolloverReadingSegment();
                    }

                    pendingEvents[writingSegment] = 0;

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("rollover writing segment %s due to segment size overlapped...", name));
                    }

                    var segmentFile = new File(path + GlobalConstants.PATH_SEPARATOR + writingSegment + ".segment");

                    writerChannel = FileChannel.open(segmentFile.toPath(), StandardOpenOption.WRITE, StandardOpenOption.READ);

                    writer = writerChannel.map(FileChannel.MapMode.READ_WRITE, 0, segmentSize);

                    writingOffset = 0;

                    writer.putInt(writingOffset, Integer.MIN_VALUE); //EOF

                    writingOffset += 4;

                    put(bytes);
                }
            }
            else
            {
                throw new Exception("writer is null...");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    protected byte[] get() throws Exception
    {
        byte[] bytes = null;

        try
        {
            if (reader != null)
            {
                if (readingSegment == writingSegment)
                {
                    if (readingOffset < writingOffset)
                    {
                        bytes = readBytes();
                    }
                }
                else
                {
                    bytes = readBytes();

                    if (bytes == null)
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("rollover reading segment %s due to read segment end of the file...", name));
                        }

                        rolloverReadingSegment();

                        bytes = get();
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.warn(String.format("corrupted event received in %s", name));

            LOGGER.error(exception);
        }

        return bytes;
    }

    protected EventStore setMaxSegments(int segments)
    {
        this.segments = segments;

        this.pendingEvents = new int[segments];

        this.droppedEvents = new int[segments];

        return this;
    }

    protected EventStore setStoreName(String name)
    {
        this.name = name;

        return this;
    }

    private void createOrOpenStore()
    {
        try
        {
            File segmentFile;

            for (var segment = 0; segment < segments; segment++)
            {
                segmentFile = new File(path + GlobalConstants.PATH_SEPARATOR + segment + ".segment");

                if (!segmentFile.exists())
                {
                    segmentFile.createNewFile();
                }
            }

            segmentFile = new File(path + GlobalConstants.PATH_SEPARATOR + "0.segment");

            writerChannel = FileChannel.open(segmentFile.toPath(), StandardOpenOption.WRITE, StandardOpenOption.READ);

            writer = writerChannel.map(FileChannel.MapMode.READ_WRITE, 0, segmentSize);

            writingSegment = 0;

            writer.putInt(0, Integer.MIN_VALUE); //EOF

            writingOffset += 4;

            readerChannel = FileChannel.open(segmentFile.toPath(), StandardOpenOption.READ);

            reader = readerChannel.map(FileChannel.MapMode.READ_ONLY, 0, segmentSize);

            readingSegment = 0;

            readingOffset = 0;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    protected int init(String readingBookmark, String writingBookmark, int pendingEvents) throws Exception
    {
        try
        {
            if (segments <= 0)
            {
                segments = 1;
            }

            if (name.trim().isEmpty())
            {
                throw new Exception("store name can not be blank...");
            }

            //an abnormal motadata stop can result in pending events not being updated properly, which may cause the event size to become negative.

            if (!CommonUtil.isNullOrEmpty(readingBookmark) && CommonUtil.getInteger(readingBookmark.split("_")[1]) > 0 && !CommonUtil.isNullOrEmpty(writingBookmark) && CommonUtil.getInteger(writingBookmark.split("_")[1]) > 0 && pendingEvents > 0)
            {
                readingSegment = CommonUtil.getInteger(readingBookmark.split("_")[0]);

                var storeFile = new File(path + GlobalConstants.PATH_SEPARATOR + readingBookmark.split("_")[0] + ".segment");

                readerChannel = FileChannel.open(storeFile.toPath(), StandardOpenOption.READ);

                reader = readerChannel.map(FileChannel.MapMode.READ_ONLY, 0, segmentSize);

                readingOffset = CommonUtil.getInteger(readingBookmark.split("_")[1]);

                writingSegment = CommonUtil.getInteger(writingBookmark.split("_")[0]);

                storeFile = new File(path + GlobalConstants.PATH_SEPARATOR + writingBookmark.split("_")[0] + ".segment");

                writerChannel = FileChannel.open(storeFile.toPath(), StandardOpenOption.WRITE, StandardOpenOption.READ);

                writer = writerChannel.map(FileChannel.MapMode.READ_WRITE, 0, segmentSize);

                writingOffset = CommonUtil.getInteger(writingBookmark.split("_")[1]);

                this.pendingEvents[writingSegment] = pendingEvents;
            }
            else
            {
                createOrOpenStore();

                pendingEvents = 0;
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            throw new Exception(String.format("failed to open segment %s...", name));
        }

        return pendingEvents;
    }

    protected void init() throws Exception
    {
        init(null, null, 0);
    }

    protected long load() throws Exception
    {
        var pendingEvents = 0L;

        try
        {
            var valid = false;

            var lockFile = new File(path + GlobalConstants.PATH_SEPARATOR + "lock");

            var bookmarks = new File(path + GlobalConstants.PATH_SEPARATOR + "bookmarks");

            if (bookmarks.exists() && !lockFile.exists())
            {
                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(path + GlobalConstants.PATH_SEPARATOR + "bookmarks");

                if (buffer != null && buffer.length() > 0)
                {
                    valid = true;

                    var tokens = CommonUtil.getString(buffer).split(VALUE_SEPARATOR_WITH_ESCAPE);

                    pendingEvents = init(tokens[0], tokens[1], tokens.length > 2 ? CommonUtil.getInteger(tokens[2]) : 0);

                    LOGGER.debug(String.format("initialize event store %s... with pending events %s ", name, pendingEvents));
                }
            }

            if (!lockFile.exists())
            {
                lockFile.createNewFile();
            }

            if (!valid)
            {
                init();

                save();

                LOGGER.debug(String.format("initialize event store %s...", name));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return pendingEvents;
    }

    private void rolloverReadingSegment()
    {
        try
        {
            reader.clear();

            readerChannel.close();

            readingSegment += 1;

            if (readingSegment == segments)
            {
                readingSegment = 0;
            }

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("rollover reading segment %s...", name));
            }

            readerChannel = FileChannel.open(new File(path + GlobalConstants.PATH_SEPARATOR + readingSegment + ".segment").toPath(), StandardOpenOption.READ);

            reader = readerChannel.map(FileChannel.MapMode.READ_ONLY, 0, segmentSize);

            readingOffset = 0;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    private byte[] readBytes()
    {
        byte[] bytes = null;

        var length = reader.getInt(readingOffset);

        if (length > 0)
        {
            readingOffset += 4;

            bytes = new byte[length];

            reader.get(readingOffset, bytes, 0, length);

            readingOffset += length;

            pendingEvents[readingSegment] -= 1;
        }

        return bytes;
    }

    protected String getReadingBookmark()
    {
        return readingSegment + "_" + readingOffset;
    }

    protected String getWritingBookmark()
    {
        return writingSegment + "_" + writingOffset;
    }

    protected int getPendingEvents()
    {
        return Arrays.stream(this.pendingEvents).sum();
    }

    protected int getDroppedEvents()
    {
        return Arrays.stream(this.droppedEvents).sum();
    }

    protected void save()
    {
        Bootstrap.vertx().fileSystem().writeFileBlocking(path + PATH_SEPARATOR + "bookmarks", Buffer.buffer(getReadingBookmark() + GlobalConstants.VALUE_SEPARATOR + getWritingBookmark() + VALUE_SEPARATOR + getPendingEvents()));
    }

    protected void close() throws Exception
    {
        readerChannel.close();

        writerChannel.close();

        if (new File(path + GlobalConstants.PATH_SEPARATOR + "lock").exists())
        {
            Bootstrap.vertx().fileSystem().deleteBlocking(path + GlobalConstants.PATH_SEPARATOR + "lock");
        }
    }
}
