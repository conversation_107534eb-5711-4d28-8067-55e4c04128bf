/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.rediscovery;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.OBJECT_API_ENDPOINT;
import static com.mindarray.TestConstants.prepareParams;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Timeout(180 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestCloudRediscovery
{

    public static final Map<Long, JsonObject> REDISCOVERY_ITEMS = new ConcurrentHashMap<>();
    private static final Logger LOGGER = new Logger(TestCloudRediscovery.class, MOTADATA_NMS, "Cloud Rediscovery Test");
    private static final JsonObject PARAMETERS = new JsonObject();

    private static final JsonArray AZURE_VMS = new JsonArray();
    public static MessageConsumer<JsonObject> messageHandler = null;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "rediscover-parameters.json");

            Assertions.assertTrue(file.exists());

            PARAMETERS.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

            Assertions.assertFalse(PARAMETERS.isEmpty());

            assertRediscoveryConsumerSetup();

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (messageHandler != null)
        {
            messageHandler.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }


    private static void assertRediscoveryConsumerSetup()
    {
        messageHandler = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var eventContext = new JsonObject();

                if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));
                }
                else
                {
                    eventContext = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);
                }

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_STREAMING_BROADCAST) && (eventContext.getString(EVENT_STATE) != null && eventContext.getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED) && eventContext.getJsonObject(EVENT_CONTEXT) != null
                        && eventContext.getJsonObject(EVENT_CONTEXT).containsKey(EVENT_SCHEDULER) && REDISCOVERY_ITEMS.containsKey(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER))))
                {
                    var schedulerContext = REDISCOVERY_ITEMS.get(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER));

                    var testContext = (VertxTestContext) schedulerContext.getValue("context");

                    var object = schedulerContext.getJsonObject("object");

                    var autoProvision = schedulerContext.getBoolean("auto.provision");

                    LOGGER.info("Received rediscovery event : " + message.body().encode());

                    if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        Assertions.assertTrue(Boolean.TRUE);

                        if ((object != null && object.isEmpty()) || autoProvision)
                        {
                            REDISCOVERY_ITEMS.remove(eventContext.getJsonObject(EVENT_CONTEXT).getLong(EVENT_SCHEDULER));

                            testContext.completeNow();
                        }
                    }
                    else if (eventContext.getJsonObject(EVENT_CONTEXT) == null || eventContext.getJsonObject(EVENT_CONTEXT).getString(NMSConstants.REDISCOVER_JOB) == null)
                    {
                        testContext.failNow(new Exception("Rediscover failed"));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    public static void testRediscover(VertxTestContext testContext, JsonObject rediscoveryParameters, JsonObject object, boolean abort)
    {
        var objects = rediscoveryParameters.getJsonArray(NMSConstants.OBJECTS);

        rediscoveryParameters.remove(NMSConstants.OBJECTS);

        var autoProvision = object.containsKey(NMSConstants.AUTO_PROVISION_STATUS) && object.getString(NMSConstants.AUTO_PROVISION_STATUS).equalsIgnoreCase(YES);

        TestAPIUtil.post(TestAPIConstants.SCHEDULER_API_ENDPOINT, rediscoveryParameters, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    try
                    {
                        Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                        var retries = new AtomicInteger();

                        var schedulerId = response.bodyAsJsonObject().getJsonArray(ID).getLong(0);

                        TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                        {
                            if (SchedulerConfigStore.getStore().getItem(schedulerId) != null)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                TestNMSUtil.setSchedulerId(response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                objects.forEach(rediscoverObject -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(rediscoverObject), STATUS_UP, DateTimeUtil.currentSeconds()));

                                LOGGER.trace(String.format("cloud rediscovery request sent for: %s", ObjectConfigStore.getStore().getObjectNames(response.bodyAsJsonObject().getJsonArray(ID))));

                                TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                                if (abort)
                                {
                                    TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_STOP, new JsonObject().put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0))
                                            .put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER));

                                    for (var index = 0; index < 5; index++)
                                    {
                                        TestUtil.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                                        TestUtil.vertx().setTimer(1000, id -> TestUtil.vertx().eventBus().send(EVENT_REDISCOVER_STOP, new JsonObject().put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0))
                                                .put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)));
                                    }

                                    testContext.completeNow();
                                }
                                else
                                {
                                    REDISCOVERY_ITEMS.put(response.bodyAsJsonObject().getJsonArray(ID).getLong(0), new JsonObject().put("object", object).put("auto.provision", autoProvision).put("metric.type", rediscoveryParameters.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getString(NMSConstants.REDISCOVER_JOB)).put("context", testContext));
                                }
                            }
                            else if (retries.get() > 5)
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                testContext.failNow("scheduler id not found in scheduler config store");
                            }
                            else
                            {
                                retries.incrementAndGet();
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        testContext.failNow(exception);
                    }
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testDeleteCloudObject(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var id = ObjectConfigStore.getStore().getItemsByType(NMSConstants.Type.AZURE_VM).getLong(0);

        var objects = ObjectConfigStore.getStore().getItem(id);

        AZURE_VMS.add(new JsonObject().put(AIOpsObject.OBJECT_ACCOUNT_ID, objects.getString(AIOpsObject.OBJECT_ACCOUNT_ID))
                .put(AIOpsObject.OBJECT_IP, objects.getString(AIOpsObject.OBJECT_IP)).put(AIOpsObject.OBJECT_NAME, objects.getString(AIOpsObject.OBJECT_NAME))
                .put(AIOpsObject.OBJECT_RESOURCE_GROUP, objects.getString(AIOpsObject.OBJECT_RESOURCE_GROUP))
                .put(AIOpsObject.OBJECT_TARGET, objects.getString(AIOpsObject.OBJECT_TARGET)).put(AIOpsObject.OBJECT_TYPE, objects.getString(AIOpsObject.OBJECT_TYPE)));

        assertNotEquals(NOT_AVAILABLE, ObjectConfigStore.getStore().getObjectIdByTarget(AZURE_VMS.getJsonObject(0).getString(AIOpsObject.OBJECT_TARGET), NMSConstants.Type.AZURE_VM));

        TestAPIUtil.delete(OBJECT_API_ENDPOINT + "/" + id, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(InfoMessageConstants.OBJECT_ARCHIVED_SUCCEEDED, response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            TestUtil.vertx().setPeriodic(1000, timer ->
            {
                if (ObjectConfigStore.getStore().getItem(id) == null)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }
            });

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testRediscoverCloudObject(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertEquals(NOT_AVAILABLE, ObjectConfigStore.getStore().getObjectIdByTarget(AZURE_VMS.getJsonObject(0).getString(AIOpsObject.OBJECT_TARGET), NMSConstants.Type.AZURE_VM));

        var objects = ObjectConfigStore.getStore().getItemsByTypes(new JsonArray().add(NMSConstants.Type.OFFICE_365.getName()).add(NMSConstants.Type.AZURE_CLOUD.getName()).add(NMSConstants.Type.AWS_CLOUD.getName()));

        testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).put(NMSConstants.OBJECTS, objects), new JsonObject(), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    @Timeout(150 * 1000)
    void testRediscoverCloudObjectAutoProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var objects = ObjectConfigStore.getStore().getItemsByTypes(new JsonArray().add(NMSConstants.Type.OFFICE_365.getName()).add(NMSConstants.Type.AZURE_CLOUD.getName()).add(NMSConstants.Type.AWS_CLOUD.getName()));

        testRediscover(testContext, prepareParams(testInfo.getTestMethod().get().getName()).put(NMSConstants.OBJECTS, objects), new JsonObject(), false);
    }

    //3667-bug
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testRediscoverCloudObject2(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var context = prepareParams("testRediscoverCloudObject").getJsonObject(Scheduler.SCHEDULER_CONTEXT).put(NMSConstants.OBJECTS, ObjectConfigStore.getStore().getItemsByType(NMSConstants.Type.OFFICE_365));

        testRediscover(testContext, prepareParams("testRediscoverCloudObject").mergeIn(context), new JsonObject(), true);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
        // bug-3464
    void testProvisionInstanceHavingUpdatedName(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var objects = ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_TYPE,
                new JsonArray().add(NMSConstants.Type.OFFICE_365.getName()).add(NMSConstants.Type.MICROSOFT_TEAMS.getName()).add(NMSConstants.Type.ONEDRIVE.getName()).add(NMSConstants.Type.EXCHANGE_ONLINE.getName())
                        .add(NMSConstants.Type.SHAREPOINT_ONLINE.getName()), ID);

        assertFalse(objects.isEmpty());

        Bootstrap.configDBService().deleteAll(DBConstants.TBL_OBJECT,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, objects),
                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                handler -> ObjectConfigStore.getStore().updateItems(objects).onComplete(asyncResponse ->
                        Bootstrap.configDBService().getAll(DBConstants.TBL_DISCOVERY_RESULT + DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.OFFICE_365.getName()).getLong(ID), asyncResult ->
                        {
                            if (asyncResult.succeeded() && !asyncResult.result().isEmpty())
                            {
                                var item = asyncResult.result().getJsonObject(0);

                                TestAPIUtil.post(OBJECT_API_ENDPOINT + "/provision",
                                        new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(item)), testContext.succeeding(response ->
                                                testContext.verify(() ->
                                                        TestUtil.vertx().setPeriodic(1000, timer ->
                                                        {
                                                            var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TARGET, item.getString(AIOpsObject.OBJECT_TARGET));

                                                            if (object != null)
                                                            {
                                                                TestUtil.vertx().cancelTimer(timer);

                                                                Bootstrap.configDBService().update(DBConstants.TBL_DISCOVERY_RESULT + DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_OBJECT_TYPE, NMSConstants.Type.OFFICE_365.getName()).getLong(ID),
                                                                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                                        item.put(AIOpsObject.OBJECT_TARGET, "Demo").put(AIOpsObject.OBJECT_NAME, "Demo"),
                                                                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                                        result ->
                                                                        {
                                                                            if (result.succeeded())
                                                                            {
                                                                                TestAPIUtil.post(OBJECT_API_ENDPOINT + "/provision",
                                                                                        new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(item)), testContext.succeeding(provisionResponse ->
                                                                                                testContext.verify(() -> TestUtil.vertx().setPeriodic(1000, periodicTimer ->
                                                                                                {
                                                                                                    var newObject = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TARGET, "Demo");

                                                                                                    if (newObject != null)
                                                                                                    {
                                                                                                        TestUtil.vertx().cancelTimer(periodicTimer);

                                                                                                        testContext.completeNow();
                                                                                                    }
                                                                                                }))));
                                                                            }
                                                                            else
                                                                            {
                                                                                testContext.completeNow();
                                                                            }
                                                                        });
                                                            }
                                                        }))));
                            }
                            else
                            {
                                testContext.completeNow();
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testAutoAssignGroups(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var objects = ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AMAZON_EC2.getName());

        assertFalse(objects.isEmpty());

        var object = objects.getJsonObject(0);

        var parent = object.getString(AIOpsObject.OBJECT_REGION);

        var child = object.getString(AIOpsObject.OBJECT_TYPE);

        var groups = GroupConfigStore.getStore().getItems(object.getJsonArray(AIOpsObject.OBJECT_GROUPS));

        assertFalse(groups.isEmpty());

        var parentId = 0L;

        var childId = 0L;

        for (var index = 0; index < groups.size(); index++)
        {
            var group = groups.getJsonObject(index);

            if (group.getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_SYSTEM))
            {
                if (group.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(parent))
                {
                    parentId = group.getLong(ID);
                }


                if (group.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(child))
                {
                    childId = group.getLong(ID);
                }
            }
        }

        Assertions.assertTrue(parentId > 0);

        Assertions.assertTrue(childId > 0);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCheckMetadataMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var objects = ObjectConfigStore.getStore().getItemsByValues(AIOpsObject.OBJECT_VENDOR, new JsonArray().add(NMSConstants.Type.AWS_CLOUD.getName()).add(NMSConstants.Type.AZURE_CLOUD.getName()));

        assertFalse(objects.isEmpty());

        var customFields = CustomMonitoringFieldConfigStore.getStore().getItems().stream().map(item -> JsonObject.mapFrom(item).getLong(ID)).toList();

        assertFalse(customFields.isEmpty());

        var valid = false;

        for (var index = 0; index < objects.size(); index++)
        {
            var object = objects.getJsonObject(index);

            if (object.getJsonObject(AIOpsObject.OBJECT_CUSTOM_FIELDS) != null)
            {
                for (var metadataMetric : object.getJsonObject(AIOpsObject.OBJECT_CUSTOM_FIELDS))
                {
                    if (customFields.contains(CommonUtil.getLong(metadataMetric.getKey())))
                    {
                        valid = true;

                        testContext.completeNow();

                        break;
                    }
                }
            }
        }

        if (!valid)
        {
            testContext.failNow(new Exception("failed to find metadata metrics in cloud monitors...."));
        }
    }
}
