/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			Notes
 *  5-Feb-2025		Ya<PERSON>iwari		MOTADATA-5007: removed "metric.plugin.variables" from first level of metric during custom metric provision
 *  19-Feb-2025		Chandresh		MOTADATA-3680: Added INSTANCE system variable for instance runbook support
 * 25-Feb-2025		Darshan Parmar	MOTADATA-5215: SonarQube Suggestions Resolution
 *  28-Feb-2025     Smit Prajapari  MOTADATA-4956: added PLUGIN_BY_INSTANCES for rule tag
 *  24-Mar-2025     <PERSON>dresh       MOTADATA-5426: Docker discovery and polling support added
 *  24-Mar-2025     <PERSON><PERSON>     MOTADATA-5432: added PING_CHECK_PACKET_RETRIES for packet count for availability ping
 *  21-Apr-2025     Umang Sharma    MOTADATA-5831: Added Support for Status Flap Metric
 *  22-Apr-2025     Darshan Parmar  MOTADATA-5382: Added OBJECT_TYPES List added
 *  6-Apr-2025      Darshan Parmar  MOTADATA-6084:  HPE Primera Type added
 *  09-Apr-2025     Darshan Parmar  MOTADATA-6084:  HPE 3PAR Type added
 *  21-May-2025     Darshan Parmar  MOTADATA-6184:  Dell EMC Unity Type added
 *  21-May-2025     jenil           MOTADATA-6220: windows_snmp, linux_snmp types added
 *  26-May-2025     Sankalp         MOTADATA-6185 : Monitoring support for IBM AS 400
 *  20-Jun-2025     Darshan Parmar  MOTADATA-6587 : certificate based credential profile constants in HTTP/HTTPS protocol
 *  23-Jun-2025     Darshan Parmar  MOTADATA-6583 : Container Orchestration Category and Tanzu Kubernetes Object Type added
 *  04-Jul-2025     Darshan Parmar  MOTADATA-6699 : Kubernetes Object Type added
 *  08-July-2025    Priyansh Sindhav MOTADATA-6400: MongoDB Application support
 *  04-Jul-2025     Jenil Kajavadara  MOTADATA-6380 : oracle weblogic type added
 *  23-Jul-2025     Chopra Deven    MOTADATA-6889: Health Overview | Monitors (Polling Issue) widget shows infinite loader.
 *  24-Jul-2025     Yash Tiwari       MOTADATA-6867 : Handling post operations if instance is deleted
 *  28-Jul-2025     Umang           MOTADATA-6754 Support for Tags and Severity
 *  25-Jul-2025     Jenil Kajavadara  MOTADATA-6942 : mysqlreplication plugin added


 * */

package com.mindarray.nms;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.util.WorkerUtil;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.MetricPlugin.METRIC_PLUGIN_NAME;
import static com.mindarray.api.MetricPlugin.METRIC_PLUGIN_VARIABLES;
import static com.mindarray.db.DBConstants.ENTITY_TYPE_SYSTEM;
import static com.mindarray.db.DBConstants.FIELD_TYPE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_POLLING_ERROR_QUERY;
import static com.mindarray.flow.FlowEngineConstants.PROTOCOL;

/**
 * NMSConstants is a utility class that provides constants used throughout the NMS (Network Management System) package.
 * <p>
 * This class contains:
 * - String constants for keys used in JSON objects and configuration
 * - Numeric constants for timeouts, retries, and other configuration values
 * - Enumerations for various types (Category, Type, Protocol, etc.)
 * - Utility methods for working with NMS-related data
 * <p>
 * The constants are organized by functionality (e.g., ping-related, SNMP-related, interface-related)
 * to make them easier to locate and maintain.
 * <p>
 * Key enumerations include:
 * - {@link Type}: Defines the types of network objects that can be managed (e.g., SERVER, ROUTER, SWITCH)
 * - {@link Category}: Categorizes network objects (e.g., NETWORK, SERVER, VIRTUALIZATION)
 * - {@link Protocol}: Defines communication protocols used for monitoring (e.g., SNMP, SSH, WMI)
 * - {@link State}: Defines possible states for objects and metrics (e.g., ENABLE, DISABLE)
 * - {@link MetricPlugin}: Defines available metric plugins for different monitoring scenarios
 * - {@link RediscoverJob}: Defines types of rediscovery jobs for network objects
 * - {@link TopologyPluginType}: Defines types of topology discovery plugins
 * <p>
 * Utility methods provide functionality for:
 * - Determining object categories and types
 * - Validating and processing metric data
 * - Managing topology and discovery operations
 * - Handling SNMP-related functionality
 * <p>
 * This class is not meant to be instantiated.
 */
public final class NMSConstants
{
    // General constants
    public static final String CORRELATION_METRICS = "correlation.metrics";
    public static final String AUTO_PROVISION_STATUS = "auto.provision.status";
    public static final String REDISCOVER_JOB = "rediscover.job";
    public static final String APPS = "apps";
    public static final String APP_PROCESS = "app.process";
    public static final String OBJECTS = "objects";
    public static final String DISCOVERED_OBJECTS = "discovered.objects";
    public static final String OBJECT = "object";
    public static final String PROCESSES = "processes";
    public static final String CONTAINERS = "containers";
    public static final String SERVICES = "services";
    public static final String NETWORK_SERVICES = "network.services";
    public static final String VMS = "vms";
    public static final String AGENTS = "agents";
    public static final String ACCESS_POINTS = "access.points";
    public static final String WAN_LINKS = "wan.links";
    public static final String CONFIG_DEVICES = "config.devices";
    public static final String CONTROLLER_MODEL = "controller.model";
    // Ping-related constants
    public static final String PING_MIN_LATENCY = "ping.min.latency.ms";
    public static final String PING_MAX_LATENCY = "ping.max.latency.ms";
    public static final String PING_SENT_PACKETS = "ping.sent.packets";
    public static final String PING_RECEIVED_PACKETS = "ping.received.packets";
    public static final String PING_LOST_PACKETS = "ping.lost.packets";
    public static final String PACKET_LOST_PERCENT = "ping.packet.lost.percent";
    public static final String PING_LATENCY = "ping.latency.ms";
    public static final String URL_ENDPOINT = "url.endpoint";
    public static final String METRIC_INSTANCES = "metric.instances";
    public static final String CLOUD_SERVICE_DOWN_INSTANCE_DISCOVERY = "cloud.service.down.instance.discovery";
    public static final String PING_CHECK_STATUS = "ping.check.status";
    public static final String PORT_CHECK_STATUS = "port.check.status";
    public static final String PORT_TYPE = "port.type";
    public static final String UDP = "udp";
    public static final String PING_CHECK_RETRIES = "ping.check.retries";
    public static final String STARTED_TIME_SECONDS = "started.time.seconds";
    public static final String STARTED_TIME = "started.time";
    // Network and IP-related constants
    public static final short FIRST_BYTE_OF_IP_ADDRESS_INDEX = 0;
    public static final short SECOND_BYTE_OF_IP_ADDRESS_INDEX = 1;
    public static final short THIRD_BYTE_OF_IP_ADDRESS_INDEX = 2;
    public static final short FOURTH_BYTE_OF_IP_ADDRESS_INDEX = 3;
    public static final String IP_OCTET_BYTE_REGEX_PATTERN = "\\.";
    public static final String NETWORK_SERVICE = "network.service";
    public static final String NETWORK_SERVICE_LATENCY = "network.service.latency.ms";
    public static final String SERVICE_CHECK_STATUS = "service.check.status";
    // Cloud-related constants
    public static final String CLOUD_SERVICE = "cloud.service";
    public static final String CLOUD_LATENCY = "cloud.latency.ms"; // Main cloud instance latency
    public static final String CLOUD_SERVICE_LATENCY = "cloud.service.latency.ms"; // Cloud services latency
    public static final String CLOUD_SECRET_KEY = "cloud.secret.key";
    public static final String CLOUD_CLIENT_ID = "cloud.client.id";
    public static final String CLOUD_ACCESS_ID = "cloud.access.id";
    public static final String CLOUD_TENANT_ID = "cloud.tenant.id";
    public static final String SNMP_TRAP_VERSION = "trap.version";
    public static final String SERVICE_CHECK_LATENCY = "service.check.latency.ms";

    // State-related constants for discovery/rediscovery scheduler/topology
    public static final String STATE = "state";
    public static final String STATE_RUNNING = "Running";
    public static final String STATE_NOT_RUNNING = "Not Running";
    public static final String STATE_NOT_REACHABLE = "Not Reachable";

    // SNMP-related constants
    public static final String SNMP_OID_GROUP_ID = "oid.group.id";
    public static final String SNMP_OID_GROUP = "oid.group";
    public static final String SNMP_OID_GROUP_OIDS = "oid.group.oids";
    public static final String SNMP_OID_GROUP_PARENT_OID = "oid.group.parent.oid";
    public static final String SNMP_OID_GROUP_NAME = "oid.group.name";
    public static final String SNMP_OID_GROUP_DEVICE_TYPE = "oid.group.device.type";
    public static final String SNMP_OID_GROUP_TYPE = "oid.group.type";
    public static final String SNMP_OID_GROUP_INVALID_OIDS = "invalid.oids";
    public static final String SNMP_OID_GROUP_VALID_OIDS = "valid.oids";
    public static final String SNMP_OID_GROUP_POLLING_TIME = "oid.group.polling.interval.sec";
    public static final String SNMP_OID_GROUP_STATE = "oid.group.state";
    public static final String SNMP_OID_GROUP_TIMEOUT = "oid.group.polling.timeout.sec";
    public static final String SNMP_CHECK_TIMEOUT_SECONDS = "snmp.check.timeout.seconds";
    public static final String APPLICATION = "application";
    public static final String SYSTEM_NETWORK_INTERFACE = "system.network.interface";
    public static final String SYSTEM_NETWORK_INTERFACE_NAME = "system.network.interface.name";
    public static final String SYSTEM_NETWORK_INTERFACE_MAC_ADDRESS = "system.network.interface.mac.address";

    // metric enricher related const
    public static final String SYSTEM_NETWORK_INTERFACE_IP_ADDRESS = "system.network.interface.ip.address";
    public static final String INTERFACE_DISCOVERY = "interface.discovery";

    //polling error notification related const
    public static final String OBJECT_MONITOR_POLLING_FAILED_NOTIFICATION_STATUS = "object.monitor.polling.failed.notification.status";
    public static final String OBJECT_SMS_NOTIFICATION_RECIPIENTS = "object.sms.notification.recipients";
    public static final String OBJECT_EMAIL_NOTIFICATION_RECIPIENTS = "object.email.notification.recipients";
    public static final String OBJECT_MONITOR_POLLING_FAILED_RENOTIFICATION_TIMER_SECONDS = "object.monitor.polling.failed.renotification.timer.seconds";

    //wan link rediscovery related const
    public static final String SOURCE_ROUTER_LOCATION = "source.router.location";
    public static final String DESTINATION_ROUTER_LOCATION = "destination.router.location";
    public static final String DESTINATION_IP = "destination.ip.address";
    public static final String SOURCE_IP = "source.ip.address";
    public static final String SOURCE_INTERFACE_NAME = "source.interface.name";
    public static final String INTERNET_SERVICE_PROVIDER = "internet.service.provider";
    public static final Set<String> IPSLA_METRIC_PLUGINS = Set.of(MetricPlugin.IPSLA_ICMP_ECHO.getName(), MetricPlugin.IPSLA_ICMP_JITTER.getName(), MetricPlugin.IPSLA_PATH_ECHO.getName());

    //runbook mac scanner const
    public static final String INTERFACES = "interfaces";
    public static final String INTERFACE = "interface";
    public static final String INTERFACE_INDEX = "interface.index";
    public static final String INTERFACE_IP = "interface.ip.address";

    public static final String INTERFACE_LOCAL_IP = "interface.local.ip.address";
    // network interface related const
    public static final String INTERFACE_ADDRESS = "interface.address";
    public static final String INTERFACE_MANAGEMENT_PORT = "interface.management.port";
    public static final String INTERFACE_NAME = "interface.name";
    public static final String INTERFACE_ALIAS = "interface.alias";
    public static final String INTERFACE_DESCRIPTION = "interface.description";
    public static final String SYSTEM_PROCESS = "system.process";
    public static final String SYSTEM_PROCESS_AVAILABILITY_STATISTICS = "system.process.availability.statistics";
    public static final String SYSTEM_SERVICE = "system.service";
    public static final String SYSTEM_SERVICE_AVAILABILITY_STATISTICS = "system.service.availability.statistics";
    public static final String ESXI_VM = "esxi.vm";
    public static final String HYPERV_VM = "hyperv.vm";
    public static final String NUTANIX_VM = "nutanix.vm";
    public static final String ARUBA_WIRELESS_ACCESS_POINT = "aruba.wireless.access.point";
    public static final String CISCO_WIRELESS_ACCESS_POINT = "cisco.wireless.access.point";
    public static final String RUCKUS_WIRELESS_ACCESS_POINT = "ruckus.wireless.access.point";
    public static final String CITRIX_XEN_VM = "citrix.xen.vm";
    public static final String TOTAL_INTERFACES = "total.interfaces";
    public static final String TOTAL_VMS = "total.vms";
    public static final String IPSLA = "ipsla";
    public static final Map<String, JsonArray> METRIC_PLUGINS_BY_AGENT_TYPE = Map.of(Type.WINDOWS.getName(), new JsonArray(new ArrayList(2)).add(MetricPlugin.WINDOWS_PROCESS.getName()).add(MetricPlugin.WINDOWS_SERVICE.getName()), Type.LINUX.getName(), new JsonArray(new ArrayList(1)).add(MetricPlugin.LINUX_PROCESS.getName()), Type.SOLARIS.getName(), new JsonArray(new ArrayList(1)).add(MetricPlugin.SOLARIS_PROCESS.getName()), Type.IBM_AIX.getName(), new JsonArray(new ArrayList(1)).add(MetricPlugin.IBM_AIX_PROCESS.getName()), Type.HP_UX.getName(), new JsonArray(new ArrayList(1)).add(MetricPlugin.HP_UX_PROCESS.getName()));
    public static final String METADATA_FIELDS = "metadata.fields"; // for cloud tags
    public static final String SYSTEM_TAGS = "system.tags";
    public static final Map<String, String> UPSTREAM_PLUGIN_MAPPERS = Map.ofEntries(
            Map.entry(MetricPlugin.DOCKER_CONTAINER_DISK.getName(), MetricPlugin.DOCKER_CONTAINER.getName()),
            Map.entry(MetricPlugin.DOCKER_CONTAINER_PROCESS.getName(), MetricPlugin.DOCKER_CONTAINER.getName()));
    public static final Set<String> REDISCOVER_JOB_METRIC_PLUGINS = Set.of(MetricPlugin.SNMP_INTERFACE.getName(), MetricPlugin.WINDOWS_PROCESS.getName(), MetricPlugin.LINUX_PROCESS.getName(),
            MetricPlugin.WINDOWS_SERVICE.getName(), MetricPlugin.NETWORK_SERVICE.getName(), MetricPlugin.HYPER_V_VM.getName(),
            MetricPlugin.CITRIX_XEN_VM.getName(), MetricPlugin.VMWARE_ESXI_VM.getName(), MetricPlugin.HP_UX_PROCESS.getName(),
            MetricPlugin.IBM_AIX_PROCESS.getName(), MetricPlugin.SOLARIS_PROCESS.getName(), MetricPlugin.LINUX_FILE.getName(),
            MetricPlugin.WINDOWS_FILE.getName(), MetricPlugin.LINUX_DIR.getName(), MetricPlugin.WINDOWS_DIR.getName(),
            MetricPlugin.IPSLA_ICMP_ECHO.getName(), MetricPlugin.IPSLA_ICMP_JITTER.getName(), MetricPlugin.IPSLA_PATH_ECHO.getName(),
            MetricPlugin.DOCKER_CONTAINER.getName(), MetricPlugin.DOCKER_CONTAINER_PROCESS.getName(), MetricPlugin.DOCKER_CONTAINER_DISK.getName());
    public static final Set<String> CUSTOM_METRIC_TYPES = Set.of(Type.CUSTOM.getName(), Type.SSH.getName(), Type.POWERSHELL.getName(),
            Type.DATABASE.getName(), Type.HTTP.getName());
    public static final Set<String> PYTHON_METRIC_TYPES = Set.of(Type.WINDOWS.getName(), Type.WINDOWS_CLUSTER.getName(), Type.HYPER_V.getName(),
            Type.HYPER_V_CLUSTER.getName());
    public static final Set<String> NETWORK_DEVICES = Set.of(Type.SNMP_DEVICE.getName(), Type.SWITCH.getName(), Type.ROUTER.getName(), Type.FIREWALL.getName(), Type.PRINTER.getName(), Type.UPS.getName(), Type.LOAD_BALANCER.getName(), Type.WIRELESS_CONTROLLER.getName(), Type.HARDWARE_SENSOR.getName(), Type.EMAIL_GATEWAY.getName(), Type.LINUX_SNMP.getName(), Type.WINDOWS_SNMP.getName());
    public static final Set<String> CLOUD_TYPES = Set.of(Type.AWS_CLOUD.getName(), Type.AZURE_CLOUD.getName(), Type.OFFICE_365.getName());
    // oracle weblogic, apache mq and glassfish server plugin not available
    public static final Set<String> APPLICATION_TYPES = Set.of(Type.POSTGRESQL.getName(), Type.ORACLE_DATABASE.getName(), Type.MYSQL.getName(), Type.MARIADB.getName(), Type.IBM_DB2.getName(), Type.SQL_SERVER.getName(), Type.SYBASE.getName(),
            Type.SAP_HANA.getName(), Type.SAP_MAXDB.getName(), Type.ZIMBRA.getName(), Type.WINDOWS_RDP.getName(), Type.APACHE_HTTP.getName(), Type.LIGHTTPD.getName(), Type.MICROSOFT_IIS.getName(), Type.HA_PROXY.getName(), Type.NGINX.getName(),
            Type.EXCHANGE_CLIENT_ACCESS_ROLE.getName(), Type.EXCHANGE_EDGE_TRANSPORT_ROLE.getName(), Type.EXCHANGE_MAILBOX_ROLE.getName(), Type.IBM_WEBSPHERE.getName(),
            Type.ACTIVE_DIRECTORY.getName(), Type.BIND9.getName(), Type.WINDOWS_DNS.getName(), Type.LINUX_DHCP.getName(), Type.WINDOWS_DHCP.getName(),
            Type.IBM_MQ.getName(), Type.MSMQ.getName(), Type.RABBITMQ.getName(), Type.APACHE_TOMCAT.getName(), Type.WILDFLY.getName(),Type.MONGODB.getName(),Type.ORACLE_WEBLOGIC.getName());
    public static final Set<String> APPLICATION_PLUGINS = Set.of(MetricPlugin.POSTGRESQL.getName(),MetricPlugin.MONGODB.getName(), MetricPlugin.ORACLE_DATABASE.getName(), MetricPlugin.MARIADB.getName(), MetricPlugin.MYSQL.getName(), MetricPlugin.IBM_DB2.getName(),
            MetricPlugin.SQL_SERVER.getName(), MetricPlugin.SYBASE.getName(), MetricPlugin.SAP_HANA.getName(), MetricPlugin.SAP_MAXDB.getName(), MetricPlugin.ZIMBRA.getName(), MetricPlugin.WINDOWS_RDP.getName(),
            MetricPlugin.APACHE_HTTP.getName(), MetricPlugin.LIGHTTPD.getName(), MetricPlugin.NGINX.getName(), MetricPlugin.MICROSOFT_IIS.getName(), MetricPlugin.HA_PROXY.getName(),
            MetricPlugin.EXCHANGE_CLIENT_ACCESS_ROLE.getName(), MetricPlugin.EXCHANGE_EDGE_TRANSPORT_ROLE.getName(), MetricPlugin.EXCHANGE_MAILBOX.getName(), MetricPlugin.EXCHANGE_MAILBOX_ROLE.getName(), MetricPlugin.IBM_WEBSPHERE.getName(),
            MetricPlugin.ACTIVE_DIRECTORY.getName(), MetricPlugin.BIND9.getName(), MetricPlugin.WINDOWS_DNS.getName(), MetricPlugin.LINUX_DHCP.getName(), MetricPlugin.WINDOWS_DHCP.getName(),
            MetricPlugin.IBM_MQ.getName(), MetricPlugin.MSMQ.getName(), MetricPlugin.RABBITMQ.getName(),
            MetricPlugin.APACHE_TOMCAT.getName(), MetricPlugin.WILDFLY.getName(),MetricPlugin.ORACLE_WEBLOGIC.getName());

    public static final Set<String> CONTAINER_PLUGINS = Set.of(MetricPlugin.DOCKER_CONTAINER.getName());
    public static final Map<String, JsonObject> PLUGINS_BY_OBJECT_TYPE = Map.ofEntries(
            Map.entry(NMSConstants.Type.LINUX.getName(), new JsonObject().put("system.process", NMSConstants.MetricPlugin.LINUX_PROCESS.getName())),
            Map.entry(NMSConstants.Type.WINDOWS.getName(), new JsonObject().put("system.process", NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName()).put("system.service", NMSConstants.MetricPlugin.WINDOWS_SERVICE.getName()))
    );
    public static final Map<String, String> DISCOVERABLE_INSTANCE_PLUGINS = Map.ofEntries(Map.entry(MetricPlugin.NETWORK_SERVICE.getName(), NETWORK_SERVICES), Map.entry(MetricPlugin.WINDOWS_PROCESS.getName(), PROCESSES),
            Map.entry(MetricPlugin.LINUX_PROCESS.getName(), PROCESSES), Map.entry(MetricPlugin.SOLARIS_PROCESS.getName(), PROCESSES), Map.entry(MetricPlugin.IBM_AIX_PROCESS.getName(), PROCESSES),
            Map.entry(MetricPlugin.HP_UX_PROCESS.getName(), PROCESSES), Map.entry(MetricPlugin.WINDOWS_SERVICE.getName(), SERVICES), Map.entry(MetricPlugin.VMWARE_ESXI_VM.getName(), VMS), Map.entry(MetricPlugin.HYPER_V_VM.getName(), VMS), Map.entry(MetricPlugin.NUTANIX_VM.getName(), VMS),
            Map.entry(MetricPlugin.CITRIX_XEN_VM.getName(), VMS), Map.entry(MetricPlugin.SNMP_INTERFACE.getName(), INTERFACES), Map.entry(MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName(), ACCESS_POINTS), Map.entry(MetricPlugin.RUCKUS_WIRELESS_ACCESS_POINT.getName(), ACCESS_POINTS), Map.entry(MetricPlugin.ARUBA_WIRELESS_ACCESS_POINT.getName(), ACCESS_POINTS),
            Map.entry(MetricPlugin.IPSLA_ICMP_ECHO.getName(), "ipsla.icmp.echo.links"), Map.entry(MetricPlugin.IPSLA_ICMP_JITTER.getName(), "ipsla.icmp.jitter.links"), Map.entry(MetricPlugin.IPSLA_PATH_ECHO.getName(), "ipsla.path.echo.links"),
            Map.entry(MetricPlugin.DOCKER_CONTAINER.getName(), CONTAINERS));

    public static final Map<String, String> DISCOVERABLE_INSTANCES = Map.ofEntries(Map.entry(MetricPlugin.NETWORK_SERVICE.getName(), NETWORK_SERVICE), Map.entry(MetricPlugin.WINDOWS_PROCESS.getName(), "system.process"),
            Map.entry(MetricPlugin.LINUX_PROCESS.getName(), "system.process"), Map.entry(MetricPlugin.SOLARIS_PROCESS.getName(), "system.process"), Map.entry(MetricPlugin.IBM_AIX_PROCESS.getName(), "system.process"),
            Map.entry(MetricPlugin.HP_UX_PROCESS.getName(), "system.process"), Map.entry(MetricPlugin.WINDOWS_SERVICE.getName(), "system.service"), Map.entry(MetricPlugin.VMWARE_ESXI_VM.getName(), "esxi.vm"),
            Map.entry(MetricPlugin.HYPER_V_VM.getName(), "hyperv.vm"), Map.entry(MetricPlugin.CITRIX_XEN_VM.getName(), "citrix.xen.vm"), Map.entry(MetricPlugin.SNMP_INTERFACE.getName(), INTERFACE), Map.entry(MetricPlugin.RUCKUS_WIRELESS_ACCESS_POINT.getName(), RUCKUS_WIRELESS_ACCESS_POINT), Map.entry(MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName(), CISCO_WIRELESS_ACCESS_POINT), Map.entry(MetricPlugin.ARUBA_WIRELESS_ACCESS_POINT.getName(), ARUBA_WIRELESS_ACCESS_POINT),
            Map.entry(MetricPlugin.IPSLA_ICMP_ECHO.getName(), "ipsla.icmp.echo"), Map.entry(MetricPlugin.IPSLA_ICMP_JITTER.getName(), "ipsla.icmp.jitter"), Map.entry(MetricPlugin.IPSLA_PATH_ECHO.getName(), "ipsla.path.echo"), Map.entry(MetricPlugin.NUTANIX_VM.getName(), NUTANIX_VM),
            Map.entry(MetricPlugin.DOCKER_CONTAINER.getName(), "docker.container"));

    public static final Set<String> CLOUD_PLUGINS = Set.of(MetricPlugin.AWS_CLOUD.getName(), MetricPlugin.AMAZON_SQS.getName(),
            MetricPlugin.AMAZON_CLOUD_FRONT.getName(), MetricPlugin.AWS_AUTO_SCALING.getName(), MetricPlugin.AWS_LAMBDA.getName(), MetricPlugin.AWS_ELASTIC_BEANSTALK.getName(), MetricPlugin.AMAZON_DOCUMENTDB.getName(),
            MetricPlugin.AMAZON_DYNAMO_DB.getName(), MetricPlugin.AMAZON_S3.getName(), MetricPlugin.AMAZON_EC2.getName(), MetricPlugin.AMAZON_SNS.getName(),
            MetricPlugin.AMAZON_RDS.getName(), MetricPlugin.AMAZON_EBS.getName(), MetricPlugin.AWS_ELB.getName(), MetricPlugin.AZURE_CLOUD.getName(), MetricPlugin.AZURE_COSMOS_DB.getName(), MetricPlugin.AZURE_VM.getName(),
            MetricPlugin.AZURE_STORAGE.getName(), MetricPlugin.AZURE_WEB_APP.getName(), MetricPlugin.AZURE_SQL_DATABASE.getName(), MetricPlugin.AZURE_SERVICE_BUS.getName(), MetricPlugin.AZURE_APPLICATION_GATEWAY.getName(),
            MetricPlugin.AZURE_FUNCTION.getName(), MetricPlugin.AZURE_LOAD_BALANCER.getName(), MetricPlugin.AZURE_VM_SCALE_SET.getName(), MetricPlugin.AZURE_CDN.getName(),
            MetricPlugin.AZURE_MYSQL.getName(), MetricPlugin.AZURE_POSTGRESQL.getName(),
            MetricPlugin.OFFICE_365.getName(), MetricPlugin.EXCHANGE_ONLINE_MAILBOX.name(),
            MetricPlugin.ONEDRIVE.getName(), MetricPlugin.SHAREPOINT_ONLINE.getName(), MetricPlugin.MICROSOFT_TEAMS.getName());

    public static final Map<String, String> INSTANCE_TYPES = Map.ofEntries(Map.entry(MetricPlugin.VMWARE_ESXI_VM.getName(), ESXI_VM), Map.entry(MetricPlugin.CITRIX_XEN_VM.getName(), CITRIX_XEN_VM), Map.entry(MetricPlugin.HYPER_V_VM.getName(), HYPERV_VM), Map.entry(MetricPlugin.NUTANIX_VM.getName(), NUTANIX_VM),
            Map.entry(MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName(), CISCO_WIRELESS_ACCESS_POINT), Map.entry(MetricPlugin.RUCKUS_WIRELESS_ACCESS_POINT.getName(), RUCKUS_WIRELESS_ACCESS_POINT), Map.entry(MetricPlugin.ARUBA_WIRELESS_ACCESS_POINT.getName(), ARUBA_WIRELESS_ACCESS_POINT),
            Map.entry(MetricPlugin.SNMP_INTERFACE.getName(), INTERFACE), Map.entry(MetricPlugin.WINDOWS_PROCESS.getName(), "system.process"),
            Map.entry(MetricPlugin.LINUX_PROCESS.getName(), "system.process"), Map.entry(MetricPlugin.SOLARIS_PROCESS.getName(), "system.process"), Map.entry(MetricPlugin.IBM_AIX_PROCESS.getName(), "system.process"),
            Map.entry(MetricPlugin.HP_UX_PROCESS.getName(), "system.process"), Map.entry(MetricPlugin.WINDOWS_SERVICE.getName(), "system.service"),
            Map.entry(MetricPlugin.IPSLA_ICMP_ECHO.getName(), IPSLA), Map.entry(MetricPlugin.IPSLA_ICMP_JITTER.getName(), IPSLA), Map.entry(MetricPlugin.IPSLA_PATH_ECHO.getName(), IPSLA),
            Map.entry(MetricPlugin.DOCKER_CONTAINER.getName(), "docker.container"));

    public static final Map<String, String> INSTANCE_IP_METRICS = Map.ofEntries(
            Map.entry(ESXI_VM, "esxi.vm.ip"),
            Map.entry(CITRIX_XEN_VM, "citrix.xen.vm.ip"),
            Map.entry(HYPERV_VM, "hyperv.vm.ip"),
            Map.entry(NUTANIX_VM, "nutanix.vm.ip"),
            Map.entry(CISCO_WIRELESS_ACCESS_POINT, "cisco.wireless.access.point.ip.address"),
            Map.entry(RUCKUS_WIRELESS_ACCESS_POINT, "ruckus.wireless.access.point.ip.address"),
            Map.entry(ARUBA_WIRELESS_ACCESS_POINT, "aruba.wireless.access.point.ip.address"),
            Map.entry(INTERFACE, "interface.ip.address"));

    //will be needing instance plugins during rule tag, and SLO profile
    public static final Map<String, JsonArray> PLUGIN_BY_INSTANCES = Map.ofEntries(
            Map.entry(InstanceType.INTERFACE.getName(), new JsonArray().add(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())),
            Map.entry(InstanceType.VM.getName(), new JsonArray().add(NMSConstants.MetricPlugin.VMWARE_ESXI_VM.getName())
                    .add(NMSConstants.MetricPlugin.CITRIX_XEN_VM.getName())
                    .add(NMSConstants.MetricPlugin.HYPER_V_VM.getName())),
            Map.entry(InstanceType.ACCESS_POINT.getName(), new JsonArray().add(NMSConstants.MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName())
                    .add(NMSConstants.MetricPlugin.ARUBA_WIRELESS_ACCESS_POINT.getName())
                    .add(NMSConstants.MetricPlugin.RUCKUS_WIRELESS_ACCESS_POINT.getName())),
            Map.entry(InstanceType.PROCESS.getName(), new JsonArray().add(NMSConstants.MetricPlugin.LINUX_PROCESS.getName())
                    .add(NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName())
                    .add(NMSConstants.MetricPlugin.SOLARIS_PROCESS.getName())
                    .add(NMSConstants.MetricPlugin.IBM_AIX_PROCESS.getName())
                    .add(NMSConstants.MetricPlugin.HP_UX_PROCESS.getName())),
            Map.entry(InstanceType.CONTAINER.getName(), new JsonArray().add(NMSConstants.MetricPlugin.DOCKER_CONTAINER.getName())),
            Map.entry(InstanceType.LINK.getName(), new JsonArray().add(NMSConstants.MetricPlugin.IPSLA_ICMP_ECHO.getName())
                    .add(NMSConstants.MetricPlugin.IPSLA_ICMP_JITTER.getName())
                    .add(NMSConstants.MetricPlugin.IPSLA_PATH_ECHO.getName())),
            Map.entry(InstanceType.SERVICE.getName(), new JsonArray().add(NMSConstants.MetricPlugin.WINDOWS_SERVICE.getName())));



    public static final Set<String> VIRTUALIZATION_CLUSTER_TYPES = Set.of(Type.VCENTER.getName(), Type.HYPER_V_CLUSTER.getName(), Type.CITRIX_XEN_CLUSTER.getName());
    public static final Set<String> VIRTUALIZATION_TYPES = Set.of(Type.VCENTER.getName(), Type.VMWARE_ESXI.getName(), Type.HYPER_V_CLUSTER.getName(), Type.HYPER_V.getName(), Type.CITRIX_XEN_CLUSTER.getName(), Type.CITRIX_XEN.getName());

    public static final Set<String> HCI_CLUSTER_TYPES = Set.of(Type.PRISM.getName());
    public static final Set<String> HCI_TYPES = Set.of(Type.PRISM.getName(), Type.NUTANIX.getName());

    public static final Set<String> DUMMY_INSTANCE_PLUGINS = Set.of(MetricPlugin.RUCKUS_WIRELESS.getName());
    public static final String TOPOLOGY_LINK_LAYER = "topology.link.layer";
    public static final String TOPOLOGY_PROTOCOLS = "topology.protocols";
    public static final String TOPOLOGY_NEXT_HOPS = "topology.next.hops";
    public static final String GENERIC_VENDOR = "Generic";
    public static final String CISCO_VENDOR = "Cisco Systems";
    public static final Map<String, Byte> LEVELS_BY_NETWORK_PROTOCOLS = Map.ofEntries(Map.entry(TopologyProtocol.CDP.getName(), AIOpsConstants.DependencyLevel.SIX.getName()),
            Map.entry(TopologyProtocol.LLDP.getName(), AIOpsConstants.DependencyLevel.SIX.getName()),
            Map.entry(TopologyProtocol.SPM.getName(), AIOpsConstants.DependencyLevel.SIX.getName()),
            Map.entry(TopologyProtocol.IS_IS.getName(), AIOpsConstants.DependencyLevel.FIVE.getName()),
            Map.entry(TopologyProtocol.OSPF.getName(), AIOpsConstants.DependencyLevel.FIVE.getName()),
            Map.entry(TopologyProtocol.BGP.getName(), AIOpsConstants.DependencyLevel.FIVE.getName()));
    public static final String METRIC_DATABASE = "metric.database";
    public static final String OUTLOOK_OFFICE = "outlook.office365.com";
    public static final String WIRELESS_CLIENT = "wireless.client";
    public static final String AWS_SERVICE = "aws.service";
    public static final String AWS_SERVICE_REGION = "aws.region";
    public static final String AZURE_SERVICE = "azure.service";
    public static final String AZURE_SERVICE_RESOURCE_GROUP = "azure.service.resource.group";
    public static final String SYSTEM_PROCESS_DESTINATION_IP = "system.process.destination.ip";
    public static final Set<String> GARBAGE_FIELDS = Set.of(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Metric.METRIC_DISCOVERY_METHOD, AIOpsObject.OBJECT_DISCOVERY_METHOD,
            INTERFACE_DISCOVERY, Metric.METRIC_CREDENTIAL_PROFILE_PROTOCOL, CredentialProfile.CREDENTIAL_PROFILE_NAME, "snmp.version", PING_CHECK_STATUS,
            "metric.polling.min.time", AIOpsObject.OBJECT_VENDOR, "snmp.community", EventBusConstants.EVENT_LATENCY, PORT, ERRORS, AIOpsObject.OBJECT_SYSTEM_OID, CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL,
            Metric.METRIC_POLLING_TIME, AIOpsObject.OBJECT_MAKE_MODEL, ERROR, AIOpsObject.OBJECT_STATE, TIMEOUT, AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG, Metric.METRIC_CREDENTIAL_PROFILE, "snmp.check.retries",
            TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY, AIOpsObject.OBJECT_EVENT_PROCESSORS, EventBusConstants.EVENT_TOPIC, FIELD_TYPE, AIOpsObject.OBJECT_CREDENTIAL_PROFILE, AIOpsObject.OBJECT_MODIFICATION_TIME, AIOpsObject.OBJECT_CREATION_TIME,
            AIOpsObject.OBJECT_CUSTOM_FIELDS, EventBusConstants.UI_EVENT_UUID, PluginEngineConstants.PLUGIN_ENGINE, AIOpsObject.OBJECT_BUSINESS_HOUR_PROFILE, AIOpsObject.OBJECT_SCHEDULER, AIOpsObject.OBJECT_CONTEXT, Metric.METRIC_CONTEXT,
            DISCOVERED_OBJECTS, Discovery.DISCOVERY_CONTEXT, OBJECTS, "ss.bin.path");
    public static final String LAST_QUERY_TIME = "last.query.time";
    public static final String LAST_POLL_TIME = "last.poll.time";
    public static final String INSTANCE_NAME = "instance.name";
    public static final String CLOUD_METRICS = "cloud.metrics";
    public static final int AVAILABILITY_PLUGIN_ID = 0;
    public static final String PREVIOUS_FLAP_TIMESTAMP = "previous.flap.timestamp";
    public static final String PREVIOUS_FLAP_VALUE = "previous.flap.value";
    public static final String PREVIOUS_FLAP_STATUS = "previous.flap.status";
    public static final String CLI_ENABLED = "cli.enabled";
    public static final String CUMULATIVE_STATUS_FLAP_DURATIONS = "cumulative-status-flap-durations";
    public static final String HOURLY_STATUS_FLAP_DURATIONS = "hourly-status-flap-durations";
    public static final String STATUS_FLAPS = "status-flaps";
    public static final int PING_CHECK_TIMEOUT_SECONDS = MotadataConfigUtil.getPingCheckTimeoutSeconds();
    public static final int PING_CHECK_PACKETS = MotadataConfigUtil.getPingCheckPackets();
    public static final String STATUS_KEY = "status.key";

    public static final String CLIENT_CERTIFICATE_CONFIGURATION = "client.certificate.configuration";

    public static final String CLIENT_CERTIFICATE = "client.certificate";

    public static final String CLIENT_KEY = "client.key";

    public static final String CERTIFICATE_AUTHORITY = "certificate.authority";

    public static final String CLIENT_CERTIFICATE_FILE = "client.certificate.file";

    public static final String CLIENT_KEY_FILE = "client.key.file";

    public static final String CERTIFICATE_AUTHORITY_FILE = "certificate.authority.file";

    // object types with protocol and category
    public static final JsonArray OBJECT_TYPES = JsonArray.of(

            //Container Orchestration
            JsonObject.of(OBJECT_TYPE, Type.TANZU_KUBERNETES, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.CONTAINER_ORCHESTRATION),
            JsonObject.of(OBJECT_TYPE, Type.KUBERNETES, PROTOCOL, Protocol.SSH, OBJECT_CATEGORY, Category.CONTAINER_ORCHESTRATION),

            // CLOUD
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_CLOUD_FRONT, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_DOCUMENTDB, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_DYNAMO_DB, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_EBS, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_EC2, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_S3, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_SNS, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_SQS, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AMAZON_RDS, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AWS_AUTO_SCALING, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AWS_CLOUD, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AWS_ELASTIC_BEANSTALK, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AWS_ELB, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AWS_LAMBDA, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_APPLICATION_GATEWAY, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_CDN, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_CLOUD, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_COSMOS_DB, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_FUNCTION, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_LOAD_BALANCER, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_MYSQL, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_POSTGRESQL, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_SERVICE_BUS, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_SQL_DATABASE, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_STORAGE, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_VM, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_VM_SCALE_SET, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.AZURE_WEB_APP, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.EXCHANGE_ONLINE, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.MICROSOFT_TEAMS, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.OFFICE_365, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.ONEDRIVE, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),
            JsonObject.of(OBJECT_TYPE, Type.SHAREPOINT_ONLINE, PROTOCOL, Protocol.CLOUD, OBJECT_CATEGORY, Category.CLOUD),


            // DATABASE

            // HCI
            JsonObject.of(OBJECT_TYPE, Type.NUTANIX, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.HCI),
            JsonObject.of(OBJECT_TYPE, Type.PRISM, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.HCI),

            // MIDDLEWARE

            // NETWORK
            JsonObject.of(OBJECT_TYPE, Type.ARUBA_WIRELESS, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_WIRELESS, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.EMAIL_GATEWAY, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.FIREWALL, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.HARDWARE_SENSOR, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.LOAD_BALANCER, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.ROUTER, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.RUCKUS_WIRELESS, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.SNMP_DEVICE, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.SWITCH, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.UPS, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.WIRELESS_CONTROLLER, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.PRINTER, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.WINDOWS_SNMP, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),
            JsonObject.of(OBJECT_TYPE, Type.LINUX_SNMP, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.NETWORK),

            // OTHER
            JsonObject.of(OBJECT_TYPE, Type.IBM_TAPE_LIBRARY, PROTOCOL, Protocol.SNMPV1V2c, OBJECT_CATEGORY, Category.OTHER),
            JsonObject.of(OBJECT_TYPE, Type.PING, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.OTHER),

            // SDN
            JsonObject.of(OBJECT_TYPE, Type.CISCO_ACI, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_MERAKI, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_MERAKI_RADIO, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_MERAKI_SECURITY, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_MERAKI_SWITCH, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_VBOND, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_VEDGE, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_VMANAGE, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_VSMART, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),
            JsonObject.of(OBJECT_TYPE, Type.NSXT, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SDN),

            // SERVER
            JsonObject.of(OBJECT_TYPE, Type.ACTIVE_DIRECTORY, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.APACHE_HTTP, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.APACHE_MQ, PROTOCOL, Protocol.JMX, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.APACHE_TOMCAT, PROTOCOL, Protocol.JMX, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.BIND9, PROTOCOL, Protocol.SSH, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.CISCO_UCS, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.DOTNET, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.EXCHANGE_CLIENT_ACCESS_ROLE, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.EXCHANGE_EDGE_TRANSPORT_ROLE, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.EXCHANGE_MAILBOX, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.EXCHANGE_MAILBOX_ROLE, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.HA_PROXY, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.HP_UX, PROTOCOL, Protocol.SSH, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.IBM_AIX, PROTOCOL, Protocol.SSH, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.IBM_AS_400, PROTOCOL, Protocol.SSH, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.IBM_DB2, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.IBM_MQ, PROTOCOL, Protocol.JMS, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.IBM_WEBSPHERE, PROTOCOL, Protocol.JMX, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.LINUX, PROTOCOL, Protocol.SSH, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.LINUX_DHCP, PROTOCOL, Protocol.SSH, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.MARIADB, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.MICROSOFT_DYNAMICS_CRM, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.MICROSOFT_IIS, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.MONGODB, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.MSMQ, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.MYSQL, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.ORACLE_DATABASE, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.ORACLE_WEBLOGIC, PROTOCOL, Protocol.JMX, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.POSTGRESQL, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.RABBITMQ, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.SAP_HANA, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.SAP_MAXDB, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.SOLARIS, PROTOCOL, Protocol.SSH, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.SQL_SERVER, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.SYBASE, PROTOCOL, Protocol.JDBC, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.WINDOWS, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.WINDOWS_CLUSTER, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.WINDOWS_DHCP, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.WINDOWS_DNS, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.WILDFLY, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.WINDOWS_RDP, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.SERVER),
            JsonObject.of(OBJECT_TYPE, Type.ZIMBRA, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SERVER),

            // SERVICE_CHECK
            JsonObject.of(OBJECT_TYPE, Type.CERTIFICATE, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.DNS, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.DOMAIN, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.EMAIL, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.FTP, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.NTP, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.PING, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.PORT, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.RADIUS, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),
            JsonObject.of(OBJECT_TYPE, Type.URL, PROTOCOL, Protocol.CUSTOM, OBJECT_CATEGORY, Category.SERVICE_CHECK),

            // STORAGE
            JsonObject.of(OBJECT_TYPE, Type.HPE_STORE_ONCE, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.STORAGE),
            JsonObject.of(OBJECT_TYPE, Type.NETAPP_ONTAP_CLUSTER, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.STORAGE),
            JsonObject.of(OBJECT_TYPE, Type.HPE_PRIMERA, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.STORAGE),
            JsonObject.of(OBJECT_TYPE, Type.HPE_3PAR, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.STORAGE),
            JsonObject.of(OBJECT_TYPE, Type.DELL_EMC_UNITY, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.STORAGE),

            // VIRTUALIZATION
            JsonObject.of(OBJECT_TYPE, Type.CITRIX_XEN, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.VIRTUALIZATION),
            JsonObject.of(OBJECT_TYPE, Type.CITRIX_XEN_CLUSTER, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.VIRTUALIZATION),
            JsonObject.of(OBJECT_TYPE, Type.HYPER_V, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.VIRTUALIZATION),
            JsonObject.of(OBJECT_TYPE, Type.HYPER_V_CLUSTER, PROTOCOL, Protocol.POWERSHELL, OBJECT_CATEGORY, Category.VIRTUALIZATION),
            JsonObject.of(OBJECT_TYPE, Type.VCENTER, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.VIRTUALIZATION),
            JsonObject.of(OBJECT_TYPE, Type.VMWARE_ESXI, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.VIRTUALIZATION),

            // WEB_SERVER
            JsonObject.of(OBJECT_TYPE, Type.LIGHTTPD, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.WEB_SERVER),
            JsonObject.of(OBJECT_TYPE, Type.NGINX, PROTOCOL, Protocol.HTTP_HTTPS, OBJECT_CATEGORY, Category.WEB_SERVER)
    );
    static final String METRIC_ENRICHER_CALCULATOR_SENT_BYTES = "sent.bytes";
    static final String METRIC_ENRICHER_CALCULATOR_RECEIVE_BYTES = "received.bytes";
    static final String INTERFACE_BIT_TYPE = "interface~bit.type";
    static final String INTERFACE_LINK_TYPE = "interface.link.type";
    static final String INTERFACE_LINK_TYPE_LAN = "LAN";
    static final String INTERFACE_LINK_TYPE_WAN = "WAN";
    static final long MAX_INTERFACE_SPEED_32_BIT = 4294967295L;
    static final String INTERFACE_IN_TRAFFIC_BYTES_PER_SEC = "interface~in.traffic.bytes.per.sec";
    static final String INTERFACE_OUT_TRAFFIC_BYTES_PER_SEC = "interface~out.traffic.bytes.per.sec";
    static final String INTERFACE_OUT_TRAFFIC_UTILIZATION = "interface~out.traffic.utilization.percent";
    static final String INTERFACE_IN_TRAFFIC_UTILIZATION = "interface~in.traffic.utilization.percent";
    static final String INTERFACE_TRAFFIC_UTILIZATION = "interface~traffic.utilization.percent";
    static final String INTERFACE_TRAFFIC_BYTES_PER_SEC = "interface~traffic.bytes.per.sec";
    static final String INTERFACE_SPEED_BYTES_PER_SEC = "interface~speed.bytes.per.sec";
    static final String INTERFACE_SENT_OCTETS = "interface~sent.octets";
    static final String INTERFACE_RECEIVED_OCTETS = "interface~received.octets";
    static final int INTERFACE_COUNTER_TYPE_32_BIT = 0;
    static final String METRIC_RATE = ".rate";
    static final String METRIC_SKIP_RATE = ".rate.";
    private static final Pattern INTERFACE_NAME_PATTERN = Pattern.compile("\\d");
    private static final Logger LOGGER = new Logger(NMSConstants.class, MOTADATA_NMS, "NMS Constants");
    private static final Map<Category, Integer> POLLER_WORKERS_BY_CATEGORY_PERCENT = Map.ofEntries(Map.entry(Category.NETWORK, 25),
            Map.entry(Category.SERVER, 17),
            Map.entry(Category.VIRTUALIZATION, 10),
            Map.entry(Category.CLOUD, 10),
            Map.entry(Category.DATABASE, 10),
            Map.entry(Category.MIDDLEWARE, 10),
            Map.entry(Category.WEB_SERVER, 10),
            Map.entry(Category.SERVICE_CHECK, 10),
            Map.entry(Category.OTHER, 10),
            Map.entry(Category.HCI, 10),
            Map.entry(Category.SDN, 10),
            Map.entry(Category.STORAGE, 10),
            Map.entry(Category.CONTAINER_ORCHESTRATION, 10),
            Map.entry(Category.CUSTOM, 10));
    private static final Map<RediscoverJob, Integer> REDISCOVERY_WORKERS_BY_JOB = Map.ofEntries(Map.entry(RediscoverJob.NETWORK_SERVICE, 1),
            Map.entry(RediscoverJob.NETWORK_INTERFACE, 1),
            Map.entry(RediscoverJob.WINDOWS_SERVICE, 1),
            Map.entry(RediscoverJob.PROCESS, 1),
            Map.entry(RediscoverJob.APP, 1),
            Map.entry(RediscoverJob.CLOUD, 1),
            Map.entry(RediscoverJob.VIRTUAL_MACHINE, 1),
            Map.entry(RediscoverJob.FILE_DIRECTORY, 1),
            Map.entry(RediscoverJob.ACCESS_POINT, 1),
            Map.entry(RediscoverJob.VIRTUAL_MACHINE_HCI, 1),
            Map.entry(RediscoverJob.WAN_LINK, 1),
            Map.entry(RediscoverJob.CONTAINER, 1));
    private static final Map<TopologyPluginType, Integer> TOPOLOGY_WORKERS_BY_PLUGIN_TYPE = Map.ofEntries(Map.entry(TopologyPluginType.SNMP, 1),
            Map.entry(TopologyPluginType.CUSTOM, 1));

    /**
     * Private constructor to prevent instantiation of this utility class.
     * This class contains only static methods and constants and should not be instantiated.
     */
    private NMSConstants()
    {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Determines the Category of a given Type.
     * <p>
     * This method maps device/service types to their corresponding categories.
     * For example, WINDOWS and LINUX types are mapped to the SERVER category,
     * while MYSQL and POSTGRESQL are mapped to the DATABASE category.
     *
     * @param type The Type to get the category for
     * @return The Category corresponding to the given Type, or null if no mapping exists
     */
    public static Category getCategory(Type type)
    {
        return switch (type)
        {
            case WINDOWS, LINUX, IBM_AIX, HP_UX, WINDOWS_CLUSTER, SOLARIS, IBM_AS_400 -> Category.SERVER;

            case NGINX, APACHE_HTTP, LIGHTTPD, MICROSOFT_IIS, HA_PROXY -> Category.WEB_SERVER;

            case ACTIVE_DIRECTORY, BIND9, WINDOWS_DNS, LINUX_DHCP, WINDOWS_DHCP, IBM_WEBSPHERE, IBM_MQ, MSMQ, RABBITMQ
            , APACHE_TOMCAT, WILDFLY, ORACLE_WEBLOGIC -> Category.MIDDLEWARE;

            case AWS_CLOUD, AMAZON_S3, AMAZON_EBS, AMAZON_EC2, AWS_ELB, AMAZON_RDS, AMAZON_SNS, AMAZON_DYNAMO_DB,
                 AMAZON_CLOUD_FRONT,
                 AWS_AUTO_SCALING, AWS_LAMBDA, AMAZON_SQS, AMAZON_DOCUMENTDB, AWS_ELASTIC_BEANSTALK, AZURE_VM,
                 AZURE_WEB_APP, AZURE_STORAGE,
                 AZURE_COSMOS_DB, AZURE_SQL_DATABASE, AZURE_CLOUD, AZURE_CDN, AZURE_APPLICATION_GATEWAY, AZURE_FUNCTION,
                 AZURE_LOAD_BALANCER,
                 AZURE_MYSQL, AZURE_POSTGRESQL, AZURE_SERVICE_BUS, AZURE_VM_SCALE_SET, OFFICE_365, EXCHANGE_ONLINE,
                 SHAREPOINT_ONLINE, MICROSOFT_TEAMS, ONEDRIVE -> Category.CLOUD;

            case VMWARE_ESXI, CITRIX_XEN, CITRIX_XEN_CLUSTER, HYPER_V, HYPER_V_CLUSTER, VCENTER ->
                    Category.VIRTUALIZATION;

            case MARIADB, MYSQL, IBM_DB2, SQL_SERVER, POSTGRESQL, SYBASE, SAP_HANA, SAP_MAXDB, ORACLE_DATABASE ,MONGODB ->
                    Category.DATABASE;

            case PING, PORT, DOMAIN, EMAIL, FTP, CERTIFICATE, URL, RADIUS, NTP, DNS -> Category.SERVICE_CHECK;

            case CISCO_WIRELESS, RUCKUS_WIRELESS, ARUBA_WIRELESS, FIREWALL, SWITCH, ROUTER, LOAD_BALANCER, PRINTER,
                 UPS, WIRELESS_CONTROLLER, SNMP_DEVICE, HARDWARE_SENSOR, NETWORK_INTERFACE, EMAIL_GATEWAY, LINUX_SNMP,
                 WINDOWS_SNMP -> Category.NETWORK;

            case CUSTOM, POWERSHELL, SSH, DATABASE, HTTP -> Category.CUSTOM;

            case PRISM, NUTANIX -> Category.HCI;

            case CISCO_VMANAGE, CISCO_VSMART, CISCO_VBOND, CISCO_VEDGE, CISCO_MERAKI, CISCO_MERAKI_SWITCH,
                 CISCO_MERAKI_SECURITY, CISCO_MERAKI_RADIO, CISCO_ACI, NSXT -> Category.SDN;

            case NETAPP_ONTAP_CLUSTER, HPE_STORE_ONCE, HPE_PRIMERA, HPE_3PAR, DELL_EMC_UNITY -> Category.STORAGE;

            case TANZU_KUBERNETES ,KUBERNETES -> Category.CONTAINER_ORCHESTRATION;

            case CISCO_UCS, EXCHANGE_CLIENT_ACCESS_ROLE, EXCHANGE_EDGE_TRANSPORT_ROLE, EXCHANGE_MAILBOX,
                 EXCHANGE_MAILBOX_ROLE, ZIMBRA, WINDOWS_RDP, IBM_TAPE_LIBRARY -> Category.OTHER;
            default -> null;
        };
    }

    /**
     * Determines the parent cloud type for a given cloud service type.
     * <p>
     * This method maps specific cloud service types to their parent cloud platform.
     * For example, AMAZON_S3 and AMAZON_EC2 are mapped to AWS_CLOUD,
     * while AZURE_VM and AZURE_WEB_APP are mapped to AZURE_CLOUD.
     *
     * @param type The cloud service Type to get the parent cloud type for
     * @return The parent cloud Type, or null if the input is not a cloud service
     */
    public static Type getCloudType(Type type)
    {
        return switch (type)
        {
            case AMAZON_S3, AMAZON_EBS, AMAZON_EC2, AWS_ELB, AMAZON_RDS, AMAZON_SNS, AMAZON_DYNAMO_DB, AWS_CLOUD,
                 AMAZON_CLOUD_FRONT, AWS_AUTO_SCALING,
                 AWS_LAMBDA, AMAZON_SQS, AMAZON_DOCUMENTDB, AWS_ELASTIC_BEANSTALK -> Type.AWS_CLOUD;

            case AZURE_VM, AZURE_WEB_APP, AZURE_STORAGE, AZURE_COSMOS_DB, AZURE_SQL_DATABASE, AZURE_CLOUD, AZURE_CDN,
                 AZURE_APPLICATION_GATEWAY, AZURE_FUNCTION,
                 AZURE_LOAD_BALANCER, AZURE_SERVICE_BUS, AZURE_VM_SCALE_SET, AZURE_MYSQL, AZURE_POSTGRESQL ->
                    Type.AZURE_CLOUD;

            case EXCHANGE_ONLINE, SHAREPOINT_ONLINE, MICROSOFT_TEAMS, ONEDRIVE, OFFICE_365 -> Type.OFFICE_365;

            default -> null;
        };
    }

    /**
     * Determines the communication protocol to use for a given device or service type.
     * <p>
     * This method maps device/service types to their appropriate communication protocols.
     * For example:
     * - Windows systems use POWERSHELL
     * - Linux systems use SSH
     * - Web servers typically use HTTP_HTTPS
     * - Database systems use JDBC
     *
     * @param type The Type to get the protocol for
     * @return The Protocol to use for communicating with the given type, or UNKNOWN if no mapping exists
     */
    public static Protocol getProtocol(Type type)
    {
        return switch (type)
        {
            case WINDOWS, WINDOWS_CLUSTER, WINDOWS_DHCP, WINDOWS_RDP, WINDOWS_DNS, ACTIVE_DIRECTORY, DOTNET,
                 EXCHANGE_CLIENT_ACCESS_ROLE, EXCHANGE_EDGE_TRANSPORT_ROLE,
                 EXCHANGE_MAILBOX_ROLE, MICROSOFT_IIS, MSMQ, MICROSOFT_DYNAMICS_CRM, EXCHANGE_MAILBOX ->
                    Protocol.POWERSHELL;

            case LINUX, SOLARIS, IBM_AIX, HP_UX, HA_PROXY, LINUX_DHCP, IBM_AS_400, KUBERNETES -> Protocol.SSH;

            case APACHE_HTTP, NGINX, WILDFLY, BIND9, LIGHTTPD, RABBITMQ, APACHE_TOMCAT, IBM_WEBSPHERE, ZIMBRA,
                 PRISM, CISCO_VMANAGE, CISCO_MERAKI, NETAPP_ONTAP_CLUSTER, HPE_STORE_ONCE, CISCO_ACI, HPE_PRIMERA,
                 HPE_3PAR, DELL_EMC_UNITY, TANZU_KUBERNETES, NSXT -> Protocol.HTTP_HTTPS;

            case IBM_DB2, MARIADB, SQL_SERVER, MYSQL, ORACLE_DATABASE,MONGODB, POSTGRESQL, SAP_HANA, SAP_MAXDB, SYBASE ->
                    Protocol.JDBC;

            case IBM_MQ -> Protocol.JMS;

            case ORACLE_WEBLOGIC -> Protocol.JMX;

            default -> Protocol.UNKNOWN;
        };
    }

    /**
     * Determines whether a port check is required for a given device or service type.
     * <p>
     * This method checks if the given type requires a port check as part of its monitoring.
     * Port checks are typically required for server-based systems, web servers, databases,
     * and other services that listen on specific ports.
     *
     * @param type The Type to check
     * @return true if a port check is required for the given type, false otherwise
     */
    public static boolean portCheckRequire(Type type)
    {
        // Determine if port check is required based on the type's category

        var category = getCategory(type);

        var result = switch (category)
        {
            case SERVER, WEB_SERVER, VIRTUALIZATION, DATABASE, MIDDLEWARE, HCI, SDN, STORAGE, CONTAINER_ORCHESTRATION ->
                    true;
            default -> false;
        };

        if (type == Type.RUCKUS_WIRELESS || type == Type.CISCO_UCS || type == Type.PORT)
        {
            result = true;
        }
        else if (type == Type.CISCO_MERAKI || type == Type.CISCO_MERAKI_SECURITY || type == Type.CISCO_MERAKI_SWITCH || type == Type.CISCO_MERAKI_RADIO)
        {
            result = false;
        }

        return result;
    }

    /**
     * Determines whether a ping check is required for a given device or service type.
     * <p>
     * This method checks if the given type requires a ping check as part of its monitoring.
     * Ping checks are typically required for network devices, servers, and other physical
     * or virtual systems that have an IP address.
     *
     * @param type The Type to check
     * @return true if a ping check is required for the given type, false otherwise
     */
    public static boolean pingCheckRequire(Type type)
    {
        //this method will check if ping check is required or not ...

        var result = switch (getCategory(type))
        {
            case SERVER, NETWORK, VIRTUALIZATION, HCI, SDN, STORAGE, CONTAINER_ORCHESTRATION, OTHER -> true;

            default -> false;
        };

        if (type == Type.PING)
        {
            result = true;
        }

        return result;
    }

    public static boolean isHypervisor(String type)
    {
        return !type.equalsIgnoreCase(Type.VMWARE_ESXI.getName()) && !type.equalsIgnoreCase(Type.HYPER_V.getName())
                && !type.equalsIgnoreCase(Type.CITRIX_XEN.getName()) && !type.equalsIgnoreCase(Type.NUTANIX.getName());
    }

    public static boolean isVirtualizationCluster(String type)
    {
        return type.equalsIgnoreCase(Type.VCENTER.getName()) || type.equalsIgnoreCase(Type.HYPER_V_CLUSTER.getName())
                || type.equalsIgnoreCase(Type.CITRIX_XEN_CLUSTER.getName());
    }

    public static boolean isHCICluster(String type)
    {
        return type.equalsIgnoreCase(Type.PRISM.getName());
    }

    public static boolean isVirtualMachineMetric(String metricPlugin)
    {
        return metricPlugin.equalsIgnoreCase(MetricPlugin.VMWARE_ESXI_VM.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.CITRIX_XEN_VM.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.HYPER_V_VM.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.NUTANIX_VM.getName());
    }

    public static boolean isWANLinkMetric(String metricPlugin)
    {
        return metricPlugin.equalsIgnoreCase(MetricPlugin.IPSLA_ICMP_ECHO.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.IPSLA_ICMP_JITTER.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.IPSLA_PATH_ECHO.getName());
    }

    public static boolean isAccessPointMetric(String metricPlugin)
    {
        return metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.RUCKUS_WIRELESS_ACCESS_POINT.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.ARUBA_WIRELESS_ACCESS_POINT.getName());
    }

    // topology const

    public static boolean isWireless(Type type)
    {
        return type == Type.ARUBA_WIRELESS || type == Type.CISCO_WIRELESS || type == Type.RUCKUS_WIRELESS;
    }

    public static boolean isSystemProcessMetric(String metricPlugin)
    {
        return metricPlugin.equalsIgnoreCase(MetricPlugin.WINDOWS_PROCESS.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.LINUX_PROCESS.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.HP_UX_PROCESS.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.SOLARIS_PROCESS.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.IBM_AIX_PROCESS.getName());
    }

    public static String getMetricPlugin(Type type)
    {
        // for custom metric if object type is router/switch/firewall we have to replace type to snmp device
        if (NETWORK_DEVICES.contains(type.getName()))
        {
            type = Type.SNMP_DEVICE;
        }

        return MetricPlugin.valueOf(type.toString()).getName();
    }

    public static void sendSNMPMetricProvisionEvent(JsonObject event)
    {
        // means snmp custom metric groups provision...so will make a context first and then provision..three possibilities of snmp device metric group provision
        //1. when user provision any snmp network device then at that time will provision created metric group of that catalog to particular object
        //2. when user assign device catalogs to any object then will first remove already assigned metric groups and provision new one
        //3. when user create any metric group in device catalog at that time will check if any objects assign to that catalog then will provision that metric
        try
        {
            var credential = new HashMap<>();

            event.mergeIn(event.getJsonObject(SNMP_OID_GROUP));

            if (!event.containsKey(Metric.METRIC_CREDENTIAL_PROFILE))
            {
                var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, event.getJsonArray(OBJECTS));

                var metrics = new ArrayList<JsonObject>();

                for (var index = 0; index < items.size(); index++)
                {
                    var metric = items.getJsonObject(index);

                    if (!credential.containsKey(metric.getLong(Metric.METRIC_OBJECT)) &&
                            metric.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(Category.NETWORK.getName())
                            && metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE) != NOT_AVAILABLE)
                    {
                        credential.put(metric.getLong(Metric.METRIC_OBJECT), metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));
                    }

                    // if snmp device catalog assign to any object or new metric created on assigned templates then remove previously assign snmp scalar/tabular metric groups first
                    if (event.containsKey(APIConstants.REQUEST) && event.getString(APIConstants.REQUEST).equalsIgnoreCase(APIConstants.REQUEST_CREATE) &&
                            (metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(MetricPlugin.SNMP_SCALAR_METRIC.getName()) || metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(MetricPlugin.SNMP_TABULAR_METRIC.getName()))
                            && metric.getJsonObject(Metric.METRIC_CONTEXT).containsKey(DBConstants.FIELD_TYPE) && metric.getJsonObject(Metric.METRIC_CONTEXT).getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_USER))
                    {
                        metrics.add(new JsonObject().put(ID, metric.getLong(ID)));
                    }
                }

                updateMetrics(metrics, EventBusConstants.EVENT_METRIC_UNPROVISION);
            }

            var metrics = new ArrayList<JsonObject>();

            for (var index = 0; index < event.getJsonArray(OBJECTS).size(); index++)
            {
                var object = ObjectConfigStore.getStore().getItem(event.getJsonArray(OBJECTS).getLong(index));

                if (event.containsKey(Metric.METRIC_CREDENTIAL_PROFILE)) //when user provision snmp device at that time will pass credential profile
                {
                    credential.put(event.getJsonArray(OBJECTS).getLong(index), event.getLong(Metric.METRIC_CREDENTIAL_PROFILE));
                }

                if (credential.containsKey(object.getLong(ID)))
                {
                    metrics.add(new JsonObject().put(Metric.METRIC_OBJECT, object.getLong(ID))
                            .put(Metric.METRIC_CREDENTIAL_PROFILE, credential.get(object.getLong(ID)))
                            .put(Metric.METRIC_STATE, object.getString(AIOpsObject.OBJECT_STATE))
                            .put(Metric.METRIC_NAME, event.getString(SNMP_OID_GROUP_NAME))
                            .put(Metric.METRIC_PLUGIN, event.getString(SNMP_OID_GROUP_TYPE).equalsIgnoreCase(OIDGroupType.SCALAR.getName()) ? MetricPlugin.SNMP_SCALAR_METRIC.getName() : MetricPlugin.SNMP_TABULAR_METRIC.getName())
                            .put(Metric.METRIC_CONTEXT, new JsonObject().put(SNMP_OID_GROUP_ID, event.getString(SNMP_OID_GROUP_ID))
                                    .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER)
                                    .put(APIConstants.ENTITY_ID, event.getLong(ID))));
                }
            }

            updateMetrics(metrics, EventBusConstants.EVENT_METRIC_PROVISION);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static void setMetricPluginContext(JsonObject metricPlugin, JsonObject object, List<JsonObject> metrics)
    {
        try
        {
            var context = new JsonObject().put(Metric.METRIC_NAME, metricPlugin.getString(METRIC_PLUGIN_NAME))
                    .put(Metric.METRIC_CATEGORY, Category.CUSTOM.getName())
                    .put(Metric.METRIC_PLUGIN, MetricPlugin.valueOfName(metricPlugin.getString(Metric.METRIC_PLUGIN)).getName()).put(Metric.METRIC_STATE, object.getString(AIOpsObject.OBJECT_STATE));

            if (object.getString(OBJECT_TARGET) != null && metricPlugin.getString(URL_ENDPOINT) != null)
            {
                metricPlugin.put(OBJECT_TARGET, object.getString(OBJECT_TARGET) + metricPlugin.getString(URL_ENDPOINT));
            }

            //#24767
            var metric = new JsonObject();

            if (NMSConstants.Type.PING.getName().equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_TYPE)) && NMSConstants.Category.OTHER.getName().equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_CATEGORY)))
            {
                metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object.getLong(ID), NMSConstants.MetricPlugin.AVAILABILITY.getName()));
            }
            else
            {
                metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object.getLong(ID), getMetricPlugin(Type.valueOfName(object.getString(OBJECT_TYPE)))));
            }

            if (metric != null)
            {
                context.remove(METRIC_PLUGIN_VARIABLES);

                if (object.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD).equalsIgnoreCase(DiscoveryMethod.AGENT.name()))
                {

                    metrics.add(context.put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.AGENT.name())
                            .put(Metric.METRIC_OBJECT, object.getLong(ID))
                            .put(Metric.METRIC_CONTEXT, metricPlugin)
                            .put(Metric.METRIC_CREDENTIAL_PROFILE, metricPlugin.containsKey(com.mindarray.api.MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE) ?
                                    metricPlugin.getLong(com.mindarray.api.MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE) : NOT_AVAILABLE));

                }

                else
                {
                    //list down metric with requested metrics type and metric plugin

                       /* if (metric.containsKey(Metric.METRIC_CONTEXT)) {
                            metricPlugin.mergeIn(metric.getJsonObject(Metric.METRIC_CONTEXT));

                            metricPlugin.remove(Metric.METRIC_CONTEXT);
                        }*/

                    metrics.add(context.put(Metric.METRIC_OBJECT, object.getLong(ID))
                            .put(Metric.METRIC_DISCOVERY_METHOD, DiscoveryMethod.REMOTE.name()).put(Metric.METRIC_CONTEXT, metricPlugin)
                            .put(Metric.METRIC_CREDENTIAL_PROFILE, metricPlugin.containsKey(com.mindarray.api.MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE) ?
                                    metricPlugin.getLong(com.mindarray.api.MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE) : metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE)));

                }

            }

            else
            {

                LOGGER.warn(String.format("failed to provision metric plugin %s for monitor %s", metricPlugin.getString(METRIC_PLUGIN_NAME), object.getString(AIOpsObject.OBJECT_NAME)));

            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static Future<Void> updateMetrics(List<JsonObject> metrics, String eventType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (!metrics.isEmpty())
            {
                var futures = new ArrayList<Future<Void>>();

                for (var metric : metrics)
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    if (metric.containsKey(OBJECTS) && metric.getValue(OBJECTS) != null)
                    {
                        var objects = metric.getJsonArray(OBJECTS);

                        for (var index = 0; index < objects.size(); index++)
                        {
                            var object = objects.getJsonObject(index);

                            if (object.containsKey(INSTANCE_TAGS) && !object.getJsonArray(INSTANCE_TAGS).isEmpty())
                            {
                                object.put(INSTANCE_TAGS, TagConfigStore.getStore().addItems(object.getJsonArray(INSTANCE_TAGS), Tag.TagType.INSTANCE.getName(), DBConstants.ENTITY_TYPE_USER));
                            }
                        }
                    }

                    Bootstrap.vertx().eventBus().request(eventType, metric, result -> future.complete());
                }

                Future.join(futures).onComplete(result ->
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CONFIG_CHANGE, new JsonObject());

                    promise.complete();
                });
            }
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    public static boolean isBaseTypeMetricPlugin(String metricPlugin)
    {
        return APPLICATION_PLUGINS.contains(metricPlugin) || metricPlugin.equalsIgnoreCase(MetricPlugin.AWS_CLOUD.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.AZURE_CLOUD.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.OFFICE_365.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_UCS.getName()) ||
                metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_WIRELESS.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.RUCKUS_WIRELESS.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.ARUBA_WIRELESS.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.CITRIX_XEN.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.VMWARE_ESXI.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.URL.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.HP_UX.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.HYPER_V.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.IBM_AIX.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.IBM_AS_400.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.LINUX.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.SOLARIS.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.WINDOWS.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.NUTANIX.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.WINDOWS_CLUSTER.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.VCENTER.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.HYPER_V_CLUSTER.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.CITRIX_XEN_CLUSTER.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.SNMP_DEVICE.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.IBM_TAPE_LIBRARY.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_VMANAGE.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_VSMART.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_VBOND.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_VEDGE.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_MERAKI.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_MERAKI_RADIO.getName())
                || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_MERAKI_SWITCH.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.CISCO_MERAKI_SECURITY.getName());
    }

    public static String getErrorCode(JsonObject result)
    {
        return result.containsKey(WorkerUtil.EXIT_CODE) && result.getInteger(WorkerUtil.EXIT_CODE).equals(WorkerUtil.EXIT_CODE_TIMED_OUT)
                ? ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT : ErrorCodes.ERROR_CODE_INTERNAL_ERROR;
    }

    public static int getPollerWorkersByCategory(Category category)
    {
        var workers = (POLLER_WORKERS_BY_CATEGORY_PERCENT.getOrDefault(category, 0) * MotadataConfigUtil.getMetricPollerWorkers()) / 100;

        return workers == 0 ? 1 : workers;
    }

    public static Integer getRediscoveryWorkersByJob(RediscoverJob rediscoverJob)
    {
        return REDISCOVERY_WORKERS_BY_JOB.getOrDefault(rediscoverJob, 0);
    }


    public static void validateJSONObject(JsonObject event)
    {
        try
        {
            event.getJsonObject(RESULT);
        }
        catch (Exception exception)
        {
            event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PARSING_SCRIPT).put(MESSAGE, ErrorMessageConstants.INVALID_JSON)
                    .put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PARSING_SCRIPT).put(MESSAGE, ErrorMessageConstants.INVALID_JSON)));
        }
    }

    public static void validateJSONArray(JsonObject event)
    {
        try
        {
            event.getJsonArray(RESULT);
        }
        catch (Exception exception)
        {
            event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PARSING_SCRIPT).put(MESSAGE, ErrorMessageConstants.INVALID_JSON)
                    .put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PARSING_SCRIPT).put(MESSAGE, ErrorMessageConstants.INVALID_JSON)));
        }
    }

    public static Future<JsonArray> getFaultyObjects(boolean normalize)
    {

        var promise = Promise.<JsonArray>promise();

        try
        {
            var items = new JsonArray();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 1; index <= MotadataConfigUtil.getMetricEnricherInstances(); index++)
            {
                var future = Promise.<Void>promise();

                futures.add(future.future());

                Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_POLLING_ERROR_QUERY + DOT_SEPARATOR + index, new JsonObject(), reply ->
                {
                    if (reply.result() != null && reply.result().body() != null && !reply.result().body().isEmpty())
                    {
                        try
                        {
                            if (CommonUtil.traceEnabled())
                            {

                                LOGGER.trace(String.format("Faulty Objects ---> %s", reply.result().body().encodePrettily()));
                            }

                            for (var entry : reply.result().body().getMap().entrySet())
                            {
                                var item = (JsonObject) entry.getValue();

                                var object = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(entry.getKey()));

                                if (object != null)
                                {
                                    item.put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME))
                                            .put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP))
                                            .put(AIOpsObject.OBJECT_HOST, object.getString(AIOpsObject.OBJECT_HOST))
                                            .put(AIOpsObject.OBJECT_TYPE, object.getString(AIOpsObject.OBJECT_TYPE));

                                    if (normalize)  // normalize groups (id to group name) and tags by converting JSONArray to comma seperated String
                                    {
                                        var groups = new JsonArray();

                                        var builder = new StringBuilder();

                                        var objectGroups = object.getJsonArray(AIOpsObject.OBJECT_GROUPS);

                                        for (var i = 0; i < objectGroups.size(); i++) {
                                            builder.setLength(0);

                                            CommonUtil.normalizeGroupHierarchy(GroupConfigStore.getStore().getItem(objectGroups.getLong(i)), builder, null);

                                            groups.add(builder.toString());
                                        }

                                        item.put(AIOpsObject.OBJECT_GROUPS, StringUtils.join(groups, COMMA_SEPARATOR))
                                                .put(AIOpsObject.OBJECT_TAGS, StringUtils.join(TagConfigStore.getStore().getItems(object.getJsonArray(AIOpsObject.OBJECT_TAGS), Tag.TagType.OBJECT, EMPTY_VALUE), COMMA_SEPARATOR));
                                    }
                                    else
                                    {
                                        item.put(AIOpsObject.OBJECT_GROUPS, object.getJsonArray(AIOpsObject.OBJECT_GROUPS))
                                                .put(AIOpsObject.OBJECT_TAGS, TagConfigStore.getStore().getItems(object.getJsonArray(AIOpsObject.OBJECT_TAGS), Tag.TagType.OBJECT, EMPTY_VALUE));
                                    }
                                }

                                items.add(item);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            future.fail(exception);
                        }
                    }

                    future.complete();
                });
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete(items);
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }

    public static Integer getTopologyWorkersByPluginType(TopologyPluginType topologyPluginType)
    {
        return TOPOLOGY_WORKERS_BY_PLUGIN_TYPE.getOrDefault(topologyPluginType, 0);
    }

    public static Byte getMetricTimeoutValueType(JsonObject event)
    {
        var type = MetricTimeoutValueType.SMALL.getName();

        if (event.containsKey(TIMEOUT))
        {
            var timeout = event.getInteger(TIMEOUT);

            if (timeout > 60 && timeout <= 150)
            {
                type = MetricTimeoutValueType.MEDIUM.getName();
            }
            else if (timeout > 150 && timeout <= 300)
            {
                type = MetricTimeoutValueType.LARGE.getName();
            }
            else if (timeout > 300)
            {
                type = MetricTimeoutValueType.EXTRA_LARGE.getName();
            }
        }

        return type;
    }

    public static int getSNMPCheckTimeoutSeconds(NMSConstants.MetricTimeoutValueType metricTimeoutValueType)
    {
        return switch (metricTimeoutValueType)
        {
            case SMALL -> 2;

            case MEDIUM -> 3;

            case LARGE -> 4;

            case EXTRA_LARGE -> 5;
        };
    }

    public static MetricTimeoutValueType getIncrementedMetricTimeoutValueType(MetricTimeoutValueType metricTimeoutValueType)
    {
        var type = MetricTimeoutValueType.SMALL;

        if (metricTimeoutValueType == MetricTimeoutValueType.SMALL)
        {
            type = MetricTimeoutValueType.MEDIUM;
        }
        else if (metricTimeoutValueType == MetricTimeoutValueType.MEDIUM)
        {
            type = MetricTimeoutValueType.LARGE;
        }
        else if (metricTimeoutValueType == MetricTimeoutValueType.LARGE)
        {
            type = MetricTimeoutValueType.EXTRA_LARGE;
        }

        return type;
    }

    public static void removeGarbageFields(JsonObject event)
    {
        event.remove(OBJECTS);

        event.remove(DISCOVERED_OBJECTS);
    }

    public static Type getType(String name)
    {
        Type type = null;

        if (name != null)
        {
            name = name.toLowerCase(Locale.ENGLISH);

            if (name.contains("win"))
            {
                type = Type.WINDOWS;
            }
            else if (name.contains("aix"))
            {
                type = Type.IBM_AIX;
            }
            else if (name.contains("nix") || name.contains("nux"))
            {
                type = Type.LINUX;
            }
            else if (name.contains("sunos"))
            {
                type = Type.SOLARIS;
            }
            else if (name.contains("hp-ux"))
            {
                type = Type.HP_UX;
            }
        }

        return type;
    }

    public static JsonArray getGroups(Type type)
    {
        var groups = new JsonArray();

        try
        {
            var items = GroupConfigStore.getStore().getItemsByValue(Group.FIELD_GROUP_NAME, type.getName());

            for (var index = 0; index < items.size(); index++)
            {
                if (items.getJsonObject(index).getString(FIELD_TYPE).equalsIgnoreCase(ENTITY_TYPE_SYSTEM))
                {
                    groups.add(items.getJsonObject(index).getLong(ID));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        if (groups.isEmpty())
        {
            LOGGER.warn(String.format("no group found for type %s", type));
        }

        return groups;
    }

    public static JsonObject getServerApps(List<JsonObject> items)
    {
        var result = new JsonObject();

        var apps = new JsonArray();

        var appProcess = new JsonObject();

        try
        {
            for (var item : items)
            {
                if (!appProcess.containsKey(item.getString(Metric.METRIC_TYPE)) && item.getJsonObject(Metric.METRIC_CONTEXT).containsKey(NMSConstants.OBJECT))
                {
                    var object = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(NMSConstants.OBJECT);

                    if (object != null)
                    {
                        appProcess.put(item.getString(Metric.METRIC_TYPE), new JsonObject().put(object.getString(AIOpsObject.OBJECT_NAME), object.getString(AIOpsObject.OBJECT_TYPE)));
                    }
                    else
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("invalid object found for : %s", item.encode()));
                        }
                    }

                    if (NMSConstants.APPLICATION_TYPES.contains(item.getString(Metric.METRIC_TYPE)))
                    {
                        apps.add(new JsonObject().put(Metric.METRIC_TYPE, item.getString(Metric.METRIC_TYPE)).put(Metric.METRIC_DISCOVERY_METHOD, item.getString(Metric.METRIC_DISCOVERY_METHOD)));            //#28048
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        result.put(NMSConstants.APPS, apps);

        result.put(NMSConstants.APP_PROCESS, appProcess);

        return result;
    }

    public static JsonArray getInstances(long id, String instance)//will be providing instanceip data in config event to showcase in monitor template
    {
        var items = new JsonArray();

        try
        {
            var instances = MetricConfigStore.getStore().getMetricInstancesByInstanceType(id, instance);

            for (var index = 0; index < instances.size(); index++)
            {
                var item = new JsonObject();

                item.put(INSTANCE, instances.getString(index));

                item.put(INSTANCE_IP, ObjectCacheStore.getStore().getInstanceIP(id, instances.getString(index).replace(GlobalConstants.INSTANCE_SEPARATOR, EMPTY_VALUE).replace("^", EMPTY_VALUE).replace("@#$", EMPTY_VALUE).replace("#@#", EMPTY_VALUE).replace("#", EMPTY_VALUE).replace("@", EMPTY_VALUE)));//as per value we are saving to DB replacing it with same separators

                items.add(item);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    public static JsonArray filterCredentialProfiles(JsonObject discovery, boolean cliEnabled)
    {
        var ids = new JsonArray();

        try
        {
            // Adding credential ids which have cli option enabled
            var items = CredentialProfileConfigStore.getStore().getItemsByMapMultiValueField(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT, NMSConstants.CLI_ENABLED, YES, ID, discovery.getJsonArray(Discovery.DISCOVERY_CREDENTIAL_PROFILES));

            if (!cliEnabled)
            {
                // Extracting ids which is not part of cli enabled credentials
                discovery.getJsonArray(Discovery.DISCOVERY_CREDENTIAL_PROFILES).stream().filter(item -> !items.contains(item)).forEach(ids::add);
            }
            else
            {
                ids.addAll(items);
            }

            return CredentialProfileConfigStore.getStore().getItems(ids);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return CredentialProfileConfigStore.getStore().getItems(discovery.getJsonArray(Discovery.DISCOVERY_CREDENTIAL_PROFILES));
    }

    public static String normalizeInterfaceName(JsonObject items, String interfaceName)
    {
        if (CommonUtil.isNullOrEmpty(interfaceName) || !interfaceName.matches(".*[a-zA-Z]+.*"))
        {
            return null;
        }

        if (interfaceName.matches("^[a-zA-Z].*\\d+.*"))
        {
            var patternMatcher = INTERFACE_NAME_PATTERN.matcher(interfaceName);

            if (patternMatcher.find())
            {
                for (var key : items.fieldNames())
                {
                    var matcher = INTERFACE_NAME_PATTERN.matcher(key);

                    if (matcher.find() && key.startsWith(interfaceName.substring(0, patternMatcher.start())) && key.substring(matcher.start()).equalsIgnoreCase(interfaceName.substring(patternMatcher.start())))
                    {
                        return items.getString(key);
                    }
                }
            }
        }

        return null;
    }

    public static Type getSDNType(Type type)
    {
        return switch (type)
        {
            case CISCO_VMANAGE, CISCO_VSMART, CISCO_VBOND, CISCO_VEDGE -> Type.CISCO_VMANAGE;

            case CISCO_MERAKI, CISCO_MERAKI_SECURITY, CISCO_MERAKI_SWITCH, CISCO_MERAKI_RADIO -> Type.CISCO_MERAKI;

            case CISCO_ACI -> Type.CISCO_ACI;

            case NSXT -> Type.NSXT;

            default -> null;
        };
    }

    // to update & validate whether instances are allowed to be updated or not
    public static boolean validateInstances(String metricPlugin, JsonObject item)
    {
        var valid = true;

        if (metricPlugin != null && (AIOpsConstants.hasDependencyProp(metricPlugin, true) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.WINDOWS_SERVICE.getName()) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName())))
        {
            var objects = MetricConfigStore.getStore().getObjects(item.getLong(ID));

            if (objects != null && item.getJsonObject(Metric.METRIC_CONTEXT) != null && item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS) != null)
            {
                var object = ObjectConfigStore.getStore().getItem(item.getLong(Metric.METRIC_OBJECT));

                if (object != null)
                {
                    var existingObjects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

                    for (var token = 0; token < existingObjects.size(); token++)
                    {
                        objects.remove(existingObjects.getJsonObject(token).getString(AIOpsObject.OBJECT_NAME));
                    }

                    if (!objects.isEmpty())
                    {
                        // on network service delete from metric objects we need to remove it from network service cache store
                        // one use case -- if 22 or 5985 port goes down at that time on network service polling we received it as a down and we update in cache store to down so on next time if particular monitor's polling scheduled at that time we get port as a down and everytime it will remain same so we abort the polling...to avoid it need this
                        if (metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()))
                        {
                            objects.keySet().stream().map(port -> CommonUtil.getInteger(port.split("\\(")[0].trim())).forEach(port -> NetworkServiceCacheStore.getStore().deleteItem(item.getLong(Metric.METRIC_OBJECT), port));
                        }
                        else
                        {
                            if (NMSConstants.isSystemProcessMetric(metricPlugin) || metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.WINDOWS_SERVICE.getName()))
                            {
                                for (var metric : MetricConfigStore.getStore().getItemsByObject(item.getLong(Metric.METRIC_OBJECT))
                                        .stream().filter(metric -> NMSConstants.APPLICATION_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN))).toList())
                                {
                                    var connection = metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(NMSConstants.OBJECT);

                                    if (connection != null && CommonUtil.isNotNullOrEmpty(connection.getString(AIOpsObject.OBJECT_NAME)) && objects.containsKey(connection.getString(AIOpsObject.OBJECT_NAME)))
                                    {
                                        existingObjects.add(objects.remove(connection.getString(AIOpsObject.OBJECT_NAME)));
                                    }
                                }

                                //MOTADATA-4586
                                valid = !objects.isEmpty();
                            }

                            if (!objects.isEmpty() && !metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.WINDOWS_SERVICE.getName()))
                            {
                                object.put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE_MULTIPLES.getName())
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP))
                                        .put(AIOpsConstants.DEPENDENCY_LEVEL, NMSConstants.isVirtualMachineMetric(metricPlugin)
                                                ? AIOpsConstants.DependencyLevel.EIGHT.getName() : AIOpsConstants.DependencyLevel.FOUR.getName());

                                for (var instance : objects.keySet())
                                {
                                    for (var dependencyType : AIOpsConstants.DependencyType.values())
                                    {
                                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType.getName(),
                                                object.put(AIOpsConstants.DEPENDENCY_TYPE, dependencyType.getName()).put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, instance));
                                    }
                                }

                                if (MetricPlugin.SNMP_INTERFACE.getName().equals(metricPlugin))
                                {
                                    DependencyMapperConfigStore.getStore().deleteItemsByInstances(object.getString(OBJECT_IP), objects.keySet());
                                }
                            }
                        }
                    }
                }
            }
        }

        return valid;
    }

    public static void updateIPSLAMetrics(String metricPlugin, long id, JsonArray objects)
    {
        IPSLA_METRIC_PLUGINS.forEach(plugin ->
        {
            var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, plugin));

            if (item != null && !plugin.equalsIgnoreCase(metricPlugin) && item.getJsonObject(Metric.METRIC_CONTEXT).containsKey(NMSConstants.OBJECTS))
            {
                objects.addAll(item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS));
            }
        });
    }

    public static boolean isStatusCheckMetric(String metricPlugin)
    {
        return metricPlugin.equalsIgnoreCase(MetricPlugin.AVAILABILITY.getName()) || metricPlugin.equalsIgnoreCase(MetricPlugin.OBJECT_STATUS.getName());
    }

    /**
     * Enriches the given JSON object with visualization tags.
     * <p>
     * For each tag in the provided set, if the object does not already contain the tag as a key,
     * this method adds a new entry with the tag as the key and a JsonArray as the value.
     * It then retrieves the tags for the object by key and adds them as a comma-separated string
     * to the JsonArray. If no tags are found, an empty value is added.
     *
     * @param visualizationTags Set of tag keys to enrich the object with
     * @param object           The JsonObject to be enriched with visualization tags
     */
    public static void enrich(Set<String> visualizationTags,JsonObject object)
    {
        if (!visualizationTags.isEmpty())
        {
            for (var visualizationTag : visualizationTags)
            {
                //Object should not contain tag as an object, thus will ignore it
                if (!object.containsKey(visualizationTag))
                {
                    object.getMap().computeIfAbsent(visualizationTag, value -> new JsonArray());

                    var tags = TagCacheStore.getStore().getObjectTagsByTagKey(object.containsKey(Configuration.CONFIG_OBJECT) ? object.getInteger(Configuration.CONFIG_OBJECT) : object.getInteger(AIOpsObject.OBJECT_ID), visualizationTag);

                    if (tags != null)
                    {
                        object.getJsonArray(visualizationTag).add(StringUtils.join(tags, COMMA_SEPARATOR));
                    }
                    else
                    {
                        object.getJsonArray(visualizationTag).add(EMPTY_VALUE);
                    }
                }
            }
        }
    }

    /**
     * Enriches the given JsonObject with severity and resolved object tags.
     * <p>
     * - Sets the severity field using the MetricPolicyCacheStore.
     * - Replaces the OBJECT_TAGS array with the resolved tag items from TagConfigStore.
     *
     * @param object The JsonObject to enrich.
     * @return The enriched JsonObject with severity and object tags.
     */
    public static JsonObject enrich(JsonObject object)
    {
        var severity = MetricPolicyCacheStore.getStore().getSeverity(object.getLong(GlobalConstants.ID));

        object.put(GlobalConstants.SEVERITY, severity != null ? severity : GlobalConstants.Severity.UNKNOWN.name());

        object.put(AIOpsObject.OBJECT_TAGS, TagConfigStore.getStore().getItems(object.getJsonArray(AIOpsObject.OBJECT_TAGS)));

        return object;
    }

    public enum LLDPPortSubType
    {
        ONE("1"),
        TWO("2"),
        THREE("3"),
        FOUR("4"),
        FIVE("5");

        private static final Map<String, LLDPPortSubType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(LLDPPortSubType::getName, e -> e)));

        private final String name;

        LLDPPortSubType(String name)
        {
            this.name = name;
        }

        public static LLDPPortSubType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum OIDGroupType
    {
        SCALAR("scalar"),
        TABULAR("tabular");

        private final String name;

        OIDGroupType(String name)
        {
            this.name = name;
        }

        public String getName()
        {
            return name;
        }
    }

    public enum TopologyFilterType
    {
        IP_ADDRESS("ip.address"),
        IP_ADDRESS_RANGE("ip.address.range"),
        TAG("tag"),
        GROUP("group");
        private static final Map<String, TopologyFilterType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(TopologyFilterType::getName, e -> e)));

        private final String name;

        TopologyFilterType(String name)
        {
            this.name = name;
        }

        public static TopologyFilterType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Represents the types of devices, services, and resources that can be monitored.
     * <p>
     * This enum defines all supported device and service types in the NMS system.
     * Each type represents a specific kind of device (e.g., WINDOWS, LINUX),
     * service (e.g., MYSQL, NGINX), or resource (e.g., AMAZON_EC2, AZURE_VM)
     * that can be discovered, monitored, and managed by the system.
     * <p>
     * Types are organized into categories (see {@link Category}) and may have
     * specific protocols associated with them (see {@link Protocol}).
     */
    public enum Type
    {
        WINDOWS("Windows"),
        LINUX("Linux"),
        IBM_AIX("IBM AIX"),
        IBM_AS_400("IBM AS/400"),
        HP_UX("HP-UX"),
        WINDOWS_CLUSTER("Windows Cluster"),
        ACTIVE_DIRECTORY("Active Directory"),
        APACHE_HTTP("Apache HTTP"),
        AWS_CLOUD("AWS Cloud"),
        AMAZON_DYNAMO_DB("Amazon DynamoDB"),
        AMAZON_EBS("Amazon EBS"),
        AMAZON_EC2("Amazon EC2"),
        AWS_ELB("AWS ELB"),
        AMAZON_RDS("Amazon RDS"),
        AMAZON_S3("Amazon S3"),
        AMAZON_SNS("Amazon SNS"),
        AMAZON_CLOUD_FRONT("Amazon CloudFront"),
        AWS_AUTO_SCALING("AWS Auto Scaling"),
        AWS_LAMBDA("AWS Lambda"),
        AMAZON_SQS("Amazon SQS"),
        AWS_ELASTIC_BEANSTALK("AWS Elastic Beanstalk"),
        AMAZON_DOCUMENTDB("Amazon DocumentDB"),
        AZURE_CLOUD("Azure Cloud"),
        AZURE_COSMOS_DB("Azure Cosmos DB"),
        AZURE_SQL_DATABASE("Azure SQL Database"),
        AZURE_STORAGE("Azure Storage"),
        AZURE_VM("Azure VM"),
        AZURE_WEB_APP("Azure WebApp"),
        AZURE_SERVICE_BUS("Azure Service Bus"),
        AZURE_APPLICATION_GATEWAY("Azure Application Gateway"),
        AZURE_FUNCTION("Azure Function"),
        AZURE_LOAD_BALANCER("Azure Load Balancer"),
        AZURE_VM_SCALE_SET("Azure VM Scale Set"),
        AZURE_CDN("Azure CDN"),
        AZURE_MYSQL("Azure MySQL Server"),
        AZURE_POSTGRESQL("Azure PostgreSQL Server"),
        BIND9("Bind9"),
        MICROSOFT_IIS("Microsoft IIS"),
        NGINX("Nginx"),
        DOTNET("Dotnet"),
        IBM_DB2("IBM Db2"),
        ORACLE_DATABASE("Oracle Database"),
        SQL_SERVER("SQL Server"),
        MYSQL("MySQL"),
        MARIADB("MariaDB"),
        POSTGRESQL("PostgreSQL"),
        MONGODB("MongoDB"),
        WINDOWS_DNS("Windows DNS"),
        WINDOWS_DHCP("Windows DHCP"),
        EXCHANGE_MAILBOX("Exchange Mailbox"),
        EXCHANGE_CLIENT_ACCESS_ROLE("Exchange Client Access Role"),
        EXCHANGE_EDGE_TRANSPORT_ROLE("Exchange Edge Transport Role"),
        EXCHANGE_MAILBOX_ROLE("Exchange Mailbox Role"),
        HA_PROXY("HAProxy"),
        IBM_MQ("IBM MQ"),
        MSMQ("MSMQ"),
        RABBITMQ("RabbitMQ"),
        APACHE_MQ("Apache MQ"),
        ORACLE_WEBLOGIC("Oracle WebLogic"),
        LIGHTTPD("Light Httpd"),
        LINUX_DHCP("Linux DHCP"),
        APACHE_TOMCAT("Apache Tomcat"),
        IBM_WEBSPHERE("IBM WebSphere"),
        WILDFLY("WildFly"),
        WINDOWS_RDP("Windows RDP"),
        SYBASE("Sybase"),
        SAP_HANA("SAP HANA"),
        SAP_MAXDB("SAP MaxDB"),
        MICROSOFT_DYNAMICS_CRM("Microsoft Dynamics CRM"),
        CERTIFICATE("SSL Certificate"),
        CISCO_UCS("Cisco UCS"),
        CISCO_WIRELESS("Cisco Wireless"),
        CITRIX_XEN("Citrix Xen"),
        CITRIX_XEN_CLUSTER("Citrix Xen Cluster"),
        DNS("DNS"),
        DOMAIN("Domain"),
        EMAIL("Email"),
        ZIMBRA("Zimbra"),
        VMWARE_ESXI("VMware ESXi"),
        FTP("FTP"),
        HYPER_V("Hyper-V"),
        HYPER_V_CLUSTER("Hyper-V Cluster"),
        NTP("NTP"),
        OFFICE_365("Office 365"),
        ONEDRIVE("OneDrive"),
        PING("Ping"),
        PORT("Port"),
        RADIUS("RADIUS"),
        RUCKUS_WIRELESS("Ruckus Wireless"),
        ARUBA_WIRELESS("Aruba Wireless"),
        SHAREPOINT_ONLINE("SharePoint Online"),
        EXCHANGE_ONLINE("Exchange Online"),
        SOLARIS("Solaris"),
        MICROSOFT_TEAMS("Microsoft Teams"),
        URL("URL"),
        VCENTER("vCenter"),
        SWITCH("Switch"),
        ROUTER("Router"),
        FIREWALL("Firewall"),
        PRINTER("Printer"),
        LOAD_BALANCER("Load Balancer"),
        UPS("UPS"),
        IBM_TAPE_LIBRARY("IBM Tape Library"),
        SNMP_DEVICE("SNMP Device"),
        WIRELESS_CONTROLLER("Wireless Controller"),
        HARDWARE_SENSOR("Hardware Sensor"),
        EMAIL_GATEWAY("Email Gateway"),
        AVAILABILITY("Availability"),
        NETWORK_SERVICE("Network Service"),
        SYSTEM_PROCESS("system.process"),
        SYSTEM_SERVICE("system.service"),
        SYSTEM_NETWORK_INTERFACE("system.network.interface"),
        SYSTEM_DISK("system.disk"),
        SYSTEM_DISK_VOLUME("system.disk.volume"),
        SYSTEM_PROCESS_NETWORK_CONNECTION("system.process.network.connection"),
        SYSTEM_CPU_CORE("system.cpu.core"),
        VIRTUAL_MACHINE("vm"),
        ACCESS_POINT("ap"),
        NETWORK_INTERFACE("interface"),
        POWERSHELL("Powershell"),
        SSH("SSH"),
        HTTP("HTTP"),
        DATABASE("Database"),
        PRISM("Prism"),
        NUTANIX("Nutanix"),
        CISCO_VMANAGE("Cisco vManage"),
        CISCO_VSMART("Cisco vSmart"),
        CISCO_VBOND("Cisco vBond"),
        CISCO_VEDGE("Cisco vEdge"),
        CISCO_MERAKI("Cisco Meraki"),
        CISCO_MERAKI_SECURITY("Cisco Meraki Security"),
        CISCO_MERAKI_SWITCH("Cisco Meraki Switch"),
        CISCO_MERAKI_RADIO("Cisco Meraki Radio"),

        NETAPP_ONTAP_CLUSTER("NetApp ONTAP Cluster"),

        HPE_STORE_ONCE("HPE StoreOnce"),
        HPE_PRIMERA("HPE Primera"),
        HPE_3PAR("HPE 3PAR"),
        CISCO_ACI("Cisco ACI"),
        DOCKER_CONTAINER("Docker Container"),
        DELL_EMC_UNITY("Dell EMC Unity"),
        WINDOWS_SNMP("Windows (SNMP)"),
        LINUX_SNMP("Linux (SNMP)"),
        NSXT("VMware NSX-T"),
        TANZU_KUBERNETES("Tanzu Kubernetes"),
        KUBERNETES("Kubernetes"),
        CUSTOM("Custom"); // for metric,runbook,topology plugin and log-collector plugin

        private static final Map<String, Type> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(Type::getName, e -> e)));
        private final String name;

        Type(String name)
        {
            this.name = name;
        }

        public static Type valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }

    }

    /**
     * Represents the high-level categories of devices and services in the NMS system.
     * <p>
     * This enum defines the categories used to group similar types of devices and services.
     * Categories provide a way to organize and filter devices and services based on their
     * general purpose or function.
     * <p>
     * Each {@link Type} is associated with a Category through the {@link #getCategory(Type)} method.
     */
    public enum Category
    {
        NETWORK("Network"),
        SERVER("Server"),
        VIRTUALIZATION("Virtualization"),
        CLOUD("Cloud"),
        SERVICE_CHECK("Service Check"),
        MIDDLEWARE("Middleware"),
        DATABASE("Database"),
        WEB_SERVER("Web Server"),
        OTHER("Other"),
        HCI("HCI"),
        SDN("SDN"),
        STORAGE("Storage"),
        CONTAINER_ORCHESTRATION("Container Orchestration"),
        CUSTOM("Custom");

        private static final Map<String, Category> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(Category::getName, e -> e)));
        private final String name;

        Category(String name)
        {
            this.name = name;
        }

        public static Category valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }

    }

    /**
     * Defines the methods used for discovering devices and services.
     * <p>
     * This enum specifies how discovery operations are performed:
     * - AGENT: Discovery is performed by an agent installed on the target system
     * - REMOTE: Discovery is performed remotely from the NMS server
     * - INLINE: Discovery is performed by the agent itself for agent-related processes/services
     */
    public enum DiscoveryMethod
    {
        AGENT,
        REMOTE,
        INLINE // means dummy metrics for agent line process/service will be polled/discovered by agent it's self
    }

    /**
     * Defines the types of targets that can be discovered.
     * <p>
     * This enum categorizes discovery targets:
     * - OBJECT: Standard network or system objects (devices, servers, etc.)
     * - URL: Web-based resources identified by URLs
     * - OTHER: Any other type of discoverable target
     */
    public enum DiscoveryTargetType
    {
        OBJECT,
        URL,
        OTHER
    }

    /**
     * Defines the types of rediscovery jobs that can be performed.
     * <p>
     * Rediscovery jobs are used to refresh information about previously discovered
     * resources. Each job type focuses on a specific kind of resource, such as
     * applications, network interfaces, virtual machines, etc.
     * <p>
     * Rediscovery ensures that the system maintains up-to-date information about
     * monitored resources, which may change over time.
     */
    public enum RediscoverJob
    {
        APP("Application"),
        NETWORK_INTERFACE("Network Interface"),
        NETWORK_SERVICE("Network Service"),
        CLOUD("Cloud"),
        VIRTUAL_MACHINE("Virtual Machine"),
        VIRTUAL_MACHINE_HCI("Virtual Machine (HCI)"),
        FILE_DIRECTORY("File/Directory"),
        PROCESS("Process"),
        WINDOWS_SERVICE("Windows Service"),
        ACCESS_POINT("Access Point"),
        WAN_LINK("WAN Link"),
        CONTAINER("Container");
        private static final Map<String, RediscoverJob> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(RediscoverJob::getName, e -> e)));
        private final String name;

        RediscoverJob(String name)
        {
            this.name = name;
        }

        public static RediscoverJob valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the possible states of objects, metrics, and other entities in the NMS system.
     * <p>
     * This enum represents the lifecycle states of various entities:
     * - NEW: Newly created entity that hasn't been fully processed yet
     * - PROVISION: Entity is being provisioned (set up) in the system
     * - UNPROVISION: Entity is being removed from the system
     * - ENABLE: Entity is active and operational
     * - DISABLE: Entity is inactive but still configured
     * - MAINTENANCE: Entity is temporarily unavailable due to maintenance
     * - SUSPEND: Entity is temporarily suspended
     * - INVALID: Entity has invalid configuration (e.g., incorrect credentials)
     * - ARCHIVE: Entity has been archived for historical reference
     */
    public enum State
    {
        NEW,
        PROVISION,
        UNPROVISION,
        ENABLE,
        DISABLE,
        MAINTENANCE,
        SUSPEND,
        INVALID, //invalid due to bogus credentials
        ARCHIVE
    }


    /**
     * Defines system variables that can be used as placeholders in templates, scripts, or notifications.
     * <p>
     * These variables provide a way to dynamically insert system or object information
     * into various contexts. Each variable is represented by a special token surrounded
     * by triple dollar signs (e.g., $$$monitor.ip$$$).
     * <p>
     * System variables can be used in notification templates, custom scripts, or
     * anywhere else where dynamic substitution of object properties is needed.
     */
    public enum SystemVariable
    {
        OBJECT_IP("$$$monitor.ip$$$"),
        PORT("$$$monitor.port$$$"),
        OBJECT_HOST("$$$monitor.host$$$"),
        OBJECT_TARGET("$$$monitor.target$$$"),
        USERNAME("$$$monitor.username$$$"),
        PASSWORD("$$$monitor.password$$$"),
        OBJECT_NAME("$$$monitor.name$$$"),
        OBJECT_TYPE("$$$monitor.type$$$"),
        OBJECT_CATEGORY("$$$monitor.category$$$"),
        OBJECT_STATUS("$$$monitor.status$$$"),
        OBJECT_GROUPS("$$$monitor.groups$$$"),
        OBJECT_UPTIME("$$$monitor.uptime$$$"),
        OBJECT_DOWNTIME("$$$monitor.downtime$$$"),
        DATABASE("$$$monitor.database$$$"),
        INSTANCE("$$$instance$$$");

        private static final Map<String, SystemVariable> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(SystemVariable::getName, e -> e)));
        private final String name;

        SystemVariable(String name)
        {
            this.name = name;
        }

        public static SystemVariable valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the network protocols used for topology discovery and mapping.
     * <p>
     * These protocols are used to discover network connections and relationships
     * between devices:
     * - CDP (Cisco Discovery Protocol): Cisco's proprietary protocol for discovering directly connected devices
     * - LLDP (Link Layer Discovery Protocol): Industry standard protocol for network discovery
     * - IS-IS (Intermediate System to Intermediate System): Routing protocol for large networks
     * - OSPF (Open Shortest Path First): Routing protocol for IP networks
     * - BGP (Border Gateway Protocol): Routing protocol for internet routing
     * - SPM (Shortest Path Monitoring): Protocol for monitoring network paths
     */
    public enum TopologyProtocol
    {
        CDP("CDP"),
        LLDP("LLDP"),
        IS_IS("IS-IS"),
        OSPF("OSPF"),
        BGP("BGP"),
        SPM("SPM");

        private static final Map<String, TopologyProtocol> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(TopologyProtocol::getName, e -> e)));
        private final String name;

        TopologyProtocol(String name)
        {
            this.name = name;
        }

        public static TopologyProtocol valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }


    /**
     * Defines the network layers at which topology links can be established.
     * <p>
     * This enum represents the OSI model layers relevant for network topology:
     * - L2: Data Link Layer connections (MAC address level)
     * - L3: Network Layer connections (IP address level)
     */
    public enum TopologyLinkLayer
    {
        L2("L2"),

        L3("L3");

        private static final Map<String, TopologyLinkLayer> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(TopologyLinkLayer::getName, e -> e)));
        private final String name;

        TopologyLinkLayer(String name)
        {
            this.name = name;
        }

        public static TopologyLinkLayer valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }


    /**
     * Defines the types of plugins used for topology discovery.
     * <p>
     * This enum represents the different mechanisms used to discover network topology:
     * - SNMP: Uses Simple Network Management Protocol to discover topology
     * - CUSTOM: Uses custom-defined methods to discover topology
     */
    public enum TopologyPluginType
    {
        SNMP("SNMP"),

        CUSTOM("Custom");

        private static final Map<String, TopologyPluginType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(TopologyPluginType::getName, e -> e)));
        private final String name;

        TopologyPluginType(String name)
        {
            this.name = name;
        }

        public static TopologyPluginType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the timeout value types for metric collection operations.
     * <p>
     * This enum represents different timeout durations for metric collection:
     * - SMALL: Short timeout for simple metric collection
     * - MEDIUM: Standard timeout for regular metric collection
     * - LARGE: Extended timeout for operations that may take significant time
     * <p>
     * These timeout values are used to determine how long the system should wait
     * for a response when collecting metrics from monitored devices or services.
     */
    public enum MetricTimeoutValueType
    {
        SMALL(CommonUtil.getByteValue(0)),

        MEDIUM(CommonUtil.getByteValue(1)),

        LARGE(CommonUtil.getByteValue(2)),

        EXTRA_LARGE(CommonUtil.getByteValue(3));

        private static final Map<Byte, MetricTimeoutValueType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(MetricTimeoutValueType::getName, e -> e)));
        private final Byte name;

        MetricTimeoutValueType(Byte name)
        {
            this.name = name;
        }

        public static MetricTimeoutValueType valueOfName(Byte name)
        {
            return VALUES.get(name);
        }

        public Byte getName()
        {
            return name;
        }
    }

    /**
     * Defines the types of metric plugins available in the NMS system.
     * <p>
     * Each enum value represents a specific metric plugin that collects data from
     * a particular type of device, service, or resource. The string value associated
     * with each enum constant is the name of the corresponding Python package that
     * implements the plugin.
     * <p>
     * Metric plugins are responsible for collecting specific performance and status
     * metrics from monitored resources, such as CPU usage, memory usage, disk space,
     * network traffic, etc.
     */
    public enum MetricPlugin
    {
        OBJECT_STATUS("objectstatus"),
        ACTIVE_DIRECTORY("activedirectory"),
        ACTIVE_DIRECTORY_ROLE("activedirectoryrole"),
        ACTIVE_DIRECTORY_REPLICATION("activedirectoryreplication"),
        APACHE_HTTP("apache"),
        AWS_CLOUD("aws"),
        AWS_BILLING("awsbilling"),
        AMAZON_DYNAMO_DB("amazondynamodb"),
        AMAZON_EBS("amazonebs"),
        AMAZON_EC2("amazonec2"),
        AWS_ELB("awselb"),
        AMAZON_RDS("amazonrds"),
        AMAZON_S3("amazons3"),
        AMAZON_SNS("amazonsns"),
        AMAZON_CLOUD_FRONT("amazoncloudfront"),
        AWS_AUTO_SCALING("awsautoscaling"),
        AWS_LAMBDA("awslambda"),
        AMAZON_SQS("amazonsqs"),
        AWS_ELASTIC_BEANSTALK("awselasticbeanstalk"),
        AMAZON_DOCUMENTDB("amazondocumentdb"),
        AZURE_CLOUD("azure"),
        AZURE_BILLING("azurebilling"),
        AZURE_COSMOS_DB("azurecosmosdb"),
        AZURE_SQL_DATABASE("azuresqldatabase"),
        AZURE_STORAGE("azurestorage"),
        AZURE_VM("azurevm"),
        AZURE_WEB_APP("azurewebapp"),
        AZURE_SERVICE_BUS("azureservicebus"),
        AZURE_APPLICATION_GATEWAY("azureapplicationgateway"),
        AZURE_FUNCTION("azurefunction"),
        AZURE_LOAD_BALANCER("azureloadbalancer"),
        AZURE_VM_SCALE_SET("azurevmscaleset"),
        AZURE_CDN("azurecdn"),
        AZURE_MYSQL("azuremysql"),
        AZURE_POSTGRESQL("azurepostgresql"),
        BIND9("bind9"),
        BIND9_NET_STAT("bind9netstat"),
        BIND9_SOCK_STAT("bind9sockstat"),
        CERTIFICATE("certificate"),
        CISCO_UCS_CHASSIS("ciscochassis"),
        CISCO_UCS("ciscoucs"),
        CISCO_UCS_FABRIC_INTERCONNECT("ciscoucsfabricinterconnect"),
        CISCO_UCS_RACK_MOUNT("ciscoucsrackmount"),
        CISCO_WIRELESS("ciscowireless"),
        CISCO_WIRELESS_ACCESS_POINT("ciscowirelessap"),
        CISCO_WIRELESS_CLIENT("ciscowirelessclient"),
        CISCO_WIRELESS_ROUGE_AP("ciscowirelessrogueap"),
        CITRIX_XEN("citrixxen"),
        CITRIX_XEN_CLUSTER("citrixxencluster"),
        CITRIX_XEN_CLUSTER_CONFIG("citrixxenclusterconfig"),
        CITRIX_XEN_CONFIG("citrixxenconfig"),
        CITRIX_XEN_NETWORK("citrixxennetwork"),
        CITRIX_XEN_STORAGE("citrixxenstorage"),
        CITRIX_XEN_VM("citrixxenvm"),
        IBM_DB2("db2"),
        IBM_DB2_DATABASE("db2database"),
        IBM_DB2_POOL("db2pool"),
        IBM_DB2_SESSION("db2session"),
        IBM_DB2_TABLE_SPACE("db2tablespace"),
        IBM_DB2_BACKUP("db2backup"),
        DNS("dns"),
        DOMAIN("domain"),
        DOTNET("dotnet"),
        EMAIL("email"),
        VMWARE_ESXI("esxi"),
        VMWARE_ESXI_CONFIG("esxiconfig"),
        VMWARE_ESXI_HARDWARE_SENSOR("esxihardwaresensor"),
        VMWARE_ESXI_NETWORK("esxinetwork"),
        VMWARE_ESXI_STORAGE("esxistorage"),
        VMWARE_ESXI_DATA_STORE("esxidatastore"),
        VMWARE_ESXI_DISK("esxidisk"),
        VMWARE_ESXI_VM("esxivm"),
        EXCHANGE_CLIENT_ACCESS_ROLE("exchangeclientaccessrole"),
        EXCHANGE_EDGE_TRANSPORT_ROLE("exchangeedgetransportrole"),
        EXCHANGE_MAILBOX("exchangemailbox"),
        EXCHANGE_MAILBOX_ROLE("exchangemailboxrole"),
        EXCHANGE_ONLINE_MAILBOX("exchangeonlinemailbox"),
        FTP("ftp"),
        HA_PROXY("haproxy"),
        HA_PROXY_SESSION("haproxysession"),
        HP_UX("hpux"),
        HP_UX_CPU_CORE("hpuxcpucore"),
        HP_UX_DISK("hpuxdisk"),
        HP_UX_PROCESS("hpuxprocess"),
        HTTP("http"),
        HYPER_V("hyperv"),
        HYPER_V_CLUSTER("hypervcluster"),
        HYPER_V_CLUSTER_STORAGE("hypervclusterstorage"),
        HYPER_V_CONFIG("hypervconfig"),
        HYPER_V_NETWORK("hypervnetwork"),
        HYPER_V_SERVICE("hypervservice"),
        HYPER_V_STORAGE("hypervstorage"),
        HYPER_V_VM("hypervvm"),
        IBM_AIX("ibmaix"),
        IBM_AIX_CPU_CORE("ibmaixcpucore"),
        IBM_AIX_DISK("ibmaixdisk"),
        IBM_AIX_NETWORK_INTERFACE("ibmaixnetworkinterface"),
        IBM_AIX_PROCESS("ibmaixprocess"),

        IBM_AS_400("ibmas400"),
        IBM_AS_400_POOL("ibmas400pool"),
        IBM_AS_400_SPOOL("ibmas400spool"),
        IBM_AS_400_JOB("ibmas400job"),
        IBM_AS_400_MESSAGE("ibmas400Message"),
        IBM_MQ("ibmmq"),
        IBM_MQ_CHANNEL("ibmmqchannel"),
        IBM_MQ_LISTENER("ibmmqlistener"),
        IBM_MQ_QUEUE("ibmmqqueue"),
        IBM_MQ_TOPIC("ibmmqtopic"),
        MICROSOFT_IIS("iis"),
        MICROSOFT_IIS_APPLICATION("iisapplication"),
        MICROSOFT_IIS_POOL("iisapppool"),
        MICROSOFT_IIS_ASP_NET("iisaspnet"),
        IPSLA("ipsla"),
        IPSLA_ICMP_ECHO("ipslaicmpecho"),
        IPSLA_ICMP_JITTER("ipslaicmpjitter"),
        IPSLA_PATH_ECHO("ipslapathecho"),
        DOCKER("docker"),
        DOCKER_CONTAINER("dockercontainer"),
        DOCKER_CONTAINER_PROCESS("dockercontainerprocess"),
        DOCKER_CONTAINER_DISK("dockercontainerdisk"),
        DATABASE("database"),
        LIGHTTPD("lighthttpd"),
        LINUX("linux"),
        LINUX_CPU_CORE("linuxcpucore"),
        LINUX_DHCP("linuxdhcp"),
        LINUX_DIR("linuxdirectory"),
        LINUX_DISK("linuxdisk"),
        LINUX_FILE("linuxfile"),
        LINUX_NETWORK_INTERFACE("linuxnetworkinterface"),
        LINUX_PROCESS("linuxprocess"),
        MARIADB("mariadb"),
        MARIADB_COMMAND("mariadbcommand"),
        MARIADB_INNO_DB("mariadbinnodb"),
        MARIADB_PROCESS("mariadbprocess"),
        MARIADB_INDEX("mariadbindex"),
        MSMQ("msmq"),
        SQL_SERVER("mssql"),
        SQL_SERVER_DATABASE("mssqldatabase"),
        SQL_SERVER_JOB("mssqljob"),
        SQL_SERVER_SESSION("mssqlsession"),
        SQL_SERVER_SLOW_QUERY("mssqlslowquery"),
        SQL_SERVER_PROCESS("mssqlprocess"),
        SQL_SERVER_BACKUP("mssqlbackup"),
        SQL_SERVER_INDEX("mssqlindex"),
        SQL_SERVER_REPLICATION("mssqlreplication"),
        MYSQL("mysql"),
        MYSQL_COMMAND("mysqlcommand"),
        MYSQL_INNO_DB("mysqlinnodb"),
        MYSQL_PROCESS("mysqlprocess"),
        MYSQL_INDEX("mysqlindex"),
        MYSQL_REPLICATION("mysqlreplication"),
        NGINX("nginx"),
        NTP("ntp"),
        OFFICE_365("office365"),
        ONEDRIVE("onedrive"),
        ORACLE_DATABASE("oracle"),
        ORACLE_ASM_DISK("oracleasmdisk"),
        ORACLE_JOB("oraclejob"),
        ORACLE_ROLLBACK_SEGMENT("oraclerollbacksegment"),
        ORACLE_SESSION("oraclesession"),
        ORACLE_SLOW_QUERY("oracleslowquery"),
        ORACLE_TABLE_SPACE("oracletablespace"),
        ORACLE_INDEX("oracleindex"),
        ORACLE_RAC("oraclerac"),
        PING("ping"),
        PORT("port"),
        POSTGRESQL("postgresql"),
        POSTGRESQL_DATABASE("postgresqldatabase"),
        POSTGRESQL_SESSION("postgresqlsession"),
        POSTGRESQL_INDEX("postgresqlindex"),
        POSTGRESQL_TABLE_SPACE("postgresqltablespace"),
        POWERSHELL("powershell"),
        RABBITMQ("rabbitmq"),
        RABBITMQ_CHANNEL("rabbitmqchannel"),
        RABBITMQ_CONNECTION("rabbitmqconnection"),
        RABBITMQ_EXCHANGE("rabbitmqexchange"),
        RABBITMQ_QUEUE("rabbitmqqueue"),
        RADIUS("radius"),
        RUCKUS_WIRELESS("ruckuswireless"),
        RUCKUS_WIRELESS_ACCESS_POINT("ruckuswirelessap"),
        RUCKUS_WIRELESS_CLIENT("ruckuswirelessclient"),
        RUCKUS_WIRELESS_ROUGE_AP("ruckuswirelessrogueap"),
        RUCKUS_WIRELESS_WLAN("ruckuswirelesswlan"),
        ARUBA_WIRELESS("arubawireless"),
        ARUBA_WIRELESS_ACCESS_POINT("arubawirelessap"),
        ARUBA_WIRELESS_CLIENT("arubawirelessclient"),
        ARUBA_WIRELESS_ROUGE_AP("arubawirelessrogueap"),
        SAP_HANA("saphana"),
        SAP_HANA_BACKUP("saphanabackup"),
        SAP_HANA_DISK("saphanadisk"),
        SAP_HANA_HOST("saphanahost"),
        SAP_HANA_SERVICE("saphanaservice"),
        SAP_HANA_SESSION("saphanasession"),
        SAP_HANA_TRANSACTION("saphanatransaction"),
        SAP_HANA_WORK_LOAD("saphanaworkload"),
        SAP_MAXDB("sapmaxdb"),
        SAP_MAXDB_CACHE("sapmaxdbcache"),
        SAP_MAXDB_COMMAND_MONITOR("sapmaxdbcommandmonitor"),
        SAP_MAXDB_DATA_VOLUME("sapmaxdbdatavolume"),
        SAP_MAXDB_LOG_VOLUME("sapmaxdblogvolume"),
        SAP_MAXDB_OMS_HEAP("sapmaxdbomsheap"),
        SAP_MAXDB_RESOURCE_MONITOR("sapmaxdbresourcemonitor"),
        SAP_MAXDB_SCHEMA("sapmaxdbschema"),
        SAP_MAXDB_LOCK("sapmaxdblock"),
        SHAREPOINT_ONLINE("sharepointonline"),
        SOLARIS("solaris"),
        SOLARIS_CPU_CORE("solariscpucore"),
        SOLARIS_DISK("solarisdisk"),
        SOLARIS_NETWORK_INTERFACE("solarisnetworkinterface"),
        SOLARIS_PROCESS("solarisprocess"),
        SSH("ssh"),
        SWITCH_PORT_MAPPER("switchportmapper"),
        SYBASE("sybase"),
        SYBASE_DATABASE("sybasedatabase"),
        SYBASE_PROCESS("sybaseprocess"),
        SYBASE_TRANSACTION("sybasetransaction"),
        SYMANTEC_MESSAGING_GATEWAY("symantecemailgateway"),
        IBM_TAPE_LIBRARY("ibmtapelibrary"),
        MICROSOFT_TEAMS("teams"),
        APACHE_TOMCAT("tomcat"),
        URL("url"),
        VCENTER("vcenter"),
        VCENTER_CLUSTER("vcentercluster"),
        VCENTER_DATA_CENTER("vcenterdatacenter"),
        VCENTER_DATA_STORE("vcenterdatastore"),
        IBM_WEBSPHERE("websphere"),
        IBM_WEBSPHERE_SERVLET("websphereservlet"),
        WILDFLY("wildfly"),
        WINDOWS("windows"),
        WINDOWS_CPU_CORE("windowscpucore"),
        WINDOWS_DHCP("windowsdhcp"),
        WINDOWS_DHCP_SCOPE("windowsdhcpscope"),
        WINDOWS_DIR("windowsdirectory"),
        WINDOWS_DISK("windowsdisk"),
        WINDOWS_DNS("windowsdns"),
        WINDOWS_FILE("windowsfile"),
        WINDOWS_NETWORK_INTERFACE("windowsnetworkinterface"),
        WINDOWS_PROCESS("windowsprocess"),
        WINDOWS_RDP("windowsrdp"),
        WINDOWS_SERVICE("windowsservice"),
        WINDOWS_TASK_SCHEDULER("windowstaskscheduler"),
        WINDOWS_CLUSTER("windowscluster"),
        WINDOWS_CLUSTER_DISK("windowsclusterdisk"),
        ZIMBRA("zimbra"),
        SNMP_DEVICE("snmp"),
        SNMP_SCALAR_METRIC("snmpscalarmetric"),
        SNMP_TABULAR_METRIC("snmptabularmetric"),
        SNMP_INTERFACE("snmpinterface"),
        SNMP_NETWORK_CONNECTION("snmpnetworkconnection"),
        MULTICAST("multicast"),
        REMOTE_VPN_CONNECTION("remotevpnconnection"),
        SITE_VPN("sitevpn"),
        VRF("vrf"),
        BGP("bgp"),
        OSPF("ospf"),
        ISIS("isis"),
        CISCO_STACK("ciscostack"),
        EXTREME_STACK("extremestack"),
        CISCO_HARDWARE_SENSOR("ciscohardwaresensor"),
        VLAN("vlan"),
        ROUTING("routing"),
        STP("stp"),
        NETWORK_SERVICE("networkservice"),
        AVAILABILITY("availability"),
        OFFICE_365_STATUS("office365status"),
        EXCHANGE_ONLINE_STATUS("exchangeonlinestatus"),
        SHAREPOINT_ONLINE_STATUS("sharepointonlinestatus"),
        MICROSOFT_TEAMS_STATUS("microsoftteamsstatus"),
        ONEDRIVE_STATUS("onedrivestatus"),
        PRISM("prism"),
        PRISM_STORAGE("prismstorage"),
        PRISM_CLUSTER("prismcluster"),
        PRISM_DISK("prismdisk"),
        NUTANIX("nutanix"),
        NUTANIX_VM("nutanixvm"),
        NUTANIX_DISK("nutanixdisk"),
        CISCO_VMANAGE("ciscovmanage"),
        CISCO_VEDGE_BGP_ROUTE("ciscovedgebgproute"),
        CISCO_VEDGE_BGP_NEIGHBOR("ciscovedgebgpneighbor"),
        CISCO_VMANAGE_SITE("ciscovmanagesite"),
        CISCO_VSMART("ciscovsmart"),
        CISCO_VBOND("ciscovbond"),
        CISCO_VEDGE("ciscovedge"),
        CISCO_VEDGE_INTERFACE("ciscovedgeinterface"),
        CISCO_VEDGE_TUNNEL("ciscovedgetunnel"),
        CISCO_VEDGE_TLOC("ciscovedgetloc"),
        CISCO_VEDGE_HARDWARE_SENSOR("ciscovedgehardwaresensor"),
        CISCO_MERAKI("ciscomeraki"),
        CISCO_MERAKI_NETWORK("ciscomerakinetwork"),
        CISCO_MERAKI_CLIENT("ciscomerakiclient"),
        CISCO_MERAKI_SECURITY("ciscomerakisecurity"),
        CISCO_MERAKI_SUBNET("ciscomerakisubnet"),
        CISCO_MERAKI_VLAN("ciscomerakivlan"),
        CISCO_MERAKI_INTERFACE("ciscomerakiinterface"),
        CISCO_MERAKI_VPN("ciscomerakivpn"),
        CISCO_MERAKI_SWITCH("ciscomerakiswitch"),
        CISCO_MERAKI_STP("ciscomerakistp"),
        CISCO_MERAKI_RADIO("ciscomerakiradio"),
        CISCO_MERAKI_CHANNEL("ciscomerakichannel"),
        CISCO_MERAKI_ETHERNET("ciscomerakiethernet"),
        CISCO_MERAKI_SSID("ciscomerakissid"),
        NETAPP_ONTAP_CLUSTER("netappontapcluster"),
        NETAPP_ONTAP_CLUSTER_NODE("netappontapclusternode"),
        NETAPP_ONTAP_CLUSTER_HARDWARE_SENSOR("netappontapclusterhardwaresensor"),
        NETAPP_ONTAP_CLUSTER_INTERFACE("netappontapclusterinterface"),
        NETAPP_ONTAP_CLUSTER_DISK("netappontapclusterdisk"),
        NETAPP_ONTAP_CLUSTER_STORAGE("netappontapclusterstorage"),
        NETAPP_ONTAP_CLUSTER_PORT("netappontapclusterport"),
        NETAPP_ONTAP_CLUSTER_SVM("netappontapclustersvm"),
        HPE_STORE_ONCE("hpestoreonce"),
        HPE_STORE_ONCE_DISK("hpestoreoncedisk"),
        HPE_STORE_ONCE_SERVICE_SET("hpestoreonceserviceset"),
        HPE_STORE_ONCE_PORT("hpestoreonceport"),
        SNMP_METRIC("snmpmetric"),
        CISCO_ACI("ciscoaci"),
        CISCO_ACI_TENANT("ciscoacitenant"),
        CISCO_ACI_FABRIC("ciscoacifabric"),
        CISCO_ACI_ENDPOINT("ciscoaciendpoint"),
        HPE_PRIMERA("hpeprimera"),
        HPE_PRIMERA_DISK("hpeprimeradisk"),
        HPE_PRIMERA_NODE("hpeprimeranode"),
        HPE_PRIMERA_HOST("hpeprimerahost"),
        HPE_PRIMERA_PORT("hpeprimeraport"),
        HPE_PRIMERA_VOLUME("hpeprimeravolume"),
        HPE_3PAR("hpe3par"),
        HPE_3PAR_DISK("hpe3pardisk"),
        HPE_3PAR_NODE("hpe3parnode"),
        HPE_3PAR_HOST("hpe3parhost"),
        HPE_3PAR_PORT("hpe3parport"),
        HPE_3PAR_VOLUME("hpe3parvolume"),
        DELL_EMC_UNITY("dellemcunity"),
        DELL_EMC_UNITY_ENCLOSURE("dellemcunityenclosure"),
        DELL_EMC_UNITY_FILE_SHARE("dellemcunityfileshare"),
        DELL_EMC_UNITY_FILE_SYSTEM("dellemcunityfilesystem"),
        DELL_EMC_UNITY_STORAGE("dellemcunitystorage"),
        DELL_EMC_UNITY_STORAGE_PROCESSOR("dellemcunitystorageprocessor"),
        NSXT("nsxt"),
        NSXT_TRANSPORT_NODE("nsxttransportnode"),
        NSXT_NODE_INTERFACE("nsxtnodeinterface"),
        NSXT_NODE_PROCESS("nsxtnodeprocess"),
        NSXT_TUNNEL("nsxttunnel"),
        NSXT_FIREWALL("nsxtfirewall"),
        NSXT_NETWORK("nsxtnetwork"),
        MONGODB("mongodb"),
        MONGO_DATABASE("mongodbdatabase"),
        MONGO_COLLECTION("mongodbcollection"),
        TANZU_KUBERNETES("tanzukubernetes"),
        TANZU_KUBERNETES_NODE("tanzukubernetesnode"),
        TANZU_KUBERNETES_NAMESPACE("tanzukubernetesnamespace"),
        TANZU_KUBERNETES_POD("tanzukubernetespod"),
        TANZU_KUBERNETES_CONTAINER("tanzukubernetescontainer"),
        TANZU_KUBERNETES_SERVICE("tanzukubernetesservice"),
        TANZU_KUBERNETES_SET("tanzukubernetesset"),
        KUBERNETES("kubernetes"),
        KUBERNETES_NODE("kubernetesnode"),
        KUBERNETES_NAMESPACE("kubernetesnamespace"),
        KUBERNETES_POD("kubernetespod"),
        KUBERNETES_CONTAINER("kubernetescontainer"),
        KUBERNETES_SERVICE("kubernetesservice"),
        KUBERNETES_SET("kubernetesset"),

        ORACLE_WEBLOGIC("weblogic"),
        ORACLE_WEBLOGIC_POOL("weblogicpool"),
        ORACLE_WEBLOGIC_SESSION("weblogicsession"),
        ORACLE_WEBLOGIC_JMS_SERVER("weblogicjmsserver"),


        CUSTOM("custom"); // serve metric plugin, runbook plugin, topology plugin, log-collector plugin, log-parser plugin for python,go scripts

        private static final Map<String, MetricPlugin> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(MetricPlugin::getName, e -> e)));
        private final String name;

        MetricPlugin(String name)
        {
            this.name = name;
        }

        public static MetricPlugin valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }

    }

    /**
     * Defines the communication protocols used to interact with monitored devices and services.
     * <p>
     * This enum represents the various protocols that can be used to communicate with
     * different types of devices and services:
     * - POWERSHELL: Used for Windows systems and services
     * - SNMPV1V2c: Simple Network Management Protocol versions 1 and 2c
     * - SNMPV3: Simple Network Management Protocol version 3 (with enhanced security)
     * - SSH: Secure Shell protocol for secure remote access
     * - JDBC: Java Database Connectivity for database systems
     * - HTTP_HTTPS: Web protocols for RESTful APIs and web services
     * - JMX: Java Management Extensions for Java applications
     * - JMS: Java Message Service for messaging systems
     * - CLOUD: Cloud-specific protocols for cloud services
     * - TELNET: Telnet protocol for remote access (less secure than SSH)
     * - CUSTOM: Custom protocols for specialized devices or services
     * - UNKNOWN: Protocol cannot be determined
     * <p>
     * The appropriate protocol for a given device or service type can be determined
     * using the {@link #getProtocol(Type)} method.
     */
    public enum Protocol
    {
        POWERSHELL("Powershell"),
        SNMPV1V2c("SNMP V1/V2c"),
        SNMPV3("SNMP V3"),
        SSH("SSH"),
        JDBC("JDBC"),
        HTTP_HTTPS("HTTP/HTTPS"),
        JMX("JMX"),
        JMS("JMS"),
        CLOUD("Cloud"),
        UNKNOWN("Unknown"),
        CUSTOM("Custom"),
        TELNET("Telnet");

        private static final Map<String, Protocol> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(Protocol::getName, e -> e)));
        private final String name;

        Protocol(String name)
        {
            this.name = name;
        }

        public static Protocol valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the authentication types supported for API and service authentication.
     * <p>
     * This enum represents the different authentication mechanisms that can be used
     * when connecting to APIs or services:
     * - BASIC: Basic authentication using username and password
     * - OAUTH: OAuth-based authentication for more secure token-based access
     * <p>
     * The authentication type determines how credentials are processed and sent
     * when making requests to external systems.
     */
    public enum AuthenticationType
    {
        BASIC("basic"),

        OAUTH("oauth"),

        CERTIFICATE("certificate");

        private static final Map<String, AuthenticationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(AuthenticationType::getName, e -> e)));
        private final String name;

        AuthenticationType(String name)
        {
            this.name = name;
        }

        public static AuthenticationType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines the OAuth grant types supported for OAuth authentication.
     * <p>
     * This enum represents the different OAuth 2.0 grant types that can be used
     * when authenticating with OAuth-enabled services:
     * - AUTHORIZATION_CODE: The Authorization Code grant type, used for server-side applications
     * where a user authorizes access and the server exchanges the authorization code for an access token
     * - PASSWORD: The Resource Owner Password Credentials grant type, where the application
     * directly collects the user's credentials and exchanges them for an access token
     * <p>
     * The grant type determines the OAuth flow used when authenticating with external systems.
     */
    public enum OAuthGrantType
    {
        AUTHORIZATION_CODE("Authorization Code"),

        PASSWORD("Password");

        private static final Map<String, OAuthGrantType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(OAuthGrantType::getName, e -> e)));
        private final String name;

        OAuthGrantType(String name)
        {
            this.name = name;
        }

        public static OAuthGrantType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum ConfigurationType
    {
        UPLOAD("upload"),

        MANUAL("manual");

        private static final Map<String, ConfigurationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(ConfigurationType::getName, e -> e)));
        private final String name;

        ConfigurationType(String name)
        {
            this.name = name;
        }

        public static ConfigurationType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum InstanceType
    {
        MONITOR("monitor"),

        INTERFACE("interface"),

        ACCESS_POINT("access point"),

        VM("vm"),

        PROCESS("process"),

        SERVICE("service"),

        CONTAINER("container"),

        LINK("link");

        private static final Map<String, InstanceType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(InstanceType::getName, e -> e)));
        private final String name;

        InstanceType(String name)
        {
            this.name = name;
        }

        public static InstanceType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
