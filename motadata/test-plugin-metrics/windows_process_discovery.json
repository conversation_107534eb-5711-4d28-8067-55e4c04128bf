{"************": {"metric.timeout": 20, "object.ip": "************", "objects": [{"object.name": "System Idle Process", "object.type": "system.process"}, {"object.name": "System", "object.type": "system.process"}, {"object.name": "smss.exe", "object.type": "system.process"}, {"object.name": "csrss.exe", "object.type": "system.process"}, {"object.name": "wininit.exe|wininit.exe", "object.type": "system.process"}, {"object.name": "services.exe", "object.type": "system.process"}, {"object.name": "winlogon.exe|winlogon.exe", "object.type": "system.process"}, {"object.name": "lsass.exe|C:\\Windows\\system32\\lsass.exe", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k DcomLaunch", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k RPCSS", "object.type": "system.process"}, {"object.name": "LogonUI.exe|\"LogonUI.exe\" /flags:0x0", "object.type": "system.process"}, {"object.name": "dwm.exe|\"dwm.exe\"", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k LocalServiceNetworkRestricted", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k netsvcs", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k LocalService", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k BlockStorageServices", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k NetworkService", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k LocalServiceNoNetwork", "object.type": "system.process"}, {"object.name": "rundll32.exe|C:\\Windows\\system32\\rundll32.exe C:\\Windows\\system32\\pla.dll,PlaHost \"Server Manager Performance Monitor\" \"$(Arg0)\"", "object.type": "system.process"}, {"object.name": "spoolsv.exe|C:\\Windows\\System32\\spoolsv.exe", "object.type": "system.process"}, {"object.name": "dsamain.exe|C:\\Windows\\System32\\dsamain.exe -sn:instance1", "object.type": "system.process"}, {"object.name": "dsamain.exe|C:\\Windows\\System32\\dsamain.exe -sn:instance2", "object.type": "system.process"}, {"object.name": "armsvc.exe|\"C:\\Program Files (x86)\\Common Files\\Adobe\\ARM\\1.0\\armsvc.exe\"", "object.type": "system.process"}, {"object.name": "Microsoft.ActiveDirectory.WebServices.exe|C:\\Windows\\ADWS\\Microsoft.ActiveDirectory.WebServices.exe", "object.type": "system.process"}, {"object.name": "AppAccess.exe|\"C:\\Program Files\\AppFirst\\AppAccess\\bin\\AppAccess.exe\"", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k apphost", "object.type": "system.process"}, {"object.name": "aspnet_state.exe|C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319\\aspnet_state.exe", "object.type": "system.process"}, {"object.name": "dsm_sa_eventmgr32.exe|\"C:\\Program Files (x86)\\Dell\\SysMgt\\dataeng\\bin\\dsm_sa_eventmgr32.exe\"", "object.type": "system.process"}, {"object.name": "dsm_sa_datamgr32.exe|\"C:\\Program Files (x86)\\Dell\\SysMgt\\dataeng\\bin\\dsm_sa_datamgr32.exe\"", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost -k ddpvssvc", "object.type": "system.process"}, {"object.name": "dfsrs.exe|C:\\Windows\\system32\\DFSRs.exe", "object.type": "system.process"}, {"object.name": "dns.exe|C:\\Windows\\system32\\dns.exe", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k ftpsvc", "object.type": "system.process"}, {"object.name": "hsflowd.exe|\"C:\\Program Files\\Host sFlow Project\\Host sFlow Agent\\hsflowd.exe\"", "object.type": "system.process"}, {"object.name": "ismserv.exe|C:\\Windows\\System32\\ismserv.exe", "object.type": "system.process"}, {"object.name": "mr2kserv.exe|\"C:\\Program Files (x86)\\Dell\\SysMgt\\sm\\mr2kserv.exe\"", "object.type": "system.process"}, {"object.name": "mqsvc.exe|C:\\Windows\\system32\\mqsvc.exe", "object.type": "system.process"}, {"object.name": "sqlservr.exe|\"C:\\Program Files\\Microsoft SQL Server\\MSSQL10_50.EPOSERVER\\MSSQL\\Binn\\sqlservr.exe\" -sEPOSERVER", "object.type": "system.process"}, {"object.name": "sqlservr.exe|\"C:\\Program Files\\Microsoft SQL Server\\MSSQL12.SQLEXPRESS\\MSSQL\\Binn\\sqlservr.exe\" -sSQLEXPRESS", "object.type": "system.process"}, {"object.name": "NMSProxy.exe|\"C:\\Program Files (x86)\\NMSProxy\\NMSProxy.exe\"", "object.type": "system.process"}, {"object.name": "dsm_om_shrsvc32.exe|\"C:\\Program Files (x86)\\Dell\\SysMgt\\oma\\bin\\dsm_om_shrsvc32.exe\"", "object.type": "system.process"}, {"object.name": "nmesrvc.exe|C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\nmesrvc.exe", "object.type": "system.process"}, {"object.name": "extjob.exe|c:\\app\\administrator\\product\\11.2.0\\dbhome_1\\Bin\\extjob.exe ORCL", "object.type": "system.process"}, {"object.name": "conhost.exe|\\??\\C:\\Windows\\system32\\conhost.exe 0x4", "object.type": "system.process"}, {"object.name": "taskeng.exe|taskeng.exe {D738119A-2613-4396-93BC-7A45DAE80B3C}", "object.type": "system.process"}, {"object.name": "omtsreco.exe|C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\omtsreco.exe \"OracleMTSRecoveryService\"", "object.type": "system.process"}, {"object.name": "OraClrAgnt.exe|C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\OraClrAgnt.exe agent_sid=CLRExtProc max_dispatchers=2 tcp_dispatchers=0 max_task_threads=6 max_sessions=25 ENVS=\"EXTPROC_DLLS=ONLY:C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\oraclr11.dll\"", "object.type": "system.process"}, {"object.name": "TNSLSNR.EXE|C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\BIN\\TNSLSNR", "object.type": "system.process"}, {"object.name": "oracle.exe|c:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\ORACLE.EXE ORCL", "object.type": "system.process"}, {"object.name": "oravssw.exe|c:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\OraVSSW.exe ORCL", "object.type": "system.process"}, {"object.name": "ossec-agent.exe|\"C:\\Program Files (x86)\\ossec-agent\\ossec-agent.exe\"", "object.type": "system.process"}, {"object.name": "perfhost.exe|C:\\Windows\\SysWow64\\perfhost.exe", "object.type": "system.process"}, {"object.name": "erlsrv.exe|\"C:\\Program Files (x86)\\erl5.10.4\\erts-5.10.4\\bin\\erlsrv.exe\"", "object.type": "system.process"}, {"object.name": "erl.exe|\"C:\\Program Files (x86)\\erl5.10.4\\erts-5.10.4\\bin\\erl.exe\" -service_event ErlSrv_RabbitMQ -nohup -sname rabbit@WIN-R2MMNT2NAK1  -pa \"C:\\\\Program Files (x86)\\\\RabbitMQ Server\\\\rabbitmq_server-3.2.3\\\\sbin\\\\..\\\\ebin\" -boot start_sasl -s rabbit boot  +W w +A30 +P 1048576 -kernel inet_default_connect_options \"[{nodelay,true}]\"   -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\\\"\"C:/Users/<USER>/AppData/Roaming/RabbitMQ/log/<EMAIL>\"\\\"}", "object.type": "system.process"}, {"object.name": "epmd.exe|C:\\PROGRA~2\\ERL510~1.4\\ERTS-5~1.4\\bin\\epmd -daemon", "object.type": "system.process"}, {"object.name": "kernel.exe|\"C:\\Program Files\\sdb\\MaxDB1\\pgm\\kernel.exe\"", "object.type": "system.process"}, {"object.name": "cmd.exe|cmd /c \"\"C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\emctl.bat\" istart dbconsole\"", "object.type": "system.process"}, {"object.name": "perl.exe|C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\\\perl\\bin\\perl.exe  C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\emwd.pl dbconsole", "object.type": "system.process"}, {"object.name": "dsm_om_connsvc32.exe|\"C:\\Program Files (x86)\\Dell\\SysMgt\\oma\\bin\\dsm_om_connsvc32.exe\"", "object.type": "system.process"}, {"object.name": "snmp.exe|C:\\Windows\\System32\\snmp.exe", "object.type": "system.process"}, {"object.name": "cmd.exe|cmd /c \"C:\\app\\administrator\\product\\11.2.0\\dbhome_1/bin/execjavatemp.bat\"", "object.type": "system.process"}, {"object.name": "java.exe|C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\jdk/bin/java  -server -Xmx192M -XX:MaxPermSize=200M -XX:MinHeapFreeRatio=20 -XX:MaxHeapFreeRatio=40    -DORACLE_HOME=C:\\app\\administrator\\product\\11.2.0\\dbhome_1 -Doracle.home=C:\\app\\administrator\\product\\11.2.0\\dbhome_1/oc4j -Doracle.oc4j.localhome=C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\WIN-R2MMNT2NAK1.devad2012.com_orcl/sysman -DEMSTATE=C:\\app\\administrator\\product\\11.2.0\\dbhome_1\\WIN-R2MMNT2NAK1.devad2012.com_orcl", "object.type": "system.process"}, {"object.name": "sqlbrowser.exe|\"C:\\Program Files (x86)\\Microsoft SQL Server\\90\\Shared\\sqlbrowser.exe\"", "object.type": "system.process"}, {"object.name": "sqlwriter.exe|\"C:\\Program Files\\Microsoft SQL Server\\90\\Shared\\sqlwriter.exe\"", "object.type": "system.process"}, {"object.name": "snmp.exe|/SharedMemory:Global\\SNMP_Shared_Memory{880a33dc-84a7-40e7-9af0-31e81bd1bd85} /TrapQSharedMemory:Global\\SNMP_TrapQ_Shared_Memory{ea8b97cb-af28-4e3c-8e6e-f356e1419306} /Wow64Mutex:Global\\SNMP_Wow64_Mutex{77cc0d11-9dfd-4238-8ce3-22c1cdeae726} /Wow64TrapQMutex:Global\\SNMP_Wow64_TRAPQ_Mutex{eacfcad4-a2b0-4315-bc38-acceeb7e355e} /Wow64Event32:Global\\SNMP_Wow64_Event32{e2575e06-866e-426b-b03f-139d85cf18b0} /Wow64Event64:Global\\SNMP_Wow64_Event64{caf30076-acb0-4006-82d7-24234d73dc12}", "object.type": "system.process"}, {"object.name": "VeraxSNMPSimulator.exe|\"C:\\Program Files (x86)\\Verax Systems\\Verax SNMP Simulator\\bin\\windows\\VeraxSNMPSimulator.exe\" //RS//VeraxSNMPSimulator", "object.type": "system.process"}, {"object.name": "vmms.exe|C:\\Windows\\system32\\vmms.exe", "object.type": "system.process"}, {"object.name": "vmware-usbarbitrator64.exe|\"C:\\Program Files (x86)\\Common Files\\VMware\\USB\\vmware-usbarbitrator64.exe\"", "object.type": "system.process"}, {"object.name": "vmware-converter-a.exe|\"C:\\Program Files (x86)\\VMware\\VMware vCenter Converter Standalone\\vmware-converter-a.exe\" -s \"C:\\ProgramData\\VMware\\VMware vCenter Converter Standalone\\converter-agent.xml\"", "object.type": "system.process"}, {"object.name": "vmwp.exe|\"C:\\Windows\\System32\\vmwp.exe\" 1560EB96-B643-43E0-B7B8-7C74E63A7CA2", "object.type": "system.process"}, {"object.name": "vmware-converter.exe|\"C:\\Program Files (x86)\\VMware\\VMware vCenter Converter Standalone\\vmware-converter.exe\" -s \"C:\\ProgramData\\VMware\\VMware vCenter Converter Standalone\\converter-server.xml\"", "object.type": "system.process"}, {"object.name": "vmware-converter.exe|\"C:\\Program Files (x86)\\VMware\\VMware vCenter Converter Standalone\\vmware-converter.exe\" -s \"C:\\ProgramData\\VMware\\VMware vCenter Converter Standalone\\converter-worker.xml\"", "object.type": "system.process"}, {"object.name": "vmwp.exe|\"C:\\Windows\\System32\\vmwp.exe\" 83BBC381-B08D-473D-A5F0-16126574B12C", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k iissvcs", "object.type": "system.process"}, {"object.name": "XenServerHealthCheck.exe|\"C:\\Program Files (x86)\\Citrix\\XenServerHealthCheckService\\XenServerHealthCheck.exe\"", "object.type": "system.process"}, {"object.name": "serv.exe|\"C:\\Program Files\\sdb\\programs\\pgm\\serv.exe\"", "object.type": "system.process"}, {"object.name": "serv.exe|\"C:\\Program Files\\sdb\\MaxDB1\\pgm\\serv.exe\"", "object.type": "system.process"}, {"object.name": "serv.exe|\"C:\\Program Files\\sdb\\DatabaseStudio\\pgm\\serv.exe\"", "object.type": "system.process"}, {"object.name": "win32sysinfo.exe|\"c:\\PROGRA~2\\ERL510~1.4\\lib\\os_mon-2.2.14\\priv\\bin\\win32sysinfo.exe\"", "object.type": "system.process"}, {"object.name": "dfssvc.exe|C:\\Windows\\system32\\dfssvc.exe", "object.type": "system.process"}, {"object.name": "nfsclnt.exe|C:\\Windows\\system32\\nfsclnt.exe", "object.type": "system.process"}, {"object.name": "nfssvc.exe|C:\\Windows\\system32\\nfssvc.exe", "object.type": "system.process"}, {"object.name": "inet_gethost.exe|\"C:\\Program Files (x86)\\erl5.10.4\\erts-5.10.4\\bin\\inet_gethost.exe\" 4", "object.type": "system.process"}, {"object.name": "dbmsrv.exe|\"C:\\Program Files\\sdb\\MaxDB1\\pgm\\dbmsrv.exe\" -DBMSHM 1520 32767 MAXDB1 sdb_ibgs_InternalEventDispatcher", "object.type": "system.process"}, {"object.name": "vds.exe|C:\\Windows\\System32\\vds.exe", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k termsvcs", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k LocalSystemNetworkRestricted", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k NetworkServiceNetworkRestricted", "object.type": "system.process"}, {"object.name": "dllhost.exe|C:\\Windows\\system32\\DllHost.exe /Processid:{88155B26-CE61-42FB-AF31-E024897ADEBF}", "object.type": "system.process"}, {"object.name": "msdtc.exe|C:\\Windows\\System32\\msdtc.exe", "object.type": "system.process"}, {"object.name": "lmgrd.exe|\"C:\\SAP\\SYSAM-2_0\\bin\\lmgrd.exe\"", "object.type": "system.process"}, {"object.name": "lmgrd.exe|\"C:\\SAP\\SYSAM-2_0\\bin\\lmgrd.exe\" -c \"C:\\SAP\\SYSAM-2_0\\licenses\" -l \"C:\\SAP\\SYSAM-2_0\\log\\SYBASE.log\" -z -s -local", "object.type": "system.process"}, {"object.name": "SYBASE.exe|SYBASE.exe -T WIN-R2MMNT2NAK1 11.14 -1 -c \";C:\\SAP\\SYSAM-2_0\\licenses;\" -lmgrd_port 6978 -srv Oct0n2773HfFhCJRSL3HxgYzer0HSTuyel0IMlFcFsfAYLRAJ72gZq1IB3Qiqn6 --lmgrd_start 6261066f -vdrestart 0  -l \"C:\\SAP\\SYSAM-2_0\\log\\SYBASE.log\"", "object.type": "system.process"}, {"object.name": "GoogleCrashHandler.exe|\"C:\\Program Files (x86)\\Google\\Update\\**********\\GoogleCrashHandler.exe\"", "object.type": "system.process"}, {"object.name": "GoogleCrashHandler64.exe|\"C:\\Program Files (x86)\\Google\\Update\\**********\\GoogleCrashHandler64.exe\"", "object.type": "system.process"}, {"object.name": "taskhostex.exe|taskhostex.exe", "object.type": "system.process"}, {"object.name": "rdpclip.exe|rdpclip", "object.type": "system.process"}, {"object.name": "explorer.exe|C:\\Windows\\Explorer.EXE", "object.type": "system.process"}, {"object.name": "rdpinput.exe|\"C:\\Windows\\System32\\rdpinput.exe\"", "object.type": "system.process"}, {"object.name": "amqmtbrn.exe|\"C:\\Program Files (x86)\\IBM\\WebSphere MQ\\bin\\amqmtbrn.exe\" -Startup", "object.type": "system.process"}, {"object.name": "jusched.exe|\"C:\\Program Files (x86)\\Common Files\\Java\\Java Update\\jusched.exe\"", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --flag-switches-begin --flag-switches-end", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=crashpad-handler \"--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\" /prefetch:7 --monitor-self-annotation=ptype=crashpad-handler \"--database=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Crashpad\" \"--metrics-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\" --url=https://clients2.google.com/cr/report --annotation=channel= --annotation=plat=Win64", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=gpu-process --gpu-preferences=UAAAAAAAAADgAAAYAAAAAAAAAAAAAAAAAABgAAAAAAAwAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAEgAAAAAAAAASAAAAAAAAAAYAAAAAgAAABAAAAAAAAAAGAAAAAAAAAAQAAAAAAAAAAAAAAAOAAAAEAAAAAAAAAABAAAADgAAAAgAAAAAAAAACAAAAAAAAAA= --mojo-platform-channel-handle=1368 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:2", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=utility --utility-sub-type=network.mojom.NetworkService --lang=en-US --service-sandbox-type=none --mojo-platform-channel-handle=1576 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:8", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=utility --utility-sub-type=storage.mojom.StorageService --lang=en-US --service-sandbox-type=utility --mojo-platform-channel-handle=1756 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:8", "object.type": "system.process"}, {"object.name": "RuntimeBroker.exe|C:\\Windows\\System32\\RuntimeBroker.exe -Embedding", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=56 --launch-time-ticks=1026497417 --mojo-platform-channel-handle=4940 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "dbstudio.exe|\"C:\\Program Files\\sdb\\DatabaseStudio\\dbstudio.exe\"", "object.type": "system.process"}, {"object.name": "dbmsrv.exe|\"C:\\Program Files\\sdb\\MAXDB12\\pgm\\dbmsrv.exe\" -P 0000000000000270000000000000027800000000000002800000000000000288", "object.type": "system.process"}, {"object.name": "taskeng.exe|taskeng.exe {246CA306-AEDD-4E7B-91F9-F1808DAFDF05}", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=13 --launch-time-ticks=4233516386 --mojo-platform-channel-handle=1672 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=68 --launch-time-ticks=4238736222 --mojo-platform-channel-handle=6800 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=69 --launch-time-ticks=4240133533 --mojo-platform-channel-handle=6912 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "dbmsrv.exe|\"C:\\Program Files\\sdb\\MAXDB12\\pgm\\dbmsrv.exe\" -P 000000000000020C00000000000001A00000000000000234000000000000021C", "object.type": "system.process"}, {"object.name": "cmd.exe|C:\\Windows\\system32\\cmd.exe  /K mscert.bat", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=79 --launch-time-ticks=92374925610 --mojo-platform-channel-handle=6488 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=80 --launch-time-ticks=92375786913 --mojo-platform-channel-handle=4280 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "cmd.exe|\"C:\\Windows\\system32\\cmd.exe\"", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=82 --launch-time-ticks=92406103171 --mojo-platform-channel-handle=5424 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=86 --launch-time-ticks=95981655801 --mojo-platform-channel-handle=3864 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=92 --launch-time-ticks=95990572724 --mojo-platform-channel-handle=4512 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=93 --launch-time-ticks=95993472877 --mojo-platform-channel-handle=5348 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=94 --launch-time-ticks=95995034786 --mojo-platform-channel-handle=1952 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=103 --launch-time-ticks=96239675461 --mojo-platform-channel-handle=5668 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=105 --launch-time-ticks=96243094937 --mojo-platform-channel-handle=7072 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=106 --launch-time-ticks=96244146960 --mojo-platform-channel-handle=3868 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=107 --launch-time-ticks=96244206988 --mojo-platform-channel-handle=5408 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=109 --launch-time-ticks=96283926076 --mojo-platform-channel-handle=6404 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "notepad.exe|\"C:\\Windows\\system32\\NOTEPAD.EXE\" C:\\Users\\<USER>\\dbmcli.txt", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=112 --launch-time-ticks=96785152887 --mojo-platform-channel-handle=7728 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=116 --launch-time-ticks=101847422874 --mojo-platform-channel-handle=8536 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "wusa.exe|wusa.exe \"C:\\Program Files (x86)\\Adobe\\Acrobat Reader DC\\Redistributables\\Windows8.1-*********-x64.msu\" /quiet /norestart", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=154 --launch-time-ticks=428570137031 --mojo-platform-channel-handle=2164 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=155 --launch-time-ticks=428749641146 --mojo-platform-channel-handle=2584 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=utility --utility-sub-type=audio.mojom.AudioService --lang=en-US --service-sandbox-type=audio --mojo-platform-channel-handle=10912 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:8", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=207 --launch-time-ticks=430695291135 --mojo-platform-channel-handle=7808 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "mmc.exe|\"C:\\Windows\\system32\\mmc.exe\" \"C:\\Windows\\system32\\services.msc\"", "object.type": "system.process"}, {"object.name": "kernel.exe|\"C:\\Program Files\\sdb\\MAXDB12\\pgm\\kernel.exe\"", "object.type": "system.process"}, {"object.name": "dbmevtdisp.exe|\"C:\\Program Files\\sdb\\MAXDB12\\bin\\dbmevtdisp\" -SDB_PARAMSVIAPIPE \\\\.\\pipe\\arg13688", "object.type": "system.process"}, {"object.name": "dbmsrv.exe|\"C:\\Program Files\\sdb\\MAXDB12\\pgm\\dbmsrv.exe\" -M 100 -P 0,110 -R \"C:\\Program Files\\sdb\\MAXDB12\" -d MAXDB12 -p 11604", "object.type": "system.process"}, {"object.name": "dbmcli.exe|dbmcli  -d MAXDB12 -u DBADMIN,Mind@123", "object.type": "system.process"}, {"object.name": "dbmsrv.exe|\"C:\\Program Files\\sdb\\MAXDB12\\pgm\\dbmsrv.exe\" -M 12c -P 0,148 -R \"C:\\Program Files\\sdb\\MAXDB12\" -d MAXDB12 -p 13196", "object.type": "system.process"}, {"object.name": "mmc.exe|\"C:\\Windows\\system32\\mmc.exe\" \"C:\\Windows\\system32\\virtmgmt.msc\"", "object.type": "system.process"}, {"object.name": "unsecapp.exe|C:\\Windows\\system32\\wbem\\unsecapp.exe -Embedding", "object.type": "system.process"}, {"object.name": "jucheck.exe|\"C:\\Program Files (x86)\\Common Files\\Java\\Java Update\\jucheck.exe\" -auto -scheduled", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=11 --launch-time-ticks=1820621466478 --mojo-platform-channel-handle=11468 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "filezilla.exe|\"C:\\Program Files (x86)\\FileZilla FTP Client\\filezilla.exe\"", "object.type": "system.process"}, {"object.name": "vmwp.exe|\"C:\\Windows\\System32\\vmwp.exe\" 81EEDF74-8EAE-4D28-A3CD-9E0626AB4AA3", "object.type": "system.process"}, {"object.name": "vmconnect.exe|\"C:\\Windows\\system32\\VmConnect.exe\" \"WIN-R2MMNT2NAK1.devad2012.com\" \"Windows-SCOM\" -G \"81EEDF74-8EAE-4D28-A3CD-9E0626AB4AA3\" -C \"0\"", "object.type": "system.process"}, {"object.name": "WmiPrvSE.exe|C:\\Windows\\system32\\wbem\\wmiprvse.exe", "object.type": "system.process"}, {"object.name": "vmconnect.exe|\"C:\\Windows\\system32\\VmConnect.exe\" \"WIN-R2MMNT2NAK1.devad2012.com\" \"windows-scsm\" -G \"CCAD4B8E-3F98-4318-A4E2-967DD55AB15A\" -C \"0\"", "object.type": "system.process"}, {"object.name": "Taskmgr.exe|\"C:\\Windows\\system32\\taskmgr.exe\" /4", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=606 --launch-time-ticks=2935983770594 --mojo-platform-channel-handle=2608 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "emagent.exe|C:\\app\\administrator\\product\\11.2.0\\dbhome_1/bin/emagent", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --extension-process --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=909 --launch-time-ticks=4335149751064 --mojo-platform-channel-handle=2980 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=952 --launch-time-ticks=4335610975830 --mojo-platform-channel-handle=14792 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=1552 --launch-time-ticks=4423021009874 --mojo-platform-channel-handle=14788 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --display-capture-permissions-policy-allowed --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=1553 --launch-time-ticks=4423022391011 --mojo-platform-channel-handle=5372 --field-trial-handle=1336,i,15908498163617785810,1199810994613862131,131072 /prefetch:1", "object.type": "system.process"}, {"object.name": "ServerManager.exe|\"C:\\Windows\\system32\\ServerManager.exe\"", "object.type": "system.process"}, {"object.name": "powershell.exe|\"C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\"", "object.type": "system.process"}, {"object.name": "jucheck.exe|\"C:\\Program Files (x86)\\Common Files\\Java\\Java Update\\jucheck.exe\" -auto", "object.type": "system.process"}, {"object.name": "OpenWith.exe|C:\\Windows\\system32\\OpenWith.exe \"\"C:\\Program Files (x86)\\Microsoft SQL Server\\MSSQL10_50.EPOSERVER\\MSSQL\\Template Data\\mastlog.ldf\"\"", "object.type": "system.process"}, {"object.name": "AdobeARM.exe|/Skip /ProcessResult /PRODUCT:Reader /LANG:ENU", "object.type": "system.process"}, {"object.name": "winrshost.exe|C:\\Windows\\system32\\WinrsHost.exe -Embedding", "object.type": "system.process"}, {"object.name": "Narrator.exe|\"C:\\Windows\\System32\\Narrator.exe\"  /hardwarebuttonlaunch", "object.type": "system.process"}, {"object.name": "dllhost.exe|C:\\Windows\\SysWOW64\\DllHost.exe /Processid:{60A90A2F-858D-42AF-8929-82BE9D99E8A1}", "object.type": "system.process"}, {"object.name": "w3wp.exe|c:\\windows\\system32\\inetsrv\\w3wp.exe -ap \"DefaultAppPool\" -v \"v4.0\" -l \"webengine4.dll\" -a \\\\.\\pipe\\iisipm286f8d9b-7b98-4cd8-a3cb-a6070f64b283 -h \"C:\\inetpub\\temp\\apppools\\DefaultAppPool\\DefaultAppPool.config\" -w \"\" -m 1 -t 20 -ta 0", "object.type": "system.process"}, {"object.name": "taskeng.exe|taskeng.exe {5030D0E9-8B30-43A9-82CB-65EB5B12CDB3}", "object.type": "system.process"}, {"object.name": "cmd.exe|cmd.exe /x/d/c \"C:\\app\\administrator\\product\\11.2.0\\dbhome_1/bin/emdctl status url https://WIN-R2MMNT2NAK1.devad2012.com:1158/em/console/aboutApplication >nul 2>&1\"", "object.type": "system.process"}, {"object.name": "emdctl.exe|C:\\app\\administrator\\product\\11.2.0\\dbhome_1/bin/emdctl  status url https://WIN-R2MMNT2NAK1.devad2012.com:1158/em/console/aboutApplication", "object.type": "system.process"}, {"object.name": "VSSVC.exe|C:\\Windows\\system32\\vssvc.exe", "object.type": "system.process"}, {"object.name": "dllhost.exe|C:\\Windows\\system32\\DllHost.exe /Processid:{9D884A48-0FB0-4833-AB70-A19405D58616}", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k swprv", "object.type": "system.process"}, {"object.name": "cmd.exe|C:\\Windows\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | select name, commandline | Format-List\"", "object.type": "system.process"}, {"object.name": "powershell.exe|powershell  -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | select name, commandline | Format-List\"", "object.type": "system.process"}], "password": "mind@123", "port": 5985, "status": "succeed", "username": "readonly"}, "************": {"metric.timeout": 20, "object.ip": "************", "objects": [{"object.name": "System Idle Process", "object.type": "system.process"}, {"object.name": "System", "object.type": "system.process"}, {"object.name": "smss.exe", "object.type": "system.process"}, {"object.name": "csrss.exe", "object.type": "system.process"}, {"object.name": "wininit.exe|wininit.exe", "object.type": "system.process"}, {"object.name": "winlogon.exe|winlogon.exe", "object.type": "system.process"}, {"object.name": "services.exe", "object.type": "system.process"}, {"object.name": "lsass.exe|C:\\Windows\\system32\\lsass.exe", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k DcomLaunch", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k RPCSS", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k LocalServiceNetworkRestricted", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k netsvcs", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k LocalService", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k LocalServiceNoNetwork", "object.type": "system.process"}, {"object.name": "spoolsv.exe|C:\\Windows\\System32\\spoolsv.exe", "object.type": "system.process"}, {"object.name": "KMSSS.exe|\"C:\\ProgramData\\KMSAuto\\bin\\KMSSS.exe\" -Port 1688 -PWin RandomKMSPID -PO14 RandomKMSPID -PO15 RandomKMSPID -AI 43200 -RI 43200 KillProcessOnPort -Log -IP", "object.type": "system.process"}, {"object.name": "sqlceip.exe|\"C:\\Program Files\\Microsoft SQL Server\\MSSQL14.MSSQLSERVER\\MSSQL\\Binn\\sqlceip.exe\" -Service", "object.type": "system.process"}, {"object.name": "sqlwriter.exe|\"C:\\Program Files\\Microsoft SQL Server\\90\\Shared\\sqlwriter.exe\"", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k LocalSystemNetworkRestricted", "object.type": "system.process"}, {"object.name": "VGAuthService.exe|\"C:\\Program Files\\VMware\\VMware Tools\\VMware VGAuth\\VGAuthService.exe\"", "object.type": "system.process"}, {"object.name": "vmtoolsd.exe|\"C:\\Program Files\\VMware\\VMware Tools\\vmtoolsd.exe\"", "object.type": "system.process"}, {"object.name": "vmms.exe|C:\\Windows\\system32\\vmms.exe", "object.type": "system.process"}, {"object.name": "WmiPrvSE.exe|C:\\Windows\\system32\\wbem\\wmiprvse.exe", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k termsvcs", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k NetworkServiceNetworkRestricted", "object.type": "system.process"}, {"object.name": "dllhost.exe|C:\\Windows\\system32\\dllhost.exe /Processid:{02D4B3F1-FD88-11D1-960D-00805FC79235}", "object.type": "system.process"}, {"object.name": "msdtc.exe|C:\\Windows\\System32\\msdtc.exe", "object.type": "system.process"}, {"object.name": "vmwp.exe|\"C:\\Windows\\System32\\vmwp.exe\" 83033B7C-6D8F-4822-94D2-203817A35C42", "object.type": "system.process"}, {"object.name": "taskhostex.exe|taskhostex.exe", "object.type": "system.process"}, {"object.name": "vmtoolsd.exe|\"C:\\Program Files\\VMware\\VMware Tools\\vmtoolsd.exe\" -n vmusr", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k NetworkServiceAndNoImpersonation", "object.type": "system.process"}, {"object.name": "sqlservr.exe|\"C:\\Program Files\\Microsoft SQL Server\\MSSQL14.MSSQLSERVER\\MSSQL\\Binn\\sqlservr.exe\" -sMSSQLSERVER", "object.type": "system.process"}, {"object.name": "iexplore.exe|\"C:\\Program Files\\Internet Explorer\\iexplore.exe\" -Embedding", "object.type": "system.process"}, {"object.name": "iexplore.exe|\"C:\\Program Files (x86)\\Internet Explorer\\IEXPLORE.EXE\" SCODEF:3704 CREDAT:275457 /prefetch:2", "object.type": "system.process"}, {"object.name": "iexplore.exe|\"C:\\Program Files (x86)\\Internet Explorer\\IEXPLORE.EXE\" SCODEF:3704 CREDAT:472069 /prefetch:2", "object.type": "system.process"}, {"object.name": "iexplore.exe|\"C:\\Program Files (x86)\\Internet Explorer\\IEXPLORE.EXE\" SCODEF:3704 CREDAT:537610 /prefetch:2", "object.type": "system.process"}, {"object.name": "explorer.exe|\"C:\\Windows\\explorer.exe\" /LOADSAVEDWINDOWS", "object.type": "system.process"}, {"object.name": "clussvc.exe|C:\\Windows\\Cluster\\clussvc.exe -s", "object.type": "system.process"}, {"object.name": "rhs.exe|C:\\Windows\\Cluster\\rhs.exe -key SYSTEM\\CurrentControlSet\\Services\\ClusSvc\\Parameters\\Rhs\\61522249-6437-40f7-b602-406efb515576 -parentPid 5620 -initEvent 932fd2e0-b7f3-4be8-b646-5cf67dd05ec2 -replyEndpoint OLE83FF2D2E64269977E73D58C45596", "object.type": "system.process"}, {"object.name": "rhs.exe|C:\\Windows\\Cluster\\rhs.exe -key SYSTEM\\CurrentControlSet\\Services\\ClusSvc\\Parameters\\Rhs\\0b20613b-01fc-4f7c-a607-ed3838f87a75 -parentPid 5620 -initEvent 20760709-c625-40f3-a26c-0983b8e24542 -replyEndpoint OLE83FF2D2E64269977E73D58C45596", "object.type": "system.process"}, {"object.name": "rhs.exe|C:\\Windows\\Cluster\\rhs.exe -key SYSTEM\\CurrentControlSet\\Services\\ClusSvc\\Parameters\\Rhs\\70702bf8-bb1f-4184-a562-ea6844f59395 -parentPid 5620 -initEvent 32b9b5b3-dd3c-4a4c-8506-dda2c37084ac -replyEndpoint OLE83FF2D2E64269977E73D58C45596", "object.type": "system.process"}, {"object.name": "dwm.exe|\"dwm.exe\"", "object.type": "system.process"}, {"object.name": "SQLAGENT.EXE|\"C:\\Program Files\\Microsoft SQL Server\\MSSQL14.MSSQLSERVER\\MSSQL\\Binn\\SQLAGENT.EXE\" -i MSSQLSERVER", "object.type": "system.process"}, {"object.name": "conhost.exe|\\??\\C:\\Windows\\system32\\conhost.exe 0x4", "object.type": "system.process"}, {"object.name": "explorer.exe|C:\\Windows\\Explorer.EXE", "object.type": "system.process"}, {"object.name": "rdpclip.exe|rdpclip", "object.type": "system.process"}, {"object.name": "rdpinput.exe|\"C:\\Windows\\System32\\rdpinput.exe\"", "object.type": "system.process"}, {"object.name": "powershell.exe|\"C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\"", "object.type": "system.process"}, {"object.name": "ServerManager.exe|\"C:\\Windows\\system32\\ServerManager.exe\"", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k NetworkService", "object.type": "system.process"}, {"object.name": "unsecapp.exe|C:\\Windows\\system32\\wbem\\unsecapp.exe -Embedding", "object.type": "system.process"}, {"object.name": "explorer.exe|C:\\Windows\\explorer.exe /factory,{ceff45ee-c862-41de-aee2-a022c81eda92} -Embedding", "object.type": "system.process"}, {"object.name": "mmc.exe|\"C:\\Windows\\system32\\mmc.exe\" \"C:\\Windows\\system32\\perfmon.msc\" /s", "object.type": "system.process"}, {"object.name": "WmiApSrv.exe|C:\\Windows\\system32\\wbem\\WmiApSrv.exe", "object.type": "system.process"}, {"object.name": "mmc.exe|\"C:\\Windows\\system32\\mmc.exe\" C:\\Windows\\system32\\Cluadmin.msc", "object.type": "system.process"}, {"object.name": "winrshost.exe|C:\\Windows\\system32\\WinrsHost.exe -Embedding", "object.type": "system.process"}, {"object.name": "cmd.exe|\"C:\\Windows\\system32\\cmd.exe\"", "object.type": "system.process"}, {"object.name": "mmc.exe|\"C:\\Windows\\system32\\mmc.exe\" \"C:\\Windows\\system32\\virtmgmt.msc\"", "object.type": "system.process"}, {"object.name": "GoogleCrashHandler.exe|\"C:\\Program Files (x86)\\Google\\Update\\**********\\GoogleCrashHandler.exe\"", "object.type": "system.process"}, {"object.name": "GoogleCrashHandler64.exe|\"C:\\Program Files (x86)\\Google\\Update\\**********\\GoogleCrashHandler64.exe\"", "object.type": "system.process"}, {"object.name": "LogonUI.exe|\"LogonUI.exe\" /flags:0x0", "object.type": "system.process"}, {"object.name": "Taskmgr.exe|\"C:\\Windows\\system32\\taskmgr.exe\" /4", "object.type": "system.process"}, {"object.name": "cmd.exe|C:\\Windows\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);@{'TotalFile'= Get-ChildItem -Path 'C:\\' -Recurse -File | Measure-Object | %{$_.Count}}|fl;@{'TotalDirectory'= Get-ChildItem -Path 'C:\\' -Recurse -Directory | Measure-Object | %{$_.Count}}|fl;Get-item 'C:\\' -force | Select-Object Mode, Name,Length,Directory, CreationTime, LastAccessTime, LastWriteTime;Get-ChildItem 'C:\\' | Measure-Object -Property Length", "object.type": "system.process"}, {"object.name": "\\' -force |  select fullname,LastWriteTime,@{N='modified_by';E={$_.GetAccessControl().Owner}};(Get-Acl 'C:\\').Access | ?{$_.IdentityReference -match $Owner} | Select IdentityReference,FileSystemRights | Format-List\"|powershell  -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);@{'TotalFile'= Get-ChildItem -Path 'C:\\' -Recurse -File | Measure-Object | %{$_.Count}}|fl;@{'TotalDirectory'= Get-ChildItem -Path 'C:\\' -Recurse -Directory | Measure-Object | %{$_.Count}}|fl;Get-item 'C:\\' -force | Select-Object Mode, Name,Length,Directory, CreationTime, LastAccessTime, LastWriteTime;Get-ChildItem 'C:\\' | Measure-Object -Property Length -sum;Get-Item 'C:\\' -force | Select", "object.type": "system.process"}, {"object.name": "cmd.exe|C:\\Windows\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | select name, commandline | Format-List\"", "object.type": "system.process"}, {"object.name": "powershell.exe|powershell  -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | select name, commandline | Format-List\"", "object.type": "system.process"}], "password": "Mind@123", "port": 5985, "status": "succeed", "username": "Administrator"}, "************": {"metric.timeout": 20, "object.ip": "************", "objects": [{"object.name": "System Idle Process", "object.type": "system.process"}, {"object.name": "System", "object.type": "system.process"}, {"object.name": "smss.exe", "object.type": "system.process"}, {"object.name": "csrss.exe", "object.type": "system.process"}, {"object.name": "wininit.exe|wininit.exe", "object.type": "system.process"}, {"object.name": "winlogon.exe|winlogon.exe", "object.type": "system.process"}, {"object.name": "services.exe", "object.type": "system.process"}, {"object.name": "lsass.exe|C:\\Windows\\system32\\lsass.exe", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k DcomLaunch", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k RPCSS", "object.type": "system.process"}, {"object.name": "LogonUI.exe|\"LogonUI.exe\" /flags:0x0", "object.type": "system.process"}, {"object.name": "dwm.exe|\"dwm.exe\"", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k LocalServiceNetworkRestricted", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k netsvcs", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k LocalService", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k NetworkService", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k LocalSystemNetworkRestricted", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k LocalServiceNoNetwork", "object.type": "system.process"}, {"object.name": "spoolsv.exe|C:\\Windows\\System32\\spoolsv.exe", "object.type": "system.process"}, {"object.name": "httpd.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.2\\bin\\httpd.exe\" -k runservice", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k apphost", "object.type": "system.process"}, {"object.name": "KMSSS.exe|\"C:\\ProgramData\\KMSAuto\\bin\\KMSSS.exe\" -Port 1688 -PWin RandomKMSPID -PO14 RandomKMSPID -PO15 RandomKMSPID -AI 43200 -RI 43200 KillProcessOnPort -Log -IP", "object.type": "system.process"}, {"object.name": "httpd.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.2\\bin\\httpd.exe\" -d \"C:/Program Files/Apache Software Foundation/Apache2.2\" -f \"C:\\Program Files\\Apache Software Foundation\\Apache2.2\\conf\\httpd.conf\" -d \"C:\\Program Files\\Apache Software Foundation\\Apache2.2\\.\"", "object.type": "system.process"}, {"object.name": "omtsreco.exe|C:\\app\\Administrator\\product\\11.2.0\\dbhome_1\\bin\\omtsreco.exe \"OracleMTSRecoveryService\"", "object.type": "system.process"}, {"object.name": "conhost.exe|\\??\\C:\\Windows\\system32\\conhost.exe 0x4", "object.type": "system.process"}, {"object.name": "ohasd.exe|C:\\app\\Administrator\\product\\11.2.0\\grid\\bin\\ohasd.exe serviceOracleOHService", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k iissvcs", "object.type": "system.process"}, {"object.name": "cmd.exe|C:\\Windows\\system32\\cmd.exe /c \"C:\\app\\Administrator\\product\\11.2.0\\dbhome_1/bin/execjavatemp.bat\"", "object.type": "system.process"}, {"object.name": "java.exe|C:\\app\\Administrator\\product\\11.2.0\\dbhome_1\\jdk/bin/java  -server -Xmx192M -XX:MaxPermSize=200M -XX:MinHeapFreeRatio=20 -XX:MaxHeapFreeRatio=40    -DORACLE_HOME=C:\\app\\Administrator\\product\\11.2.0\\dbhome_1 -Doracle.home=C:\\app\\Administrator\\product\\11.2.0\\dbhome_1/oc4j -Doracle.oc4j.localhome=C:\\app\\Administrator\\product\\11.2.0\\dbhome_1\\WIN-RC9AF0C6565_ASMDB/sysman -DEMSTATE=C:\\app\\Administrator\\product\\11.2.0\\dbhome_1\\WIN-RC9AF0C6565_ASMDB -Doracle.j2ee.dont.use.memory.archive=true", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\System32\\svchost.exe -k termsvcs", "object.type": "system.process"}, {"object.name": "svchost.exe|C:\\Windows\\system32\\svchost.exe -k NetworkServiceNetworkRestricted", "object.type": "system.process"}, {"object.name": "msdtc.exe|C:\\Windows\\System32\\msdtc.exe", "object.type": "system.process"}, {"object.name": "oraagent.exe|C:\\app\\Administrator\\product\\11.2.0\\grid\\bin\\oraagent.exe", "object.type": "system.process"}, {"object.name": "cssdagent.exe|C:\\app\\Administrator\\product\\11.2.0\\grid/bin/cssdagent.exe", "object.type": "system.process"}, {"object.name": "TNSLSNR.EXE|C:\\app\\Administrator\\product\\11.2.0\\grid\\BIN\\TNSLSNR", "object.type": "system.process"}, {"object.name": "ocssd.exe|C:\\app\\Administrator\\product\\11.2.0\\grid/bin/ocssd.exe \"\"", "object.type": "system.process"}, {"object.name": "oracle.exe|c:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\ORACLE.EXE TEST11111", "object.type": "system.process"}, {"object.name": "oracle.exe|c:\\app\\administrator\\product\\11.2.0\\grid\\bin\\ORACLE.EXE +ASM", "object.type": "system.process"}, {"object.name": "oracle.exe|c:\\app\\administrator\\product\\11.2.0\\dbhome_1\\bin\\ORACLE.EXE ASMDB", "object.type": "system.process"}, {"object.name": "taskhostex.exe|taskhostex.exe", "object.type": "system.process"}, {"object.name": "rdpclip.exe|rdpclip", "object.type": "system.process"}, {"object.name": "explorer.exe|C:\\Windows\\Explorer.EXE", "object.type": "system.process"}, {"object.name": "db2systray.exe|\"C:\\Program Files\\IBM\\SQLLIB9\\BIN\\db2systray.exe\" DB2", "object.type": "system.process"}, {"object.name": "ApacheMonitor.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.2\\bin\\ApacheMonitor.exe\"", "object.type": "system.process"}, {"object.name": "cmd.exe|\"C:\\Windows\\system32\\cmd.exe\"", "object.type": "system.process"}, {"object.name": "perfmon.exe|\"C:\\Windows\\system32\\perfmon.exe\" /res", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\"", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=crashpad-handler \"--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\" /prefetch:7 --monitor-self-annotation=ptype=crashpad-handler \"--database=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Crashpad\" \"--metrics-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\" --url=https://clients2.google.com/cr/report --annotation=channel= --annotation=plat=Win64", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=gpu-process --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --gpu-preferences=SAAAAAAAAADgAAAwAAAAAAAAAAAAAAAAAABgAAAAAAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB4AAAAAAAAAHgAAAAAAAAAKAAAAAQAAAAgAAAAAAAAACgAAAAAAAAAMAAAAAAAAAA4AAAAAAAAABAAAAAAAAAAAAAAAAUAAAAQAAAAAAAAAAAAAAAGAAAAEAAAAAAAAAABAAAABQAAABAAAAAAAAAAAQAAAAYAAAAIAAAAAAAAAAgAAAAAAAAA --mojo-platform-channel-handle=1248 /prefetch:2", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=utility --utility-sub-type=network.mojom.NetworkService --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --lang=en-US --service-sandbox-type=none --mojo-platform-channel-handle=1620 /prefetch:8", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=utility --utility-sub-type=storage.mojom.StorageService --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --lang=en-US --service-sandbox-type=utility --mojo-platform-channel-handle=1920 /prefetch:8", "object.type": "system.process"}, {"object.name": "RuntimeBroker.exe|C:\\Windows\\System32\\RuntimeBroker.exe -Embedding", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=13 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=2856 /prefetch:1", "object.type": "system.process"}, {"object.name": "powershell.exe|\"C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\"", "object.type": "system.process"}, {"object.name": "mmc.exe|\"C:\\Windows\\system32\\mmc.exe\" \"C:\\Windows\\system32\\services.msc\"", "object.type": "system.process"}, {"object.name": "sqlplus.exe|sqlplus  / as sysdba", "object.type": "system.process"}, {"object.name": "winrshost.exe|C:\\Windows\\system32\\WinrsHost.exe -Embedding", "object.type": "system.process"}, {"object.name": "MySQLWorkbench.exe|\"C:\\Program Files\\MySQL\\MySQL Workbench 8.0 CE\\MySQLWorkbench.exe\"", "object.type": "system.process"}, {"object.name": "mysqld.exe|\"C:\\Program Files\\MySQL\\MySQL Server 5.7\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 5.7\\my.ini\" MySQL57", "object.type": "system.process"}, {"object.name": "mysqld.exe|\"C:\\Program Files\\MySQL\\MySQL Server 5.6\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 5.6\\my.ini\" MySQL5.6", "object.type": "system.process"}, {"object.name": "mysqld.exe|\"C:\\Program Files\\MySQL\\MySQL Server 5.5\\bin\\mysqld\" --defaults-file=\"C:\\Program Files\\MySQL\\MySQL Server 5.5\\my.ini\" MySQL5.5", "object.type": "system.process"}, {"object.name": "mysqld-nt.exe|\"C:\\Program Files\\MySQL\\MySQL Server 5.0\\bin\\mysqld-nt\" --defaults-file=\"C:\\Program Files\\MySQL\\MySQL Server 5.0\\my.ini\" MySQL5", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4290 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=7184 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4292 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=9528 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4293 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=8844 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4294 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=4248 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4296 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=9852 /prefetch:1", "object.type": "system.process"}, {"object.name": "Taskmgr.exe|\"C:\\Windows\\system32\\taskmgr.exe\" /4", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4316 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=4700 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4319 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=10528 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4320 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=5916 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4321 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=4176 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4322 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=10432 /prefetch:1", "object.type": "system.process"}, {"object.name": "chrome.exe|\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\" --type=renderer --field-trial-handle=1232,17834674226049537915,13220874973982697999,131072 --disable-gpu-compositing --lang=en-US --device-scale-factor=1 --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=4323 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=7424 /prefetch:1", "object.type": "system.process"}, {"object.name": "sqlplus.exe|sqlplus  system/motadata@fd00:1:1:1::132:1521/orcltest", "object.type": "system.process"}, {"object.name": "WmiPrvSE.exe|C:\\Windows\\system32\\wbem\\wmiprvse.exe", "object.type": "system.process"}, {"object.name": "taskhost.exe|taskhost.exe", "object.type": "system.process"}, {"object.name": "dllhost.exe|C:\\Windows\\system32\\DllHost.exe /Processid:{133EAC4F-5891-4D04-BADA-D84870380A80}", "object.type": "system.process"}, {"object.name": "cmd.exe|C:\\Windows\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | select name, commandline | Format-List\"", "object.type": "system.process"}, {"object.name": "powershell.exe|powershell  -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | select name, commandline | Format-List\"", "object.type": "system.process"}], "password": "Mind@123", "port": 5985, "status": "succeed", "username": "administrator"}}