/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Configuration;
import com.mindarray.config.ConfigConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;

public class ConfigurationCacheStore extends AbstractCacheStore
{

    private static final ConfigurationCacheStore STORE = new ConfigurationCacheStore();
    private static final Logger LOGGER = new Logger(ConfigurationCacheStore.class, GlobalConstants.MOTADATA_STORE, "Configuration Cache Store");
    /*
    Assume, Cache size per backup file = 10KB [Snappy compressed]
    If device have two backup file and in worst case customer have also marked baseline version then,
    Cache size per device = 10KB * 3 = 30KB
    NO. of devices = 10000
    Total cache size = 30KB * 10000 = 300MB
     */
    private final Map<Integer, byte[]> recentRunningBackupRecords = new ConcurrentHashMap<>();
    private final Map<Integer, byte[]> recentStartUpBackupRecords = new ConcurrentHashMap<>();
    private final Map<Integer, byte[]> recentBaselineBackupRecords = new ConcurrentHashMap<>();
    private final Map<Long, JsonObject> runningOperations = new ConcurrentHashMap<>();

    private ConfigurationCacheStore()
    {
    }

    public static ConfigurationCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (GlobalConstants.BootstrapType.COLLECTOR == Bootstrap.bootstrapType())
        {
            promise.fail("remote event processor can not init store... it must be master or default boot sequence...");
        }
        else
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_CONFIGURATION, result ->
            {

                if (result.succeeded())
                {
                    var futures = new ArrayList<Future<Object>>();

                    for (var index = 0; index < result.result().size(); index++)
                    {
                        var init = Promise.promise();

                        futures.add(init.future());

                        var item = result.result().getJsonObject(index);

                        populate(item, ConfigConstants.ConfigBackupType.RUNNING, false) // Running File
                                .compose(future -> populate(item, ConfigConstants.ConfigBackupType.STARTUP, false)) // Startup File
                                .compose(future -> populate(item, ConfigConstants.ConfigBackupType.RUNNING, true)) // Baseline
                                .onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        init.complete();

                                        LOGGER.debug(String.format("item added %s into the store", item.encodePrettily()));
                                    }
                                    else
                                    {
                                        init.fail(asyncResult.cause().getMessage());

                                        LOGGER.fatal(String.format("Failed to add item in the cache store, object : %s, reason : %s", item.getString(AIOpsObject.OBJECT_NAME), asyncResult.cause().getMessage()));
                                    }
                                });
                    }

                    Future.all(futures).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            promise.complete();

                            LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                        }
                        else
                        {
                            promise.fail(asyncResult.cause().getMessage());

                            LOGGER.fatal(String.format("store %s initialization fail...", this.getClass().getSimpleName()));
                        }
                    });
                }
                else
                {
                    LOGGER.error(result.cause());

                    promise.fail(result.cause().getMessage());
                }
            });
        }

        return promise.future();
    }

    private Future<Void> populate(JsonObject item, ConfigConstants.ConfigBackupType type, boolean baseline)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var currentVersion = getRecentVersion(item, type, baseline);

            if (currentVersion != NOT_AVAILABLE)
            {
                Bootstrap.configDBService().get(String.format(type.getName().equalsIgnoreCase(ConfigConstants.ConfigBackupType.RUNNING.getName()) ? DBConstants.TBL_CONFIG_RUNNING_RESULT : DBConstants.TBL_CONFIG_STARTUP_RESULT, currentVersion),
                        new JsonObject().put(DBConstants.FIELD_NAME, Configuration.CONFIG_OBJECT).put(VALUE, item.getInteger(Configuration.CONFIG_OBJECT)),
                        result ->
                        {
                            if (result.succeeded())
                            {
                                for (var start = 0; start < result.result().size(); start++)
                                {
                                    if (baseline)
                                    {
                                        updateRecentBaselineBackupRecords(item.getInteger(Configuration.CONFIG_OBJECT), result.result().getJsonObject(start).getBinary(Configuration.CONFIG_BACKUP_FILE_CONTENT));
                                    }
                                    else
                                    {
                                        switch (type)
                                        {
                                            case RUNNING ->
                                                    updateRecentRunningBackupRecords(item.getInteger(Configuration.CONFIG_OBJECT), result.result().getJsonObject(start).getBinary(Configuration.CONFIG_BACKUP_FILE_CONTENT));

                                            case STARTUP ->
                                                    updateRecentStartUpBackupRecords(item.getInteger(Configuration.CONFIG_OBJECT), result.result().getJsonObject(start).getBinary(Configuration.CONFIG_BACKUP_FILE_CONTENT));
                                        }
                                    }
                                }

                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause().getMessage());
                            }
                        });
            }
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.complete();
        }

        return promise.future();
    }

    private int getRecentVersion(JsonObject item, ConfigConstants.ConfigBackupType type, boolean baseline)
    {
        var version = switch (type)
        {
            case RUNNING ->
                    item.containsKey(Configuration.CONFIG_BACKUP_FILE_RUNNING_CURRENT_VERSION) ? item.getInteger(Configuration.CONFIG_BACKUP_FILE_RUNNING_CURRENT_VERSION) : NOT_AVAILABLE;

            case STARTUP ->
                    item.containsKey(Configuration.CONFIG_BACKUP_FILE_STARTUP_CURRENT_VERSION) ? item.getInteger(Configuration.CONFIG_BACKUP_FILE_STARTUP_CURRENT_VERSION) : NOT_AVAILABLE;
        };

        if (baseline)
        {
            version = item.containsKey(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION) && item.getInteger(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION) != DUMMY_NUMERIC_VALUE ? item.getInteger(Configuration.CONFIG_BACKUP_FILE_BASELINE_VERSION) : NOT_AVAILABLE;
        }

        return version;
    }

    public byte[] getRecentRunningBackupRecords(int objectId)
    {
        return recentRunningBackupRecords.get(objectId);
    }

    public byte[] getRecentStartUpBackupRecords(int objectId)
    {
        return recentStartUpBackupRecords.get(objectId);
    }

    public byte[] getRecentBaselineBackupRecords(int objectId)
    {
        return recentBaselineBackupRecords.get(objectId);
    }

    public void updateRecentRunningBackupRecords(int objectId, byte[] bytes)
    {
        recentRunningBackupRecords.put(objectId, bytes);
    }

    public void updateRecentStartUpBackupRecords(int objectId, byte[] bytes)
    {
        recentStartUpBackupRecords.put(objectId, bytes);
    }

    public void updateRecentBaselineBackupRecords(int objectId, byte[] bytes)
    {
        recentBaselineBackupRecords.put(objectId, bytes);
    }

    public void delete(int objectId)
    {
        recentRunningBackupRecords.remove(objectId);

        recentStartUpBackupRecords.remove(objectId);

        recentBaselineBackupRecords.remove(objectId);
    }

    public void deleteBaselineRecords(int objectId)
    {
        recentBaselineBackupRecords.remove(objectId);
    }

    public JsonObject getRunningOperation(long id)
    {
        return runningOperations.getOrDefault(id, null);
    }

    public void startOperation(long id, ConfigConstants.ConfigOperation type)
    {
        var state = type.getName().equalsIgnoreCase(ConfigConstants.ConfigOperation.UPGRADE.getName()) ? ConfigConstants.ConfigOperationState.IN_QUEUE.getName() : NMSConstants.STATE_RUNNING;

        if (runningOperations.containsKey(id))
        {
            runningOperations.get(id).put(type.getName(), state);
        }
        else
        {
            runningOperations.put(id, new JsonObject().put(type.getName(), state));
        }
    }

    public void completeOperation(long id, ConfigConstants.ConfigOperation type)
    {
        if (runningOperations.containsKey(id))
        {
            runningOperations.get(id).remove(type.getName());
        }
    }

    public void updateOperation(long id, ConfigConstants.ConfigOperation type, String state)
    {
        if (runningOperations.containsKey(id))
        {
            runningOperations.get(id).put(type.getName(), state);
        }
        else
        {
            runningOperations.put(id, new JsonObject().put(type.getName(), state));
        }
    }

    public void removeOperation(long id)
    {
        runningOperations.remove(id);
    }
}
