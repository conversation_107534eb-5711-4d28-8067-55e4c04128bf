{"172.16.10.10": {"result": {"sap.maxdb.resource.monitor": [{"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.session.id": 16496, "sap.maxdb.resource.monitor.sql.query": "UPDATE PERSONAL SET NAME='Meier' WHERE PNR='4711' AND NAME='meier'", "sap.maxdb.resource.monitor.executions": 12362.0, "sap.maxdb.resource.monitor.qualified.rows": 74172.0, "sap.maxdb.resource.monitor.virtual.reads": 12361.0, "sap.maxdb.resource.monitor.fetched.rows": 0.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 1.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.session.id": 16498, "sap.maxdb.resource.monitor.sql.query": "SELECT * FROM PERSONAL WHERE PNR='4711'", "sap.maxdb.resource.monitor.executions": 12362.0, "sap.maxdb.resource.monitor.qualified.rows": 74172.0, "sap.maxdb.resource.monitor.virtual.reads": 12362.0, "sap.maxdb.resource.monitor.fetched.rows": 12362.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 663, "sap.maxdb.resource.monitor.session.id": 16456, "sap.maxdb.resource.monitor.sql.query": "SELECT GROUPNAME FROM USERS WHERE USERNAME=USER", "sap.maxdb.resource.monitor.executions": 90.0, "sap.maxdb.resource.monitor.qualified.rows": 2880.0, "sap.maxdb.resource.monitor.virtual.reads": 990.0, "sap.maxdb.resource.monitor.fetched.rows": 90.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 665, "sap.maxdb.resource.monitor.session.id": 16456, "sap.maxdb.resource.monitor.sql.query": "SELECT * FROM DB_STATE WHERE DESCRIPTION = 'Monitor rows'", "sap.maxdb.resource.monitor.executions": 43.0, "sap.maxdb.resource.monitor.qualified.rows": 1032.0, "sap.maxdb.resource.monitor.virtual.reads": 43.0, "sap.maxdb.resource.monitor.fetched.rows": 43.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 667, "sap.maxdb.resource.monitor.session.id": 16456, "sap.maxdb.resource.monitor.sql.query": "SELECT NUMERIC_VALUE FROM DB_STATE WHERE DESCRIPTION = 'Diagnose monitor selectivity'", "sap.maxdb.resource.monitor.executions": 12.0, "sap.maxdb.resource.monitor.qualified.rows": 288.0, "sap.maxdb.resource.monitor.virtual.reads": 12.0, "sap.maxdb.resource.monitor.fetched.rows": 12.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 666, "sap.maxdb.resource.monitor.session.id": 16456, "sap.maxdb.resource.monitor.sql.query": "SELECT NUMERIC_VALUE FROM DB_STATE WHERE DESCRIPTION = 'Diagnose monitor read'", "sap.maxdb.resource.monitor.executions": 6.0, "sap.maxdb.resource.monitor.qualified.rows": 144.0, "sap.maxdb.resource.monitor.virtual.reads": 0.0, "sap.maxdb.resource.monitor.fetched.rows": 0.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 668, "sap.maxdb.resource.monitor.session.id": 16456, "sap.maxdb.resource.monitor.sql.query": "SELECT NUMERIC_VALUE FROM DB_STATE WHERE DESCRIPTION = 'Diagnose monitor time'", "sap.maxdb.resource.monitor.executions": 6.0, "sap.maxdb.resource.monitor.qualified.rows": 144.0, "sap.maxdb.resource.monitor.virtual.reads": 6.0, "sap.maxdb.resource.monitor.fetched.rows": 6.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 669, "sap.maxdb.resource.monitor.session.id": 16456, "sap.maxdb.resource.monitor.sql.query": "SELECT * FROM DB_STATE WHERE DESCRIPTION = 'Diagnose Analyze' AND (VALUE = 'on' OR VALUE = 'counting')", "sap.maxdb.resource.monitor.executions": 4.0, "sap.maxdb.resource.monitor.qualified.rows": 96.0, "sap.maxdb.resource.monitor.virtual.reads": 4.0, "sap.maxdb.resource.monitor.fetched.rows": 4.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.session.id": 16456, "sap.maxdb.resource.monitor.sql.query": "SELECT VALUE FROM DB_STATE WHERE DESCRIPTION = 'Diagnose Analyze' AND VALUE = 'counting'", "sap.maxdb.resource.monitor.executions": 3.0, "sap.maxdb.resource.monitor.qualified.rows": 72.0, "sap.maxdb.resource.monitor.virtual.reads": 3.0, "sap.maxdb.resource.monitor.fetched.rows": 3.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.session.id": 16456, "sap.maxdb.resource.monitor.sql.query": "SELECT VALUE FROM DB_STATE WHERE DESCRIPTION = 'Monitor data collection' AND VALUE = 'on'", "sap.maxdb.resource.monitor.executions": 3.0, "sap.maxdb.resource.monitor.qualified.rows": 72.0, "sap.maxdb.resource.monitor.virtual.reads": 3.0, "sap.maxdb.resource.monitor.fetched.rows": 3.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.session.id": 16486, "sap.maxdb.resource.monitor.sql.query": "SELECT SCHEMANAME FROM DOMAIN.SCHEMAS ORDER BY SCHEMANAME", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 28.0, "sap.maxdb.resource.monitor.virtual.reads": 20.0, "sap.maxdb.resource.monitor.fetched.rows": 9.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.session.id": 16486, "sap.maxdb.resource.monitor.sql.query": "SELECT CURRENT_SCHEMA FROM DUAL", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 1.0, "sap.maxdb.resource.monitor.virtual.reads": 1.0, "sap.maxdb.resource.monitor.fetched.rows": 1.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 670, "sap.maxdb.resource.monitor.session.id": 16486, "sap.maxdb.resource.monitor.sql.query": "SELECT * FROM LOCKS", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 1.0, "sap.maxdb.resource.monitor.virtual.reads": 1.0, "sap.maxdb.resource.monitor.fetched.rows": 1.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}], "correlation.metrics": ["sap.maxdb.resource.monitor"]}, "errors": []}, "172.16.8.71": {"result": {"sap.maxdb.resource.monitor": [{"sap.maxdb.resource.monitor.id": 6, "sap.maxdb.resource.monitor.sql.query": "SELECT groupname from users where username=user", "sap.maxdb.resource.monitor.executions": 80.0, "sap.maxdb.resource.monitor.qualified.rows": 2240.0, "sap.maxdb.resource.monitor.virtual.reads": 1200.0, "sap.maxdb.resource.monitor.fetched.rows": 80.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 1000}, {"sap.maxdb.resource.monitor.id": 9, "sap.maxdb.resource.monitor.sql.query": "SELECT decode(value,'YES',true,false) from sysinfo.activeconfiguration where parametername = 'EnableCommandMonitor'", "sap.maxdb.resource.monitor.executions": 26.0, "sap.maxdb.resource.monitor.qualified.rows": 25610.0, "sap.maxdb.resource.monitor.virtual.reads": 156.0, "sap.maxdb.resource.monitor.fetched.rows": 26.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 1000}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT decode(constraints,'<DISABLED>', false, true) from SYSINFO.COMMANDMONITORCONSTRAINTS order by id desc LIMIT 1", "sap.maxdb.resource.monitor.executions": 23.0, "sap.maxdb.resource.monitor.qualified.rows": 962.0, "sap.maxdb.resource.monitor.virtual.reads": 122.0, "sap.maxdb.resource.monitor.fetched.rows": 23.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 7, "sap.maxdb.resource.monitor.sql.query": "SELECT usermode from users where username=?", "sap.maxdb.resource.monitor.executions": 6.0, "sap.maxdb.resource.monitor.qualified.rows": 168.0, "sap.maxdb.resource.monitor.virtual.reads": 90.0, "sap.maxdb.resource.monitor.fetched.rows": 6.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 8, "sap.maxdb.resource.monitor.sql.query": "SELECT decode(value,'YES',true,false) from sysinfo.activeconfiguration where parametername = 'UseSharedSQL'", "sap.maxdb.resource.monitor.executions": 5.0, "sap.maxdb.resource.monitor.qualified.rows": 4925.0, "sap.maxdb.resource.monitor.virtual.reads": 30.0, "sap.maxdb.resource.monitor.fetched.rows": 5.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT decode(value,'YES',true,false) from sysinfo.activeconfiguration where parametername = 'UseExtendedTimeMeasurement'", "sap.maxdb.resource.monitor.executions": 5.0, "sap.maxdb.resource.monitor.qualified.rows": 4925.0, "sap.maxdb.resource.monitor.virtual.reads": 30.0, "sap.maxdb.resource.monitor.fetched.rows": 5.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 3, "sap.maxdb.resource.monitor.sql.query": "SELECT schemaname FROM domain.schemas order by schemaname", "sap.maxdb.resource.monitor.executions": 4.0, "sap.maxdb.resource.monitor.qualified.rows": 212.0, "sap.maxdb.resource.monitor.virtual.reads": 40.0, "sap.maxdb.resource.monitor.fetched.rows": 28.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 4, "sap.maxdb.resource.monitor.sql.query": "SELECT current_schema FROM dual", "sap.maxdb.resource.monitor.executions": 4.0, "sap.maxdb.resource.monitor.qualified.rows": 4.0, "sap.maxdb.resource.monitor.virtual.reads": 4.0, "sap.maxdb.resource.monitor.fetched.rows": 4.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 5, "sap.maxdb.resource.monitor.sql.query": "SELECT sessionid from sysinfo.sessions where own = 'YES'", "sap.maxdb.resource.monitor.executions": 4.0, "sap.maxdb.resource.monitor.qualified.rows": 10.0, "sap.maxdb.resource.monitor.virtual.reads": 24.0, "sap.maxdb.resource.monitor.fetched.rows": 4.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT numeric_value from db_state where description = 'Diagnose monitor selectivity'", "sap.maxdb.resource.monitor.executions": 2.0, "sap.maxdb.resource.monitor.qualified.rows": 44.0, "sap.maxdb.resource.monitor.virtual.reads": 12.0, "sap.maxdb.resource.monitor.fetched.rows": 2.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT numeric_value from db_state where description = 'Diagnose monitor time'", "sap.maxdb.resource.monitor.executions": 2.0, "sap.maxdb.resource.monitor.qualified.rows": 44.0, "sap.maxdb.resource.monitor.virtual.reads": 12.0, "sap.maxdb.resource.monitor.fetched.rows": 2.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 10, "sap.maxdb.resource.monitor.sql.query": "SELECT numeric_value from db_state where description = 'Diagnose monitor read'", "sap.maxdb.resource.monitor.executions": 2.0, "sap.maxdb.resource.monitor.qualified.rows": 44.0, "sap.maxdb.resource.monitor.virtual.reads": 12.0, "sap.maxdb.resource.monitor.fetched.rows": 2.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 1, "sap.maxdb.resource.monitor.sql.query": "SELECT true from domain.users where username = ? and usermode = 'SYSDBA'", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 28.0, "sap.maxdb.resource.monitor.virtual.reads": 15.0, "sap.maxdb.resource.monitor.fetched.rows": 1.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 2, "sap.maxdb.resource.monitor.sql.query": "SELECT true from domain.users where username = ? and isreplicationuser = 'YES'", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 21.0, "sap.maxdb.resource.monitor.virtual.reads": 1.0, "sap.maxdb.resource.monitor.fetched.rows": 0.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT distinct substr(resetdate,1,19) from sysinfo.commandstatisticsreset", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 33.0, "sap.maxdb.resource.monitor.virtual.reads": 10.0, "sap.maxdb.resource.monitor.fetched.rows": 1.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT substr(onlinestatedate,1,19) from instance", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 1.0, "sap.maxdb.resource.monitor.virtual.reads": 6.0, "sap.maxdb.resource.monitor.fetched.rows": 1.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}], "correlation.metrics": ["sap.maxdb.resource.monitor"]}, "errors": []}, "172.16.10.84": {"result": {"sap.maxdb.resource.monitor": [{"sap.maxdb.resource.monitor.id": 6, "sap.maxdb.resource.monitor.sql.query": "SELECT groupname from users where username=user", "sap.maxdb.resource.monitor.executions": 80.0, "sap.maxdb.resource.monitor.qualified.rows": 2240.0, "sap.maxdb.resource.monitor.virtual.reads": 1200.0, "sap.maxdb.resource.monitor.fetched.rows": 80.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 1000}, {"sap.maxdb.resource.monitor.id": 9, "sap.maxdb.resource.monitor.sql.query": "SELECT decode(value,'YES',true,false) from sysinfo.activeconfiguration where parametername = 'EnableCommandMonitor'", "sap.maxdb.resource.monitor.executions": 26.0, "sap.maxdb.resource.monitor.qualified.rows": 25610.0, "sap.maxdb.resource.monitor.virtual.reads": 156.0, "sap.maxdb.resource.monitor.fetched.rows": 26.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 1000}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT decode(constraints,'<DISABLED>', false, true) from SYSINFO.COMMANDMONITORCONSTRAINTS order by id desc LIMIT 1", "sap.maxdb.resource.monitor.executions": 23.0, "sap.maxdb.resource.monitor.qualified.rows": 962.0, "sap.maxdb.resource.monitor.virtual.reads": 122.0, "sap.maxdb.resource.monitor.fetched.rows": 23.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 7, "sap.maxdb.resource.monitor.sql.query": "SELECT usermode from users where username=?", "sap.maxdb.resource.monitor.executions": 6.0, "sap.maxdb.resource.monitor.qualified.rows": 168.0, "sap.maxdb.resource.monitor.virtual.reads": 90.0, "sap.maxdb.resource.monitor.fetched.rows": 6.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 8, "sap.maxdb.resource.monitor.sql.query": "SELECT decode(value,'YES',true,false) from sysinfo.activeconfiguration where parametername = 'UseSharedSQL'", "sap.maxdb.resource.monitor.executions": 5.0, "sap.maxdb.resource.monitor.qualified.rows": 4925.0, "sap.maxdb.resource.monitor.virtual.reads": 30.0, "sap.maxdb.resource.monitor.fetched.rows": 5.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT decode(value,'YES',true,false) from sysinfo.activeconfiguration where parametername = 'UseExtendedTimeMeasurement'", "sap.maxdb.resource.monitor.executions": 5.0, "sap.maxdb.resource.monitor.qualified.rows": 4925.0, "sap.maxdb.resource.monitor.virtual.reads": 30.0, "sap.maxdb.resource.monitor.fetched.rows": 5.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 3, "sap.maxdb.resource.monitor.sql.query": "SELECT schemaname FROM domain.schemas order by schemaname", "sap.maxdb.resource.monitor.executions": 4.0, "sap.maxdb.resource.monitor.qualified.rows": 212.0, "sap.maxdb.resource.monitor.virtual.reads": 40.0, "sap.maxdb.resource.monitor.fetched.rows": 28.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 4, "sap.maxdb.resource.monitor.sql.query": "SELECT current_schema FROM dual", "sap.maxdb.resource.monitor.executions": 4.0, "sap.maxdb.resource.monitor.qualified.rows": 4.0, "sap.maxdb.resource.monitor.virtual.reads": 4.0, "sap.maxdb.resource.monitor.fetched.rows": 4.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 5, "sap.maxdb.resource.monitor.sql.query": "SELECT sessionid from sysinfo.sessions where own = 'YES'", "sap.maxdb.resource.monitor.executions": 4.0, "sap.maxdb.resource.monitor.qualified.rows": 10.0, "sap.maxdb.resource.monitor.virtual.reads": 24.0, "sap.maxdb.resource.monitor.fetched.rows": 4.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT numeric_value from db_state where description = 'Diagnose monitor selectivity'", "sap.maxdb.resource.monitor.executions": 2.0, "sap.maxdb.resource.monitor.qualified.rows": 44.0, "sap.maxdb.resource.monitor.virtual.reads": 12.0, "sap.maxdb.resource.monitor.fetched.rows": 2.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT numeric_value from db_state where description = 'Diagnose monitor time'", "sap.maxdb.resource.monitor.executions": 2.0, "sap.maxdb.resource.monitor.qualified.rows": 44.0, "sap.maxdb.resource.monitor.virtual.reads": 12.0, "sap.maxdb.resource.monitor.fetched.rows": 2.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 10, "sap.maxdb.resource.monitor.sql.query": "SELECT numeric_value from db_state where description = 'Diagnose monitor read'", "sap.maxdb.resource.monitor.executions": 2.0, "sap.maxdb.resource.monitor.qualified.rows": 44.0, "sap.maxdb.resource.monitor.virtual.reads": 12.0, "sap.maxdb.resource.monitor.fetched.rows": 2.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 1, "sap.maxdb.resource.monitor.sql.query": "SELECT true from domain.users where username = ? and usermode = 'SYSDBA'", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 28.0, "sap.maxdb.resource.monitor.virtual.reads": 15.0, "sap.maxdb.resource.monitor.fetched.rows": 1.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 2, "sap.maxdb.resource.monitor.sql.query": "SELECT true from domain.users where username = ? and isreplicationuser = 'YES'", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 21.0, "sap.maxdb.resource.monitor.virtual.reads": 1.0, "sap.maxdb.resource.monitor.fetched.rows": 0.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT distinct substr(resetdate,1,19) from sysinfo.commandstatisticsreset", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 33.0, "sap.maxdb.resource.monitor.virtual.reads": 10.0, "sap.maxdb.resource.monitor.fetched.rows": 1.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}, {"sap.maxdb.resource.monitor.id": 0, "sap.maxdb.resource.monitor.sql.query": "SELECT substr(onlinestatedate,1,19) from instance", "sap.maxdb.resource.monitor.executions": 1.0, "sap.maxdb.resource.monitor.qualified.rows": 1.0, "sap.maxdb.resource.monitor.virtual.reads": 6.0, "sap.maxdb.resource.monitor.fetched.rows": 1.0, "sap.maxdb.resource.monitor.vwaits": 0.0, "sap.maxdb.resource.monitor.physical.io.ops": 0.0, "sap.maxdb.resource.monitor.runtime.ms": 0}], "correlation.metrics": ["sap.maxdb.resource.monitor"]}, "errors": []}}