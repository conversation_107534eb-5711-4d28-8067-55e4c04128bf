/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			        Notes
 *  28-Feb-2025		Darshan Parmar		    MOTADATA-5215: SonarQube Suggestions Resolution
 *  10-Mar-2025		Pruthviraj Jadeja		MOTADATA-5331 : ignore netroute metric in column mapper
 *  21-Apr-2025     <PERSON><PERSON>            Added Support for Status Flap Metric
 *  June-5-2025     <PERSON><PERSON>            Added Support for widget/Alert for Netroute.
 */
package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.config.ConfigConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.io.File;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.EVENT;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TYPE;
import static org.apache.http.HttpStatus.SC_INTERNAL_SERVER_ERROR;

public class Misc extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(Misc.class, MOTADATA_API, "Misc API");

    public Misc()
    {
        super("misc", MiscConfigStore.getStore(), LOGGER, new CipherUtil());
    }

    @Override
    public void init(Router router)
    {
        router.get("/models").handler(this::getModels);

        router.get("/archived-status").handler(this::getArchivedStatus);

        router.get("/column-mappers").handler(this::getColumnMappers);

        router.get("/indexable-columns").handler(this::getIndexableColumns);

        router.get("/flow-interfaces").handler(this::getFlowInterfaces);

        router.get("/sources").handler(routingContext -> send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, EventSourceConfigStore.getStore().getItemsByMultiValueField(EVENT_TYPE, routingContext.request().getParam("group.type")))));

        router.get("/vendors").handler(routingContext -> send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, SNMPTrapVendorCacheStore.getStore().getItems())));

        router.get("/firmware-files").handler(this::getFirmwareFiles);

        router.get("/md5-checksum").handler(this::generateMD5Checksum);

        router.get("/counters").handler(this::getCounters);

        router.get("/countries").handler(routingContext -> send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, GeoDBUtil.getCountries())));

    }

    private void getArchivedStatus(RoutingContext routingContext)
    {

        try
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, new JsonObject().put("archive.enabled", MotadataConfigUtil.archivedObjectEnabled())));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getModels(RoutingContext routingContext)
    {
        try
        {
            var filter = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            if (filter != null && !filter.isEmpty())
            {
                //for topology plugin return make model based on vendor selection
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, new JsonArray(SNMPDeviceCatalogConfigStore.getStore().flatMap().values().stream().filter(item -> filter.getString(AIOpsObject.OBJECT_VENDOR).equalsIgnoreCase(item.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_VENDOR)))
                                .map(item -> item.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_MODEL)).distinct().collect(Collectors.toList()))));
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getColumnMappers(RoutingContext routingContext)
    {
        try
        {
            var filter = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            if (filter.containsKey(VisualizationConstants.VISUALIZATION_GROUP_TYPE))
            {
                if (filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC.getName()) || filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName()) || filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.NETROUTE_METRIC.getName()))
                {
                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
                    {
                        if (reply.succeeded())
                        {
                            var event = reply.result().body();

                            if (filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC.getName()))
                            {
                                var result = new JsonObject();

                                if (filter.containsKey(ENTITIES))
                                {
                                    var pluginIds = MetricConfigStore.getStore().getMetricPluginsByObjects(filter.getJsonArray(ENTITIES));

                                    for (var entry : event.getJsonObject(DatastoreConstants.METRIC_COLUMNS).getMap().entrySet())
                                    {
                                        //disregard the motadata health agent counter and only use the health screen metrics. metrics starting with the name "motadata" should be used instead.

                                        var ids = JsonObject.mapFrom(entry.getValue()).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                                        if (!entry.getKey().contains(((JsonObject) entry.getValue()).getString(DatastoreConstants.MAPPER_INSTANCE) + VisualizationConstants.RESERVED_METRICS)
                                                && ids.stream().anyMatch(pluginIds::contains) &&
                                                !ids.contains(DatastoreConstants.PluginId.NETROUTE_METRIC.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.NETROUTE_STATUS_DURATION_METRIC.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.SLO_INSTANCE.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.SLO.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.SLO_FLAP.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.SLO_INSTANCE_FLAP.getName()))
                                        {
                                            result.put(entry.getKey(), entry.getValue());
                                        }
                                    }
                                }
                                else
                                {

                                    for (var entry : event.getJsonObject(DatastoreConstants.METRIC_COLUMNS).getMap().entrySet())
                                    {
                                        //disregard the motadata health agent counter and only use the health screen metrics. metrics starting with the name "motadata" should be used instead.

                                        var ids = JsonObject.mapFrom(entry.getValue()).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                                        if (!entry.getKey().contains(((JsonObject) entry.getValue()).getString(DatastoreConstants.MAPPER_INSTANCE) + VisualizationConstants.RESERVED_METRICS)
                                                && !JsonObject.mapFrom(entry.getValue()).containsKey(DatastoreConstants.MAPPER_DURATION) && !ids.contains(DatastoreConstants.PluginId.NETROUTE_METRIC.getName()) && !ids.contains(DatastoreConstants.PluginId.NETROUTE_STATUS_DURATION_METRIC.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.SLO_INSTANCE.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.SLO.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.SLO_FLAP.getName()) &&
                                                !ids.contains(DatastoreConstants.PluginId.SLO_INSTANCE_FLAP.getName())
                                        )
                                        {
                                            result.put(entry.getKey(), entry.getValue());
                                        }
                                    }
                                }

                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                        .put(GlobalConstants.RESULT, result));
                            }
                            else if (filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.NETROUTE_METRIC.getName()))
                            {
                                var result = new JsonObject();

                                if (filter.containsKey(ENTITIES))
                                {
                                    var pluginIds = MetricConfigStore.getStore().getMetricPluginsByObjects(filter.getJsonArray(ENTITIES));

                                    for (var entry : event.getJsonObject(DatastoreConstants.METRIC_COLUMNS).getMap().entrySet())
                                    {
                                        //disregard the motadata health agent counter and only use the health screen metrics. metrics starting with the name "motadata" should be used instead.

                                        var ids = JsonObject.mapFrom(entry.getValue()).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                                        if (!entry.getKey().contains(((JsonObject) entry.getValue()).getString(DatastoreConstants.MAPPER_INSTANCE) + VisualizationConstants.RESERVED_METRICS)
                                                && ids.stream().anyMatch(pluginIds::contains) && (ids.contains(DatastoreConstants.PluginId.NETROUTE_METRIC.getName())))
                                        {
                                            result.put(entry.getKey(), entry.getValue());
                                        }
                                    }
                                }
                                else
                                {
                                    for (var entry : event.getJsonObject(DatastoreConstants.METRIC_COLUMNS).getMap().entrySet())
                                    {
                                        //disregard the motadata health agent counter and only use the health screen metrics. metrics starting with the name "motadata" should be used instead.

                                        var ids = JsonObject.mapFrom(entry.getValue()).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                                        if (!entry.getKey().contains(((JsonObject) entry.getValue()).getString(DatastoreConstants.MAPPER_INSTANCE) + VisualizationConstants.RESERVED_METRICS)
                                                && !JsonObject.mapFrom(entry.getValue()).containsKey(DatastoreConstants.MAPPER_DURATION) && ids.contains(DatastoreConstants.PluginId.NETROUTE_METRIC.getName()))
                                        {
                                            result.put(entry.getKey(), entry.getValue());
                                        }
                                    }
                                }

                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                        .put(GlobalConstants.RESULT, result));
                            }
                            else if (filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName()))
                            {
                                var result = new JsonObject();

                                event.getJsonObject(DatastoreConstants.METRIC_COLUMNS).getMap().forEach((key, value) ->
                                {
                                    try
                                    {
                                        if (JsonObject.mapFrom(value).getString(DatastoreConstants.MAPPER_STATUS).equalsIgnoreCase(YES))
                                        {
                                            var ids = JsonObject.mapFrom(value).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                                            if (filter.getString(VisualizationConstants.VISUALIZATION_CATEGORY) != null && filter.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GAUGE.getName()))
                                            {
                                                for (var statusType : StatusType.values())
                                                {

                                                    var column = EMPTY_VALUE;

                                                    if (key.contains(INSTANCE_SEPARATOR))
                                                    {
                                                        column = key.split(INSTANCE_SEPARATOR)[0] + INSTANCE_SEPARATOR + CommonUtil.getString(statusType).toLowerCase() + ".count";
                                                    }

                                                    else
                                                    {

                                                        column = key.split("\\.")[0] + "." + CommonUtil.getString(statusType).toLowerCase() + ".count";
                                                    }

                                                    if (!result.containsKey(column) && !ids.contains(DatastoreConstants.PluginId.NETROUTE_STATUS_DURATION_METRIC.getName()))
                                                    {
                                                        result.put(column, value);
                                                    }
                                                }
                                            }
                                            else if (filter.getString(VisualizationConstants.VISUALIZATION_TYPE) != null && filter.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_REPORT))
                                            {
                                                var column = EMPTY_VALUE;

                                                if (key.contains(INSTANCE_SEPARATOR))
                                                {
                                                    column = key.split(INSTANCE_SEPARATOR)[0].toLowerCase();
                                                }
                                                else
                                                {
                                                    column = APIConstants.Entity.OBJECT.getName().toLowerCase();
                                                }

                                                if (!result.containsKey(column) && !ids.contains(DatastoreConstants.PluginId.NETROUTE_STATUS_DURATION_METRIC.getName()))
                                                {
                                                    result.put(column, value);
                                                }
                                            }
                                            else
                                            {
                                                for (var statusType : StatusType.values())
                                                {
                                                    var column = EMPTY_VALUE;

                                                    try
                                                    {
                                                        if (key.contains(INSTANCE_SEPARATOR))
                                                        {
                                                            var tokens = key.split(INSTANCE_SEPARATOR);

                                                            column = tokens[0] + INSTANCE_SEPARATOR + CommonUtil.getString(statusType).toLowerCase() + "time." + tokens[1].split("\\.")[1];
                                                        }
                                                        else
                                                        {
                                                            var tokens = key.split("\\.");

                                                            column = tokens[0] + DOT_SEPARATOR + CommonUtil.getString(statusType).toLowerCase() + "time." + tokens[2];
                                                        }

                                                        if (!result.containsKey(column) && !ids.contains(DatastoreConstants.PluginId.NETROUTE_STATUS_DURATION_METRIC.getName()))
                                                        {
                                                            result.put(column, value);
                                                        }
                                                    }

                                                    catch (Exception exception)
                                                    {
                                                        LOGGER.error(exception);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ignored)
                                    {
                                        // ignore
                                    }
                                });

                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                        .put(GlobalConstants.RESULT, result));
                            }
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                    .put(STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, reply.cause().getMessage()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace())));
                        }
                    });
                }

                /*
                 * Handles the retrieval of status flap instances for visualization.
                 * This method checks if the visualization group type is `status.flap`
                 * and prepares a result containing the status flap instances, we will be taking monitor and interface now.
                 */
                else if (filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()))
                {
                    var result = new JsonObject();

                    result.put(APIConstants.Entity.OBJECT.getName().toLowerCase(), APIConstants.Entity.OBJECT.getName().toLowerCase());

                    for (var entry : MotadataConfigUtil.getStatusFlapInstances())
                    {
                        result.put(entry, entry);
                    }

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.RESULT, result));
                }
                else
                {
                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, "", reply ->
                    {
                        if (reply.succeeded())
                        {
                            var event = reply.result().body();

                            if (filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()))
                            {
                                //in log plugin id is always greater than DBConstants.PluginId.LOG_EVENT.ordinal() so loading that specific columns only which contains greater from 600000 or 499999
                                var result = new JsonObject();

                                for (var entry : event.getJsonObject(DatastoreConstants.EVENT_COLUMNS).getMap().entrySet())
                                {
                                    if (JsonObject.mapFrom(entry.getValue()).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).stream().map(CommonUtil::getInteger).anyMatch(id -> id > DatastoreConstants.PluginId.LOG_EVENT.getName() || id == DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() || id == DatastoreConstants.PluginId.LOG_EVENT_STAT.getName()))
                                    {
                                        result.put(entry.getKey(), entry.getValue());
                                    }
                                }

                                result.remove(EVENT);

                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                        .put(GlobalConstants.RESULT, result));
                            }
                            else if (filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))
                            {
                                var result = new JsonObject();

                                for (var entry : event.getJsonObject(DatastoreConstants.EVENT_COLUMNS).getMap().entrySet())
                                {
                                    if (JsonObject.mapFrom(entry.getValue()).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).stream().map(CommonUtil::getInteger).anyMatch(id -> id == DatastoreConstants.PluginId.FLOW_EVENT.getName() || id == DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName()))
                                    {
                                        result.put(entry.getKey(), entry.getValue());
                                    }
                                }

                                result.remove(EVENT);

                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                        .put(GlobalConstants.RESULT, result));
                            }
                            else if (filter.getString(VisualizationConstants.VISUALIZATION_GROUP_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP.getName()))
                            {
                                var result = new JsonObject();

                                for (var entry : event.getJsonObject(DatastoreConstants.EVENT_COLUMNS).getMap().entrySet())
                                {
                                    if (JsonObject.mapFrom(entry.getValue()).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).stream().map(CommonUtil::getInteger).anyMatch(id -> id == DatastoreConstants.PluginId.TRAP_EVENT.getName()))
                                    {
                                        result.put(entry.getKey(), entry.getValue());
                                    }
                                }

                                result.remove(EVENT);

                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                        .put(GlobalConstants.RESULT, result));
                            }
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                    .put(STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, reply.cause().getMessage()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(ERROR, CommonUtil.formatStackTrace(reply.cause().getStackTrace())));
                        }
                    });
                }
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getFlowInterfaces(RoutingContext routingContext)
    {
        try
        {
            var entities = new JsonObject();

            var sources = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER))).getJsonArray(GlobalConstants.ENTITIES);

            var instances = new HashMap<String, JsonObject>(); // for caching provisioned event source device interfaces to merge collections

            if (sources != null && !sources.isEmpty())
            {
                for (var i = 0; i < sources.size(); i++)
                {
                    var source = sources.getString(i);

                    if (source != null)
                    {
                        var objects = MetricConfigStore.getStore().getNetworkInterfacesBySource(source);

                        if (objects != null && !objects.isEmpty())
                        {
                            for (var index = 0; index < objects.size(); index++)
                            {
                                var object = objects.getJsonObject(index);

                                instances.put(source + GlobalConstants.SEPARATOR + object.getString(NMSConstants.INTERFACE_INDEX), object);
                            }

                            var items = FlowSamplingRateConfigStore.getStore().getItemsByValue(EventBusConstants.EVENT_SOURCE, source);

                            for (var index = 0; index < items.size(); index++)
                            {
                                var item = items.getJsonObject(index);

                                var samplingKey = item.getString(EventBusConstants.EVENT_SOURCE) + GlobalConstants.SEPARATOR + item.getString(NMSConstants.INTERFACE_INDEX);

                                if (instances.containsKey(samplingKey) && instances.get(samplingKey) != null)
                                {
                                    entities.put(instances.get(samplingKey).getString(NMSConstants.INTERFACE_NAME), instances.get(samplingKey).getString(NMSConstants.INTERFACE_INDEX));
                                }
                            }
                        }
                        else
                        {
                            var items = FlowSamplingRateConfigStore.getStore().getItemsByValue(EventBusConstants.EVENT_SOURCE, source);

                            for (var interfaceIndex = 0; interfaceIndex < items.size(); interfaceIndex++)
                            {
                                entities.put("interface-index-" + items.getJsonObject(interfaceIndex).getString(NMSConstants.INTERFACE_INDEX), items.getJsonObject(interfaceIndex).getString(NMSConstants.INTERFACE_INDEX));
                            }
                        }
                    }
                }
            }

            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    private void getIndexableColumns(RoutingContext routingContext)
    {
        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_INDEXABLE_COLUMN_QUERY, new JsonObject().put(PLUGIN_ID, JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER))).getJsonArray(PLUGIN_ID)), reply ->
                    send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).mergeIn(reply.result().body())));

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    /**
     * Method will send file name, file size of all the files present in CONFIG_FIRMWARE_DEFAULT_DIR
     *
     * @param routingContext API Routing Context
     */
    private void getFirmwareFiles(RoutingContext routingContext)
    {
        try
        {
            var entities = new JsonArray();

            for (var file : Objects.requireNonNull(new File(ConfigConstants.CONFIG_FIRMWARE_DEFAULT_DIR).listFiles()))
            {
                entities.add(new JsonObject()
                        .put(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILE_NAME, file.getName())
                        .put(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILE_SIZE, file.length()));
            }

            this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILES, entities));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    /**
     * Method used to generate MD5 checksum for the particular file only
     * Before this method, we are creating MD5 for all the files present in the directory which causing
     * API slowness
     * Provided required permissions to the firmware image file, as it is required when we copy file from server to particular device using TFTP
     *
     * @param routingContext context
     */
    private void generateMD5Checksum(RoutingContext routingContext)
    {
        try
        {

            var file = new File(ConfigConstants.CONFIG_FIRMWARE_DEFAULT_DIR + PATH_SEPARATOR + routingContext.request().getParam(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILE_NAME));

            CommonUtil.setFilePermissions(file, RW_PERMISSION); // As during firmware upgrade operation we need permission to set otherwise file won't be copied.

            ConfigConstants.getFirmwareFile(file).onComplete(result ->
            {
                if (result.succeeded())
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILES, result.result()));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION, result.cause().getMessage())));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getCounters(RoutingContext routingContext)
    {
        try
        {
            var params = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            var instance = params.getString(INSTANCE);

            var filters = new HashSet<String>();

            var counters = new HashSet<String>();

            var result = new JsonObject();

            if (NMSConstants.PLUGIN_BY_INSTANCES.containsKey(instance))
            {
                var plugins = NMSConstants.PLUGIN_BY_INSTANCES.get(instance);

                result.put(VisualizationConstants.PLUGINS, plugins);

                for (var plugin : plugins)
                {
                    counters.add(NMSConstants.DISCOVERABLE_INSTANCES.get(plugin.toString()));

                    MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, plugin.toString()).stream()
                            .map(item -> JsonObject.mapFrom(item).getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS))
                            .filter(Objects::nonNull)
                            .forEach(objects -> filters.addAll(objects.getJsonObject(0).getMap().keySet()));
                }

                // remove unnecessary fields
                filters.remove(AIOpsObject.OBJECT_TYPE);
                filters.remove(STATUS);
            }

            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, result.put(COUNTERS, counters).put(FILTERS, filters)));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            APIUtil.sendResponse(exception, routingContext);
        }
    }
}
