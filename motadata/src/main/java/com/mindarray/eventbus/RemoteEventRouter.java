/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*

 *   Change Logs:
 *   Date          Author              Notes
 *   2025-02-06    <PERSON><PERSON> Sharma      Added Compliance as Remote Event
 *   19-Feb-2025  Chandresh		 MOTADATA-3680: Added command variable replacement for non-custom plugins
 *  28-Feb-2025		Darshan Parmar	MOTADATA-5215: SonarQube Suggestions Resolution
 */

package com.mindarray.eventbus;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.manager.MotadataAppManager;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mindarray.ErrorCodes.*;
import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.db.DBConstants.TBL_REMOTE_EVENT_PROCESSOR;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * RemoteEventRouter is responsible for routing events to appropriate remote event processors in a distributed system.
 * <p>
 * This class serves as a central hub for event distribution, handling various types of events such as discovery,
 * metric polling, rediscovery, topology, and plugin engine events. It implements load balancing across multiple
 * event processors based on their current load, latency, and availability.
 * <p>
 * Key responsibilities include:
 * - Routing events to the most suitable event processor based on load balancing algorithm
 * - Tracking event acknowledgements and handling timeouts
 * - Managing event processor registration and status
 * - Collecting and analyzing performance metrics for event processors
 * - Handling failover when an event processor becomes unavailable
 * <p>
 * The router maintains various caches and statistics to optimize routing decisions and provide
 * health information about the event processing system.
 */
public class RemoteEventRouter extends AbstractVerticle
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(RemoteEventRouter.class, GlobalConstants.MOTADATA_EVENT_BUS, "Remote Event Router");

    /**
     * Maximum number of samples to keep for statistics (latency, pending events, idle workers)
     */
    private static final int MAX_SAMPLES = 100;

    /**
     * Maximum time in milliseconds to wait for event acknowledgement before taking action
     */
    private final long maxEventAcknowledgeTimeoutMillis = MotadataConfigUtil.getEventAcknowledgeTimeoutMillis();

    /**
     * Stores remaining event processors for failover.
     * When multiple event processors are selected for an event (discovery/poll/rediscover),
     * one is chosen for initial processing and the rest are stored here for failover.
     * Key: Event ID, Value: Array of remaining event processor IDs
     */
    private final Map<Long, JsonArray> remainingEventProcessors = new HashMap<>();

    /**
     * Stores event contexts for all active events.
     * Key: Event ID, Value: Event context as JsonObject
     */
    private final Map<Long, JsonObject> eventContexts = new HashMap<>();

    /**
     * Tracks event acknowledgement timestamps.
     * Key: Event ID, Value: Timestamp when event was sent (for timeout calculation)
     */
    private final Map<Long, Long> eventAcks = new HashMap<>();

    /**
     * Stores original message contexts for events requiring replies.
     * Key: Event ID, Value: Original message that needs a reply
     */
    private final Map<Long, Message<JsonObject>> eventReplyContexts = new HashMap<>();

    /**
     * Tracks latency statistics by event processor and event type.
     * Stores the last 100 latency values for each processor/event type combination.
     * Key format: "processorUUID + separator + eventType"
     * For metric polling, event types include: metric, batch.metric, availability, etc.
     */
    private final Map<String, List<Float>> latenciesByEventProcessor = new HashMap<>();

    /**
     * Tracks pending events statistics by event processor and event type.
     * Stores the last 100 pending event counts for each processor/event type combination.
     * Key format: "processorUUID + separator + eventType"
     */
    private final Map<String, List<Float>> pendingEventsByEventProcessor = new HashMap<>();

    /**
     * Tracks idle worker statistics by event processor and event type.
     * Stores the last 100 idle worker counts for each processor/event type combination.
     * Key format: "processorUUID + separator + eventType"
     */
    private final Map<String, List<Float>> idleWorkersByEventProcessor = new HashMap<>();

    /**
     * Tracks total pending events by event processor.
     * Key: Event processor UUID, Value: List of event IDs pending for this processor
     */
    private final Map<String, List<Long>> eventsByEventProcessor = new HashMap<>();

    /**
     * Stores event processor configuration details.
     * Key: Event processor ID, Value: Event processor configuration
     */
    private final Map<Long, JsonObject> itemsByEventProcessor = new HashMap<>();

    /**
     * Initializes the RemoteEventRouter verticle.
     * <p>
     * This method sets up event bus consumers for various event types and initializes
     * the event processor registry. It also starts a periodic timer to track event
     * acknowledgements and handle timeouts.
     * <p>
     * The method handles the following event types:
     * - EVENT_ENGINE_STATS: Publishes statistics about the event engine
     * - EVENT_CHANGE_NOTIFICATION: Handles notifications about event processor changes
     * - EVENT_REGISTRATION: Processes event processor registration requests
     * - EVENT_ACKNOWLEDGEMENT: Processes event acknowledgements
     * - EVENT_CLEAR_CONTEXT: Clears event contexts
     * - EVENT_HEALTH_MANAGER_NOTIFICATION: Processes health statistics from event processors
     * - Various event types (discovery, metric poll, etc.): Routes events to appropriate processors
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_ENGINE_STATS, message ->
                vertx.eventBus().publish(EVENT_ENGINE_STATS_RESPONSE, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                        .put(EventBusConstants.ENGINE_TYPE, config().getString(EVENT_TYPE))
                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                        .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, eventContexts.size())
                                //.put("pending.event.processors", eventsByEventProcessor.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, value -> value.getValue().size())))
                                .put("pending.event.acks", eventAcks.size()))));

        var remoteEventProcessors = RemoteEventProcessorConfigStore.getStore().getItems();

        for (var index = 0; index < remoteEventProcessors.size(); index++)
        {
            update(remoteEventProcessors.getJsonObject(index), false);
        }

        publish(null);

        //Execute every 2000 millis for to track event acknowledgement in event processor
        vertx.setPeriodic(2000, timer -> trackEvent());

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            try
            {
                switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {
                    case DELETE_REMOTE_POLLER -> itemsByEventProcessor.remove(event.getLong(ID));

                    case ADD_REMOTE_POLLER, UPDATE_REMOTE_POLLER ->
                            RemoteEventProcessorConfigStore.getStore().addItem(event.getLong(ID)).onComplete(result ->
                            {
                                try
                                {
                                    update(event.getJsonObject(EVENT_CONTEXT), true);

                                    RemoteEventProcessorCacheStore.getStore().updateDuration(event.getLong(ID), DateTimeUtil.currentSeconds());
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });

                    default ->
                    {
                        // do nothing
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_REGISTRATION, message ->
        {
            try
            {
                register(message.body());
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_ACKNOWLEDGEMENT, message ->
        {
            try
            {
                var event = message.body();

                var eventId = event.getLong(EVENT_ID);

                if (eventAcks.containsKey(eventId))
                {
                    eventAcks.remove(eventId);

                    EventBusConstants.updateEvent(eventId, String.format(InfoMessageConstants.EVENT_TRACKER_ACKNOWLEDGEMENT_RECEIVED, event.containsKey(Agent.AGENT_UUID)
                            ? event.getString(Agent.AGENT_UUID) : event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), DateTimeUtil.timestamp()));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CLEAR_CONTEXT, message ->
        {
            try
            {
                clear(message.body(), true);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_HEALTH_MANAGER_NOTIFICATION, message ->
        {
            try
            {
                var event = message.body();

                var eventProcessor = event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID);

                var statistics = event.getJsonObject(HealthUtil.HEALTH_STATS);

                if (event.getString(ENGINE_TYPE).equalsIgnoreCase(EVENT_METRIC_POLL))
                {
                    track(eventProcessor + SEPARATOR + RemoteEvent.METRIC.name(), statistics.getInteger(HealthUtil.PENDING_EVENTS), statistics.getInteger(HealthUtil.IDLE_WORKERS));

                    track(eventProcessor + SEPARATOR + RemoteEvent.BATCH_METRIC.name(), statistics.getInteger(HealthUtil.PENDING_BATCH_EVENTS), statistics.getInteger(HealthUtil.IDLE_WORKERS));

                    for (var index = 0; index < statistics.getJsonArray(Metric.METRIC_CATEGORY).size(); index++)
                    {
                        var category = statistics.getJsonArray(Metric.METRIC_CATEGORY).getJsonObject(index);

                        if (category.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()))
                        {
                            track(eventProcessor + SEPARATOR + RemoteEvent.OBJECT_STATUS.name(), category.getInteger("metric.category.pending.events"), category.getInteger("metric.category.idle.workers"));
                        }
                        else if (category.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase("network.service"))
                        {
                            track(eventProcessor + SEPARATOR + RemoteEvent.NETWORK_SERVICE.name(), category.getInteger("metric.category.pending.events"), category.getInteger("metric.category.idle.workers"));
                        }
                    }
                }
                else
                {
                    track(eventProcessor + SEPARATOR + getRemoteEvent(event), statistics.getInteger(HealthUtil.PENDING_EVENTS), statistics.getInteger(HealthUtil.IDLE_WORKERS));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
        {
            try
            {
                var event = message.body();

                if (event != null && CommonUtil.isNotNullOrEmpty(event.getString(EVENT_TYPE)))
                {
                    var eventId = event.getLong(EVENT_ID);

                    var eventType = event.getString(EVENT_TYPE);

                    if (eventType.equalsIgnoreCase(EVENT_DISCOVERY) || eventType.equalsIgnoreCase(EVENT_METRIC_POLL) || eventType.equalsIgnoreCase(EVENT_REDISCOVER)
                            || eventType.equalsIgnoreCase(EVENT_PLUGIN_ENGINE) || eventType.equalsIgnoreCase(EVENT_TOPOLOGY)
                            || eventType.equalsIgnoreCase(EVENT_AGENT_CONFIGURATION_CHANGE) || eventType.equalsIgnoreCase(EVENT_CONFIG_UPGRADE_MANAGER)) // only for manual provision of process/service/file/directory rediscover objects
                    {
                        if (!event.containsKey(PluginEngineConstants.PLUGIN_ENGINE))
                        {
                            var pluginEngine = PluginEngineConstants.getPluginEngine(event);

                            if (pluginEngine != null)
                            {
                                event.put(PluginEngineConstants.PLUGIN_ENGINE, pluginEngine);

                                // update plugin.engine type in event tracker context
                                vertx.eventBus().send(EVENT_PROGRESS_UPDATE, new JsonObject().put(EVENT_ID, eventId)
                                        .put(EVENT_CONTEXT, event));
                            }
                        }

                        // MOTADATA-3680
                        if (!(PluginEngineConstants.isCustomPlugin(event)) && event.containsKey(PluginEngineConstants.COMMAND))
                        {
                            event.put(PluginEngineConstants.COMMAND, CommonUtil.replaceScriptVariables(PluginEngineConstants.COMMAND, event));
                        }

                        if (NMSConstants.DiscoveryMethod.valueOf(event.containsKey(AIOpsObject.OBJECT_DISCOVERY_METHOD) ? event.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD) : event.getString(Metric.METRIC_DISCOVERY_METHOD)) == NMSConstants.DiscoveryMethod.AGENT)
                        {
                            var agentUUID = AgentConfigStore.getStore().getAgentUUID(event.getLong(AIOpsObject.OBJECT_AGENT));

                            if (agentUUID != null)
                            {
                                var item = ObjectStatusCacheStore.getStore().getItem(ObjectConfigStore.getStore().getItemByAgentId(event.getLong(AIOpsObject.OBJECT_AGENT)).getLong(ID));

                                if (item == null || item.equalsIgnoreCase(STATUS_UP)) // in case of log/packet agent cache is clear as we are not maintain status at that time
                                {
                                    // for metric plugin / runbook plugin event we will replace variables from master side and pass it to agent so in agent side only we have to spawn a process after script dumped into a file
                                    if (PluginEngineConstants.isCustomPlugin(event))
                                    {
                                        event.put(PluginEngineConstants.SCRIPT, CommonUtil.replaceScriptVariables(event, true));
                                    }

                                    else if (event.containsKey(PluginEngineConstants.PARSING_SCRIPT))
                                    {
                                        event.put(PluginEngineConstants.PARSING_SCRIPT, CommonUtil.replaceScriptVariables(event, false));
                                    }

                                    eventContexts.put(eventId, event);

                                    if (event.containsKey(EVENT_REPLY) && event.getString(EVENT_REPLY).equalsIgnoreCase(YES))
                                    {
                                        eventReplyContexts.put(eventId, message);
                                    }

                                    event.put(SYSTEM_LOG_LEVEL, CommonUtil.getLogLevel());

                                    //put into event acknowledgements for tracking purpose...
                                    eventAcks.put(eventId, System.currentTimeMillis());

                                    vertx.eventBus().send(EVENT_PUBLICATION, event.put(Agent.AGENT_UUID, agentUUID)
                                            .put(EVENT_TOPIC, AGENT_TOPIC).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()));
                                }

                                else
                                {
                                    LOGGER.warn(String.format("agent %s down: aborting event %s", event.getLong(AIOpsObject.OBJECT_AGENT), eventId));

                                    vertx.eventBus().send(EVENT_ABORT, new JsonObject().put(EVENT_ID, eventId)
                                            .put(MESSAGE, "agent is down").put(ERROR_CODE, ErrorCodes.ERROR_CODE_AGENT_DOWN));
                                }

                            }

                            else
                            {
                                LOGGER.warn(String.format("agent %s deleted: aborting event %s", event.getLong(AIOpsObject.OBJECT_AGENT), eventId));

                                vertx.eventBus().send(EVENT_ABORT, new JsonObject().put(EVENT_ID, eventId)
                                        .put(MESSAGE, "agent not found in the store").put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND));

                            }

                        }

                        else
                        {
                            var eventProcessors = event.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS, null);

                            if (eventProcessors == null || eventProcessors.isEmpty())
                            {
                                eventProcessors = new JsonArray(new ArrayList<>(this.itemsByEventProcessor.keySet()));
                            }

                            route(event.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, eventProcessors).put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC), message);
                        }
                    }
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        promise.complete();
    }

    /**
     * Tracks event acknowledgements and handles timeouts.
     * <p>
     * This method is called periodically to check for events that have not been acknowledged
     * within the configured timeout period. For timed-out events, it:
     * <p>
     * 1. Removes the event from the acknowledgement tracking map
     * 2. Notifies the original event processor to discard the event
     * 3. If there are remaining event processors available for failover:
     * - Updates the event tracker with timeout information
     * - Routes the event to the next available event processor
     * 4. If no more event processors are available:
     * - Aborts the event with an "attempt exceeded" error
     * - Cleans up associated resources
     * <p>
     * This mechanism ensures events are not lost due to event processor failures
     * and implements the failover strategy for the event routing system.
     */
    private void trackEvent()
    {
        try
        {

            var iterator = this.eventAcks.entrySet().iterator();

            while (iterator.hasNext())
            {
                var event = iterator.next();

                //check current time - event sending time is exceed to default timeout if it satisfies then select another event processor  from remaining event processor and send back
                if ((System.currentTimeMillis() - event.getValue()) >= maxEventAcknowledgeTimeoutMillis)
                {
                    var context = eventContexts.get(event.getKey());

                    iterator.remove();

                    if (context != null)
                    {
                        var uuid = context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID);

                        eventsByEventProcessor.get(uuid).remove(event.getKey());

                        // send timeout event to remote engine to notify that the event has to be discarded...

                        if (uuid.equalsIgnoreCase(Bootstrap.getRegistrationId()))
                        {
                            EventCacheStore.getStore().deleteItem(event.getKey());
                        }

                        else
                        {
                            vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().mergeIn(context).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_TIME_OUT));
                        }

                        if (remainingEventProcessors.get(event.getKey()) != null && !remainingEventProcessors.get(event.getKey()).isEmpty())
                        {
                            EventBusConstants.updateEvent(event.getKey(), String.format(ErrorMessageConstants.EVENT_TRACKER_EVENT_ACKNOWLEDGE_TIMED_OUT, maxEventAcknowledgeTimeoutMillis));

                            var eventType = context.getString(EventBusConstants.EVENT_TYPE);

                            var metricExist = MetricConfigStore.getStore().existItem(context.getLong(GlobalConstants.ID));

                            var objectExist = ObjectConfigStore.getStore().existItem(context.getLong(GlobalConstants.ID));

                            //means valid event and context ...
                            if ((eventType.equalsIgnoreCase(EventBusConstants.EVENT_METRIC_POLL) && metricExist)
                                    || (eventType.equalsIgnoreCase(EventBusConstants.EVENT_DISCOVERY) && DiscoveryCacheStore.getStore().discoveryRunning(context.getLong(GlobalConstants.ID)))
                                    || ((metricExist || objectExist) && (eventType.equalsIgnoreCase(EventBusConstants.EVENT_REDISCOVER) || eventType.equalsIgnoreCase(EventBusConstants.EVENT_TOPOLOGY) || eventType.equalsIgnoreCase(EventBusConstants.EVENT_PLUGIN_ENGINE))))
                            {
                                context.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, remainingEventProcessors.get(event.getKey()));

                                route(context, eventReplyContexts.get(event.getKey()));
                            }

                            else //something went wrong, abort the event... //metric removed ... discard event..
                            {
                                var message = EMPTY_VALUE;

                                if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_METRIC_POLL))
                                {
                                    message = String.format(ErrorMessageConstants.METRIC_POLL_FAILED, String.format("Either metric %s removed or disabled", context.getString(Metric.METRIC_NAME)));
                                }

                                else if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_DISCOVERY))
                                {
                                    message = String.format(ErrorMessageConstants.DISCOVERY_FAILED, "Either discovery removed or aborted");
                                }

                                else if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_REDISCOVER))
                                {
                                    message = String.format(ErrorMessageConstants.REDISCOVER_FAILED, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, context.getString(AIOpsObject.OBJECT_NAME)));
                                }

                                else if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_TOPOLOGY))
                                {
                                    message = String.format(ErrorMessageConstants.TOPOLOGY_RUN_ERROR, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, context.getString(AIOpsObject.OBJECT_NAME)));
                                }

                                else if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_PLUGIN_ENGINE))
                                {
                                    message = String.format(ErrorMessageConstants.PLUGIN_ENGINE_FAILED, context.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, context.getString(AIOpsObject.OBJECT_NAME)));
                                }

                                clear(context.put(MESSAGE, message).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR), false);

                                vertx.eventBus().send(EventBusConstants.EVENT_ABORT, new JsonObject().put(EventBusConstants.EVENT_ID, event.getKey()).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(GlobalConstants.MESSAGE, message));
                            }
                        }
                        else
                        {
                            //means attempts done ... now clear cache

                            var errors = new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(MESSAGE, EVENT_ROUTER_ATTEMPT_EXCEEDED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_EVENT_ROUTER_LIMIT));

                            send(context.put(GlobalConstants.STATUS, STATUS_ABORT).put(ERRORS, errors).put(ERROR, errors.encodePrettily())
                                    .put(MESSAGE, EVENT_ROUTER_ATTEMPT_EXCEEDED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_EVENT_ROUTER_LIMIT));
                        }
                    }
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Tracks performance metrics for a specific event type and event processor.
     * <p>
     * This method records both pending events and idle worker counts for a given
     * event type and event processor combination. These metrics are used for
     * load balancing decisions.
     *
     * @param eventType     The combined key of event processor UUID and event type
     * @param pendingEvents The number of pending events for this combination
     * @param idleWorker    The number of idle workers for this combination
     */
    private void track(String eventType, int pendingEvents, int idleWorker)
    {
        track(this.idleWorkersByEventProcessor.get(eventType), idleWorker);

        track(this.pendingEventsByEventProcessor.get(eventType), pendingEvents);
    }

    /**
     * Adds a value to a list of metrics, maintaining a maximum size.
     * <p>
     * This method adds a new value to a list of metrics (like latency, pending events,
     * or idle workers). If the list has reached its maximum size (MAX_SAMPLES),
     * the oldest value is removed before adding the new one.
     *
     * @param items The list of metrics to update
     * @param value The new value to add to the list
     */
    private void track(List<Float> items, int value)
    {
        if (items != null)
        {
            if (items.size() == MAX_SAMPLES)
            {
                items.removeFirst();
            }

            items.add(CommonUtil.getFloat(value));
        }
    }

    /**
     * Routes events to the most suitable event processor using a load balancing algorithm.
     * <p>
     * This method implements the core load balancing logic for distributing events across
     * available event processors. It:
     * <p>
     * 1. Evaluates all qualified event processors for the given event
     * 2. Calculates a "frequency" score for each processor based on:
     * - Current pending events
     * - Historical latency
     * - Available idle workers
     * 3. Selects the processor with the lowest frequency score (least loaded)
     * 4. Stores remaining processors for potential failover
     * 5. Updates tracking maps and sends the event to the selected processor
     * <p>
     * If no qualified processors are available, the event is aborted with an appropriate error.
     *
     * @param event   The event to be routed, containing event type, ID, and other metadata
     * @param message The original message context (for reply handling, can be null)
     */
    private void route(JsonObject event, Message<JsonObject> message)
    {
        try
        {

            var eventProcessors = event.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS);

            var eventType = getRemoteEvent(event);

            String uuid = null;

            var frequency = 0f;

            for (var index = 0; index < eventProcessors.size(); index++)
            {
                var eventProcessor = itemsByEventProcessor.get(eventProcessors.getLong(index));

                if (eventProcessor != null && RemoteEventProcessorCacheStore.getStore().getDuration(eventProcessor.getLong(ID)) > 0)
                {
                    //get event processor uuid from  store
                    var eventProcessorUUID = eventProcessor.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID);

                    var pending = CommonUtil.getFloat(!eventsByEventProcessor.get(eventProcessorUUID).isEmpty() ? eventsByEventProcessor.get(eventProcessorUUID).size() : 1);

                    var eventProcessorFrequency = pending;

                    // if only 1 event processor assigned or frequency is 0 then no need to calculate load-balancing
                    if (eventProcessors.size() > 1)
                    {
                        var context = getMeanValue(eventProcessorUUID + SEPARATOR + eventType);

                        //1. (latency * pending events) / idle workers
                        //2. get frequency of event processor id, particular event processor  id have how many pending event....select lowest frequency event processor  id and send event to it.
                        eventProcessorFrequency = context.getFloat(HealthUtil.IDLE_WORKERS) > 0 && context.getFloat(HealthUtil.PENDING_EVENTS) > 0 && context.getFloat(EVENT_LATENCY) > 0 ?
                                (context.getFloat(EVENT_LATENCY) * context.getFloat(HealthUtil.PENDING_EVENTS) * pending) / context.getFloat(HealthUtil.IDLE_WORKERS) : pending;
                    }

                    if (uuid == null || eventProcessorFrequency < frequency)
                    {
                        uuid = eventProcessorUUID;

                        frequency = eventProcessorFrequency;
                    }
                }
            }

            var eventId = event.getLong(EventBusConstants.EVENT_ID);

            if (message != null && event.containsKey(EventBusConstants.EVENT_REPLY) && event.getString(EventBusConstants.EVENT_REPLY).equalsIgnoreCase(YES))
            {
                eventReplyContexts.put(eventId, message);
            }

            if (uuid != null)
            {
                event.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, uuid);

                eventsByEventProcessor.get(uuid).add(eventId);

                //remove selected event processor  _id and save remaining event processor to be used if selected event processor does not respond in the specified time

                var iterator = eventProcessors.iterator();

                while (iterator.hasNext())
                {
                    var remoteEventProcessor = itemsByEventProcessor.get(CommonUtil.getLong(iterator.next()));

                    if (remoteEventProcessor == null || remoteEventProcessor.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(uuid))
                    {
                        iterator.remove();
                    }
                }

                if (!eventProcessors.isEmpty())
                {
                    //add remaining event processors if it's size is greater than 0
                    remainingEventProcessors.put(eventId, eventProcessors);
                }

                //prepare context with selected event processor  UUID  because this message is received by all event processors and match uuid to perform the event
                eventContexts.put(eventId, event);

                EventBusConstants.updateEvent(eventId, String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_DISPATCHED, uuid, DateTimeUtil.timestamp()));

                //check if qualify uuid is Default otherwise send to event processors
                if (Bootstrap.getRegistrationId().equalsIgnoreCase(uuid))
                {
                    EventCacheStore.getStore().addItem(eventId);

                    vertx.eventBus().send(event.getString(EventBusConstants.EVENT_TYPE), event);

                }

                else
                {
                    //put into event acknowledgements for tracking purpose...
                    eventAcks.put(eventId, System.currentTimeMillis());

                    vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, event);

                }
            }

            else
            {
                //means either assigned even processors are deleted or down...

                var errors = new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(MESSAGE, EVENT_ROUTER_COLLECTOR_NOT_AVAILABLE).put(ERROR_CODE, ErrorCodes.ERROR_CODE_EVENT_ROUTER_NO_COLLECTOR));

                send(event.put(GlobalConstants.STATUS, STATUS_ABORT).put(ERRORS, errors).put(ERROR, errors.encodePrettily())
                        .put(MESSAGE, EVENT_ROUTER_COLLECTOR_NOT_AVAILABLE).put(ERROR_CODE, ErrorCodes.ERROR_CODE_EVENT_ROUTER_NO_COLLECTOR));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Determines the appropriate RemoteEvent type based on the event context.
     * <p>
     * This method maps the event type and other properties from the event context
     * to the corresponding RemoteEvent enum value. The mapping logic handles:
     * <p>
     * - Metric polling events: Mapped to METRIC, BATCH_METRIC, OBJECT_STATUS, or NETWORK_SERVICE
     * based on the metric plugin type
     * - Discovery events: Mapped to DISCOVERY
     * - Rediscovery events: Mapped to REDISCOVERY
     * - Plugin engine events: Mapped to PLUGIN_ENGINE
     * - Topology events: Mapped to TOPOLOGY
     * - Config upgrade events: Mapped to CONFIG_UPGRADE
     * - Compliance policy events: Mapped to COMPLIANCE
     *
     * @param event The event context containing event type and other metadata
     * @return The string representation of the corresponding RemoteEvent enum value,
     * or null if no mapping is found
     */
    private String getRemoteEvent(JsonObject event)
    {
        String remoteEvent = null;

        var eventType = event.getString(EVENT_TYPE);

        if (eventType.equalsIgnoreCase(EVENT_METRIC_POLL))
        {
            if (event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()))
            {
                remoteEvent = RemoteEvent.NETWORK_SERVICE.name();
            }

            else if (event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName())
                    || event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.PING.getName()))
            {
                remoteEvent = RemoteEvent.OBJECT_STATUS.name();
            }

            else
            {
                remoteEvent = PluginEngineConstants.hasBatchSupport(event) ? RemoteEvent.BATCH_METRIC.name() : RemoteEvent.METRIC.name();
            }
        }

        else if (eventType.equalsIgnoreCase(EVENT_DISCOVERY))
        {
            remoteEvent = RemoteEvent.DISCOVERY.name();
        }

        else if (eventType.equalsIgnoreCase(EVENT_REDISCOVER))
        {
            remoteEvent = RemoteEvent.REDISCOVERY.name();
        }

        else if (eventType.equalsIgnoreCase(EVENT_PLUGIN_ENGINE))
        {
            remoteEvent = RemoteEvent.PLUGIN_ENGINE.name();
        }

        else if (eventType.equalsIgnoreCase(EVENT_TOPOLOGY))
        {
            remoteEvent = RemoteEvent.TOPOLOGY.name();
        }

        else if (eventType.equalsIgnoreCase(EVENT_CONFIG_UPGRADE_MANAGER))
        {
            remoteEvent = RemoteEvent.CONFIG_UPGRADE.name();
        }

        else if (eventType.equalsIgnoreCase(EVENT_COMPLIANCE_POLICY))
        {
            remoteEvent = RemoteEvent.COMPLIANCE.name();
        }

        return remoteEvent;
    }

    /**
     * Calculates mean values for latency, pending events, and idle workers for a specific key.
     * <p>
     * This method computes the average values of performance metrics for a given
     * event processor and event type combination. These mean values are used in the
     * load balancing algorithm to determine the most suitable event processor.
     * <p>
     * The method ensures that pending events is never zero (minimum 1) to avoid
     * division by zero in the load balancing calculation.
     *
     * @param key The combined key of event processor UUID and event type
     * @return A JsonObject containing mean values for latency, pending events, and idle workers
     */
    private JsonObject getMeanValue(String key)
    {
        var context = new JsonObject().put(EVENT_LATENCY, 0f).put(HealthUtil.PENDING_EVENTS, 0f).put(HealthUtil.IDLE_WORKERS, 0f);

        calculateMean(this.latenciesByEventProcessor.get(key), EVENT_LATENCY, context);

        calculateMean(this.pendingEventsByEventProcessor.get(key), HealthUtil.PENDING_EVENTS, context);

        calculateMean(this.idleWorkersByEventProcessor.get(key), HealthUtil.IDLE_WORKERS, context);

        // Ensure pending events is never zero to avoid division by zero in load balancing calculation
        // A minimum value of 1 is used when no pending events are present
        if (context.getFloat(HealthUtil.PENDING_EVENTS) == 0f)
        {
            context.put(HealthUtil.PENDING_EVENTS, CommonUtil.getFloat(1));
        }

        return context;
    }

    /**
     * Calculates the mean (average) value of a list of metrics.
     * <p>
     * This utility method computes the average value of a list of metrics
     * (like latency, pending events, or idle workers) and stores it in the
     * provided context object under the specified key.
     *
     * @param items   The list of metric values to average
     * @param key     The key under which to store the result in the context
     * @param context The JsonObject to update with the calculated mean
     */
    private void calculateMean(List<Float> items, String key, JsonObject context)
    {
        if (items != null && !items.isEmpty())
        {
            // total value sum / size
            context.put(key, items.stream().reduce(0f, Float::sum) / items.size());
        }
    }

    /**
     * Clears event-related resources after processing or timeout.
     * <p>
     * This method is called when an event has been processed (successfully or with errors)
     * or when an event has timed out. It:
     * <p>
     * 1. Removes the event from tracking maps (remainingEventProcessors, eventAcks)
     * 2. Retrieves and removes the event context
     * 3. Sends a reply if the event requires one
     * 4. Updates event processor statistics if requested
     * 5. Records latency information for the event processor if available
     * <p>
     * This cleanup ensures that resources are properly released after event processing
     * and that performance metrics are updated for future load balancing decisions.
     *
     * @param event                The event context containing event ID and other metadata
     * @param updateEventProcessor Whether to update event processor statistics
     */
    private void clear(JsonObject event, boolean updateEventProcessor)
    {
        try
        {
            var eventId = event.getLong(EventBusConstants.EVENT_ID);

            remainingEventProcessors.remove(eventId);

            eventAcks.remove(eventId);

            if (eventContexts.containsKey(eventId))
            {
                event.put(EVENT_TYPE, eventContexts.remove(eventId).getString(EVENT_TYPE));

                if (eventReplyContexts.containsKey(eventId))
                {
                    eventReplyContexts.remove(eventId).reply(event);
                }

                var uuid = event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID);

                if (uuid != null)
                {
                    if (updateEventProcessor && eventsByEventProcessor.get(uuid) != null)
                    {
                        eventsByEventProcessor.get(uuid).remove(eventId);
                    }

                    if (event.containsKey(EVENT_LATENCY))
                    {
                        var latencies = this.latenciesByEventProcessor.get(uuid + SEPARATOR + getRemoteEvent(event));

                        if (latencies != null)
                        {
                            if (latencies.size() == MAX_SAMPLES)
                            {
                                latencies.removeFirst();
                            }

                            latencies.add(CommonUtil.getFloat(event.getValue(EVENT_LATENCY)));
                        }
                    }
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    /**
     * Sends completed events to the appropriate handler based on event type.
     * <p>
     * This method routes completed events to their respective handlers on the event bus.
     * Different event types are sent to different addresses:
     * <p>
     * - EVENT_DISCOVERY: Sent to EVENT_DISCOVERY_RESPONSE with 100% progress
     * - EVENT_METRIC_POLL: Published to EVENT_METRIC_POLL_RESPONSE
     * - EVENT_REDISCOVER: Sent to EVENT_REDISCOVER_RESPONSE
     * - EVENT_TOPOLOGY: Sent to EVENT_TOPOLOGY_RESPONSE
     * - EVENT_PLUGIN_ENGINE: Sent to EVENT_PLUGIN_ENGINE_RESPONSE
     * - EVENT_CONFIG_UPGRADE_MANAGER: Sent to EVENT_CONFIG_RESPONSE_PROCESSOR
     * <p>
     * The method also handles reply contexts if the event requires a reply,
     * and cleans up tracking maps after sending the event.
     *
     * @param event The completed event to be sent to its handler
     */
    private void send(JsonObject event)
    {
        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("event %s completed by remote event router", CommonUtil.removeSensitiveFields(event, true)));
        }

        // for custom scripts we are sending event.context key to agent side with replace variables and in attempt limit case it cause a problem in event tracker side
        event.remove(EventBusConstants.EVENT_CONTEXT);

        switch (event.getString(EventBusConstants.EVENT_TYPE))
        {
            case EventBusConstants.EVENT_DISCOVERY ->
                    vertx.eventBus().send(EventBusConstants.EVENT_DISCOVERY_RESPONSE, event.put(Discovery.DISCOVERY_PROGRESS, 100.00));

            case EventBusConstants.EVENT_METRIC_POLL ->
                    vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLL_RESPONSE, event);

            case EventBusConstants.EVENT_REDISCOVER ->
                    vertx.eventBus().send(EventBusConstants.EVENT_REDISCOVER_RESPONSE, event);

            case EventBusConstants.EVENT_TOPOLOGY ->
                    vertx.eventBus().send(EventBusConstants.EVENT_TOPOLOGY_RESPONSE, event);

            case EventBusConstants.EVENT_PLUGIN_ENGINE ->
                    vertx.eventBus().send(EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE, event);

            case EventBusConstants.EVENT_CONFIG_UPGRADE_MANAGER ->
                    vertx.eventBus().send(EVENT_CONFIG_RESPONSE_PROCESSOR, event);

            default ->
            {
                // do nothing
            }
        }

        var eventId = event.getLong(EventBusConstants.EVENT_ID);

        if (eventReplyContexts.containsKey(eventId))
        {
            eventReplyContexts.remove(eventId).reply(event);
        }

        remainingEventProcessors.remove(eventId);

        eventAcks.remove(eventId);
    }

    /**
     * Registers or updates an event processor in the system.
     * <p>
     * This method handles the registration process for event processors:
     * 1. For new event processors: Creates a new entry in the configuration database
     * 2. For existing event processors: Updates their information in the database
     * <p>
     * The method performs validation to ensure that certain types of event processors
     * (APP/DATASTORE with STANDALONE/PRIMARY/SECONDARY installation modes) have only
     * one entry in the system.
     * <p>
     * It also checks version compatibility between the event processor and the system,
     * and takes appropriate actions based on compatibility:
     * - For compatible versions: Sets the processor status to UP
     * - For incompatible versions: Sets the processor status to DOWN and sends a stop command
     * - For upgradable versions: Marks the processor for potential upgrade
     *
     * @param event The registration event containing event processor details such as
     *              UUID, type, installation mode, version, and IP address
     */
    private void register(JsonObject event)
    {
        try
        {
            LOGGER.info(String.format("register event : %s ", event.encodePrettily()));

            var version = event.getString(REMOTE_EVENT_PROCESSOR_VERSION, EMPTY_VALUE);

            event.put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, EventBusConstants.getInstallationMode(event));

            var installationMode = event.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE);

            var type = event.getString(REMOTE_EVENT_PROCESSOR_TYPE);

            var valid = true;

            // validation for type : APP/DATASTORE , mode ; STANDALONE/PRIMARY/SECONDARY should have only one entry

            if ((type.equalsIgnoreCase(BootstrapType.APP.name()) || type.equalsIgnoreCase(BootstrapType.DATASTORE.name())) && (installationMode.equalsIgnoreCase(InstallationMode.STANDALONE.name()) || installationMode.equalsIgnoreCase(InstallationMode.PRIMARY.name()) || installationMode.equalsIgnoreCase(InstallationMode.SECONDARY.name())))
            {
                var items = RemoteEventProcessorConfigStore.getStore().flatItemsByValue(REMOTE_EVENT_PROCESSOR_TYPE, type, REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, installationMode);

                // if any other entry than existing one is there for registration then will check uuid.
                if (!items.isEmpty())
                {
                    valid = items.getJsonObject(0).getString(REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(event.getString(REMOTE_EVENT_PROCESSOR_UUID));
                }
            }

            if (valid)
            {
                //#25056
                // we are checking by uuid and type as well.
                // because default/master and db will have same uuid when they are deployed on same machine , but they'll have different type.
                var eventProcessors = RemoteEventProcessorConfigStore.getStore().flatItemsByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE));

                var eventProcessor = !eventProcessors.isEmpty() ? eventProcessors.getJsonObject(0) : null;

                if (eventProcessor != null && !eventProcessor.isEmpty())
                {
                    Bootstrap.configDBService().update(TBL_REMOTE_EVENT_PROCESSOR,
                            new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).
                                    put(VALUE, eventProcessor.getLong(GlobalConstants.ID)),
                            new JsonObject().put(REMOTE_EVENT_PROCESSOR_VERSION, version).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE))
                                    .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, event.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)),
                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_REMOTE_POLLER.name())
                                            .put(EVENT_CONTEXT, eventProcessor).put(ID, eventProcessor.getLong(ID)));

                                    var item = RemoteEventProcessorCacheStore.getStore().getItem(eventProcessor.getLong(ID));

                                    var artifact = ArtifactConfigStore.getStore().getItem(eventProcessor.getLong(ID));

                                    artifact.put(MotadataApp.ARTIFACT_VERSION, version)
                                            .put(MotadataApp.ARTIFACT_MODE, event.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).toLowerCase());

                                    // for master , we are not checking compatibility

                                    if (event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.APP.name()) || MotadataConfigUtil.compatible(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE), version))
                                    {
                                        item.put(DURATION, event.getLong(DURATION, DateTimeUtil.currentSeconds()))
                                                .put(STATUS, STATUS_UP)
                                                .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                                .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING);

                                        if (MotadataConfigUtil.upgradable(MotadataConfigUtil.getVersion(), version))
                                        {
                                            artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_UPGRADE_REQUIRED);
                                        }
                                        else
                                        {
                                            artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                                        }

                                        EventBusConstants.publish(UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(STATUS, STATUS_SUCCEED).put(MESSAGE, String.format("remote event %s registered successfully", eventProcessor.getString(REMOTE_EVENT_PROCESSOR_IP))));

                                        publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_REGISTRATION).put(MESSAGE, String.format("remote event %s registered successfully", eventProcessor.getString(REMOTE_EVENT_PROCESSOR_IP))));

                                        LOGGER.info(String.format("remote event processor %s updated successfully...", event.encodePrettily()));
                                    }
                                    else
                                    {
                                        artifact.put(ERROR_CODE, ERROR_CODE_NOT_COMPATIBLE);

                                        LOGGER.warn(String.format("failed to register the remote event processor %s reason: remote event processor version is not compatible.", event.getString(REMOTE_EVENT_PROCESSOR_IP)));

                                        item.put(DURATION, event.getLong(DURATION, DateTimeUtil.currentSeconds()))
                                                .put(STATUS, STATUS_DOWN)
                                                .put(EVENT_TIMESTAMP, event.getLong(DURATION, DateTimeUtil.currentSeconds()))
                                                .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_RUNNING);

                                        vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                                .put(EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_STOP)
                                                .put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID))
                                                .put(SYSTEM_BOOTSTRAP_TYPE, eventProcessor.getString(REMOTE_EVENT_PROCESSOR_TYPE))
                                                .put(ID, eventProcessor.getLong(ID)));

                                        EventBusConstants.publish(UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(STATUS, STATUS_FAIL).put(MESSAGE, String.format("failed to register the remote event processor %s reason: remote event processor version is not compatible.", event.getString(REMOTE_EVENT_PROCESSOR_IP))));

                                        publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_FAIL).put(EVENT_TYPE, EVENT_REGISTRATION).put(MESSAGE, String.format("failed to register the remote event processor %s reason: remote event processor version is not compatible.", event.getString(REMOTE_EVENT_PROCESSOR_IP))));
                                    }

                                    RemoteEventProcessorCacheStore.getStore().updateItem(eventProcessor.getLong(ID), item);

                                    vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_ARTIFACT.name()).put(EVENT_CONTEXT, artifact).put(EVENT_COPY_REQUIRED, false));
                                }
                                else
                                {
                                    LOGGER.warn(String.format("failed to update remote event processor %s...", event.encodePrettily()));
                                }

                            });
                }

                else
                {
                    event.put(ID, CommonUtil.newId());

                    Bootstrap.configDBService().save(DBConstants.TBL_REMOTE_EVENT_PROCESSOR, event, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_REMOTE_POLLER.name())
                                    .put(EVENT_CONTEXT, event).put(ID, event.getLong(ID)));

                            var item = RemoteEventProcessorCacheStore.getStore().getItem(event.getLong(ID));

                            var artifact = new JsonObject().put(ID, event.getLong(ID))
                                    .put(MotadataApp.ARTIFACT_ID, event.getString(REMOTE_EVENT_PROCESSOR_UUID))
                                    .put(MotadataApp.ARTIFACT_TYPE, event.getString(REMOTE_EVENT_PROCESSOR_TYPE).toLowerCase())
                                    .put(MotadataApp.ARTIFACT_VERSION, event.getString(REMOTE_EVENT_PROCESSOR_VERSION))
                                    .put(MotadataApp.ARTIFACT_MODE, event.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).toLowerCase());

                            // for master, we are not checking compatibility

                            if (event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.APP.name()) || MotadataConfigUtil.compatible(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE), version))
                            {
                                item.put(DURATION, event.getLong(DURATION, DateTimeUtil.currentSeconds()))
                                        .put(STATUS, STATUS_UP)
                                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                        .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING);

                                if (MotadataConfigUtil.upgradable(MotadataConfigUtil.getVersion(), version))
                                {
                                    artifact.put(ERROR_CODE, ERROR_CODE_UPGRADE_REQUIRED);
                                }
                                else
                                {
                                    artifact.put(ERROR_CODE, ERROR_CODE_SUCCESS);
                                }

                                LOGGER.info(String.format("remote event processor %s registered successfully...", event.encodePrettily()));
                            }
                            else
                            {
                                artifact.put(ERROR_CODE, ERROR_CODE_NOT_COMPATIBLE);

                                LOGGER.warn(String.format(REMOTE_EVENT_PROCESSOR_REGISTRATION_FAILED, event.getString(REMOTE_EVENT_PROCESSOR_IP), "version is not compatible"));

                                item.put(DURATION, DateTimeUtil.currentSeconds())
                                        .put(STATUS, STATUS_DOWN)
                                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                        .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_RUNNING);

                                vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                        .put(EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_STOP).put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(SYSTEM_BOOTSTRAP_TYPE, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE))
                                        .put(ID, event.getLong(ID)));

                                EventBusConstants.publish(UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(REMOTE_EVENT_PROCESSOR_REGISTRATION_FAILED, event.getString(REMOTE_EVENT_PROCESSOR_IP), "version is not compatible")));

                                publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_FAIL).put(EVENT_TYPE, EVENT_REGISTRATION).put(MESSAGE, String.format(REMOTE_EVENT_PROCESSOR_REGISTRATION_FAILED, event.getString(REMOTE_EVENT_PROCESSOR_IP), "version is not compatible")));
                            }

                            RemoteEventProcessorCacheStore.getStore().updateItem(event.getLong(ID), item);

                            vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_ARTIFACT.name()).put(EVENT_CONTEXT, artifact).put(EVENT_COPY_REQUIRED, false));
                        }
                        else
                        {
                            LOGGER.warn(String.format("failed to update remote event processor %s...", event.encodePrettily()));
                        }
                    });
                }
            }
            else
            {
                LOGGER.warn(String.format("Invalid entry of remote event processor : %s  ", event.encodePrettily()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Updates the local cache with event processor information and optionally publishes configuration data.
     * <p>
     * This method:
     * 1. Adds or updates event processor information in the local cache
     * 2. Initializes tracking structures for the event processor (for latency, pending events, idle workers)
     * 3. Optionally publishes configuration data to remote event processors
     * <p>
     * The method applies specific logic to determine which event processors to track:
     * - The current instance (if not a DATASTORE type)
     * - COLLECTOR type event processors
     * - APP type event processors in PRIMARY or SECONDARY installation mode
     *
     * @param eventProcessor The event processor configuration to update
     * @param publish        Whether to publish configuration data to the event processor
     */
    private void update(JsonObject eventProcessor, boolean publish)
    {
        var uuid = eventProcessor.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID);

        if ((Bootstrap.getRegistrationId().equalsIgnoreCase(uuid) && !eventProcessor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))
                || eventProcessor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.COLLECTOR.name())
                || (eventProcessor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.APP.name()) && (eventProcessor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.SECONDARY.name()) || eventProcessor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.PRIMARY.name()))))
        {
            LOGGER.info(String.format("remote poller id %s and uuid : %s ", eventProcessor.getLong(ID), eventProcessor.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)));

            itemsByEventProcessor.put(eventProcessor.getLong(ID), eventProcessor);

            eventsByEventProcessor.computeIfAbsent(uuid, value -> new ArrayList<>());

            for (var remoteEvent : RemoteEvent.values())
            {
                var key = uuid + SEPARATOR + remoteEvent.name();

                latenciesByEventProcessor.computeIfAbsent(key, value -> new ArrayList<>());

                pendingEventsByEventProcessor.computeIfAbsent(key, value -> new ArrayList<>());

                idleWorkersByEventProcessor.computeIfAbsent(key, value -> new ArrayList<>());
            }
        }

        // for remote event processor need to share config data
        if (publish && !Bootstrap.getRegistrationId().equalsIgnoreCase(uuid))
        {
            publish(uuid);
        }
    }

    /**
     * Publishes configuration data to remote event processors.
     * <p>
     * This method collects configuration data from all available config stores
     * and sends it to a specific remote event processor or to all processors.
     * It's used to ensure that remote event processors have up-to-date configuration
     * information for processing events.
     * <p>
     * The method:
     * 1. Collects enriched configuration data from all registered config stores
     * 2. Adds license details to the configuration data
     * 3. Sends the data to the specified event processor or to all processors if uuid is null
     *
     * @param uuid The UUID of the specific event processor to send configuration to,
     *             or null to send to all event processors
     */
    private void publish(String uuid)
    {
        var items = new JsonObject();

        for (var configStore : AbstractConfigStore.ConfigStore.values())
        {
            var store = AbstractConfigStore.getConfigStore(configStore);

            if (store != null)
            {
                var item = store.enrich();

                if (item != null)
                {
                    items.mergeIn(item);
                }
            }
        }

        if (!items.isEmpty())
        {
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject()
                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                    .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, uuid)
                    .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.CONFIG_INIT.name())
                    .put(GlobalConstants.RESULT, items).put(LICENSE, LicenseUtil.getLicenseDetails()));

            LOGGER.info(String.format("publishing config init remote event processor : %s ", uuid));
        }
    }

    /**
     * Enum representing the different types of remote events that can be processed.
     * <p>
     * These event types are used for routing events to appropriate event processors
     * and for tracking performance metrics by event type.
     */
    private enum RemoteEvent
    {
        /**
         * Regular metric polling events
         */
        METRIC,

        /**
         * Batch metric polling events for efficient processing of multiple metrics
         */
        BATCH_METRIC,

        /**
         * Object status/availability check events
         */
        OBJECT_STATUS,

        /**
         * Network service monitoring events
         */
        NETWORK_SERVICE,

        /**
         * Discovery events for finding new objects
         */
        DISCOVERY,

        /**
         * Topology mapping events
         */
        TOPOLOGY,

        /**
         * Plugin engine execution events
         */
        PLUGIN_ENGINE,

        /**
         * Rediscovery events for updating object information
         */
        REDISCOVERY,

        /**
         * Configuration upgrade events
         */
        CONFIG_UPGRADE,

        /**
         * Compliance policy check events
         */
        COMPLIANCE
    }
}
