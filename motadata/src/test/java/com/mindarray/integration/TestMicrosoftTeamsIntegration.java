/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.integration;

import com.mindarray.ErrorCodes;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestConstants;
import com.mindarray.TestUtil;
import com.mindarray.api.APIConstants;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.Integration;
import com.mindarray.api.IntegrationProfile;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.INTEGRATION_API_ENDPOINT;
import static com.mindarray.eventbus.EventBusConstants.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestMicrosoftTeamsIntegration
{
    private static final Logger LOGGER = new Logger(TestMicrosoftTeamsIntegration.class, INTEGRATION_DIR, "Test Microsoft Teams Integration");

    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testSyncMicrosoftTeamsInvalid(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\n  \"integration.type\": \"Microsoft Teams\",\n  \"integration.context\": {\n    \"integration.credential.profile\": 9999999999,\n    \"timeout\": 60,\n    \"auto.sync\": \"no\",\n    \"sync.interval\": 4,\n    \"target\": \"https://graph.microsoft.com/v1.0/\"\n  },\n  \"integration.attributes\": {},\n  \"id\": 10000000000003,\n  \"_type\": \"0\"\n}");

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000003L, payload, response ->
        {
            if (response.succeeded())
            {
                try
                {
                    testContext.awaitCompletion(5, TimeUnit.SECONDS);

                    var context = new JsonObject().put(EVENT_TYPE, UI_ACTION_INTEGRATION_SYNC)
                            .put(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName())
                            .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                            .put(ID, IntegrationConstants.IntegrationId.MICROSOFT_TEAMS.getName());

                    assertIntegrationSyncTestResult(testContext, STATUS_FAIL, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND);

                    TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_SYNC, context);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            }
            else
            {
                LOGGER.error(response.cause());

                testContext.failNow(response.cause());
            }
        });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testSyncMicrosoftTeams(VertxTestContext testContext)
    {
        var credential = TestConstants.prepareParams("testMicrosoftTeamCredentialProfile");

        Assertions.assertNotNull(credential);

        credential.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credential.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME) + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                var payload = new JsonObject("{\n  \"integration.type\": \"Microsoft Teams\",\n  \"integration.context\": {\n    \"integration.credential.profile\": 9999999999,\n    \"timeout\": 60,\n    \"auto.sync\": \"no\",\n    \"sync.interval\": 4,\n    \"target\": \"https://graph.microsoft.com/v1.0/\"\n  },\n  \"integration.attributes\": {},\n  \"id\": 10000000000003,\n  \"_type\": \"0\"\n}");

                if (credentialId != null && credentialId != 0)
                {
                    payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, credentialId);

                    TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000003L, payload, response ->
                    {
                        if (response.succeeded())
                        {
                            try
                            {
                                testContext.awaitCompletion(5, TimeUnit.SECONDS);

                                var context = new JsonObject().put(EVENT_TYPE, UI_ACTION_INTEGRATION_SYNC)
                                        .put(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName())
                                        .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                                        .put(ID, IntegrationConstants.IntegrationId.MICROSOFT_TEAMS.getName());

                                assertIntegrationSyncTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

                                TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_SYNC, context);
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                testContext.failNow(exception);
                            }
                        }
                        else
                        {
                            LOGGER.error(response.cause());

                            testContext.failNow(response.cause());
                        }
                    });
                }
                else
                {
                    testContext.failNow("not able to create credential profile");
                }
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    private void assertIntegrationSyncTestResult(VertxTestContext testContext, String status, String errorCode)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (context.containsKey(EVENT_STATE) && context.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_COMPLETED) && context.containsKey(EVENT_TYPE) && context.getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_INTEGRATION_SYNC))
                {
                    Assertions.assertEquals(context.getString(STATUS), status);

                    if (errorCode != null)
                    {
                        Assertions.assertEquals(context.getString(ERROR_CODE), errorCode);
                    }

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(result -> testContext.failNow(exception));

            }
        });
    }

}
