package com.mindarray.slo;

import com.mindarray.GlobalConstants;
import com.mindarray.api.SLOProfile;
import com.mindarray.integration.TestServiceOpsIntegration;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.Logger;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSLOProcessor
{
    private static final Logger LOGGER = new Logger(TestServiceOpsIntegration.class, GlobalConstants.MOTADATA_SLO, "Test ServiceOps Integration");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var monitorAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorAvailabilitySLO");
            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorPerformanceSLO");
            var instanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstanceAvailabilitySLO");
            var instancePerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstancePerformanceSLO");
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }
}
