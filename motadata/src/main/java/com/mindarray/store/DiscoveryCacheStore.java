/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Discovery;
import com.mindarray.util.CommonUtil;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DiscoveryCacheStore extends AbstractCacheStore
{
    private static final DiscoveryCacheStore STORE = new DiscoveryCacheStore();

    private final Map<Long, Long> runningDiscoveries = new ConcurrentHashMap<>();

    private final Map<Long, Integer> progressByDiscovery = new ConcurrentHashMap<>();

    private final Map<Long, Integer> totalProbesByDiscovery = new ConcurrentHashMap<>();

    private final Map<Long, JsonArray> discoveredObjects = new ConcurrentHashMap<>();

    private final Map<Long, JsonArray> failedObjects = new ConcurrentHashMap<>();

    private final Map<Long, JsonArray> discoveryProgressEvents = new ConcurrentHashMap<>();

    private final Map<Long, JsonObject> probes = new ConcurrentHashMap<>();

    private DiscoveryCacheStore()
    {
    }

    public static DiscoveryCacheStore getStore()
    {
        return STORE;
    }

    public void init(long discoveryId, int probes)
    {
        progressByDiscovery.put(discoveryId, 0);

        totalProbesByDiscovery.put(discoveryId, probes);

        discoveryProgressEvents.put(discoveryId, new JsonArray());

        discoveredObjects.put(discoveryId, new JsonArray());

        failedObjects.put(discoveryId, new JsonArray());
    }

    public void addProbes(long discoveryId, JsonObject probes)
    {
        this.probes.put(discoveryId, probes);
    }

    public JsonObject getProbes(long discoveryId)
    {
        return probes.containsKey(discoveryId) ? probes.get(discoveryId).copy() : new JsonObject();
    }

    public void startDiscovery(long discoveryId)
    {
        runningDiscoveries.put(discoveryId, CommonUtil.newEventId());
    }

    public long getDiscoveryEventId(long discoveryId)
    {
        return runningDiscoveries.get(discoveryId);
    }

    public boolean discoveryRunning(long discoveryId)
    {
        return runningDiscoveries.containsKey(discoveryId);
    }

    public void completeDiscovery(long discoveryId)
    {
        progressByDiscovery.put(discoveryId, 0);

        totalProbesByDiscovery.remove(discoveryId);

        discoveryProgressEvents.remove(discoveryId);

        discoveredObjects.remove(discoveryId);

        runningDiscoveries.remove(discoveryId);

        failedObjects.remove(discoveryId);

        probes.remove(discoveryId);

    }

    public JsonObject getDiscoveryStatistics(long discoveryId)
    {
        return new JsonObject().put(Discovery.DISCOVERY_TOTAL_OBJECTS, totalProbesByDiscovery.getOrDefault(discoveryId, 0))
                .put(Discovery.DISCOVERY_DISCOVERED_OBJECTS, discoveredObjects.containsKey(discoveryId) ? discoveredObjects.get(discoveryId).size() : 0)
                .put(Discovery.DISCOVERY_FAILED_OBJECTS, failedObjects.containsKey(discoveryId) ? failedObjects.get(discoveryId).size() : 0)
                .put(Discovery.DISCOVERY_PROGRESS, progressByDiscovery.getOrDefault(discoveryId, 0));
    }

    public JsonArray getDiscoveredObjects(long discoveryId)
    {
        return discoveredObjects.containsKey(discoveryId) ? discoveredObjects.get(discoveryId).copy() : null;
    }

    public JsonArray getFailedObjects(long discoveryId)
    {
        return failedObjects.containsKey(discoveryId) ? failedObjects.get(discoveryId).copy() : null;
    }

    public JsonArray getDiscoveryProgressEvents(long discoveryId)
    {
        return discoveryProgressEvents.containsKey(discoveryId) ? discoveryProgressEvents.get(discoveryId).copy() : new JsonArray(new ArrayList<>(1));
    }

    public int updateDiscoveryProgress(long discoveryId)
    {
        var progress = ((discoveredObjects.get(discoveryId).size() + failedObjects.get(discoveryId).size()) * 100) / totalProbesByDiscovery.get(discoveryId);

        progressByDiscovery.put(discoveryId, progress);

        return progress;
    }

    public void addFailedObject(long discoveryId, JsonObject failedObject)
    {
        failedObjects.get(discoveryId).add(failedObject);
    }

    public void addDiscoveredObject(long discoveryId, JsonObject discoveredObject)
    {
        discoveredObjects.get(discoveryId).add(discoveredObject);
    }

    public void addDiscoveryProgressEvent(long discoveryId, JsonObject discoveryProgressEvent)
    {
        if (probes.get(discoveryId) != null) //for cloud category
        {
            probes.get(discoveryId).stream().forEach(item -> JsonObject.mapFrom(item).remove(discoveryProgressEvent.getString(AIOpsObject.OBJECT_TARGET)));
        }

        discoveryProgressEvents.get(discoveryId).add(discoveryProgressEvent);
    }
}
