package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.store.SLOCycleConfigStore;
import com.mindarray.util.Logger;
import io.vertx.ext.web.Router;

import java.util.Set;

import static com.mindarray.api.APIConstants.*;

public class SLOCycle extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(SLOCycle.class, GlobalConstants.MOTADATA_API, "SLO Cycle API");

    public SLOCycle()
    {
        super("slo-cycles", SLOCycleConfigStore.getStore(), LOGGER);
    }

    public static final String SLO_CYCLE_NAME = "slo.cycle.name";
    public static final String SLO_CYCLE_START_TIME = "slo.cycle.start.time"; // Epoch seconds of the starting of the day (ex. for 8-8-2025 it will be 1751932800)
    public static final String SLO_CYCLE_END_TIME = "slo.cycle.end.time"; // Epoch seconds of the ending of the cycle
    public static final String SLO_PROFILE_ID = "slo.profile.id"; // FK to SLOProfileConfigStore
    public static final String SLO_CYCLE_TOTAL_DURATION = "slo.cycle.total.duration";
    public static final String SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME = "slo.cycle.acceptable.violation.time";

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router, Set.of(REQUEST_CREATE, REQUEST_DELETE, REQUEST_UPDATE));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
