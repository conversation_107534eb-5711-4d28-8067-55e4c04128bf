{"*************": {"credential.profile.name": "Default SNMP", "discovery.credential.profiles": [{"_type": "0", "credential.profile.name": "Default SNMP", "credential.profile.protocol": "SNMP V1/V2c", "id": 10000000000001, "object.ip": "*************", "port": 161, "snmp.community": "public", "snmp.version": "v2c", "timeout": 60}], "object.credential.profile": 10000000000001, "object.ip": "*************", "objects": [{"object.host": "admin", "object.ip": "*************", "object.name": "admin", "object.system.oid": ".*******.4.1.8072.3.2.10"}], "port": 161, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}}