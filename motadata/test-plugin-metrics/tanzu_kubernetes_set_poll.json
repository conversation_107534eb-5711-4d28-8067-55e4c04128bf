{"172.16.14.93": {"result": {"kubernetes.daemon.set": [{"kubernetes.daemon.set": "default-cadvisor", "kubernetes.daemon.set.available.replicas": 2, "kubernetes.daemon.set.creation.time": "Mon, Apr 14 2025, 08:11", "kubernetes.daemon.set.current.replicas": 2, "kubernetes.daemon.set.desired.replicas": 2, "kubernetes.daemon.set.name": "cadvisor", "kubernetes.daemon.set.namespace": "default", "kubernetes.daemon.set.ready.replicas": 2, "kubernetes.daemon.set.updated.replicas": 2}, {"kubernetes.daemon.set": "kube-system-kube-proxy", "kubernetes.daemon.set.available.replicas": 3, "kubernetes.daemon.set.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.daemon.set.current.replicas": 3, "kubernetes.daemon.set.desired.replicas": 3, "kubernetes.daemon.set.name": "kube-proxy", "kubernetes.daemon.set.namespace": "kube-system", "kubernetes.daemon.set.node.selector": "kubernetes.io/os=linux", "kubernetes.daemon.set.ready.replicas": 3, "kubernetes.daemon.set.updated.replicas": 3}, {"kubernetes.daemon.set": "kube-system-weave-net", "kubernetes.daemon.set.available.replicas": 3, "kubernetes.daemon.set.creation.time": "Sun, Feb 09 2025, 05:47", "kubernetes.daemon.set.current.replicas": 3, "kubernetes.daemon.set.desired.replicas": 3, "kubernetes.daemon.set.name": "weave-net", "kubernetes.daemon.set.namespace": "kube-system", "kubernetes.daemon.set.ready.replicas": 3, "kubernetes.daemon.set.updated.replicas": 3}], "kubernetes.daemon.sets": 3, "kubernetes.deployment": [{"kubernetes.deployment": "default-prometheus", "kubernetes.deployment.available.replicas": 1, "kubernetes.deployment.name": "prometheus", "kubernetes.deployment.namespace": "default", "kubernetes.deployment.ready.replicas": 1, "kubernetes.deployment.total.replicas": 1}, {"kubernetes.deployment": "kube-system-coredns", "kubernetes.deployment.available.replicas": 2, "kubernetes.deployment.name": "coredns", "kubernetes.deployment.namespace": "kube-system", "kubernetes.deployment.ready.replicas": 2, "kubernetes.deployment.total.replicas": 2}, {"kubernetes.deployment": "kube-system-metrics-server", "kubernetes.deployment.available.replicas": 1, "kubernetes.deployment.name": "metrics-server", "kubernetes.deployment.namespace": "kube-system", "kubernetes.deployment.ready.replicas": 1, "kubernetes.deployment.total.replicas": 1}], "kubernetes.deployments": 3, "kubernetes.replica.set": [{"kubernetes.replica.set": "default-prometheus-7f6cdfb75d", "kubernetes.replica.set.current.replicas": 1, "kubernetes.replica.set.desired.replicas": 1, "kubernetes.replica.set.name": "prometheus-7f6cdfb75d", "kubernetes.replica.set.namespace": "default", "kubernetes.replica.set.ready.replicas": 1}, {"kubernetes.replica.set": "kube-system-coredns-7c65d6cfc9", "kubernetes.replica.set.current.replicas": 2, "kubernetes.replica.set.desired.replicas": 2, "kubernetes.replica.set.name": "coredns-7c65d6cfc9", "kubernetes.replica.set.namespace": "kube-system", "kubernetes.replica.set.ready.replicas": 2}, {"kubernetes.replica.set": "kube-system-metrics-server-54bf7cdd6", "kubernetes.replica.set.current.replicas": 0, "kubernetes.replica.set.desired.replicas": 0, "kubernetes.replica.set.name": "metrics-server-54bf7cdd6", "kubernetes.replica.set.namespace": "kube-system"}, {"kubernetes.replica.set": "kube-system-metrics-server-6794476995", "kubernetes.replica.set.current.replicas": 1, "kubernetes.replica.set.desired.replicas": 1, "kubernetes.replica.set.name": "metrics-server-6794476995", "kubernetes.replica.set.namespace": "kube-system", "kubernetes.replica.set.ready.replicas": 1}], "kubernetes.replica.sets": 4, "kubernetes.stateful.set": [{"kubernetes.stateful.set": "default-my-statefulset", "kubernetes.stateful.set.creation.time": "Mon, Feb 24 2025, 13:36", "kubernetes.stateful.set.current.replicas": 1, "kubernetes.stateful.set.name": "my-statefulset", "kubernetes.stateful.set.namespace": "default", "kubernetes.stateful.set.replicas": 1, "kubernetes.stateful.set.service": "my-service", "kubernetes.stateful.set.updated.replicas": 1}], "kubernetes.stateful.sets": 1}}}