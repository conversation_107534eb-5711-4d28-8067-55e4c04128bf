{"entity": "Personal Access Token", "table": "tbl_config_personal_access_token", "version": "1.0", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props": [{"name": "personal.access.token", "title": "Token", "type": "string"}, {"name": "personal.access.token.name", "title": "Token Name", "type": "string", "rules": ["required", "unique"]}, {"name": "personal.access.token.description", "title": "Token Description", "type": "string"}, {"name": "personal.access.token.validity", "title": "Token Validity", "type": "string"}, {"name": "personal.access.token.user", "title": "Token User", "type": "numeric", "rules": ["required"]}], "entries": [{"type": "inline", "records": [], "version": "1.0"}]}