/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 */

package com.mindarray.netroute;

import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.store.NetRouteCacheStore;
import com.mindarray.store.NetRouteConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.datastore.DatastoreConstants.PluginId.NETROUTE_STATUS_DURATION_METRIC;
import static com.mindarray.eventbus.EventBusConstants.CHANGE_NOTIFICATION_TYPE;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN;
import static com.mindarray.netroute.NetRouteConstants.NETROUTE_AVAILABILITY_PLUGIN;

/**
 * Calculates and tracks the status of network routes.
 * <p>
 * This class is responsible for monitoring the status of network routes, detecting status
 * changes, and calculating metrics related to network route availability and performance.
 * It processes status information from network route polling operations and stores the
 * results for analysis and visualization.
 * <p>
 * Key responsibilities include:
 * <ul>
 *   <li>Tracking network route status over time</li>
 *   <li>Detecting and recording status changes (flaps)</li>
 *   <li>Calculating duration metrics for different status states</li>
 *   <li>Storing status information in the data store</li>
 *   <li>Sending notifications about status changes</li>
 * </ul>
 * <p>
 * The calculator uses a buffer-based approach to efficiently store and update status
 * information in the data store, minimizing the overhead of frequent updates.
 */
public class NetRouteStatusCalculator extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(NetRouteStatusCalculator.class, MOTADATA_NETROUTE, "NetRoute Status Calculator");

    /**
     * Array of availability metric types (seconds and percent)
     */
    private static final JsonArray AVAILABILITY_METRICS = new JsonArray().add("seconds").add("percent");

    /**
     * Interval (in seconds) for flushing status flap durations to disk
     */
    private static final int STATUS_FLAP_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getStatusFlapDurationDumpTimerSeconds();

    /**
     * Interval (in seconds) for flushing network route status information
     */
    private static final int STATUS_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getNetRouteStatusFlushTimerSeconds();

    /**
     * StringBuilder for building strings efficiently
     */
    private final StringBuilder builder = new StringBuilder(0);

    /**
     * JsonObject to store duration information for network route statuses
     */
    private final JsonObject durations = new JsonObject();

    /**
     * Flag indicating whether the durations have been updated
     */
    private final AtomicBoolean updated = new AtomicBoolean();

    /**
     * Set of mapper names for column mapping
     */
    private Set<String> mappers;

    /**
     * Flag indicating whether the data is dirty and needs to be saved
     */
    private boolean dirty = false;

    /**
     * Initializes the NetRouteStatusCalculator verticle.
     * <p>
     * This method sets up the status tracking system for network routes. It performs the following tasks:
     * <ul>
     *   <li>Initializes the mappers set for column mapping</li>
     *   <li>Loads existing status flap durations from disk if available</li>
     *   <li>Sets up periodic timers for flushing status information to disk and the data store</li>
     *   <li>Sets up event bus consumers for handling status change notifications</li>
     * </ul>
     * The method uses two main timers:
     * <ul>
     *   <li>Status flap flush timer - Controls how often status flap durations are saved to disk</li>
     *   <li>Path status flush timer - Controls how often network route status information is written to the data store</li>
     * </ul>
     *
     * @param promise Promise to be completed when initialization is done
     */
    @Override
    public void start(Promise<Void> promise)
    {
        mappers = new HashSet<>();

        var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + NetRouteConstants.NETROUTE_STATUS_FLAP_DURATIONS);

        if (file.exists())
        {
            var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

            if (buffer != null && buffer.getBytes().length > 0)
            {
                durations.mergeIn(new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))));

                LOGGER.info(String.format("%s loaded from the backup file...", NetRouteConstants.NETROUTE_STATUS_FLAP_DURATIONS));
            }
        }
        else
        {
            vertx.fileSystem().createFileBlocking(file.getPath());
        }

        var statusFlapFlushTimer = new AtomicInteger(STATUS_FLAP_FLUSH_TIMER_SECONDS);

        var pathStatusFlushTimer = new AtomicInteger(STATUS_FLUSH_TIMER_SECONDS);

        vertx.setPeriodic(10 * 1000L, timer ->
        {
            try
            {
                statusFlapFlushTimer.set(statusFlapFlushTimer.get() - 10);

                pathStatusFlushTimer.set(pathStatusFlushTimer.get() - 10);

                var timestamp = DateTimeUtil.currentSeconds();

                for (var item : NetRouteCacheStore.getStore().getItems().entrySet())
                {
                    var object = NetRouteConfigStore.getStore().getItem(item.getKey(), false);

                    if (object != null)
                    {
                        var id = CommonUtil.getString(item.getKey());

                        durations.getMap().putIfAbsent(id, 0);

                        durations.put(id, durations.getInteger(id) + 10);

                        if (pathStatusFlushTimer.get() <= 0)
                        {
                            var buffer = Buffer.buffer();

                            appendBytes(buffer, timestamp, NETROUTE_STATUS_DURATION_METRIC.getName(), item.getKey());

                            buffer.appendIntLE(EMPTY_VALUE.length());

                            buffer.appendString(EMPTY_VALUE);

                            updateScalarBatch(METRIC, APIConstants.Entity.NETROUTE.getName().toLowerCase() + "." + item.getValue().toLowerCase() + TIME, NETROUTE_STATUS_DURATION_METRIC.getName(), buffer, true, true);

                            updateScalarBatch(STATUS, item.getValue().toUpperCase(), NETROUTE_STATUS_DURATION_METRIC.getName(), buffer, false, false);

                            updateScalarBatch(REASON, EMPTY_VALUE, NETROUTE_STATUS_DURATION_METRIC.getName(), buffer, false, false);

                            updateScalarBatch(DURATION, CommonUtil.getString(STATUS_FLUSH_TIMER_SECONDS), NETROUTE_STATUS_DURATION_METRIC.getName(), buffer, false, false);

                            vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.AVAILABILITY.getName(), buffer.getBytes());
                        }
                    }
                }

                if (statusFlapFlushTimer.get() <= 0)
                {
                    if (!durations.isEmpty() || updated.get())
                    {
                        vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + NetRouteConstants.NETROUTE_STATUS_FLAP_DURATIONS,
                                Buffer.buffer(CodecUtil.compress(new JsonObject().mergeIn(durations).encode().getBytes())));

                        dirty = true;
                    }

                    NetRouteCacheStore.getStore().dump();

                    updated.set(false);

                    statusFlapFlushTimer.set(STATUS_FLAP_FLUSH_TIMER_SECONDS);
                }

                if (pathStatusFlushTimer.get() <= 0)
                {
                    pathStatusFlushTimer.set(STATUS_FLUSH_TIMER_SECONDS);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION
                , message ->
                {
                    try
                    {
                        var event = message.body();

                        if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.DELETE_NETROUTE)
                        {
                            var id = event.getLong(ID);

                            NetRouteCacheStore.getStore().deleteItem(id);

                            durations.remove(CommonUtil.getString(id));

                            updated.set(true);
                        }
                        else if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.UPDATE_CACHE)
                        {
                            if (dirty)
                            {
                                dirty = false;

                                HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, NetRouteConstants.NETROUTE_STATUS_FLAP_DURATIONS).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + NetRouteConstants.NETROUTE_STATUS_FLAP_DURATIONS)));
                            }

                            if (NetRouteCacheStore.getStore().dirty())
                            {
                                NetRouteCacheStore.getStore().setDirty(false);

                                HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, NetRouteConstants.NETROUTE_STATUS_FLAPS).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + NetRouteConstants.NETROUTE_STATUS_FLAPS)));

                                HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, NetRouteConstants.NETROUTE_STATUS_FLAPS).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + NetRouteConstants.NETROUTE_HOPS_REGISTRY_INFO)));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                }).exceptionHandler(LOGGER::error);

        promise.complete();
    }

    /**
     * Appends common header information to a buffer for data storage.
     * <p>
     * This method adds the standard header information required for all data records
     * in the data store. The header includes:
     * <ul>
     *   <li>Timestamp - When the data was collected</li>
     *   <li>Plugin ID - Identifier for the plugin that generated the data</li>
     *   <li>Entity ID - Identifier for the network route</li>
     * </ul>
     * This method is used as a helper by other methods that need to prepare data
     * for storage in the data store.
     *
     * @param bytes     Buffer to append the header information to
     * @param timestamp Timestamp (in seconds) when the data was collected
     * @param pluginId  Identifier for the plugin that generated the data
     * @param id        Identifier for the network route
     */
    private void appendBytes(Buffer bytes, long timestamp, int pluginId, long id)
    {
        bytes.setLongLE(0, timestamp);

        var plugin = pluginId + DASH_SEPARATOR + NETROUTE_AVAILABILITY_PLUGIN;

        bytes.appendIntLE(CommonUtil.getString(plugin).length());

        bytes.appendString(CommonUtil.getString(plugin));

        bytes.appendByte(DatastoreConstants.DatastoreFormat.VERTICAL.getName().byteValue());

        bytes.appendByte(CommonUtil.getByteValue(DatastoreConstants.DatastoreType.NETROUTE_STATUS_METRIC.ordinal()));

        bytes.appendLongLE(id);
    }

    /**
     * Updates a scalar batch with column and value information.
     * <p>
     * This method adds column and value information to a buffer for batch updates to the data store.
     * It handles both regular columns and mapper columns, which require special handling for
     * efficient storage and retrieval.
     * <p>
     * The method performs the following steps:
     * <ul>
     *   <li>Appends the column name to the buffer</li>
     *   <li>Appends the value to the buffer</li>
     *   <li>Handles mapper columns if specified</li>
     *   <li>Updates the dirty flag to indicate that data has been modified</li>
     * </ul>
     *
     * @param column      The name of the column to update
     * @param value       The value to set for the column
     * @param pluginId    The identifier of the plugin that generated the data
     * @param buffer      The buffer to append the data to
     * @param mapper      Whether this column is a mapper column
     * @param mapperValue Whether this column's value should be used as a mapper value
     */
    private void updateScalarBatch(String column, String value, int pluginId, Buffer buffer, boolean mapper, boolean mapperValue)
    {
        try
        {
            builder.setLength(0);

            var category = DatastoreConstants.DataCategory.STRING.getName();

            if (column.equalsIgnoreCase(ID) || column.equalsIgnoreCase(DURATION))
            {
                category = DatastoreConstants.DataCategory.NUMERIC.getName();
            }

            buffer.appendByte(category);

            buffer.appendIntLE(column.length());

            buffer.appendString(column);

            if (category == DatastoreConstants.DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
            {
                buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
            }
            else
            {
                buffer.appendIntLE(value.length());

                buffer.appendString(value);
            }

            if (mapper)
            {
                var mapperColumn = mapperValue ? value : column;

                for (var index = 0; index < AVAILABILITY_METRICS.size(); index++)
                {
                    builder.append(DatastoreConstants.DataCategory.NUMERIC.getName())
                            .append(COLUMN_SEPARATOR).append(pluginId).append(COLUMN_SEPARATOR)
                            .append(mapperColumn).append(".").append(AVAILABILITY_METRICS.getString(index)).append(COLUMN_SEPARATOR).append(YES);

                    if (!mappers.contains(builder.toString()))
                    {
                        mappers.add(builder.toString());

                        vertx.eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                                new JsonObject()
                                        .put(CHANGE_NOTIFICATION_TYPE, UPDATE_METRIC_COLUMN.name())
                                        .put(DatastoreConstants.MAPPER, builder.toString()));
                    }

                    builder.setLength(0);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Stops the NetRouteStatusCalculator verticle.
     * <p>
     * This method is called when the verticle is undeployed. It completes the provided
     * promise to signal successful shutdown.
     *
     * @param promise Promise to be completed when shutdown is done
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        // Complete the promise to signal successful shutdown
        promise.complete();
    }
}
