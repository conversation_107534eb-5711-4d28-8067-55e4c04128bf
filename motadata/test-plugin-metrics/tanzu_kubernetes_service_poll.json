{"************": {"result": {"kubernetes.service": [{"kubernetes.service": "default-kubernetes", "kubernetes.service.cluster.ip.address": "*********", "kubernetes.service.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.service.name": "kubernetes", "kubernetes.service.namespace": "default", "kubernetes.service.ports": "443/TCP", "kubernetes.service.type": "ClusterIP"}, {"kubernetes.service": "default-prometheus", "kubernetes.service.cluster.ip.address": "************", "kubernetes.service.creation.time": "Mon, Apr 14 2025, 08:12", "kubernetes.service.name": "prometheus", "kubernetes.service.namespace": "default", "kubernetes.service.ports": "9090/TCP", "kubernetes.service.type": "NodePort"}, {"kubernetes.service": "kube-system-kube-dns", "kubernetes.service.application": "kube-dns", "kubernetes.service.cluster.ip.address": "*********0", "kubernetes.service.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.service.name": "kube-dns", "kubernetes.service.namespace": "kube-system", "kubernetes.service.ports": "53/UDP,53/TCP,9153/TCP", "kubernetes.service.type": "ClusterIP"}, {"kubernetes.service": "kube-system-metrics-server", "kubernetes.service.application": "metrics-server", "kubernetes.service.cluster.ip.address": "*************", "kubernetes.service.creation.time": "Fri, Apr 11 2025, 05:26", "kubernetes.service.name": "metrics-server", "kubernetes.service.namespace": "kube-system", "kubernetes.service.ports": "443/TCP", "kubernetes.service.type": "ClusterIP"}], "kubernetes.services": 4}}}