{"************": {"errors": [], "metric.name": "Network", "metric.timeout": 10, "object.ip": "************", "port": 161, "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 22}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 23}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 80}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 1813}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6123}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6125}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 9000}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49152}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 64516}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 68}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 1812}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 7000}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49153}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 69}], "network.connection.tcp.connections": 3, "network.connection.tcp.error.segments": 522, "network.connection.tcp.retransmitted.segments": 143, "network.connection.udp.connections": 12, "network.connection.udp.error.segments": 4601}, "snmp.community": "public", "snmp.version": "v2c"}, "fd00:1:1:1::47": {"errors": [], "metric.name": "Network", "metric.timeout": 10, "object.ip": "fd00:1:1:1::47", "port": 161, "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 22}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 23}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 80}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6343}], "network.connection.tcp.connections": 3, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 80, "network.connection.udp.connections": 3, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c"}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844577937", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587759], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844577939", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:01.186 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}], "event.id": 58829800623942, "event.timestamp": 1654844927, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587795, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587759, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587760, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:38:38.401 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "isis2.isis2", "object.id": 1, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "isis2.isis2", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 55464}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 0, "network.connection.udp.connections": 3, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844581172", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587762], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844581175", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:04.161 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800623963, "event.timestamp": 1654845286, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587801, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587762, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587763, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:44:37.729 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000008, 10000000000013], "object.host": "<PERSON><PERSON><PERSON><PERSON>", "object.id": 2, "object.ip": "***********", "object.make.model": "Juniper", "object.name": "<PERSON><PERSON><PERSON><PERSON>", "object.snmp.device.catalog": 58829800562463, "object.state": "ENABLE", "object.system.oid": ".*******.4.1.2636.*******.1", "object.target": "***********", "object.type": "Router", "object.vendor": "Juniper Networks", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 22}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 51627}, {"destination.ip": "*********", "destination.port": 31883, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 60843}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 111}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 60171}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 666}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6159}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6234}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "*********", "destination.port": 54611, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 38}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6988}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32010}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 6666}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 52239}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "closed", "source.ip": "*********", "source.port": 58593}, {"destination.ip": "*********", "destination.port": 60843, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 65124}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 513}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 2606}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6156}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31341}, {"destination.ip": "*********", "destination.port": 60171, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 54611}, {"destination.ip": "*********", "destination.port": 65124, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56243}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6012}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6151}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6152}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7210}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31343}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32008}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32033}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33010}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 2049}, {"destination.ip": "*********", "destination.port": 52239, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6171}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7250}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32003}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32009}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 33002}, {"destination.ip": "*********", "destination.port": 56243, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 514}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6161}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32011}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33072}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 2049}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 988}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49152}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49153}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 111}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 965}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31340}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 62538}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 56994}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6333}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 8503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31342}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 4784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 65467}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 6666}], "network.connection.tcp.connections": 49, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 0, "network.connection.udp.connections": 20, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844584149", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587765], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844584150", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:07.135 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800623989, "event.timestamp": 1654845645, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587805, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587765, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587766, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:50:37.77 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "bgp2.bgp2.com", "object.id": 3, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "bgp2.bgp2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "***********", "destination.port": 60111, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "***********", "source.port": 179}, {"destination.ip": "***********", "destination.port": 56337, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "***********", "source.port": 179}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 58605}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 52574}], "network.connection.tcp.connections": 2, "network.connection.tcp.error.segments": 24, "network.connection.tcp.retransmitted.segments": 137, "network.connection.udp.connections": 4, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844587128", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587768], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844587133", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:10.124 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624018, "event.timestamp": 1654845705, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587813, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587768, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587769, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:51:37.707 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000008, 10000000000013], "object.host": "bgpjunos", "object.id": 4, "object.ip": "***********", "object.make.model": "Juniper", "object.name": "bgpjunos", "object.snmp.device.catalog": 58829800562463, "object.state": "ENABLE", "object.system.oid": ".*******.4.1.2636.*******.1", "object.target": "***********", "object.type": "Router", "object.vendor": "Juniper Networks", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6161}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6156}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6152}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32003}, {"destination.ip": "*********", "destination.port": 6153, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 62796}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 513}, {"destination.ip": "*********", "destination.port": 56259, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "*********", "destination.port": 52819, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 2606}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32011}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 2049}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7250}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6988}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32010}, {"destination.ip": "*********", "destination.port": 56806, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 33002}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6159}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56806}, {"destination.ip": "***********", "destination.port": 179, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "***********", "source.port": 56337}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 514}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6151}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "closed", "source.ip": "*********", "source.port": 52103}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 111}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 179}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6171}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31343}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32008}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 22}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7210}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 38}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33072}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6153}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 55601}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32033}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 6666}, {"destination.ip": "*********", "destination.port": 56040, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 65531}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6154}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31341}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33010}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 666}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6234}, {"destination.ip": "*********", "destination.port": 62796, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 6153}, {"destination.ip": "*********", "destination.port": 62281, "network.connection.protocol": "TCP", "network.connection.state": "closed", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 55601, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 31883, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 52819}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6012}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33083}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56259}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 64965}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32009}, {"destination.ip": "*********", "destination.port": 64965, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56040}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 51627}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49152}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49153}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49798}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 54417}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 6666}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 111}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31340}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31342}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 61168}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 736}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 8503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 65467}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 2049}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 661}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6333}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 4784}], "network.connection.tcp.connections": 58, "network.connection.tcp.error.segments": 1, "network.connection.tcp.retransmitted.segments": 1, "network.connection.udp.connections": 21, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844590141", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587771], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844590142", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:13.468 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624047, "event.timestamp": 1654845765, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587817, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587771, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587772, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:52:38.346 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf2.ospf2.com", "object.id": 5, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf2.ospf2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "***********", "destination.port": 43394, "network.connection.protocol": "TCP", "network.connection.state": "time wait", "source.ip": "***********", "source.port": 80}, {"destination.ip": "***********", "destination.port": 59686, "network.connection.protocol": "TCP", "network.connection.state": "time wait", "source.ip": "***********", "source.port": 80}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 59706}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 63376}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}], "network.connection.tcp.connections": 2, "network.connection.tcp.error.segments": 11, "network.connection.tcp.retransmitted.segments": 4789, "network.connection.udp.connections": 4, "network.connection.udp.error.segments": 242}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844593464", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587774], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844593469", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:16.814 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}], "event.id": 58829800624079, "event.timestamp": 1654845825, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587825, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587774, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587775, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:53:38.985 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf3.ospf3.com", "object.id": 6, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf3.ospf3.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 55168}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 500}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 56616}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 61014}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 4500}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 52187}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 54620}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 60957}], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 45, "network.connection.tcp.retransmitted.segments": 617, "network.connection.udp.connections": 10, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844599761", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587780], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844599762", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:23.133 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}], "event.id": 58829800624142, "event.timestamp": 1654845885, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587837, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587780, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587781, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:54:40.317 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf1.ospf1.com", "object.id": 8, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf1.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 57842}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 53601}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 9525, "network.connection.tcp.retransmitted.segments": 2131, "network.connection.udp.connections": 4, "network.connection.udp.error.segments": 0}, "snmp.authentication.password": "ospf1md5", "snmp.authentication.protocol": "MD5", "snmp.security.level": "Authentication No Privacy", "snmp.security.user.name": "ospf1md5", "snmp.version": "v3", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844596807", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587777], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844596808", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:19.767 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624143, "event.timestamp": 1654845885, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587832, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587777, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587778, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:54:39.607 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000008, 10000000000013], "object.host": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "object.id": 7, "object.ip": "***********", "object.make.model": "Juniper", "object.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "object.snmp.device.catalog": 58829800562463, "object.state": "ENABLE", "object.system.oid": ".*******.4.1.2636.*******.1", "object.target": "***********", "object.type": "Router", "object.vendor": "Juniper Networks", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6159}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32010}, {"destination.ip": "*********", "destination.port": 61229, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 38}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 111}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6151}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6161}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32003}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33010}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "*********", "destination.port": 56487, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 514}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 666}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6152}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6171}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6234}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 6666}, {"destination.ip": "*********", "destination.port": 60991, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32033}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 51627}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 2049}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56376}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "closed", "source.ip": "*********", "source.port": 65099}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 2606}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6156}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32011}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33072}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 33002}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56487}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 22}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6988}, {"destination.ip": "*********", "destination.port": 56376, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 49327, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32008}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 61229}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6012}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7210}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7250}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31341}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31343}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 49327}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 513}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32009}, {"destination.ip": "*********", "destination.port": 57889, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 57889}, {"destination.ip": "*********", "destination.port": 31883, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 60991}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 4784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31340}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49152}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49153}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 60247}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 704}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6333}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6784}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 2049}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 654}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 8503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31342}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 64428}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 111}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 65467}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 6666}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}], "network.connection.tcp.connections": 49, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 1, "network.connection.udp.connections": 20, "network.connection.udp.error.segments": 7327}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844611499", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587789], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844611499", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:34.700 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.*******.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.*******.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}], "event.id": 58829800624237, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587988, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587789, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587790, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Firewall", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:42.448 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000011, 10000000000009, 10000000000013], "object.host": "fg_firewall.mindarray.com", "object.id": 11, "object.ip": "***********", "object.make.model": "Fortinet FortiGate 100E", "object.name": "fg_firewall.mindarray.com", "object.snmp.device.catalog": 58829800537401, "object.state": "ENABLE", "object.system.oid": ".*******.4.1.12356.101.1.1041", "object.target": "***********", "object.type": "Firewall", "object.vendor": "Fortinet", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 65788178, "network.connection.udp.connections": 0, "network.connection.udp.error.segments": 385}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "**********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844603127", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587783], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844603129", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:27.148 pm 10/06/2022", "discovery.target": "**********", "discovery.target.name": "**********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}], "event.id": 58829800624244, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587853, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587783, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587784, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:40.986 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "cisco_core.motadata.local", "object.id": 9, "object.ip": "**********", "object.make.model": "Cisco Catalyst 93xx Switch Stack", "object.name": "cisco_core.motadata.local", "object.snmp.device.catalog": 58829800549968, "object.state": "ENABLE", "object.system.oid": ".*******.*******.2494", "object.target": "**********", "object.type": "Switch", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 49196}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 52944}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 52976}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 54467}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 51705}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 52576}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 52673}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 53217}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 51936}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 50260}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 51559}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 53368}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 2228}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 50076}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 52123}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 54377}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 53090}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 54469}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 51471}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 500}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 4500}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 52274}], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 868, "network.connection.udp.connections": 24, "network.connection.udp.error.segments": 1}, "snmp.security.level": "No Authentication No Privacy", "snmp.security.user.name": "trapuser", "snmp.version": "v3", "status": "succeed", "timeout": 60}, "************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844607145", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587786], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844607146", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:31.505 pm 10/06/2022", "discovery.target": "************", "discovery.target.name": "************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}], "event.id": 58829800624250, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587977, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587786, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587787, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:41.954 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "cisco2960.motadata.local", "object.id": 10, "object.ip": "************", "object.make.model": "Cisco Catalyst 2960 Series", "object.name": "cisco2960.motadata.local", "object.snmp.device.catalog": 58829800538399, "object.state": "ENABLE", "object.system.oid": ".*******.*******.697", "object.target": "************", "object.type": "Switch", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 65324}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 67}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 51961}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 58693}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 61708}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 62151}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 64846}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 59098}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 49414}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 52405}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 61121}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 1967}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 1975}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 2228}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 54052}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 55046}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 58999}, {"network.connection.protocol": "UDP", "source.ip": "**********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 50956}, {"network.connection.protocol": "UDP", "source.ip": "************", "source.port": 57472}, {"network.connection.protocol": "UDP", "source.ip": "*************", "source.port": 22}], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 2985, "network.connection.udp.connections": 22, "network.connection.udp.error.segments": 2}, "snmp.community": "public", "snmp.version": "v1", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846112590", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588009], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846112590", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:37.60 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.*******.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.*******.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}], "event.id": 58829800624569, "event.timestamp": 1654846125, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588088, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588009, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800588010, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:39.88 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "Leaf1-MIMIC8145", "object.id": 12, "object.ip": "***********", "object.make.model": "Cisco Nexus 9396PX", "object.name": "Leaf1-MIMIC8145", "object.snmp.device.catalog": 58829800549415, "object.state": "ENABLE", "object.system.oid": ".*******.*******2.3.1.3.1508", "object.target": "***********", "object.type": "Switch", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 9769, "network.connection.udp.connections": 0, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846121810", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588090], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846121812", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:45.715 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624608, "event.timestamp": 1654846135, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588144, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588090, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800588091, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:47.740 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "hp4202vl-48g-MIMIC8806", "object.id": 13, "object.ip": "*************", "object.make.model": "ProCurve Switch 4202vl-48G", "object.name": "hp4202vl-48g-MIMIC8806", "object.snmp.device.catalog": 58829800527850, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 23}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 80}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 1506}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 1513}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 0}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 69}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 1064}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 1507}], "network.connection.tcp.connections": 4, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 0, "network.connection.udp.connections": 5, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846130604", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588151], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846130605", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:54.513 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624647, "event.timestamp": 1654846145, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588205, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588151, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800588152, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:56.534 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "hp4202vl-48g-MIMIC9136", "object.id": 14, "object.ip": "*************", "object.make.model": "ProCurve Switch 4202vl-48G", "object.name": "hp4202vl-48g-MIMIC9136", "object.snmp.device.catalog": 58829800527850, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 23}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 80}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 1506}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 1513}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 69}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 1064}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 1507}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 0}], "network.connection.tcp.connections": 4, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 0, "network.connection.udp.connections": 5, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846148578", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588298], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846148579", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:59:12.934 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624702, "event.timestamp": 1654846155, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588379, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588298, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800588299, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:59:14.965 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "J4850A-MIMIC8814", "object.id": 16, "object.ip": "*************", "object.make.model": "HP ProCurve Switch 5304XL", "object.name": "J4850A-MIMIC8814", "object.snmp.device.catalog": 58829800561806, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 23}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 80}, {"destination.ip": "************", "destination.port": 43070, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "***********", "source.port": 23}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 0}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}], "network.connection.tcp.connections": 3, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 11, "network.connection.udp.connections": 3, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846139370", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588211], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846139371", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:59:03.840 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624705, "event.timestamp": 1654846155, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588292, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800588211, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800588212, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:59:05.868 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "J4850A-MIMIC9087", "object.id": 15, "object.ip": "*************", "object.make.model": "HP ProCurve Switch 5304XL", "object.name": "J4850A-MIMIC9087", "object.snmp.device.catalog": 58829800561806, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "************", "destination.port": 43070, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "***********", "source.port": 23}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 23}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 80}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 0}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}], "network.connection.tcp.connections": 3, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 11, "network.connection.udp.connections": 3, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844596807", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587777], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844596808", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:19.767 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624143, "event.timestamp": 1654845885, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587832, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587777, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587778, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:54:39.607 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000008, 10000000000013], "object.host": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "object.id": 7, "object.ip": "***********", "object.make.model": "Juniper", "object.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "object.snmp.device.catalog": 58829800562463, "object.state": "ENABLE", "object.system.oid": ".*******.4.1.2636.*******.1", "object.target": "***********", "object.type": "Router", "object.vendor": "Juniper Networks", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6159}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32010}, {"destination.ip": "*********", "destination.port": 61229, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 38}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 111}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6151}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6161}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32003}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33010}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "*********", "destination.port": 56487, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 514}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 666}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6152}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6171}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6234}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 6666}, {"destination.ip": "*********", "destination.port": 60991, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32033}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 51627}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 2049}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56376}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "closed", "source.ip": "*********", "source.port": 65099}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 2606}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6156}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32011}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33072}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 33002}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56487}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 22}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6988}, {"destination.ip": "*********", "destination.port": 56376, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 49327, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32008}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 61229}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6012}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7210}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7250}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31341}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31343}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 49327}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 513}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32009}, {"destination.ip": "*********", "destination.port": 57889, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 57889}, {"destination.ip": "*********", "destination.port": 31883, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 60991}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 4784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31340}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49152}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49153}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 60247}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 704}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6333}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6784}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 2049}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 654}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 8503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31342}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 64428}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 111}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 65467}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 6666}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}], "network.connection.tcp.connections": 49, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 1, "network.connection.udp.connections": 20, "network.connection.udp.error.segments": 7327}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844599761", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587780], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844599762", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:23.133 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}], "event.id": 58829800624142, "event.timestamp": 1654845885, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587837, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587780, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587781, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:54:40.317 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf1.ospf1.com", "object.id": 8, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf1.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 57842}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 53601}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 9525, "network.connection.tcp.retransmitted.segments": 2131, "network.connection.udp.connections": 4, "network.connection.udp.error.segments": 0}, "snmp.authentication.password": "ospf1md5", "snmp.authentication.protocol": "MD5", "snmp.security.level": "Authentication No Privacy", "snmp.security.user.name": "ospf1md5", "snmp.version": "v3", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844590141", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587771], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844590142", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:13.468 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624047, "event.timestamp": 1654845765, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587817, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587771, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587772, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:52:38.346 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf2.ospf2.com", "object.id": 5, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf2.ospf2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "***********", "destination.port": 43394, "network.connection.protocol": "TCP", "network.connection.state": "time wait", "source.ip": "***********", "source.port": 80}, {"destination.ip": "***********", "destination.port": 59686, "network.connection.protocol": "TCP", "network.connection.state": "time wait", "source.ip": "***********", "source.port": 80}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 59706}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 63376}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}], "network.connection.tcp.connections": 2, "network.connection.tcp.error.segments": 11, "network.connection.tcp.retransmitted.segments": 4789, "network.connection.udp.connections": 4, "network.connection.udp.error.segments": 242}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844593464", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587774], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844593469", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:16.814 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}], "event.id": 58829800624079, "event.timestamp": 1654845825, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587825, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587774, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587775, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:53:38.985 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf3.ospf3.com", "object.id": 6, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf3.ospf3.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 55168}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 500}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 56616}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 61014}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 4500}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 52187}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 54620}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 60957}], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 45, "network.connection.tcp.retransmitted.segments": 617, "network.connection.udp.connections": 10, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844577937", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587759], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844577939", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:01.186 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.5"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.1"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.2"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.3"}, {"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.1.4"}], "event.id": 58829800623942, "event.timestamp": 1654844927, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587795, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587759, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587760, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:38:38.401 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "isis2.isis2", "object.id": 1, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "isis2.isis2", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 55464}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}], "network.connection.tcp.connections": 0, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 0, "network.connection.udp.connections": 3, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844581172", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587762], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844581175", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:04.161 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800623963, "event.timestamp": 1654845286, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587801, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587762, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587763, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:44:37.729 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000008, 10000000000013], "object.host": "<PERSON><PERSON><PERSON><PERSON>", "object.id": 2, "object.ip": "***********", "object.make.model": "Juniper", "object.name": "<PERSON><PERSON><PERSON><PERSON>", "object.snmp.device.catalog": 58829800562463, "object.state": "ENABLE", "object.system.oid": ".*******.4.1.2636.*******.1", "object.target": "***********", "object.type": "Router", "object.vendor": "Juniper Networks", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 22}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 51627}, {"destination.ip": "*********", "destination.port": 31883, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 60843}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 111}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 60171}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 666}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6159}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6234}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "*********", "destination.port": 54611, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 38}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6988}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32010}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 6666}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 52239}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "closed", "source.ip": "*********", "source.port": 58593}, {"destination.ip": "*********", "destination.port": 60843, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 65124}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 513}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 2606}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6156}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31341}, {"destination.ip": "*********", "destination.port": 60171, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 54611}, {"destination.ip": "*********", "destination.port": 65124, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56243}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6012}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6151}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6152}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7210}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31343}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32008}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32033}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33010}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 2049}, {"destination.ip": "*********", "destination.port": 52239, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6171}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7250}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32003}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32009}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 33002}, {"destination.ip": "*********", "destination.port": 56243, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 514}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6161}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32011}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33072}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 2049}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 988}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49152}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49153}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 111}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 965}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31340}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 62538}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 56994}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6333}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 8503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31342}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 4784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 65467}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 6666}], "network.connection.tcp.connections": 49, "network.connection.tcp.error.segments": 0, "network.connection.tcp.retransmitted.segments": 0, "network.connection.udp.connections": 20, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844584149", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587765], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844584150", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:07.135 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800623989, "event.timestamp": 1654845645, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587805, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587765, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587766, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:50:37.77 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "bgp2.bgp2.com", "object.id": 3, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "bgp2.bgp2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "***********", "destination.port": 60111, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "***********", "source.port": 179}, {"destination.ip": "***********", "destination.port": 56337, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "***********", "source.port": 179}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 58605}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 162}, {"network.connection.protocol": "UDP", "source.ip": "***********", "source.port": 52574}], "network.connection.tcp.connections": 2, "network.connection.tcp.error.segments": 24, "network.connection.tcp.retransmitted.segments": 137, "network.connection.udp.connections": 4, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844587128", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587768], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844587133", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:10.124 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624018, "event.timestamp": 1654845705, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587813, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587768, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Network Connection", "metric.object": 58829800587769, "metric.plugin": "snmpnetworkconnection", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:51:37.707 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000008, 10000000000013], "object.host": "bgpjunos", "object.id": 4, "object.ip": "***********", "object.make.model": "Juniper", "object.name": "bgpjunos", "object.snmp.device.catalog": 58829800562463, "object.state": "ENABLE", "object.system.oid": ".*******.4.1.2636.*******.1", "object.target": "***********", "object.type": "Router", "object.vendor": "Juniper Networks", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 208, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"correlation.metrics": ["network.connection"], "network.connection": [{"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6161}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6156}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6152}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32003}, {"destination.ip": "*********", "destination.port": 6153, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 62796}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 513}, {"destination.ip": "*********", "destination.port": 56259, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "*********", "destination.port": 52819, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31883}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 2606}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32011}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 2049}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7250}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6988}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32010}, {"destination.ip": "*********", "destination.port": 56806, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 33002}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6159}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56806}, {"destination.ip": "***********", "destination.port": 179, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "***********", "source.port": 56337}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 514}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6151}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "closed", "source.ip": "*********", "source.port": 52103}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 111}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 179}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6171}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31343}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32008}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 22}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 7210}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 38}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33072}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6153}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 55601}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32033}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 6666}, {"destination.ip": "*********", "destination.port": 56040, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 65531}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6154}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 31341}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33010}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "*********", "source.port": 31884}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 666}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6234}, {"destination.ip": "*********", "destination.port": 62796, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 6153}, {"destination.ip": "*********", "destination.port": 62281, "network.connection.protocol": "TCP", "network.connection.state": "closed", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 55601, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 31883, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 52819}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 6012}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 33083}, {"destination.ip": "*********", "destination.port": 31884, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56259}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 64965}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 32009}, {"destination.ip": "*********", "destination.port": 64965, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 9000}, {"destination.ip": "*********", "destination.port": 9000, "network.connection.protocol": "TCP", "network.connection.state": "established", "source.ip": "*********", "source.port": 56040}, {"destination.ip": "0.0.0.0", "destination.port": 0, "network.connection.protocol": "TCP", "network.connection.state": "listen", "source.ip": "0.0.0.0", "source.port": 51627}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49152}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49153}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 49798}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 54417}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 6666}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 111}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 161}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31340}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 31342}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 61168}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 736}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 8503}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 65467}, {"network.connection.protocol": "UDP", "source.ip": "*********", "source.port": 2049}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 661}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 3784}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 6333}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 514}, {"network.connection.protocol": "UDP", "source.ip": "0.0.0.0", "source.port": 4784}], "network.connection.tcp.connections": 58, "network.connection.tcp.error.segments": 1, "network.connection.tcp.retransmitted.segments": 1, "network.connection.udp.connections": 21, "network.connection.udp.error.segments": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}}