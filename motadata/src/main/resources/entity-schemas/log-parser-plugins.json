{"entity": "<PERSON>g Pa<PERSON><PERSON>", "table": "tbl_config_log_parser_plugin", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "log.parser.plugin.name", "title": "Name", "type": "string", "rules": ["required", "unique"]}, {"name": "log.parser.plugin.context", "title": "<PERSON><PERSON><PERSON> Plugin Context", "type": "map", "rules": ["required"]}], "entries": [{"type": "script", "directory": "plugins", "records": [{"id": 10000000000001, "log.parser.plugin.name": "CiscoASALogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoASALogParserPlugin.java"}}, {"id": 10000000000002, "log.parser.plugin.name": "CiscoSwitchLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoSwitchLogParserPlugin.java"}}, {"id": 10000000000003, "log.parser.plugin.name": "FortinetLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "FortinetLogParserPlugin.java"}}, {"id": 10000000000004, "log.parser.plugin.name": "LinuxLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "LinuxLogParserPlugin.java"}}, {"id": 10000000000005, "log.parser.plugin.name": "MicrosoftExchangeLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MicrosoftExchangeLogParserPlugin.java"}}, {"id": 10000000000006, "log.parser.plugin.name": "MySQLLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MySQLLogParserPlugin.java"}}, {"id": 10000000000007, "log.parser.plugin.name": "MySQLSlowQueryLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MySQLSlowQueryLogParserPlugin.java"}}, {"id": 10000000000008, "log.parser.plugin.name": "OracleDatabaseLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "OracleDatabaseLogParserPlugin.java"}}, {"id": 10000000000009, "log.parser.plugin.name": "PaloAltoLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "PaloAltoLogParserPlugin.java"}}, {"id": 10000000000010, "log.parser.plugin.name": "PostgreSQLLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "PostgreSQLLogParserPlugin.java"}}, {"id": 10000000000011, "log.parser.plugin.name": "SonicWallLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "SonicWallLogParserPlugin.java"}}, {"id": 10000000000012, "log.parser.plugin.name": "SophosLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "SophosLogParserPlugin.java"}}, {"id": 10000000000013, "log.parser.plugin.name": "WindowsFirewallLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "WindowsFirewallLogParserPlugin.java"}}, {"id": 10000000000014, "log.parser.plugin.name": "WindowsLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "WindowsLogParserPlugin.java"}}, {"id": 10000000000015, "log.parser.plugin.name": "WindowsUpdateLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "WindowsUpdateLogParserPlugin.java"}}, {"id": 10000000000016, "log.parser.plugin.name": "NutanixLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "NutanixLogParserPlugin.java"}}, {"id": 10000000000017, "log.parser.plugin.name": "VMwareESXiLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "VMwareESXiLogParserPlugin.java"}}, {"id": 10000000000018, "log.parser.plugin.name": "VMwarevCenterLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "VMwarevCenterLogParserPlugin.java"}}, {"id": 10000000000019, "log.parser.plugin.name": "AzureLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "AzureLogParserPlugin.java"}}, {"id": 10000000000020, "log.parser.plugin.name": "CiscoACILogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoACILogParserPlugin.java"}}], "version": "1.4"}, {"type": "script", "directory": "plugins", "records": [{"id": 10000000000021, "log.parser.plugin.name": "CiscoRouterLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoRouterLogParserPlugin.java"}}], "version": "1.5"}, {"type": "script", "directory": "plugins", "records": [{"id": 10000000000022, "log.parser.plugin.name": "ApacheSolrLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "ApacheSolrLogParserPlugin.java"}}, {"id": 10000000000023, "log.parser.plugin.name": "APIAuditServiceLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "APIAuditServiceLogParserPlugin.java"}}, {"id": 10000000000024, "log.parser.plugin.name": "ArrayDDoSLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "ArrayDDoSLogParserPlugin.java"}}, {"id": 10000000000025, "log.parser.plugin.name": "ArubaLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "ArubaLogParserPlugin.java"}}, {"id": 10000000000026, "log.parser.plugin.name": "BrocadeSwitchLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "BrocadeSwitchLogParserPlugin.java"}}, {"id": 10000000000027, "log.parser.plugin.name": "CheckPointLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CheckPointLogParserPlugin.java"}}, {"id": 10000000000028, "log.parser.plugin.name": "CheckPointFirewallLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CheckPointFirewallLogParserPlugin.java"}}, {"id": 10000000000029, "log.parser.plugin.name": "CommonAccessLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CommonAccessLogParserPlugin.java"}}, {"id": 10000000000030, "log.parser.plugin.name": "FortiManagerLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "FortiManagerLogParserPlugin.java"}}, {"id": 10000000000031, "log.parser.plugin.name": "IronportStatusLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "IronportStatusLogParserPlugin.java"}}, {"id": 10000000000032, "log.parser.plugin.name": "FortiWANLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "FortiWANLogParserPlugin.java"}}, {"id": 10000000000033, "log.parser.plugin.name": "MongoDBLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MongoDBLogParserPlugin.java"}}, {"id": 10000000000034, "log.parser.plugin.name": "MSSQLDatabaseAuditLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MSSQLDatabaseAuditLogParserPlugin.java"}}, {"id": 10000000000035, "log.parser.plugin.name": "OCSRequestLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "OCSRequestLogParserPlugin.java"}}, {"id": 10000000000036, "log.parser.plugin.name": "PostgreSQLSlowQueryLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "PostgreSQLSlowQueryLogParserPlugin.java"}}, {"id": 10000000000037, "log.parser.plugin.name": "SMSFSortedAuditLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "SMSFSortedAuditLogParserPlugin.java"}}, {"id": 10000000000038, "log.parser.plugin.name": "TejasSwitchLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "TejasSwitchLogParserPlugin.java"}}, {"id": 10000000000039, "log.parser.plugin.name": "WatchGuardFirewallLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "WatchGuardFirewallLogParserPlugin.java"}}], "version": "1.6"}, {"type": "script", "directory": "plugins", "records": [{"id": 10000000000031, "log.parser.plugin.name": "IronPortStatusLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "IronPortStatusLogParserPlugin.java"}}], "version": "1.7"}, {"type": "script", "directory": "plugins", "records": [{"id": 10000000000040, "log.parser.plugin.name": "CiscoConfigChangeLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "CiscoConfigChangeLogParserPlugin.java"}}, {"id": 10000000000041, "log.parser.plugin.name": "JuniperConfigChangeLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "JuniperConfigChangeLogParserPlugin.java"}}], "version": "1.8"}, {"type": "script", "directory": "plugins", "records": [{"id": 10000000000042, "log.parser.plugin.name": "PFSenseLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "PFSenseLogParserPlugin.java"}}, {"id": 10000000000043, "log.parser.plugin.name": "WebSphereLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "WebSphereLogParserPlugin.java"}}], "version": "1.9"}]}