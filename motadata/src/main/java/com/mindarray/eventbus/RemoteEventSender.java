/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	11-Jul-2025		<PERSON><PERSON><PERSON><PERSON><PERSON>		    Renamed from remote-event-forwarder to sender , make it general to send event to APP.
*/


package com.mindarray.eventbus;


import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * The RemoteEventSender class forwards events to remote components using ZeroMQ's PUSH socket.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Connecting to remote endpoints for event forwarding</li>
 *   <li>Encrypting and compressing messages before transmission</li>
 *   <li>Formatting messages with appropriate topic headers</li>
 *   <li>Managing connection lifecycle and error handling</li>
 *   <li>Configuring high-water marks to handle back-pressure</li>
 * </ul>
 * <p>
 * The RemoteEventSender connects to a specified host and port, and forwards events received
 * from the Vert.x event bus to that endpoint. It adapts its behavior based on the bootstrap type
 * (APP, AGENT, COLLECTOR, MANAGER, etc.) to handle different event types and routing requirements.
 * Example usage:
 * <pre>
 * // Create and deploy a RemoteEventSender
 * RemoteEventSender forwarder = new RemoteEventSender(5555, "remote-host");
 * vertx.deployVerticle(forwarder);
 *
 * // Send an event to be forwarded
 * JsonObject event = new JsonObject()
 *     .put("type", "metric-data")
 *     .put("value", 75.5)
 *     .put("timestamp", System.currentTimeMillis());
 *
 * vertx.eventBus().send("EVENT_REMOTE", event);
 * </pre>
 */
public class RemoteEventSender extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(RemoteEventSender.class, GlobalConstants.MOTADATA_EVENT_BUS, "Remote Event Sender");

    private final TransportClient client;

    public RemoteEventSender(JsonObject config)
    {
        client = TransportProvider.getClient(config);
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            client.connect().onComplete(handler ->
            {
                try
                {
                    if (handler.succeeded())
                    {
                        var cipherUtil = new CipherUtil();

                        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP)
                        {
                            if (config().getString(EVENT_TYPE).equalsIgnoreCase(EVENT_HA_OBSERVER))
                            {
                                vertx.eventBus().<JsonObject>localConsumer(EVENT_HA_OBSERVER, message ->
                                {
                                    try
                                    {
                                        if (message.body() != null && !message.body().isEmpty())
                                        {
                                            client.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.MOTADATA_OBSERVER_TOPIC.length())).appendString(EventBusConstants.MOTADATA_OBSERVER_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);
                                    }
                                });
                            }
                            else
                            {
                                vertx.eventBus().<JsonObject>localConsumer(EVENT_REMOTE, message ->
                                {
                                    try
                                    {
                                        if (message.body() != null && !message.body().isEmpty())
                                        {
                                            client.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC.length())).appendString(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);
                                    }
                                });
                            }
                        }
                        else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.FLOW_COLLECTOR ||
                                Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR ||
                                Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_PROCESSOR ||
                                Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR)
                        {
                            vertx.eventBus().<JsonObject>localConsumer(EVENT_REMOTE, message ->
                            {
                                try
                                {
                                    var event = message.body(); // for HA we will pass ha topic so send message on that topic itself

                                    if (CommonUtil.isNotNullOrEmpty(event.getString(EventBusConstants.EVENT_TOPIC)))
                                    {
                                        client.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(event.getString(EventBusConstants.EVENT_TOPIC).length())).appendString(event.getString(EventBusConstants.EVENT_TOPIC)).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                                    }
                                    else
                                    {
                                        client.send(Buffer.buffer().appendShortLE(CommonUtil.getShort((EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC + EventBusConstants.EVENT_TOPIC_DELIMITER).length())).appendString(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC + EventBusConstants.EVENT_TOPIC_DELIMITER).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                                    }
                                }

                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }

                            }).exceptionHandler(LOGGER::error);
                        }
                        else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.AGENT)
                        {
                            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_AGENT, message ->
                            {
                                try
                                {
                                    if (message.body() != null && !message.body().isEmpty())
                                    {
                                        client.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.AGENT_TOPIC.length())).appendString(EventBusConstants.AGENT_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        }
                        else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.MANAGER)
                        {
                            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_MOTADATA_MANAGER, message ->
                            {
                                try
                                {
                                    if (message.body() != null && !message.body().isEmpty())
                                    {
                                        client.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.MOTADATA_MANAGER_TOPIC.length())).appendString(EventBusConstants.MOTADATA_MANAGER_TOPIC).appendString(message.body().encode()).getBytes());
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        }

                        promise.complete();
                    }
                    else
                    {
                        promise.fail(handler.cause());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        try
        {
            client.close();

            promise.complete();
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }
    }
}
