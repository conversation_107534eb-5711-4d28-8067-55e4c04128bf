/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.Tag;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Tag.TAG;
import static com.mindarray.api.Tag.TAG_TYPE;
import static com.mindarray.db.DBConstants.FIELD_NAME;
import static com.mindarray.db.DBConstants.TBL_TAG;

public class TagConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(TagConfigStore.class, GlobalConstants.MOTADATA_STORE, "Tag Config Store");

    private static final TagConfigStore STORE = new TagConfigStore();

    private final Map<String, Long> itemsByName = new ConcurrentHashMap<>();//key ->  tag name ,value -> id

    private final Map<Long, String> itemsById = new ConcurrentHashMap<>();//key ->  id ,value -> tag name

    private TagConfigStore()
    {
        super(DBConstants.TBL_TAG, LOGGER, true);
    }

    public static TagConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    TagConfigStore.getStore().updateItems(items);

                    promise.complete();
                }

                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init tag config store...");

                    LOGGER.error(exception);
                }
            }

            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    //type = "0"  : Default tags(System tags)
    //type = "1"  : User tags
    //type = "" : All tags (Default + User tags)
    public JsonArray getItems(JsonArray ids, Tag.TagType tagType, String type)
    {
        var items = new JsonArray();

        try
        {
            for (var index = 0; index < ids.size(); index++)
            {
                var item = this.items.get(ids.getLong(index));

                if (item.getJsonArray(TAG_TYPE).contains(tagType.getName()) && (item.getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(type) || EMPTY_VALUE.equalsIgnoreCase(type)))
                {
                    items.add(item.getString(Tag.TAG));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    public JsonArray getItemsByTagType(Tag.TagType type)
    {
        JsonArray items = null;

        try
        {
            items = new JsonArray(this.items.values().stream().filter(entries -> entries.getJsonArray(TAG_TYPE).contains(type.getName())).map(entries -> entries.getString(Tag.TAG)).collect(Collectors.toList()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    private void addItems(JsonArray items, JsonArray ids, JsonObject record)
    {
        try
        {
            if (items != null && !items.isEmpty())
            {
                Bootstrap.configDBService().saveAll(TBL_TAG, items, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.failed())
                    {
                        LOGGER.warn(String.format("failed to add tags, reason: %s", result.cause().getMessage()));
                    }
                });
            }

            if (!ids.isEmpty())
            {
                Bootstrap.configDBService().updateAll(TBL_TAG,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, ids),
                        record,
                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            if (result.failed())
                            {
                                LOGGER.warn(String.format("failed to update tags, reason: %s", result.cause().getMessage()));
                            }
                        });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public void updateItems(Map<Long, JsonObject> items)
    {
        if (!items.isEmpty())
        {
            for (var entry : items.entrySet())
            {
                itemsByName.put(entry.getValue().getString(TAG), entry.getKey());

                itemsById.put(entry.getKey(), entry.getValue().getString(TAG));
            }
        }
    }

    public JsonArray getIdsByItems(JsonArray items)
    {
        var ids = new JsonArray();

        try
        {
            for (var index = 0; index < items.size(); index++)
            {
                ids.add(itemsByName.get(items.getString(index)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return ids;
    }

    public JsonArray addItems(JsonArray tags, String tagType, String type)
    {
        var hashCodes = new JsonArray();

        try
        {
            var items = new JsonArray();

            var params = new JsonObject();

            var ids = new JsonArray();

            for (var index = 0; index < tags.size(); index++)
            {
                var update = false;

                var tag = tags.getString(index);

                var hashCode = (long) tag.hashCode();

                var id = itemsByName.getOrDefault(tag, -1L);

                //if tag is not stored in table
                if (id == NOT_AVAILABLE)
                {
                    itemsByName.put(tag, hashCode);

                    itemsById.put(hashCode, tag);

                    hashCodes.add(hashCode);

                    var item = new JsonObject().put(DBConstants.FIELD_TYPE, type).put(Tag.TAG, tag).put(TAG_TYPE, new JsonArray().add(tagType)).put(ID, hashCode);

                    this.items.put(hashCode, item);

                    items.add(item);
                }
                else
                {
                    var item = this.items.get(id);

                    var tagTypes = item.getJsonArray(TAG_TYPE);

                    var itemType = item.getString(DBConstants.FIELD_TYPE);

                    if (!tagTypes.contains(tagType))
                    {
                        update = true;

                        params.put(TAG_TYPE, tagTypes.add(tagType));
                    }

                    //if tag is User tag(1), But we receive same tag as System tag(0) we will change it's type to System Tag
                    if (DBConstants.ENTITY_TYPE_USER.equalsIgnoreCase(itemType) && DBConstants.ENTITY_TYPE_SYSTEM.equalsIgnoreCase(type))
                    {
                        update = true;

                        params.put(DBConstants.FIELD_TYPE, type);
                    }

                    if (update)
                    {
                        ids.add(id);

                        this.items.put(hashCode, item.mergeIn(params));
                    }

                    hashCodes.add(id);
                }
            }

            if (!items.isEmpty() || !ids.isEmpty())
            {
                addItems(items, ids, params);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return hashCodes;
    }

    public String getTag(long id)
    {
        return itemsById.getOrDefault(id, EMPTY_VALUE);
    }

    public long getTag(String name)
    {
        return itemsByName.getOrDefault(name, DUMMY_ID);
    }

    public JsonArray getItems(JsonArray ids)
    {
        var items = new JsonArray();

        try
        {
            if (ids != null)
            {
                for (var i = 0; i < ids.size(); i++)
                {
                    if (itemsById.get(ids.getLong(i)) != null)
                    {
                        items.add(itemsById.get(ids.getLong(i)));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }
}
