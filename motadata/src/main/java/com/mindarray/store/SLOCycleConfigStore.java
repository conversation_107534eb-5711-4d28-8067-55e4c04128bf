package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.SLOCycle;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;

public class SLOCycleConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(SLOCycleConfigStore.class, GlobalConstants.MOTADATA_STORE, "SLO Cycle Config Store");

    private static final SLOCycleConfigStore STORE = new SLOCycleConfigStore();

    private final AtomicLong COUNTER = new AtomicLong(0);

    private SLOCycleConfigStore()
    {
        super(DBConstants.TBL_SLO_CYCLE, LOGGER, false);
    }

    public static SLOCycleConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                int maximum = 0;

                try
                {
                    for (var item : items.values())
                    {
                        if (item.getInteger(GlobalConstants.ID) > maximum)
                        {
                            maximum = item.getInteger(GlobalConstants.ID);
                        }
                    }

                    COUNTER.set(maximum);

                    promise.complete();
                }
                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init SLO cycle config store...");

                    LOGGER.error(exception);
                }
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    public long newId()
    {
        return COUNTER.incrementAndGet();
    }
}
