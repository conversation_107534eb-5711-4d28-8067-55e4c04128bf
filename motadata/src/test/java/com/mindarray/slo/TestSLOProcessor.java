package com.mindarray.slo;

import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.api.SLOCycle;
import com.mindarray.api.SLOProfile;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.TreeMap;

import static com.mindarray.GlobalConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSLOProcessor
{
    private static final Logger LOGGER = new Logger(TestSLOProcessor.class, GlobalConstants.MOTADATA_SLO, "Test SLO Processor");

    // Test data context
    private static final JsonObject testContext = new JsonObject();
    private static MessageConsumer<JsonObject> messageConsumer;

    // SLO Processor instance and reflection fields
    private static SLOProcessor sloProcessor;
    private static Field sloDetailsField;
    private static Field sloCyclesField;
    private static Field availabilitySLOProfilesField;
    private static Field sloEventsField;
    private static Field countField;

    // Calculation methods via reflection
    private static Method getSLOAchievedPercentageMethod;
    private static Method getSLOViolatedSecondsMethod;
    private static Method getSLOMeanTimeToResolveMethod;
    private static Method getSLOMeanTimeBetweenFailureMethod;
    private static Method getSLOErrorBudgetLeftPercentageMethod;
    private static Method getSLOErrorBudgetLeftSecondsMethod;
    private static Method getSLOBurnRateSecondsMethod;
    private static Method getSLOBurnRateMethod;
    private static Method getSLOStatusMethod;
    private static Method updateSLOMethod;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            // Get existing SLO profiles created by TestSLOProfile
            var monitorAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorAvailabilitySLO");
            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorPerformanceSLO");
            var instanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstanceAvailabilitySLO");
            var instancePerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstancePerformanceSLO");

            // Store profile IDs for testing
            TestSLOProcessor.testContext.put("monitorAvailabilityId", monitorAvailabilitySLO.getLong(ID));
            TestSLOProcessor.testContext.put("monitorPerformanceId", monitorPerformanceSLO.getLong(ID));
            TestSLOProcessor.testContext.put("instanceAvailabilityId", instanceAvailabilitySLO.getLong(ID));
            TestSLOProcessor.testContext.put("instancePerformanceId", instancePerformanceSLO.getLong(ID));

            // Initialize SLOProcessor instance for testing
            sloProcessor = new SLOProcessor();

            // Setup reflection access to private fields
            setupReflectionFields();

            // Setup reflection access to private methods
            setupReflectionMethods();

            LOGGER.info("SLO Processor test setup completed successfully");
            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("Running test case: %s", testInfo.getTestMethod().get().getName()));
        testContext.completeNow();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    // ======================== Setup Helper Methods ========================

    /**
     * Setup reflection access to private fields of SLOProcessor
     */
    private static void setupReflectionFields() throws Exception
    {
        sloDetailsField = SLOProcessor.class.getDeclaredField("sloDetails");
        sloDetailsField.setAccessible(true);

        sloCyclesField = SLOProcessor.class.getDeclaredField("sloCycles");
        sloCyclesField.setAccessible(true);

        availabilitySLOProfilesField = SLOProcessor.class.getDeclaredField("availabilitySLOProfiles");
        availabilitySLOProfilesField.setAccessible(true);

        sloEventsField = SLOProcessor.class.getDeclaredField("sloEvents");
        sloEventsField.setAccessible(true);

        countField = SLOProcessor.class.getDeclaredField("count");
        countField.setAccessible(true);
    }

    /**
     * Setup reflection access to private methods of SLOProcessor
     */
    private static void setupReflectionMethods() throws Exception
    {
        getSLOAchievedPercentageMethod = SLOProcessor.class.getDeclaredMethod("getSLOAchievedPercentage", JsonObject.class, JsonObject.class);
        getSLOAchievedPercentageMethod.setAccessible(true);

        getSLOViolatedSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOViolatedSeconds", JsonObject.class, JsonObject.class);
        getSLOViolatedSecondsMethod.setAccessible(true);

        getSLOMeanTimeToResolveMethod = SLOProcessor.class.getDeclaredMethod("getSLOMeanTimeToResolve", JsonObject.class, JsonObject.class);
        getSLOMeanTimeToResolveMethod.setAccessible(true);

        getSLOMeanTimeBetweenFailureMethod = SLOProcessor.class.getDeclaredMethod("getSLOMeanTimeBetweenFailure", JsonObject.class, JsonObject.class, long.class);
        getSLOMeanTimeBetweenFailureMethod.setAccessible(true);

        getSLOErrorBudgetLeftPercentageMethod = SLOProcessor.class.getDeclaredMethod("getSLOErrorBudgetLeftPercentage", JsonObject.class, JsonObject.class);
        getSLOErrorBudgetLeftPercentageMethod.setAccessible(true);

        getSLOErrorBudgetLeftSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOErrorBudgetLeftSeconds", JsonObject.class, JsonObject.class);
        getSLOErrorBudgetLeftSecondsMethod.setAccessible(true);

        getSLOBurnRateSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOBurnRateSeconds", JsonObject.class, JsonObject.class);
        getSLOBurnRateSecondsMethod.setAccessible(true);

        getSLOBurnRateMethod = SLOProcessor.class.getDeclaredMethod("getSLOBurnRate", long.class, JsonObject.class);
        getSLOBurnRateMethod.setAccessible(true);

        getSLOStatusMethod = SLOProcessor.class.getDeclaredMethod("getSLOStatus", JsonObject.class, JsonObject.class, int.class, int.class);
        getSLOStatusMethod.setAccessible(true);

        updateSLOMethod = SLOProcessor.class.getDeclaredMethod("updateSLO", JsonObject.class, SLOConstants.SLOFlapStatus.class, long.class, long.class, String.class, long.class, long.class, boolean.class);
        updateSLOMethod.setAccessible(true);
    }

    // ======================== SLO Calculation Tests ========================

    /**
     * Test SLO achieved percentage calculation
     * Formula: (duration left * 100) / total duration
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testSLOAchievedPercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Create test data with known values
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 8640L); // 86.4% of 10000 seconds left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 10000L); // Total duration 10000 seconds

            // Expected: (8640 * 100) / 10000 = 86.4%
            var expectedPercentage = 86.4f;

            var actualPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedPercentage, actualPercentage, 0.01f, "SLO achieved percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedPercentage, actualPercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO violated seconds calculation
     * Formula: total duration - duration left
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testSLOViolatedSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 7200L); // 2 hours left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Expected: 86400 - 7200 = 79200 seconds violated
            var expectedViolatedSeconds = 79200L;

            var actualViolatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedViolatedSeconds, actualViolatedSeconds, "SLO violated seconds calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedViolatedSeconds, actualViolatedSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Mean Time To Resolve (MTTR) calculation
     * Formula: violated seconds / down incident count
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testSLOMeanTimeToResolveCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 4); // 4 down incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Violated seconds: 86400 - 82800 = 3600 seconds
            // MTTR: 3600 / 4 = 900 seconds (15 minutes per incident)
            var expectedMTTR = 900L;

            var actualMTTR = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedMTTR, actualMTTR, "MTTR calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTTR, actualMTTR));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test MTTR calculation with zero incidents (edge case)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSLOMeanTimeToResolveWithZeroIncidents(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L) // Full duration left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0); // No incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Expected: 0 (no incidents means no MTTR)
            var expectedMTTR = 0L;

            var actualMTTR = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedMTTR, actualMTTR, "MTTR calculation with zero incidents failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTTR, actualMTTR));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Mean Time Between Failure (MTBF) calculation
     * Formula: (total duration - violated seconds) / up incident count
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testSLOMeanTimeBetweenFailureCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.UP_INCIDENT_COUNT, 3); // 3 up incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            var duration = 86400L;

            // Violated seconds: 86400 - 82800 = 3600 seconds
            // Uptime: 86400 - 3600 = 82800 seconds
            // MTBF: 82800 / 3 = 27600 seconds (7.67 hours between failures)
            var expectedMTBF = 27600L;

            var actualMTBF = (Long) getSLOMeanTimeBetweenFailureMethod.invoke(sloProcessor, sloData, sloCycle, duration);

            assertEquals(expectedMTBF, actualMTBF, "MTBF calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTBF, actualMTBF));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Error Budget Left Percentage calculation
     * Formula: 100 - ((total duration - duration left) * 100 / acceptable violation time)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testSLOErrorBudgetLeftPercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 85500L); // 900 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours total
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget (2.4 hours)

            // Violated: 86400 - 85500 = 900 seconds
            // Error budget used: (900 * 100) / 8640 = 10.42%
            // Error budget left: 100 - 10.42 = 89.58%
            var expectedErrorBudgetLeft = 89.58f;

            var actualErrorBudgetLeft = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedErrorBudgetLeft, actualErrorBudgetLeft, 0.01f, "Error budget left percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedErrorBudgetLeft, actualErrorBudgetLeft));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Error Budget Left Seconds calculation
     * Formula: acceptable violation time - (total duration - duration left)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testSLOErrorBudgetLeftSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 84600L); // 1800 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours total
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            // Violated: 86400 - 84600 = 1800 seconds
            // Error budget left: 8640 - 1800 = 6840 seconds
            var expectedErrorBudgetLeftSeconds = 6840L;

            var actualErrorBudgetLeftSeconds = (Long) getSLOErrorBudgetLeftSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedErrorBudgetLeftSeconds, actualErrorBudgetLeftSeconds, "Error budget left seconds calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedErrorBudgetLeftSeconds, actualErrorBudgetLeftSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Burn Rate Seconds calculation
     * Formula: current violated seconds - last violated seconds
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testSLOBurnRateSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 84600L) // Current: 1800 seconds violated
                    .put(SLOConstants.LAST_VIOLATED_SECONDS, 900L); // Previous: 900 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Current violated: 86400 - 84600 = 1800 seconds
            // Burn rate: 1800 - 900 = 900 seconds burned since last calculation
            var expectedBurnRateSeconds = 900L;

            var actualBurnRateSeconds = (Long) getSLOBurnRateSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedBurnRateSeconds, actualBurnRateSeconds, "Burn rate seconds calculation failed");

            // Verify that last violated seconds is updated
            assertEquals(1800L, sloData.getLong(SLOConstants.LAST_VIOLATED_SECONDS).longValue(), "Last violated seconds should be updated");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedBurnRateSeconds, actualBurnRateSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Burn Rate Percentage calculation
     * Formula: (burn rate seconds * 100) / total duration
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testSLOBurnRatePercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var burnRateSeconds = 1800L; // 30 minutes burned

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Expected: (1800 * 100) / 86400 = 2.08%
            var expectedBurnRatePercentage = 2.08f;

            var actualBurnRatePercentage = (Float) getSLOBurnRateMethod.invoke(sloProcessor, burnRateSeconds, sloCycle);

            assertEquals(expectedBurnRatePercentage, actualBurnRatePercentage, 0.01f, "Burn rate percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedBurnRatePercentage, actualBurnRatePercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Status determination based on achieved percentage
     * OK: >= warning threshold
     * WARNING: >= target but < warning
     * BREACHED: < target
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testSLOStatusCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 10000L);

            var target = 90; // 90% target
            var warning = 95; // 95% warning

            // Test BREACHED status (< 90%)
            var sloDataBreached = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 8500L); // 85% achieved

            var statusBreached = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataBreached, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.BREACHED.ordinal(), statusBreached.intValue(), "BREACHED status calculation failed");

            // Test WARNING status (>= 90% but < 95%)
            var sloDataWarning = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 9200L); // 92% achieved

            var statusWarning = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataWarning, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.WARNING.ordinal(), statusWarning.intValue(), "WARNING status calculation failed");

            // Test OK status (>= 95%)
            var sloDataOk = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 9600L); // 96% achieved

            var statusOk = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataOk, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.OK.ordinal(), statusOk.intValue(), "OK status calculation failed");

            LOGGER.info(String.format("%s: BREACHED=%d, WARNING=%d, OK=%d", testInfo.getTestMethod().get().getName(), statusBreached, statusWarning, statusOk));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Update and State Management Tests ========================

    /**
     * Test SLO update for first poll (initialization)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testSLOUpdateFirstPoll(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L); // Full duration initially

            var currentTimestamp = DateTimeUtil.currentSeconds();
            var objectId = 12345L;
            var instance = "test-instance";
            var sloId = testContext.getLong("monitorAvailabilityId");
            var cycleId = 1L;

            // First poll should initialize the data
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, currentTimestamp, objectId, instance, sloId, cycleId, false);

            // Verify initialization
            assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be HEALTHY");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be set");
            assertEquals(1, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Up incident count should be 1");

            LOGGER.info(String.format("%s: First poll initialization successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update for status change (flap detection)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testSLOUpdateStatusChange(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, DateTimeUtil.currentSeconds() - 3600) // 1 hour ago
                    .put(SLOConstants.UP_INCIDENT_COUNT, 1)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0);

            var currentTimestamp = DateTimeUtil.currentSeconds();
            var objectId = 12345L;
            var instance = "test-instance";
            var sloId = testContext.getLong("monitorAvailabilityId");
            var cycleId = 1L;

            // Change status from HEALTHY to DEGRADED
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, currentTimestamp, objectId, instance, sloId, cycleId, false);

            // Verify status change
            assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should change to DEGRADED");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be updated");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be set");
            assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down incident count should increment");

            LOGGER.info(String.format("%s: Status change from HEALTHY to DEGRADED successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update for continuous degraded state (duration tracking)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testSLOUpdateContinuousDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var initialDurationLeft = 86400L;
            var lastBreachedTimestamp = DateTimeUtil.currentSeconds() - 1800; // 30 minutes ago
            var currentTimestamp = DateTimeUtil.currentSeconds();

            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, initialDurationLeft)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.DEGRADED.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.LAST_BREACHED_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 1);

            var objectId = 12345L;
            var instance = "test-instance";
            var sloId = testContext.getLong("monitorAvailabilityId");
            var cycleId = 1L;

            // Continue in DEGRADED state
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, currentTimestamp, objectId, instance, sloId, cycleId, false);

            // Verify duration is reduced by the time spent in degraded state
            var expectedDurationLeft = initialDurationLeft - (currentTimestamp - lastBreachedTimestamp);
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be updated");

            LOGGER.info(String.format("%s: Continuous degraded state tracking successful. Duration reduced by %d seconds",
                testInfo.getTestMethod().get().getName(), currentTimestamp - lastBreachedTimestamp));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update for recovery from degraded state
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testSLOUpdateRecoveryFromDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var initialDurationLeft = 86400L;
            var lastBreachedTimestamp = DateTimeUtil.currentSeconds() - 900; // 15 minutes ago
            var currentTimestamp = DateTimeUtil.currentSeconds();

            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, initialDurationLeft)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.DEGRADED.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.LAST_BREACHED_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 1)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 0);

            var objectId = 12345L;
            var instance = "test-instance";
            var sloId = testContext.getLong("monitorAvailabilityId");
            var cycleId = 1L;

            // Recover to HEALTHY state
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, currentTimestamp, objectId, instance, sloId, cycleId, false);

            // Verify recovery
            var expectedDurationLeft = initialDurationLeft - (currentTimestamp - lastBreachedTimestamp);
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by total degraded time");
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should change to HEALTHY");
            assertEquals(1, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Up incident count should increment");

            LOGGER.info(String.format("%s: Recovery from degraded state successful. Total degraded time: %d seconds",
                testInfo.getTestMethod().get().getName(), currentTimestamp - lastBreachedTimestamp));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Event Bus Integration Tests ========================

    /**
     * Test SLO update event processing for performance SLO
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testSLOUpdateEventProcessingPerformance(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("monitorPerformanceId");
            var objectId = 77789039783L; // Test object ID
            var instance = EMPTY_VALUE;
            var currentTimestamp = DateTimeUtil.currentSeconds();

            // Setup test data in sloDetails map
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            var instanceMap = new java.util.HashMap<String, JsonObject>();
            var testData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, currentTimestamp - 300)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 1)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0);

            instanceMap.put(objectId.toString(), testData);
            sloDetailsMap.put(sloId, instanceMap);

            // Setup sloCycles map
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            sloCyclesMap.put(sloId, 1L);

            // Create performance SLO update event (CRITICAL severity)
            var inspectedResult = new JsonObject()
                    .put(SEVERITY, GlobalConstants.Severity.CRITICAL.name())
                    .put("entity.id", objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp)
                    .put(ID, sloId);

            // Listen for SLO update processing
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_SLO_UPDATE, message -> testContext.verify(() ->
            {
                try
                {
                    // Verify the event was processed
                    var updatedData = sloDetailsMap.get(sloId).get(objectId.toString());
                    assertNotNull(updatedData, "SLO data should exist after processing");

                    // For CRITICAL severity, status should change to DEGRADED
                    assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), updatedData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(),
                        "Status should change to DEGRADED for CRITICAL severity");

                    assertEquals(1, updatedData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(),
                        "Down incident count should increment");

                    LOGGER.info(String.format("%s: Performance SLO update event processed successfully", testInfo.getTestMethod().get().getName()));

                    messageConsumer.unregister();
                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                    testContext.failNow(exception.getMessage());
                }
            }));

            // Send the event
            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, inspectedResult);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update event processing for availability SLO
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testSLOUpdateEventProcessingAvailability(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("monitorAvailabilityId");
            var objectId = 77789039783L;
            var instance = EMPTY_VALUE;
            var currentTimestamp = DateTimeUtil.currentSeconds();

            // Setup availability SLO profiles list
            var availabilitySLOProfilesList = (java.util.List<Long>) availabilitySLOProfilesField.get(null);
            if (!availabilitySLOProfilesList.contains(sloId))
            {
                availabilitySLOProfilesList.add(sloId);
            }

            // Setup test data
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            var instanceMap = new java.util.HashMap<String, JsonObject>();
            var testData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, currentTimestamp - 300)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 1)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0);

            instanceMap.put(objectId.toString(), testData);
            sloDetailsMap.put(sloId, instanceMap);

            // Setup sloCycles map
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            sloCyclesMap.put(sloId, 1L);

            // Create availability SLO update event (DOWN status)
            var inspectedResult = new JsonObject()
                    .put(SEVERITY, GlobalConstants.Severity.DOWN.name())
                    .put("entity.id", objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);
            // Note: No ID field for availability SLO events

            // Listen for SLO update processing
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_SLO_UPDATE, message -> testContext.verify(() ->
            {
                try
                {
                    // Verify the event was processed for availability SLO
                    var updatedData = sloDetailsMap.get(sloId).get(objectId.toString());
                    assertNotNull(updatedData, "SLO data should exist after processing");

                    // For DOWN severity, status should change to DEGRADED
                    assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), updatedData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(),
                        "Status should change to DEGRADED for DOWN severity");

                    assertEquals(1, updatedData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(),
                        "Down incident count should increment");

                    LOGGER.info(String.format("%s: Availability SLO update event processed successfully", testInfo.getTestMethod().get().getName()));

                    messageConsumer.unregister();
                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                    testContext.failNow(exception.getMessage());
                }
            }));

            // Send the event
            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, inspectedResult);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Edge Cases and Error Handling Tests ========================

    /**
     * Test calculation with zero total duration (edge case)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testCalculationWithZeroTotalDuration(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 0L);

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 0L); // Zero duration

            // Test achieved percentage with zero duration
            var achievedPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0.0f, achievedPercentage, 0.01f, "Achieved percentage should be 0 for zero duration");

            // Test violated seconds with zero duration
            var violatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0L, violatedSeconds.longValue(), "Violated seconds should be 0 for zero duration");

            LOGGER.info(String.format("%s: Zero duration edge case handled correctly", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test calculation with negative duration left (edge case)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testCalculationWithNegativeDurationLeft(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, -1000L); // Negative duration left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Test achieved percentage with negative duration
            var achievedPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            assertTrue(achievedPercentage <= 0.0f, "Achieved percentage should be <= 0 for negative duration left");

            // Test violated seconds with negative duration
            var violatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(87400L, violatedSeconds.longValue(), "Violated seconds should be total + abs(negative duration)");

            LOGGER.info(String.format("%s: Negative duration left edge case handled correctly", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test error budget calculation when fully exhausted
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testErrorBudgetFullyExhausted(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 77760L); // 8640 seconds violated (exactly error budget)

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            // Error budget should be 0%
            var errorBudgetLeftPercentage = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0.0f, errorBudgetLeftPercentage, 0.01f, "Error budget should be 0% when fully exhausted");

            // Error budget seconds should be 0
            var errorBudgetLeftSeconds = (Long) getSLOErrorBudgetLeftSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0L, errorBudgetLeftSeconds.longValue(), "Error budget seconds should be 0 when fully exhausted");

            LOGGER.info(String.format("%s: Fully exhausted error budget handled correctly", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test error budget calculation when over-exhausted (negative)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testErrorBudgetOverExhausted(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 70000L); // 16400 seconds violated (more than error budget)

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            // Error budget should be negative percentage
            var errorBudgetLeftPercentage = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            assertTrue(errorBudgetLeftPercentage < 0.0f, "Error budget should be negative when over-exhausted");

            // Error budget seconds should be negative
            var errorBudgetLeftSeconds = (Long) getSLOErrorBudgetLeftSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertTrue(errorBudgetLeftSeconds < 0L, "Error budget seconds should be negative when over-exhausted");

            LOGGER.info(String.format("%s: Over-exhausted error budget handled correctly. Percentage: %.2f%%, Seconds: %d",
                testInfo.getTestMethod().get().getName(), errorBudgetLeftPercentage, errorBudgetLeftSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Data Structure Validation Tests ========================

    /**
     * Test SLO details map structure and data integrity
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testSLODetailsMapStructure(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            assertNotNull(sloDetailsMap, "SLO details map should be initialized");

            // Add test data to verify structure
            var sloId = testContext.getLong("monitorAvailabilityId");
            var objectId = "test-object-123";
            var instanceMap = new java.util.HashMap<String, JsonObject>();

            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.UP_INCIDENT_COUNT, 1)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0);

            instanceMap.put(objectId, sloData);
            sloDetailsMap.put(sloId, instanceMap);

            // Verify structure
            assertTrue(sloDetailsMap.containsKey(sloId), "SLO details map should contain the SLO ID");
            assertTrue(sloDetailsMap.get(sloId).containsKey(objectId), "Instance map should contain the object ID");

            var retrievedData = sloDetailsMap.get(sloId).get(objectId);
            assertEquals(86400L, retrievedData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should match");
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), retrievedData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should match");

            LOGGER.info(String.format("%s: SLO details map structure validated successfully", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycles map structure and data integrity
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testSLOCyclesMapStructure(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            assertNotNull(sloCyclesMap, "SLO cycles map should be initialized");

            // Add test data
            var sloId = testContext.getLong("monitorPerformanceId");
            var cycleId = 12345L;
            sloCyclesMap.put(sloId, cycleId);

            // Verify structure
            assertTrue(sloCyclesMap.containsKey(sloId), "SLO cycles map should contain the SLO ID");
            assertEquals(cycleId, sloCyclesMap.get(sloId).longValue(), "Cycle ID should match");

            LOGGER.info(String.format("%s: SLO cycles map structure validated successfully", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO events map structure for availability SLOs
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testSLOEventsMapStructure(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloEventsMap = (Map<Long, TreeMap<Long, Integer>>) sloEventsField.get(null);
            assertNotNull(sloEventsMap, "SLO events map should be initialized");

            // Add test data
            var sloId = testContext.getLong("instanceAvailabilityId");
            var timestamp = DateTimeUtil.currentSeconds();
            var eventCount = 5;

            var timeEventMap = new TreeMap<Long, Integer>();
            timeEventMap.put(timestamp, eventCount);
            sloEventsMap.put(sloId, timeEventMap);

            // Verify structure
            assertTrue(sloEventsMap.containsKey(sloId), "SLO events map should contain the SLO ID");
            assertTrue(sloEventsMap.get(sloId).containsKey(timestamp), "Time event map should contain the timestamp");
            assertEquals(eventCount, sloEventsMap.get(sloId).get(timestamp).intValue(), "Event count should match");

            LOGGER.info(String.format("%s: SLO events map structure validated successfully", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test availability SLO profiles list structure
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testAvailabilitySLOProfilesList(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var availabilitySLOProfilesList = (java.util.List<Long>) availabilitySLOProfilesField.get(null);
            assertNotNull(availabilitySLOProfilesList, "Availability SLO profiles list should be initialized");

            // Add test data
            var availabilitySloId = testContext.getLong("monitorAvailabilityId");
            if (!availabilitySLOProfilesList.contains(availabilitySloId))
            {
                availabilitySLOProfilesList.add(availabilitySloId);
            }

            // Verify structure
            assertTrue(availabilitySLOProfilesList.contains(availabilitySloId), "List should contain availability SLO ID");

            // Verify performance SLO is not in availability list
            var performanceSloId = testContext.getLong("monitorPerformanceId");
            assertFalse(availabilitySLOProfilesList.contains(performanceSloId), "List should not contain performance SLO ID");

            LOGGER.info(String.format("%s: Availability SLO profiles list validated successfully", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Complex Scenario Tests ========================

    /**
     * Test complete SLO lifecycle with multiple status changes
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testCompleteSLOLifecycle(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("instancePerformanceId");
            var objectId = 99999L;
            var instance = "lifecycle-test-instance";
            var cycleId = 2L;
            var baseTimestamp = DateTimeUtil.currentSeconds() - 7200; // Start 2 hours ago

            // Initialize SLO data
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L); // 24 hours initially

            // Scenario 1: First poll - HEALTHY
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, baseTimestamp, objectId, instance, sloId, cycleId, false);
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be HEALTHY");
            assertEquals(1, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Initial up count should be 1");

            // Scenario 2: Status change to DEGRADED (1 hour later)
            var degradedTimestamp = baseTimestamp + 3600;
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, degradedTimestamp, objectId, instance, sloId, cycleId, false);
            assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should change to DEGRADED");
            assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down count should increment");

            // Scenario 3: Continue in DEGRADED state (30 minutes later)
            var continueDegradedTimestamp = degradedTimestamp + 1800;
            var durationBeforeContinue = sloData.getLong(SLOConstants.DURATION_LEFT);
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, continueDegradedTimestamp, objectId, instance, sloId, cycleId, false);
            var durationAfterContinue = sloData.getLong(SLOConstants.DURATION_LEFT);
            assertTrue(durationAfterContinue < durationBeforeContinue, "Duration should decrease while in DEGRADED state");

            // Scenario 4: Recovery to HEALTHY (15 minutes later)
            var recoveryTimestamp = continueDegradedTimestamp + 900;
            var durationBeforeRecovery = sloData.getLong(SLOConstants.DURATION_LEFT);
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, recoveryTimestamp, objectId, instance, sloId, cycleId, false);
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should recover to HEALTHY");
            assertEquals(2, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Up count should increment on recovery");

            // Calculate total degraded time: 1800 + 900 = 2700 seconds (45 minutes)
            var expectedDurationLeft = 86400L - 2700L;
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Final duration should reflect total degraded time");

            // Test calculations with final state
            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L)
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            var achievedPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            var expectedAchievedPercentage = (expectedDurationLeft * 100.0f) / 86400L;
            assertEquals(expectedAchievedPercentage, achievedPercentage, 0.01f, "Achieved percentage should match calculated value");

            var mttr = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);
            var expectedMTTR = 2700L / 1; // Total degraded time / down incidents
            assertEquals(expectedMTTR, mttr.longValue(), "MTTR should match calculated value");

            LOGGER.info(String.format("%s: Complete SLO lifecycle test successful. Final achieved: %.2f%%, MTTR: %d seconds",
                testInfo.getTestMethod().get().getName(), achievedPercentage, mttr));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO calculations with high precision requirements
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testHighPrecisionSLOCalculations(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Test with very small durations for precision
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 999999L) // 999.999 seconds left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 3)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 7)
                    .put(SLOConstants.LAST_VIOLATED_SECONDS, 1L);

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 1000000L) // 1 million seconds
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 50000L); // 5% error budget

            // Test achieved percentage precision
            var achievedPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            var expectedAchievedPercentage = 99.9999f; // (999999 * 100) / 1000000
            assertEquals(expectedAchievedPercentage, achievedPercentage, 0.0001f, "High precision achieved percentage failed");

            // Test violated seconds precision
            var violatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(1L, violatedSeconds.longValue(), "Violated seconds should be exactly 1");

            // Test MTTR precision
            var mttr = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0L, mttr.longValue(), "MTTR should be 0 for 1 second violation across 3 incidents");

            // Test error budget precision
            var errorBudgetLeftPercentage = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            var expectedErrorBudgetLeft = 100.0f - ((1.0f * 100.0f) / 50000.0f); // 100 - (1*100/50000) = 99.998%
            assertEquals(expectedErrorBudgetLeft, errorBudgetLeftPercentage, 0.001f, "High precision error budget calculation failed");

            LOGGER.info(String.format("%s: High precision calculations successful. Achieved: %.4f%%, Error Budget: %.3f%%",
                testInfo.getTestMethod().get().getName(), achievedPercentage, errorBudgetLeftPercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        try
        {
            // Clean up any test data if needed
            LOGGER.info("SLO Processor tests completed successfully");
            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }
}
