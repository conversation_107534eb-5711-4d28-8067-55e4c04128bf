/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.rediscovery;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.OBJECT_PROVISION_API_ENDPOINT;
import static com.mindarray.eventbus.EventBusConstants.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(50 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestWANLinkRediscovery
{
    public static final JsonObject PARAMETERS = new JsonObject();
    public static final JsonObject TEST_PARAMETERS = new JsonObject();
    private static final Logger LOGGER = new Logger(TestWANLinkRediscovery.class, MOTADATA_NMS, "WAN Link Rediscovery Test");
    public static MessageConsumer<JsonObject> messageConsumer = null;
    private static long slaObjectId = 0L;
    private static long credentialId = 0L;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "rediscover-parameters.json");

            var csvFile = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "csv-range-discovery.json");

            Assertions.assertTrue(file.exists());

            Assertions.assertTrue(csvFile.exists());

            PARAMETERS.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

            TEST_PARAMETERS.mergeIn(new JsonObject(Files.readString(csvFile.toPath(), StandardCharsets.UTF_8)));

            Assertions.assertFalse(PARAMETERS.isEmpty());

            slaObjectId = ObjectConfigStore.getStore().getItemByIP(PARAMETERS.getJsonObject("TestWANLinkRediscovery").getString("ipsla"));

            credentialId = CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, "snmp-v2c-***********").getLong(ID);

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    public static JsonObject prepareParams(String parameter)
    {
        return new JsonObject().mergeIn(PARAMETERS.getJsonObject(parameter));
    }

    private static void assertWANLinkRediscoverTestResult(JsonObject item, int totalObjects, VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var event = message.body();

                if (event.getBinary(EVENT_CONTEXT) != null && event.containsKey(EVENT_COMPRESSION_TYPE) && event.getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.containsKey(EVENT_TYPE) && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_REDISCOVER) && eventContext.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.WAN_LINK.getName()))
                    {
                        LOGGER.info(String.format("Received response for WAN Link Rediscovery: %s", eventContext.encode()));

                        Assertions.assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                        Assertions.assertNotNull(eventContext.getJsonArray(NMSConstants.OBJECTS));

                        var objects = eventContext.getJsonArray(NMSConstants.OBJECTS);

                        Assertions.assertNotNull(objects);

                        Assertions.assertEquals(objects.size(), totalObjects);

                        for (var index = 0; index < objects.size(); index++)
                        {
                            var context = objects.getJsonObject(index);

                            Assertions.assertNotNull(context);

                            Assertions.assertEquals(STATUS_PENDING, context.getString(STATUS));

                            Assertions.assertNotNull(Metric.METRIC_PLUGIN);

                            Assertions.assertNotNull(MESSAGE);

                            Assertions.assertEquals(NMSConstants.State.UNPROVISION.name(), context.getString(NMSConstants.STATE));

                            var object = context.getJsonObject(NMSConstants.OBJECT);

                            Assertions.assertNotNull(object);

                            Assertions.assertTrue(object.size() >= 4);

                            context.put(AIOpsObject.OBJECT_NAME, ObjectConfigStore.getStore().getItem(context.getLong(ID)).getString(AIOpsObject.OBJECT_NAME));

                            context.put(AIOpsObject.OBJECT_CREDENTIAL_PROFILE, object.getLong(AIOpsObject.OBJECT_CREDENTIAL_PROFILE));

                            context.put(AIOpsObject.OBJECT_TYPE, object.getString(AIOpsObject.OBJECT_TYPE)).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

                            TestAPIUtil.post(OBJECT_PROVISION_API_ENDPOINT, new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(context)), testContext.succeeding(response ->
                                    testContext.verify(() ->
                                    {
                                    })));
                        }
                    }
                    else if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS))
                    {
                        LOGGER.info(String.format("Received response for WAN Link Provisioning: %s", eventContext.encode()));

                        Assertions.assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                        Assertions.assertNotNull(eventContext.getJsonArray(NMSConstants.OBJECTS));

                        var objects = eventContext.getJsonArray(NMSConstants.OBJECTS);

                        Assertions.assertNotNull(objects);

                        Assertions.assertFalse(objects.isEmpty());

                        for (var index = 0; index < objects.size(); index++)
                        {
                            Assertions.assertTrue(objects.getJsonObject(index).containsKey(INSTANCE_TAGS));

                            Assertions.assertTrue(objects.getJsonObject(index).getJsonArray(INSTANCE_TAGS).size() >= 2);
                        }

                        testContext.completeNow();
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        TestUtil.vertx().eventBus().send(UI_ACTION_REDISCOVER, item);
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testIPSLAICMPEchoRediscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        assertWANLinkRediscoverTestResult(prepareParams(testInfo.getTestMethod().get().getName()).put(ID, slaObjectId).put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, credentialId)
                .put("ipsla.operation.type", NMSConstants.MetricPlugin.IPSLA_ICMP_ECHO.getName())
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId()), 1, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testIPSLAICMPJitterRediscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        assertWANLinkRediscoverTestResult(prepareParams(testInfo.getTestMethod().get().getName()).put(ID, slaObjectId).put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, credentialId)
                .put("ipsla.operation.type", NMSConstants.MetricPlugin.IPSLA_ICMP_JITTER.getName())
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId()), 1, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testIPSLAPathEchoRediscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));


        assertWANLinkRediscoverTestResult(prepareParams(testInfo.getTestMethod().get().getName()).put(ID, slaObjectId).put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, credentialId)
                .put("ipsla.operation.type", NMSConstants.MetricPlugin.IPSLA_PATH_ECHO.getName())
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId()), 1, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testIPSLACSVRediscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        try
        {
            var csv = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + TEST_PARAMETERS.getJsonObject("ipsla").getString("csv"));

            if (csv.exists())
            {
                FileUtils.copyFile(csv, new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + TEST_PARAMETERS.getJsonObject("ipsla").getString("csv")));


                assertWANLinkRediscoverTestResult(prepareParams(testInfo.getTestMethod().get().getName()).put(ID, slaObjectId).put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, credentialId)
                        .put(Discovery.DISCOVERY_TARGET, TEST_PARAMETERS.getJsonObject("ipsla").getString("csv"))
                        .put("rediscovery.name", testInfo.getTestMethod().get().getName())
                        .put(APIConstants.SESSION_ID, TestUtil.getSessionId()), FileUtils.readLines(csv, StandardCharsets.UTF_8).size() - 1, testContext);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
