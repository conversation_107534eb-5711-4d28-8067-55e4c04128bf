{"entity": "System Process", "table": "tbl_config_system_process", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "system.process", "title": "Process", "type": "string", "rules": ["required"]}, {"name": "system.process.app.type", "title": "Application Type", "type": "string"}, {"name": "system.process.os", "title": "OS", "type": "string", "rules": ["required"], "values": ["Windows", "Linux", "IBM AIX", "HP-UX", "Solar<PERSON>"]}], "entries": [{"type": "inline", "records": [{"system.process": "nginx", "system.process.app.type": "<PERSON><PERSON><PERSON>", "system.process.os": "Linux", "id": 10000000000001}, {"system.process": "java | wildfly", "system.process.app.type": "<PERSON><PERSON><PERSON>", "system.process.os": "Linux", "id": 10000000000002}, {"system.process": "java | catalina", "system.process.app.type": "Apache Tomcat", "system.process.os": "Linux", "id": 10000000000003}, {"system.process": "java | websphere", "system.process.app.type": "IBM WebSphere", "system.process.os": "Linux", "id": 10000000000004}, {"system.process": "apache", "system.process.app.type": "Apache HTTP", "system.process.os": "Linux", "id": 10000000000005}, {"system.process": "sqlservr.exe", "system.process.app.type": "SQL Server", "system.process.os": "Windows", "id": 10000000000006}, {"system.process": "oracle.exe", "system.process.app.type": "Oracle Database", "system.process.os": "Windows", "id": 10000000000007}, {"system.process": "mysqld.exe", "system.process.app.type": "MySQL", "system.process.os": "Windows", "id": 10000000000008}, {"system.process": "db2dasrrm.exe", "system.process.app.type": "IBM Db2", "system.process.os": "Windows", "id": 10000000000009}, {"system.process": "postgres.exe", "system.process.app.type": "PostgreSQL", "system.process.os": "Windows", "id": 10000000000010}, {"system.process": "lighttpd", "system.process.app.type": "Light Httpd", "system.process.os": "Linux", "id": 10000000000011}, {"system.process": "named", "system.process.app.type": "Bind9", "system.process.os": "Linux", "id": 10000000000012}, {"system.process": "zimbra", "system.process.app.type": "Zimbra", "system.process.os": "Linux", "id": 10000000000013}, {"system.process": "haproxy", "system.process.app.type": "HAProxy", "system.process.os": "Linux", "id": 10000000000014}, {"system.process": "wasservice.exe", "system.process.app.type": "IBM WebSphere", "system.process.os": "Windows", "id": 10000000000015}, {"system.process": "amqsvc.exe", "system.process.app.type": "IBM MQ", "system.process.os": "Windows", "id": 10000000000016}, {"system.process": "sqlsrvr.exe", "system.process.app.type": "Sybase", "system.process.os": "Windows", "id": 10000000000017}, {"system.process": "erlsrv.exe", "system.process.app.type": "RabbitMQ", "system.process.os": "Windows", "id": 10000000000018}, {"system.process": "inetinfo.exe", "system.process.app.type": "Microsoft IIS", "system.process.os": "Windows", "id": 10000000000019}, {"system.process": "dhcpd", "system.process.app.type": "Linux DHCP", "system.process.os": "Linux", "id": 10000000000020}, {"system.process": "saphoste", "system.process.app.type": "SAP HANA", "system.process.os": "Linux", "id": 10000000000021}, {"system.process": "postgres", "system.process.app.type": "PostgreSQL", "system.process.os": "Linux", "id": 10000000000022}, {"system.process": "ora_dbrm_xe", "system.process.app.type": "Oracle Database", "system.process.os": "Linux", "id": 10000000000023}, {"system.process": "db2sysc", "system.process.app.type": "IBM Db2", "system.process.os": "Linux", "id": 10000000000024}, {"system.process": "runmqlsr", "system.process.app.type": "IBM MQ", "system.process.os": "Linux", "id": 10000000000025}, {"system.process": "mysqld", "system.process.app.type": "MySQL", "system.process.os": "Linux", "id": 10000000000026}, {"system.process": "rabbitmq", "system.process.app.type": "RabbitMQ", "system.process.os": "Linux", "id": 10000000000027}, {"system.process": "dataserver", "system.process.app.type": "Sybase", "system.process.os": "Linux", "id": 10000000000028}, {"system.process": "httpd.exe", "system.process.app.type": "Apache HTTP", "system.process.os": "Windows", "id": 10000000000029}, {"system.process": "nginxsvc.exe", "system.process.app.type": "<PERSON><PERSON><PERSON>", "system.process.os": "Windows", "id": 10000000000030}, {"system.process": "Tomcat7.exe", "system.process.app.type": "Apache Tomcat", "system.process.os": "Windows", "id": 10000000000031}, {"system.process": "Tomcat9.exe", "system.process.app.type": "Apache Tomcat", "system.process.os": "Windows", "id": 10000000000032}, {"system.process": "wildfly-service.exe", "system.process.app.type": "<PERSON><PERSON><PERSON>", "system.process.os": "Windows", "id": 10000000000033}, {"system.process": "NortonSecurity.exe", "system.process.os": "Windows", "id": 10000000000034}, {"system.process": "mcshield.exe", "system.process.os": "Windows", "id": 10000000000035}, {"system.process": "avp.exe", "system.process.os": "Windows", "id": 10000000000036}, {"system.process": "coreServiceShell.exe", "system.process.os": "Windows", "id": 10000000000037}, {"system.process": "oracle", "system.process.app.type": "Oracle Database", "system.process.os": "IBM AIX", "id": 10000000000038}, {"system.process": "vserver", "system.process.app.type": "SAP MaxDB", "system.process.os": "Linux", "id": 10000000000039}, {"system.process": "wildfly", "system.process.app.type": "<PERSON><PERSON><PERSON>", "system.process.os": "Linux", "id": 10000000000040}, {"system.process": "tomcat", "system.process.app.type": "Apache Tomcat", "system.process.os": "Linux", "id": 10000000000041}, {"system.process": "mysqld", "system.process.app.type": "MariaDB", "system.process.os": "Linux", "id": 10000000000042}, {"system.process": "mongod", "system.process.app.type": "MongoDB", "system.process.os": "Linux", "id": 10000000000043}, {"system.process": "mongod", "system.process.app.type": "MongoDB", "system.process.os": "Windows", "id": 10000000000044}, {"system.process": "weblogic.Server", "system.process.app.type": "Oracle WebLogic", "system.process.os": "Linux", "id": 10000000000045}, {"system.process": "weblogic-launcher.jar", "system.process.app.type": "Oracle WebLogic", "system.process.os": "Windows", "id": 10000000000046}], "version": "1.1"}]}