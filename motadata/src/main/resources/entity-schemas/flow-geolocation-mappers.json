{"entity": "Flow Geolocation Mapper", "table": "tbl_config_flow_geolocation_mapper", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "flow.geolocation.mapper.profile.name", "title": "Profile Name", "type": "string", "rules": ["unique", "required"]}, {"name": "flow.geolocation.mapper.description", "title": "Description", "type": "string", "rules": []}, {"name": "flow.geolocation.mapper.country", "title": "Country", "type": "string", "rules": ["required"]}, {"name": "flow.geolocation.mapper.city", "title": "City", "type": "string", "rules": []}, {"name": "flow.geolocation.mapper.group", "title": "IP Group/IP", "type": "list", "rules": ["required"]}]}