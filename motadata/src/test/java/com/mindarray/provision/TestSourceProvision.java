/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.provision;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.APIConstants;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.LogParserConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.TestAPIConstants.EVENT_SOURCE_API_ENDPOINT;
import static com.mindarray.api.LogParser.LOG_PARSER_ENTITIES;
import static com.mindarray.eventbus.EventBusConstants.EVENT_SOURCE;
import static com.mindarray.log.LogEngineConstants.*;
import static org.apache.http.HttpStatus.SC_OK;

@ExtendWith(VertxExtension.class)
@Timeout(360 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSourceProvision
{
    private static long id;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testCreateEventSource(@NotNull VertxTestContext testContext) throws InterruptedException
    {
        var context = new JsonObject()
                .put(EVENT_SOURCE, "1.2.3.4")
                .put(ASSIGNED_LOG_PARSERS, new JsonArray().add(10000000000001L).add(10000000000002L))
                .put(SOURCE_GROUPS, new JsonArray().add(10000000000050L).add(10000000000051L))
                .put(EVENT_SOURCE_TYPE, "IBM AIX");

        TestAPIUtil.post(EVENT_SOURCE_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            id = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

            Assertions.assertNotNull(EventSourceConfigStore.getStore().getItem(id));

            var retries = new AtomicInteger(3);

            TestUtil.vertx().setPeriodic(5000, timer ->
            {

                // assert assigned parsers

                var items = LogParserConfigStore.getStore().getItems(context.getJsonArray(ASSIGNED_LOG_PARSERS));

                for (var index = 0; index < items.size(); index++)
                {
                    var parser = items.getJsonObject(index);

                    if (parser.getJsonArray(LOG_PARSER_ENTITIES).contains(context.getString(EVENT_SOURCE)))
                    {
                        Assertions.assertTrue(parser.getJsonArray(LOG_PARSER_ENTITIES).contains(context.getString(EVENT_SOURCE)));

                        TestUtil.vertx().cancelTimer(timer);  // complete

                        testContext.completeNow();
                    }
                    else
                    {
                        if (retries.decrementAndGet() <= 0)
                        {
                            testContext.failNow("max attempt exceeded..");
                        }
                    }
                }
            });

        })));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testUpdateEventSource(VertxTestContext testContext) throws InterruptedException
    {
        var context = new JsonObject()
                .put(EVENT_SOURCE, "1.2.3.4")
                .put(ASSIGNED_LOG_PARSERS, new JsonArray().add(10000000000001L).add(10000000000002L))
                .put(SOURCE_GROUPS, new JsonArray().add(10000000000050L))
                .put(EVENT_SOURCE_TYPE, "Azure Cloud");

        TestAPIUtil.put(EVENT_SOURCE_API_ENDPOINT + "/" + id, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            var item = EventSourceConfigStore.getStore().getItem(id);

            Assertions.assertNotNull(item);

            Assertions.assertFalse(item.getJsonArray(SOURCE_GROUPS).contains(10000000000051L)); // removed groups

            Assertions.assertEquals(context.getString(EVENT_SOURCE_TYPE), item.getString(EVENT_SOURCE_TYPE));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void testAssignEventSource(VertxTestContext testContext) throws InterruptedException
    {
        var context = new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(10000000000003L));

        TestAPIUtil.put(EVENT_SOURCE_API_ENDPOINT + "/" + id + "/assign", context, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            var item = EventSourceConfigStore.getStore().getItem(id);

            Assertions.assertNotNull(item);

            var retries = new AtomicInteger(3);

            TestUtil.vertx().setPeriodic(5000, timer ->
            {

                // check assigned parser
                var items = LogParserConfigStore.getStore().getItems(context.getJsonArray(APIConstants.REQUEST_PARAMS));

                Assertions.assertNotNull(items);

                if (items.getJsonObject(0).getJsonArray(LOG_PARSER_ENTITIES).contains("1.2.3.4"))
                {
                    Assertions.assertTrue(items.getJsonObject(0).getJsonArray(LOG_PARSER_ENTITIES).contains("1.2.3.4"));

                    testContext.completeNow();
                }
                else
                {
                    if (retries.decrementAndGet() <= 0)
                    {
                        testContext.failNow("max attempt exceeded..");
                    }
                }
            });

        })));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    public void testUnassignEventSource(VertxTestContext testContext) throws InterruptedException
    {
        var context = new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(10000000000003L));

        TestAPIUtil.put(EVENT_SOURCE_API_ENDPOINT + "/" + id + "/unassign", context, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            var item = EventSourceConfigStore.getStore().getItem(id);

            Assertions.assertNotNull(item);

            var retries = new AtomicInteger(3);

            TestUtil.vertx().setPeriodic(5000, timer ->
            {

                // check assigned parser
                var items = LogParserConfigStore.getStore().getItems(context.getJsonArray(APIConstants.REQUEST_PARAMS));

                if (!items.getJsonObject(0).getJsonArray(LOG_PARSER_ENTITIES).contains("1.2.3.4"))
                {
                    Assertions.assertFalse(items.getJsonObject(0).getJsonArray(LOG_PARSER_ENTITIES).contains("1.2.3.4"));

                    testContext.completeNow();
                }
                else
                {
                    if (retries.decrementAndGet() <= 0)
                    {
                        testContext.failNow("max attempt exceeded..");
                    }
                }
            });

        })));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    public void testGetEventSource(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.get(EVENT_SOURCE_API_ENDPOINT + "/" + id, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals(id, response.bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).getLong(GlobalConstants.ID));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    public void testGetAllEventSource(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.get(EVENT_SOURCE_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertFalse(response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty());

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}
