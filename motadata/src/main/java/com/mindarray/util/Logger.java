/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author			Notes
 *  18-Mar-2025     Smit            MOTADATA-5431: Module Level Logging
 */

package com.mindarray.util;

import com.mindarray.GlobalConstants;
import com.mindarray.store.LoggerCacheStore;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * A custom logging implementation for the Motadata application.
 * <p>
 * This class provides methods for different log levels (error, info, warn, debug, fatal, trace)
 * and supports module-level logging through the LoggerCacheStore. Each log entry includes:
 * - Timestamp (date and time)
 * - Log level
 * - Class name (for error logs)
 * - Message content
 * <p>
 * Log files are organized by:
 * - Module (subdirectory under logs)
 * - Date (part of the filename)
 * - Component (part of the filename)
 * <p>
 * Debug and trace logs are only written if the corresponding log level is enabled
 * and the module qualifies for detailed logging.
 */
public final class Logger
{
    /**
     * Directory where all log files are stored
     */
    public static final String LOG_DIRECTORY = "logs";

    /**
     * Fully qualified name of the class using this logger
     */
    private final String className;

    /**
     * Module name for organizing logs into subdirectories
     */
    private final String module;

    /**
     * Pattern for the log filename, where $$$ is replaced with date and @@@ with component name
     */
    private final String logFileName;

    /**
     * Creates a new Logger instance for the specified class, module, and component.
     *
     * @param clazz     The class that will use this logger (used to record the class name in logs)
     * @param module    The module name (used for organizing logs into subdirectories)
     * @param component The component name (used as part of the log filename)
     */
    public Logger(Class clazz, String module, String component)
    {
        this.className = clazz.getName();

        this.module = module;

        this.logFileName = "$$$-@@@.log".replace("@@@", component.replace("\\", "-"));

        // Register this module with the LoggerCacheStore for module-level logging control
        LoggerCacheStore.getStore().add(module);
    }

    /**
     * Logs an error message with stack trace for a Throwable.
     * <p>
     * This method creates a log entry with ERROR level, including the exception's class name,
     * message, and stack trace. The log is written to a file in the module's directory.
     *
     * @param exception The Throwable to log
     */
    public void error(Throwable exception)
    {
        try
        {
            // Create logs directory if it doesn't exist
            var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY;
            var logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Create module-specific directory if it doesn't exist
            logFile += GlobalConstants.PATH_SEPARATOR + this.module;
            logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Format current date and time
            var date = new Date();
            var simpleDateFormat = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);
            var currentDate = simpleDateFormat.format(date);

            // Create log file path with date in filename
            logFile = logFile + GlobalConstants.PATH_SEPARATOR + logFileName;
            logFile = logFile.replace("$$$", currentDate);

            // Format time for log entry
            var simpleTimeFormat = new SimpleDateFormat(GlobalConstants.LOGGER_TIME_FORMAT);
            var currentTime = simpleTimeFormat.format(date);

            // Write log entry with exception details and stack trace
            FileUtils.writeStringToFile(new File(logFile),
                    currentDate + " " + currentTime + " ERROR [" + this.className + "]: " +
                            exception.getClass().getCanonicalName() + ": " + exception.getMessage() +
                            GlobalConstants.NEW_LINE + getStackTrace(exception.getStackTrace()) +
                            GlobalConstants.NEW_LINE, StandardCharsets.UTF_8, true);
        }
        catch (Exception ignored)
        {
            // Ignore exceptions that occur during logging to prevent infinite loops
        }
    }

    /**
     * Logs an error message with stack trace for an Exception.
     * <p>
     * This method creates a log entry with ERROR level, including the exception's class name,
     * message, and stack trace. The log is written to a file in the module's directory.
     * <p>
     * This is a convenience method that delegates to error(Throwable).
     *
     * @param exception The Exception to log
     */
    public void error(Exception exception)
    {
        try
        {
            // Create logs directory if it doesn't exist
            var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY;
            var logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Create module-specific directory if it doesn't exist
            logFile += GlobalConstants.PATH_SEPARATOR + this.module;
            logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Format current date and time
            var date = new Date();
            var simpleDateFormat = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);
            var currentDate = simpleDateFormat.format(date);

            // Create log file path with date in filename
            logFile = logFile + GlobalConstants.PATH_SEPARATOR + logFileName;
            logFile = logFile.replace("$$$", currentDate);

            // Format time for log entry
            var simpleTimeFormat = new SimpleDateFormat(GlobalConstants.LOGGER_TIME_FORMAT);
            var currentTime = simpleTimeFormat.format(date);

            // Write log entry with exception details and stack trace
            FileUtils.writeStringToFile(new File(logFile),
                    currentDate + " " + currentTime + " ERROR [" + this.className + "]: " +
                            exception.getClass().getCanonicalName() + ": " + exception.getMessage() +
                            GlobalConstants.NEW_LINE + getStackTrace(exception.getStackTrace()) +
                            GlobalConstants.NEW_LINE, StandardCharsets.UTF_8, true);
        }
        catch (Exception ignored)
        {
            // Ignore exceptions that occur during logging to prevent infinite loops
        }
    }

    /**
     * Formats a stack trace array into a readable string.
     *
     * @param stackTraceElements The stack trace elements to format
     * @return A formatted string representation of the stack trace
     */
    private String getStackTrace(StackTraceElement[] stackTraceElements)
    {
        var stackTrace = new StringBuilder();

        try
        {
            if (stackTraceElements != null)
            {
                for (var stackTraceElement : stackTraceElements)
                {
                    // Format each stack trace element with standard Java format
                    stackTrace.append("\tat ").append(stackTraceElement).append(GlobalConstants.NEW_LINE);
                }
            }
        }
        catch (Exception ignored)
        {
            // Ignore exceptions during stack trace formatting
        }

        return stackTrace.toString();
    }

    /**
     * Logs an informational message.
     * <p>
     * This method creates a log entry with INFO level. The log is written to a file
     * in the module's directory. Info logs are always written regardless of the log level.
     *
     * @param message The message to log (will be converted to string using toString())
     */
    public void info(Object message)
    {
        try
        {
            // Create logs directory if it doesn't exist
            var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY;
            var logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Create module-specific directory if it doesn't exist
            logFile += GlobalConstants.PATH_SEPARATOR + this.module;
            logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Format current date and time
            var date = new Date();
            var simpleDateFormat = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);
            var currentDate = simpleDateFormat.format(date);

            // Create log file path with date in filename
            logFile = logFile + GlobalConstants.PATH_SEPARATOR + logFileName;
            logFile = logFile.replace("$$$", currentDate);

            // Format time for log entry
            var simpleTimeFormat = new SimpleDateFormat(GlobalConstants.LOGGER_TIME_FORMAT);
            var currentTime = simpleTimeFormat.format(date);

            // Write log entry with INFO level and message
            FileUtils.writeStringToFile(new File(logFile),
                    currentDate + " " + currentTime + " INFO " + message + GlobalConstants.NEW_LINE,
                    StandardCharsets.UTF_8, true);
        }
        catch (Exception ignored)
        {
            // Ignore exceptions that occur during logging
        }
    }

    /**
     * Logs a warning message.
     * <p>
     * This method creates a log entry with WARN level. The log is written to a file
     * in the module's directory. Warning logs are always written regardless of the log level.
     *
     * @param message The message to log (will be converted to string using toString())
     */
    public void warn(Object message)
    {
        try
        {
            // Create logs directory if it doesn't exist
            var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY;
            var logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Create module-specific directory if it doesn't exist
            logFile += GlobalConstants.PATH_SEPARATOR + this.module;
            logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Format current date and time
            var date = new Date();
            var simpleDateFormat = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);
            var currentDate = simpleDateFormat.format(date);

            // Create log file path with date in filename
            logFile = logFile + GlobalConstants.PATH_SEPARATOR + logFileName;
            logFile = logFile.replace("$$$", currentDate);

            // Format time for log entry
            var simpleTimeFormat = new SimpleDateFormat(GlobalConstants.LOGGER_TIME_FORMAT);
            var currentTime = simpleTimeFormat.format(date);

            // Write log entry with WARN level and message
            FileUtils.writeStringToFile(new File(logFile),
                    currentDate + " " + currentTime + " WARN " + message + GlobalConstants.NEW_LINE,
                    StandardCharsets.UTF_8, true);
        }
        catch (Exception ignored)
        {
            // Ignore exceptions that occur during logging
        }
    }

    /**
     * Logs an event with a custom status.
     * <p>
     * This method creates a log entry with a custom status level enclosed in square brackets.
     * The log is written to a file in the module's directory. Event logs are always written
     * regardless of the log level.
     *
     * @param status The status level to use (e.g., "START", "COMPLETE", "FAIL")
     * @param event  The event message to log
     */
    public void logEvent(String status, String event)
    {
        try
        {
            // Create logs directory if it doesn't exist
            var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY;
            var logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Create module-specific directory if it doesn't exist
            logFile += GlobalConstants.PATH_SEPARATOR + this.module;
            logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Format current date and time
            var date = new Date();
            var simpleDateFormat = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);
            var currentDate = simpleDateFormat.format(date);

            // Create log file path with date in filename
            logFile = logFile + GlobalConstants.PATH_SEPARATOR + logFileName;
            logFile = logFile.replace("$$$", currentDate);

            // Format time for log entry
            var simpleTimeFormat = new SimpleDateFormat(GlobalConstants.LOGGER_TIME_FORMAT);
            var currentTime = simpleTimeFormat.format(date);

            // Write log entry with custom status and event message
            FileUtils.writeStringToFile(new File(logFile),
                    currentDate + " " + currentTime + String.format(" [%s] ", status) + event + GlobalConstants.NEW_LINE,
                    StandardCharsets.UTF_8, true);
        }
        catch (Exception ignored)
        {
            // Ignore exceptions that occur during logging
        }
    }

    /**
     * Logs a debug message if debug logging is enabled for this module.
     * <p>
     * This method creates a log entry with DEBUG level, but only if:
     * 1. Debug logging is enabled globally (via CommonUtil.debugEnabled())
     * 2. The module qualifies for detailed logging (via LoggerCacheStore)
     * <p>
     * Debug logs are useful for detailed information during development and troubleshooting.
     *
     * @param message The message to log (will be converted to string using toString())
     */
    public void debug(Object message)
    {
        try
        {
            // Only log if debug is enabled and this module qualifies for detailed logging
            if (CommonUtil.debugEnabled() && LoggerCacheStore.getStore().qualify(this.module))
            {
                // Create logs directory if it doesn't exist
                var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY;
                var logDir = new File(logFile);
                if (!logDir.exists())
                {
                    logDir.mkdir();
                }

                // Create module-specific directory if it doesn't exist
                logFile += GlobalConstants.PATH_SEPARATOR + this.module;
                logDir = new File(logFile);
                if (!logDir.exists())
                {
                    logDir.mkdir();
                }

                // Format current date and time
                var date = new Date();
                var simpleDateFormat = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);
                var currentDate = simpleDateFormat.format(date);

                // Create log file path with date in filename
                logFile = logFile + GlobalConstants.PATH_SEPARATOR + logFileName;
                logFile = logFile.replace("$$$", currentDate);

                // Format time for log entry
                var simpleTimeFormat = new SimpleDateFormat(GlobalConstants.LOGGER_TIME_FORMAT);
                var currentTime = simpleTimeFormat.format(date);

                // Write log entry with DEBUG level and message
                FileUtils.writeStringToFile(new File(logFile),
                        currentDate + " " + currentTime + " DEBUG " + message + GlobalConstants.NEW_LINE,
                        StandardCharsets.UTF_8, true);
            }
        }
        catch (Exception ignored)
        {
            // Ignore exceptions that occur during logging
        }
    }

    /**
     * Logs a fatal error message.
     * <p>
     * This method creates a log entry with FATAL level. The log is written to a file
     * in the module's directory. Fatal logs are always written regardless of the log level
     * and are used for critical errors that may cause the application to terminate.
     *
     * @param message The message to log (will be converted to string using toString())
     */
    public void fatal(Object message)
    {
        try
        {
            // Create logs directory if it doesn't exist
            var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY;
            var logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Create module-specific directory if it doesn't exist
            logFile += GlobalConstants.PATH_SEPARATOR + this.module;
            logDir = new File(logFile);
            if (!logDir.exists())
            {
                logDir.mkdir();
            }

            // Format current date and time
            var date = new Date();
            var simpleDateFormat = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);
            var currentDate = simpleDateFormat.format(date);

            // Create log file path with date in filename
            logFile = logFile + GlobalConstants.PATH_SEPARATOR + logFileName;
            logFile = logFile.replace("$$$", currentDate);

            // Format time for log entry
            var simpleTimeFormat = new SimpleDateFormat(GlobalConstants.LOGGER_TIME_FORMAT);
            var currentTime = simpleTimeFormat.format(date);

            // Write log entry with FATAL level and message
            FileUtils.writeStringToFile(new File(logFile),
                    currentDate + " " + currentTime + " FATAL " + message + GlobalConstants.NEW_LINE,
                    StandardCharsets.UTF_8, true);
        }
        catch (Exception ignored)
        {
            // Ignore exceptions that occur during logging
        }
    }

    /**
     * Logs a trace message if trace logging is enabled for this module.
     * <p>
     * This method creates a log entry with TRACE level, but only if:
     * 1. Trace logging is enabled globally (via CommonUtil.traceEnabled())
     * 2. The module qualifies for detailed logging (via LoggerCacheStore)
     * <p>
     * Trace logs provide the most detailed level of logging, typically used for
     * fine-grained debugging of specific components or flows.
     *
     * @param message The message to log (will be converted to string using toString())
     */
    public void trace(Object message)
    {
        try
        {
            // Only log if trace is enabled and this module qualifies for detailed logging
            if (CommonUtil.traceEnabled() && LoggerCacheStore.getStore().qualify(this.module))
            {
                // Create logs directory if it doesn't exist
                var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY;
                var logDir = new File(logFile);
                if (!logDir.exists())
                {
                    logDir.mkdir();
                }

                // Create module-specific directory if it doesn't exist
                logFile += GlobalConstants.PATH_SEPARATOR + this.module;
                logDir = new File(logFile);
                if (!logDir.exists())
                {
                    logDir.mkdir();
                }

                // Format current date and time
                var date = new Date();
                var simpleDateFormat = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);
                var currentDate = simpleDateFormat.format(date);

                // Create log file path with date in filename
                logFile = logFile + GlobalConstants.PATH_SEPARATOR + logFileName;
                logFile = logFile.replace("$$$", currentDate);

                // Format time for log entry
                var simpleTimeFormat = new SimpleDateFormat(GlobalConstants.LOGGER_TIME_FORMAT);
                var currentTime = simpleTimeFormat.format(date);

                // Write log entry with TRACE level and message
                FileUtils.writeStringToFile(new File(logFile),
                        currentDate + " " + currentTime + " TRACE " + message + GlobalConstants.NEW_LINE,
                        StandardCharsets.UTF_8, true);
            }
        }
        catch (Exception ignored)
        {
            // Ignore exceptions that occur during logging
        }
    }


}
