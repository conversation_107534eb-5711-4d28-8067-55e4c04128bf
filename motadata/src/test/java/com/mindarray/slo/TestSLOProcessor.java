package com.mindarray.slo;

import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.api.SLOCycle;
import com.mindarray.api.SLOProfile;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.TreeMap;

import static com.mindarray.GlobalConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSLOProcessor
{
    private static final Logger LOGGER = new Logger(TestSLOProcessor.class, GlobalConstants.MOTADATA_SLO, "Test SLO Processor");

    // Test data context
    private static final JsonObject context = new JsonObject();
    private static MessageConsumer<JsonObject> messageConsumer;

    // SLO Processor instance and reflection fields
    private static SLOProcessor sloProcessor;
    private static Field sloDetailsField;
    private static Field sloCyclesField;
    private static Field availabilitySLOProfilesField;
    private static Field sloEventsField;
    private static Field countField;

    // Calculation methods via reflection
    private static Method getSLOAchievedPercentageMethod;
    private static Method getSLOViolatedSecondsMethod;
    private static Method getSLOMeanTimeToResolveMethod;
    private static Method getSLOMeanTimeBetweenFailureMethod;
    private static Method getSLOErrorBudgetLeftPercentageMethod;
    private static Method getSLOErrorBudgetLeftSecondsMethod;
    private static Method getSLOBurnRateSecondsMethod;
    private static Method getSLOBurnRateMethod;
    private static Method getSLOStatusMethod;
    private static Method updateSLOMethod;

    // Cycle management methods via reflection
    private static Method qualifyCyclesMethod;
    private static Method startCycleMethod;
    private static Method qualifyAndAssignEntitiesMethod;
    private static Method filterMethod;
    private static Method processMethod;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            // Get existing SLO profiles created by TestSLOProfile
            var monitorAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorAvailabilitySLO");
            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "MonitorPerformanceSLO");
            var instanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstanceAvailabilitySLO");
            var instancePerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, "InstancePerformanceSLO");

            // Store profile IDs for testing
            context.put("monitorAvailabilityId", monitorAvailabilitySLO.getLong(ID));
            context.put("monitorPerformanceId", monitorPerformanceSLO.getLong(ID));
            TestSLOProcessor.testContext.put("instanceAvailabilityId", instanceAvailabilitySLO.getLong(ID));
            TestSLOProcessor.testContext.put("instancePerformanceId", instancePerformanceSLO.getLong(ID));

            // Initialize SLOProcessor instance for testing
            sloProcessor = new SLOProcessor();

            // Setup reflection access to private fields
            setupReflectionFields();

            // Setup reflection access to private methods
            setupReflectionMethods();

            LOGGER.info("SLO Processor test setup completed successfully");
            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("Running test case: %s", testInfo.getTestMethod().get().getName()));
        testContext.completeNow();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    // ======================== Setup Helper Methods ========================

    /**
     * Setup reflection access to private fields of SLOProcessor
     */
    private static void setupReflectionFields() throws Exception
    {
        sloDetailsField = SLOProcessor.class.getDeclaredField("sloDetails");
        sloDetailsField.setAccessible(true);

        sloCyclesField = SLOProcessor.class.getDeclaredField("sloCycles");
        sloCyclesField.setAccessible(true);

        availabilitySLOProfilesField = SLOProcessor.class.getDeclaredField("availabilitySLOProfiles");
        availabilitySLOProfilesField.setAccessible(true);

        sloEventsField = SLOProcessor.class.getDeclaredField("sloEvents");
        sloEventsField.setAccessible(true);

        countField = SLOProcessor.class.getDeclaredField("count");
        countField.setAccessible(true);
    }

    /**
     * Setup reflection access to private methods of SLOProcessor
     */
    private static void setupReflectionMethods() throws Exception
    {
        getSLOAchievedPercentageMethod = SLOProcessor.class.getDeclaredMethod("getSLOAchievedPercentage", JsonObject.class, JsonObject.class);
        getSLOAchievedPercentageMethod.setAccessible(true);

        getSLOViolatedSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOViolatedSeconds", JsonObject.class, JsonObject.class);
        getSLOViolatedSecondsMethod.setAccessible(true);

        getSLOMeanTimeToResolveMethod = SLOProcessor.class.getDeclaredMethod("getSLOMeanTimeToResolve", JsonObject.class, JsonObject.class);
        getSLOMeanTimeToResolveMethod.setAccessible(true);

        getSLOMeanTimeBetweenFailureMethod = SLOProcessor.class.getDeclaredMethod("getSLOMeanTimeBetweenFailure", JsonObject.class, JsonObject.class, long.class);
        getSLOMeanTimeBetweenFailureMethod.setAccessible(true);

        getSLOErrorBudgetLeftPercentageMethod = SLOProcessor.class.getDeclaredMethod("getSLOErrorBudgetLeftPercentage", JsonObject.class, JsonObject.class);
        getSLOErrorBudgetLeftPercentageMethod.setAccessible(true);

        getSLOErrorBudgetLeftSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOErrorBudgetLeftSeconds", JsonObject.class, JsonObject.class);
        getSLOErrorBudgetLeftSecondsMethod.setAccessible(true);

        getSLOBurnRateSecondsMethod = SLOProcessor.class.getDeclaredMethod("getSLOBurnRateSeconds", JsonObject.class, JsonObject.class);
        getSLOBurnRateSecondsMethod.setAccessible(true);

        getSLOBurnRateMethod = SLOProcessor.class.getDeclaredMethod("getSLOBurnRate", long.class, JsonObject.class);
        getSLOBurnRateMethod.setAccessible(true);

        getSLOStatusMethod = SLOProcessor.class.getDeclaredMethod("getSLOStatus", JsonObject.class, JsonObject.class, int.class, int.class);
        getSLOStatusMethod.setAccessible(true);

        updateSLOMethod = SLOProcessor.class.getDeclaredMethod("updateSLO", JsonObject.class, SLOConstants.SLOFlapStatus.class, long.class, long.class, String.class, long.class, long.class, boolean.class);
        updateSLOMethod.setAccessible(true);

        // Cycle management methods
        qualifyCyclesMethod = SLOProcessor.class.getDeclaredMethod("qualifyCycles", JsonArray.class, AtomicInteger.class);
        qualifyCyclesMethod.setAccessible(true);

        startCycleMethod = SLOProcessor.class.getDeclaredMethod("startCycle", JsonObject.class);
        startCycleMethod.setAccessible(true);

        qualifyAndAssignEntitiesMethod = SLOProcessor.class.getDeclaredMethod("qualifyAndAssignEntities", JsonObject.class, JsonObject.class);
        qualifyAndAssignEntitiesMethod.setAccessible(true);

        filterMethod = SLOProcessor.class.getDeclaredMethod("filter", JsonObject.class, JsonArray.class);
        filterMethod.setAccessible(true);

        processMethod = SLOProcessor.class.getDeclaredMethod("process", JsonObject.class);
        processMethod.setAccessible(true);
    }

    // ======================== SLO Cycle Management Tests ========================

    /**
     * Test SLO cycle qualification with expired cycles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testSLOCycleQualificationExpiredCycles(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTime = DateTimeUtil.currentSeconds();
            var pastEndTime = currentTime - 3600; // Ended 1 hour ago
            var pastStartTime = pastEndTime - 86400; // Started 25 hours ago

            // Create expired SLO profile
            var expiredSLOProfile = new JsonObject()
                    .put(ID, testContext.getLong("monitorAvailabilityId"))
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.DAILY.name())
                    .put(SLOProfile.SLO_PROFILE_START_TIME, pastStartTime)
                    .put(SLOProfile.SLO_PROFILE_END_TIME, pastEndTime);

            var sloProfiles = new JsonArray().add(expiredSLOProfile);
            var index = new AtomicInteger(0);

            // Get initial cycles count
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            var initialCyclesCount = sloCyclesMap.size();

            // Test cycle qualification
            qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, index);

            // Verify that expired cycle triggers new cycle creation
            assertTrue(sloCyclesMap.size() >= initialCyclesCount, "New cycle should be created for expired SLO");

            LOGGER.info(String.format("%s: Expired cycle qualification test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle qualification with active cycles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testSLOCycleQualificationActiveCycles(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTime = DateTimeUtil.currentSeconds();
            var futureEndTime = currentTime + 3600; // Ends in 1 hour
            var pastStartTime = currentTime - 3600; // Started 1 hour ago

            // Create active SLO profile
            var activeSLOProfile = new JsonObject()
                    .put(ID, testContext.getLong("monitorPerformanceId"))
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.DAILY.name())
                    .put(SLOProfile.SLO_PROFILE_START_TIME, pastStartTime)
                    .put(SLOProfile.SLO_PROFILE_END_TIME, futureEndTime);

            var sloProfiles = new JsonArray().add(activeSLOProfile);
            var index = new AtomicInteger(0);

            // Setup existing active cycle
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            var existingCycleId = 12345L;
            sloCyclesMap.put(testContext.getLong("monitorPerformanceId"), existingCycleId);
            var initialCyclesCount = sloCyclesMap.size();

            // Test cycle qualification
            qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, index);

            // Verify that active cycle is not restarted
            assertEquals(initialCyclesCount, sloCyclesMap.size(), "Active cycle should not be restarted");
            assertEquals(existingCycleId, sloCyclesMap.get(testContext.getLong("monitorPerformanceId")).longValue(),
                "Existing cycle ID should remain unchanged");

            LOGGER.info(String.format("%s: Active cycle qualification test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle qualification with different frequencies
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testSLOCycleQualificationDifferentFrequencies(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTime = DateTimeUtil.currentSeconds();

            // Test Weekly frequency
            var weeklySLOProfile = new JsonObject()
                    .put(ID, testContext.getLong("instanceAvailabilityId"))
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.WEEKLY.name())
                    .put(SLOProfile.SLO_PROFILE_START_TIME, currentTime - 604800) // 1 week ago
                    .put(SLOProfile.SLO_PROFILE_END_TIME, currentTime - 3600); // Ended 1 hour ago

            // Test Monthly frequency
            var monthlySLOProfile = new JsonObject()
                    .put(ID, testContext.getLong("instancePerformanceId"))
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.MONTHLY.name())
                    .put(SLOProfile.SLO_PROFILE_START_TIME, currentTime - 2592000) // 30 days ago
                    .put(SLOProfile.SLO_PROFILE_END_TIME, currentTime - 3600); // Ended 1 hour ago

            var sloProfiles = new JsonArray().add(weeklySLOProfile).add(monthlySLOProfile);
            var index = new AtomicInteger(0);

            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            var initialCyclesCount = sloCyclesMap.size();

            // Test cycle qualification for different frequencies
            qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, index);

            // Verify cycles are created for different frequencies
            assertTrue(sloCyclesMap.size() >= initialCyclesCount, "Cycles should be created for different frequencies");

            LOGGER.info(String.format("%s: Different frequencies qualification test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle qualification with disabled profiles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSLOCycleQualificationDisabledProfiles(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTime = DateTimeUtil.currentSeconds();

            // Create disabled SLO profile
            var disabledSLOProfile = new JsonObject()
                    .put(ID, 99999L) // Use unique ID for disabled profile
                    .put(SLOProfile.SLO_PROFILE_ENABLED, false) // Disabled
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.DAILY.name())
                    .put(SLOProfile.SLO_PROFILE_START_TIME, currentTime - 86400)
                    .put(SLOProfile.SLO_PROFILE_END_TIME, currentTime - 3600);

            var sloProfiles = new JsonArray().add(disabledSLOProfile);
            var index = new AtomicInteger(0);

            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            var initialCyclesCount = sloCyclesMap.size();

            // Test cycle qualification for disabled profile
            qualifyCyclesMethod.invoke(sloProcessor, sloProfiles, index);

            // Verify no cycle is created for disabled profile
            assertFalse(sloCyclesMap.containsKey(99999L), "No cycle should be created for disabled profile");

            LOGGER.info(String.format("%s: Disabled profiles qualification test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Cycle Start Tests ========================

    /**
     * Test SLO cycle start for enabled availability profile
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testSLOCycleStartAvailabilityProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("monitorAvailabilityId");
            var currentTime = DateTimeUtil.currentSeconds();

            // Create availability SLO profile for cycle start
            var availabilitySLOProfile = new JsonObject()
                    .put(ID, sloId)
                    .put(SLOProfile.SLO_PROFILE_NAME, "TestAvailabilitySLO")
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_TYPE, SLOConstants.SLOType.AVAILABILITY.name())
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.DAILY.name())
                    .put(SLOProfile.SLO_PROFILE_TARGET, 95)
                    .put(SLOProfile.SLO_PROFILE_WARNING, 98)
                    .put(SLOProfile.SLO_PROFILE_START_TIME, currentTime)
                    .put(SLOProfile.SLO_PROFILE_END_TIME, currentTime + 86400)
                    .put(SLOProfile.SLO_PROFILE_ENTITY_ASSIGNMENT, SLOConstants.SLOEntityAssignment.DIRECT.name())
                    .put(SLOProfile.SLO_PROFILE_ENTITIES, new JsonArray().add(new JsonObject().put(ID, 77789039783L)));

            // Setup availability SLO profiles list
            var availabilitySLOProfilesList = (java.util.List<Long>) availabilitySLOProfilesField.get(null);
            if (!availabilitySLOProfilesList.contains(sloId))
            {
                availabilitySLOProfilesList.add(sloId);
            }

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            var initialDetailsSize = sloDetailsMap.size();

            // Test cycle start
            startCycleMethod.invoke(sloProcessor, availabilitySLOProfile);

            // Verify SLO details are initialized
            assertTrue(sloDetailsMap.containsKey(sloId), "SLO details should be created for availability profile");
            assertTrue(sloDetailsMap.size() >= initialDetailsSize, "SLO details map should grow");

            // Verify entity assignment
            var entityMap = sloDetailsMap.get(sloId);
            assertNotNull(entityMap, "Entity map should be created");
            assertTrue(entityMap.containsKey("77789039783"), "Entity should be assigned to SLO");

            var entityData = entityMap.get("77789039783");
            assertNotNull(entityData, "Entity data should be initialized");
            assertEquals(86400L, entityData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Initial duration should be set");

            LOGGER.info(String.format("%s: Availability SLO cycle start test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle start for performance profile
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testSLOCycleStartPerformanceProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("monitorPerformanceId");
            var currentTime = DateTimeUtil.currentSeconds();

            // Create performance SLO profile for cycle start
            var performanceSLOProfile = new JsonObject()
                    .put(ID, sloId)
                    .put(SLOProfile.SLO_PROFILE_NAME, "TestPerformanceSLO")
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_TYPE, SLOConstants.SLOType.PERFORMANCE.name())
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.WEEKLY.name())
                    .put(SLOProfile.SLO_PROFILE_TARGET, 90)
                    .put(SLOProfile.SLO_PROFILE_WARNING, 95)
                    .put(SLOProfile.SLO_PROFILE_START_TIME, currentTime)
                    .put(SLOProfile.SLO_PROFILE_END_TIME, currentTime + 604800) // 1 week
                    .put(SLOProfile.SLO_PROFILE_ENTITY_ASSIGNMENT, SLOConstants.SLOEntityAssignment.DIRECT.name())
                    .put(SLOProfile.SLO_PROFILE_ENTITIES, new JsonArray().add(new JsonObject().put(ID, 77789039783L)));

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            var initialDetailsSize = sloDetailsMap.size();

            // Test cycle start for performance SLO
            startCycleMethod.invoke(sloProcessor, performanceSLOProfile);

            // Verify SLO details are initialized
            assertTrue(sloDetailsMap.containsKey(sloId), "SLO details should be created for performance profile");

            var entityMap = sloDetailsMap.get(sloId);
            assertNotNull(entityMap, "Entity map should be created");
            assertTrue(entityMap.containsKey("77789039783"), "Entity should be assigned to performance SLO");

            var entityData = entityMap.get("77789039783");
            assertNotNull(entityData, "Entity data should be initialized");
            assertEquals(604800L, entityData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Initial duration should be 1 week");

            // Verify performance SLO is not in availability list
            var availabilitySLOProfilesList = (java.util.List<Long>) availabilitySLOProfilesField.get(null);
            assertFalse(availabilitySLOProfilesList.contains(sloId), "Performance SLO should not be in availability list");

            LOGGER.info(String.format("%s: Performance SLO cycle start test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle start with tag-based entity assignment
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testSLOCycleStartTagBasedAssignment(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = 88888L; // Use unique ID for tag-based test
            var currentTime = DateTimeUtil.currentSeconds();

            // Create SLO profile with tag-based entity assignment
            var tagBasedSLOProfile = new JsonObject()
                    .put(ID, sloId)
                    .put(SLOProfile.SLO_PROFILE_NAME, "TestTagBasedSLO")
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_TYPE, SLOConstants.SLOType.AVAILABILITY.name())
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.DAILY.name())
                    .put(SLOProfile.SLO_PROFILE_TARGET, 99)
                    .put(SLOProfile.SLO_PROFILE_WARNING, 99.5)
                    .put(SLOProfile.SLO_PROFILE_START_TIME, currentTime)
                    .put(SLOProfile.SLO_PROFILE_END_TIME, currentTime + 86400)
                    .put(SLOProfile.SLO_PROFILE_ENTITY_ASSIGNMENT, SLOConstants.SLOEntityAssignment.TAGS.name())
                    .put(SLOProfile.SLO_PROFILE_TAGS, new JsonArray().add("production").add("critical"));

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);

            // Test cycle start with tag-based assignment
            startCycleMethod.invoke(sloProcessor, tagBasedSLOProfile);

            // Verify SLO details structure is created (entities will be resolved by qualifyAndAssignEntities)
            assertTrue(sloDetailsMap.containsKey(sloId), "SLO details should be created for tag-based profile");

            LOGGER.info(String.format("%s: Tag-based entity assignment cycle start test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycle start with group-based entity assignment
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testSLOCycleStartGroupBasedAssignment(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = 77777L; // Use unique ID for group-based test
            var currentTime = DateTimeUtil.currentSeconds();

            // Create SLO profile with group-based entity assignment
            var groupBasedSLOProfile = new JsonObject()
                    .put(ID, sloId)
                    .put(SLOProfile.SLO_PROFILE_NAME, "TestGroupBasedSLO")
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_TYPE, SLOConstants.SLOType.PERFORMANCE.name())
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.MONTHLY.name())
                    .put(SLOProfile.SLO_PROFILE_TARGET, 85)
                    .put(SLOProfile.SLO_PROFILE_WARNING, 90)
                    .put(SLOProfile.SLO_PROFILE_START_TIME, currentTime)
                    .put(SLOProfile.SLO_PROFILE_END_TIME, currentTime + 2592000) // 30 days
                    .put(SLOProfile.SLO_PROFILE_ENTITY_ASSIGNMENT, SLOConstants.SLOEntityAssignment.GROUPS.name())
                    .put(SLOProfile.SLO_PROFILE_GROUPS, new JsonArray().add(new JsonObject().put(ID, 12345L)));

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);

            // Test cycle start with group-based assignment
            startCycleMethod.invoke(sloProcessor, groupBasedSLOProfile);

            // Verify SLO details structure is created
            assertTrue(sloDetailsMap.containsKey(sloId), "SLO details should be created for group-based profile");

            LOGGER.info(String.format("%s: Group-based entity assignment cycle start test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Entity Assignment and Filtering Tests ========================

    /**
     * Test entity qualification and assignment for monitor instances
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testEntityQualificationMonitorInstances(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("monitorAvailabilityId");

            // Create SLO profile for monitor instances
            var sloProfile = new JsonObject()
                    .put(ID, sloId)
                    .put(SLOProfile.SLO_PROFILE_ENTITY_TYPE, NMSConstants.EntityType.MONITOR.name())
                    .put(SLOProfile.SLO_PROFILE_ENTITY_ASSIGNMENT, SLOConstants.SLOEntityAssignment.DIRECT.name())
                    .put(SLOProfile.SLO_PROFILE_ENTITIES, new JsonArray()
                        .add(new JsonObject().put(ID, 77789039783L))
                        .add(new JsonObject().put(ID, 77789039784L)));

            // Create SLO cycle
            var sloCycle = new JsonObject()
                    .put(ID, 1L)
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            sloDetailsMap.put(sloId, new java.util.HashMap<>());

            // Test entity qualification and assignment
            qualifyAndAssignEntitiesMethod.invoke(sloProcessor, sloProfile, sloCycle);

            // Verify entities are assigned
            var entityMap = sloDetailsMap.get(sloId);
            assertNotNull(entityMap, "Entity map should exist");
            assertTrue(entityMap.containsKey("77789039783"), "First entity should be assigned");
            assertTrue(entityMap.containsKey("77789039784"), "Second entity should be assigned");

            // Verify entity data initialization
            var entity1Data = entityMap.get("77789039783");
            assertNotNull(entity1Data, "Entity 1 data should be initialized");
            assertEquals(86400L, entity1Data.getLong(SLOConstants.DURATION_LEFT).longValue(), "Entity 1 duration should be set");

            var entity2Data = entityMap.get("77789039784");
            assertNotNull(entity2Data, "Entity 2 data should be initialized");
            assertEquals(86400L, entity2Data.getLong(SLOConstants.DURATION_LEFT).longValue(), "Entity 2 duration should be set");

            LOGGER.info(String.format("%s: Monitor instances entity qualification test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test entity filtering with include/exclude conditions
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testEntityFilteringWithConditions(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Create test entity data
            var testEntity = new JsonObject()
                    .put(ID, 77789039783L)
                    .put(NAME, "test-server-01")
                    .put("status", "UP")
                    .put("location", "datacenter-1")
                    .put("environment", "production");

            // Create filter conditions - include production entities, exclude DOWN status
            var filterConditions = new JsonArray()
                .add(new JsonObject()
                    .put("field", "environment")
                    .put("operator", "EQUALS")
                    .put("value", "production")
                    .put("type", "INCLUDE"))
                .add(new JsonObject()
                    .put("field", "status")
                    .put("operator", "NOT_EQUALS")
                    .put("value", "DOWN")
                    .put("type", "INCLUDE"));

            // Test filtering - should pass (production AND not DOWN)
            var filterResult = (Boolean) filterMethod.invoke(sloProcessor, testEntity, filterConditions);
            assertTrue(filterResult, "Entity should pass filter conditions");

            // Test with failing condition
            testEntity.put("status", "DOWN");
            var filterResultFail = (Boolean) filterMethod.invoke(sloProcessor, testEntity, filterConditions);
            assertFalse(filterResultFail, "Entity should fail filter conditions when status is DOWN");

            // Test with exclude condition
            var excludeConditions = new JsonArray()
                .add(new JsonObject()
                    .put("field", "environment")
                    .put("operator", "EQUALS")
                    .put("value", "production")
                    .put("type", "EXCLUDE"));

            testEntity.put("status", "UP"); // Reset status
            var excludeResult = (Boolean) filterMethod.invoke(sloProcessor, testEntity, excludeConditions);
            assertFalse(excludeResult, "Entity should be excluded when matching exclude condition");

            LOGGER.info(String.format("%s: Entity filtering with conditions test successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Event Bus Cycle Management Tests ========================

    /**
     * Test SLO cycle qualify event handling
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testSLOCycleQualifyEventHandling(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var currentTime = DateTimeUtil.currentSeconds();

            // Setup test SLO profile that needs cycle qualification
            var testSLOProfile = new JsonObject()
                    .put(ID, 55555L)
                    .put(SLOProfile.SLO_PROFILE_ENABLED, true)
                    .put(SLOProfile.SLO_PROFILE_FREQUENCY, SLOConstants.SLOFrequency.DAILY.name())
                    .put(SLOProfile.SLO_PROFILE_START_TIME, currentTime - 86400)
                    .put(SLOProfile.SLO_PROFILE_END_TIME, currentTime - 3600); // Expired

            // Listen for cycle qualification processing
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_SLO_CYCLE_QUALIFY, message -> testContext.verify(() ->
            {
                try
                {
                    var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);

                    // Verify that cycle qualification was triggered
                    assertNotNull(sloCyclesMap, "SLO cycles map should exist");

                    LOGGER.info(String.format("%s: SLO cycle qualify event handling successful", testInfo.getTestMethod().get().getName()));

                    messageConsumer.unregister();
                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                    testContext.failNow(exception.getMessage());
                }
            }));

            // Send cycle qualify event
            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_CYCLE_QUALIFY, new JsonObject());

            // Wait for processing
            TestUtil.vertx().setTimer(2000, id ->
            {
                if (messageConsumer != null)
                {
                    messageConsumer.unregister();
                    testContext.completeNow();
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Calculation Tests ========================

    /**
     * Test SLO achieved percentage calculation
     * Formula: (duration left * 100) / total duration
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testSLOAchievedPercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Create test data with known values
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 8640L); // 86.4% of 10000 seconds left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 10000L); // Total duration 10000 seconds

            // Expected: (8640 * 100) / 10000 = 86.4%
            var expectedPercentage = 86.4f;

            var actualPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedPercentage, actualPercentage, 0.01f, "SLO achieved percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedPercentage, actualPercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO violated seconds calculation
     * Formula: total duration - duration left
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testSLOViolatedSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 7200L); // 2 hours left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Expected: 86400 - 7200 = 79200 seconds violated
            var expectedViolatedSeconds = 79200L;

            var actualViolatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedViolatedSeconds, actualViolatedSeconds, "SLO violated seconds calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedViolatedSeconds, actualViolatedSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Mean Time To Resolve (MTTR) calculation
     * Formula: violated seconds / down incident count
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testSLOMeanTimeToResolveCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 4); // 4 down incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Violated seconds: 86400 - 82800 = 3600 seconds
            // MTTR: 3600 / 4 = 900 seconds (15 minutes per incident)
            var expectedMTTR = 900L;

            var actualMTTR = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedMTTR, actualMTTR, "MTTR calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTTR, actualMTTR));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test MTTR calculation with zero incidents (edge case)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testSLOMeanTimeToResolveWithZeroIncidents(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L) // Full duration left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0); // No incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Expected: 0 (no incidents means no MTTR)
            var expectedMTTR = 0L;

            var actualMTTR = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedMTTR, actualMTTR, "MTTR calculation with zero incidents failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTTR, actualMTTR));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Mean Time Between Failure (MTBF) calculation
     * Formula: (total duration - violated seconds) / up incident count
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testSLOMeanTimeBetweenFailureCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 82800L) // 23 hours left
                    .put(SLOConstants.UP_INCIDENT_COUNT, 3); // 3 up incidents

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            var duration = 86400L;

            // Violated seconds: 86400 - 82800 = 3600 seconds
            // Uptime: 86400 - 3600 = 82800 seconds
            // MTBF: 82800 / 3 = 27600 seconds (7.67 hours between failures)
            var expectedMTBF = 27600L;

            var actualMTBF = (Long) getSLOMeanTimeBetweenFailureMethod.invoke(sloProcessor, sloData, sloCycle, duration);

            assertEquals(expectedMTBF, actualMTBF, "MTBF calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedMTBF, actualMTBF));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Error Budget Left Percentage calculation
     * Formula: 100 - ((total duration - duration left) * 100 / acceptable violation time)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testSLOErrorBudgetLeftPercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 85500L); // 900 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours total
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget (2.4 hours)

            // Violated: 86400 - 85500 = 900 seconds
            // Error budget used: (900 * 100) / 8640 = 10.42%
            // Error budget left: 100 - 10.42 = 89.58%
            var expectedErrorBudgetLeft = 89.58f;

            var actualErrorBudgetLeft = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedErrorBudgetLeft, actualErrorBudgetLeft, 0.01f, "Error budget left percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedErrorBudgetLeft, actualErrorBudgetLeft));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test Error Budget Left Seconds calculation
     * Formula: acceptable violation time - (total duration - duration left)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testSLOErrorBudgetLeftSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 84600L); // 1800 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours total
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            // Violated: 86400 - 84600 = 1800 seconds
            // Error budget left: 8640 - 1800 = 6840 seconds
            var expectedErrorBudgetLeftSeconds = 6840L;

            var actualErrorBudgetLeftSeconds = (Long) getSLOErrorBudgetLeftSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedErrorBudgetLeftSeconds, actualErrorBudgetLeftSeconds, "Error budget left seconds calculation failed");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedErrorBudgetLeftSeconds, actualErrorBudgetLeftSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Burn Rate Seconds calculation
     * Formula: current violated seconds - last violated seconds
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testSLOBurnRateSecondsCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 84600L) // Current: 1800 seconds violated
                    .put(SLOConstants.LAST_VIOLATED_SECONDS, 900L); // Previous: 900 seconds violated

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Current violated: 86400 - 84600 = 1800 seconds
            // Burn rate: 1800 - 900 = 900 seconds burned since last calculation
            var expectedBurnRateSeconds = 900L;

            var actualBurnRateSeconds = (Long) getSLOBurnRateSecondsMethod.invoke(sloProcessor, sloData, sloCycle);

            assertEquals(expectedBurnRateSeconds, actualBurnRateSeconds, "Burn rate seconds calculation failed");

            // Verify that last violated seconds is updated
            assertEquals(1800L, sloData.getLong(SLOConstants.LAST_VIOLATED_SECONDS).longValue(), "Last violated seconds should be updated");

            LOGGER.info(String.format("%s: Expected: %d seconds, Actual: %d seconds", testInfo.getTestMethod().get().getName(), expectedBurnRateSeconds, actualBurnRateSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Burn Rate Percentage calculation
     * Formula: (burn rate seconds * 100) / total duration
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testSLOBurnRatePercentageCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var burnRateSeconds = 1800L; // 30 minutes burned

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L); // 24 hours total

            // Expected: (1800 * 100) / 86400 = 2.08%
            var expectedBurnRatePercentage = 2.08f;

            var actualBurnRatePercentage = (Float) getSLOBurnRateMethod.invoke(sloProcessor, burnRateSeconds, sloCycle);

            assertEquals(expectedBurnRatePercentage, actualBurnRatePercentage, 0.01f, "Burn rate percentage calculation failed");

            LOGGER.info(String.format("%s: Expected: %.2f%%, Actual: %.2f%%", testInfo.getTestMethod().get().getName(), expectedBurnRatePercentage, actualBurnRatePercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO Status determination based on achieved percentage
     * OK: >= warning threshold
     * WARNING: >= target but < warning
     * BREACHED: < target
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testSLOStatusCalculation(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 10000L);

            var target = 90; // 90% target
            var warning = 95; // 95% warning

            // Test BREACHED status (< 90%)
            var sloDataBreached = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 8500L); // 85% achieved

            var statusBreached = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataBreached, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.BREACHED.ordinal(), statusBreached.intValue(), "BREACHED status calculation failed");

            // Test WARNING status (>= 90% but < 95%)
            var sloDataWarning = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 9200L); // 92% achieved

            var statusWarning = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataWarning, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.WARNING.ordinal(), statusWarning.intValue(), "WARNING status calculation failed");

            // Test OK status (>= 95%)
            var sloDataOk = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 9600L); // 96% achieved

            var statusOk = (Integer) getSLOStatusMethod.invoke(sloProcessor, sloDataOk, sloCycle, target, warning);
            assertEquals(SLOConstants.SLOStatus.OK.ordinal(), statusOk.intValue(), "OK status calculation failed");

            LOGGER.info(String.format("%s: BREACHED=%d, WARNING=%d, OK=%d", testInfo.getTestMethod().get().getName(), statusBreached, statusWarning, statusOk));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== SLO Update and State Management Tests ========================

    /**
     * Test SLO update for first poll (initialization)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testSLOUpdateFirstPoll(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L); // Full duration initially

            var currentTimestamp = DateTimeUtil.currentSeconds();
            var objectId = 12345L;
            var instance = "test-instance";
            var sloId = testContext.getLong("monitorAvailabilityId");
            var cycleId = 1L;

            // First poll should initialize the data
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, currentTimestamp, objectId, instance, sloId, cycleId, false);

            // Verify initialization
            assertNotNull(sloData.getInteger(SLOConstants.LAST_SLO_STATUS), "Last SLO status should be set");
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be HEALTHY");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be set");
            assertEquals(1, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Up incident count should be 1");

            LOGGER.info(String.format("%s: First poll initialization successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update for status change (flap detection)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testSLOUpdateStatusChange(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, DateTimeUtil.currentSeconds() - 3600) // 1 hour ago
                    .put(SLOConstants.UP_INCIDENT_COUNT, 1)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0);

            var currentTimestamp = DateTimeUtil.currentSeconds();
            var objectId = 12345L;
            var instance = "test-instance";
            var sloId = testContext.getLong("monitorAvailabilityId");
            var cycleId = 1L;

            // Change status from HEALTHY to DEGRADED
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, currentTimestamp, objectId, instance, sloId, cycleId, false);

            // Verify status change
            assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should change to DEGRADED");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_TIMESTAMP).longValue(), "Last timestamp should be updated");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be set");
            assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down incident count should increment");

            LOGGER.info(String.format("%s: Status change from HEALTHY to DEGRADED successful", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update for continuous degraded state (duration tracking)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testSLOUpdateContinuousDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var initialDurationLeft = 86400L;
            var lastBreachedTimestamp = DateTimeUtil.currentSeconds() - 1800; // 30 minutes ago
            var currentTimestamp = DateTimeUtil.currentSeconds();

            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, initialDurationLeft)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.DEGRADED.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.LAST_BREACHED_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 1);

            var objectId = 12345L;
            var instance = "test-instance";
            var sloId = testContext.getLong("monitorAvailabilityId");
            var cycleId = 1L;

            // Continue in DEGRADED state
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, currentTimestamp, objectId, instance, sloId, cycleId, false);

            // Verify duration is reduced by the time spent in degraded state
            var expectedDurationLeft = initialDurationLeft - (currentTimestamp - lastBreachedTimestamp);
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by degraded time");
            assertEquals(currentTimestamp, sloData.getLong(SLOConstants.LAST_BREACHED_TIMESTAMP).longValue(), "Last breached timestamp should be updated");

            LOGGER.info(String.format("%s: Continuous degraded state tracking successful. Duration reduced by %d seconds",
                testInfo.getTestMethod().get().getName(), currentTimestamp - lastBreachedTimestamp));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update for recovery from degraded state
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testSLOUpdateRecoveryFromDegradedState(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var initialDurationLeft = 86400L;
            var lastBreachedTimestamp = DateTimeUtil.currentSeconds() - 900; // 15 minutes ago
            var currentTimestamp = DateTimeUtil.currentSeconds();

            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, initialDurationLeft)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.DEGRADED.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.LAST_BREACHED_TIMESTAMP, lastBreachedTimestamp)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 1)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 0);

            var objectId = 12345L;
            var instance = "test-instance";
            var sloId = testContext.getLong("monitorAvailabilityId");
            var cycleId = 1L;

            // Recover to HEALTHY state
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, currentTimestamp, objectId, instance, sloId, cycleId, false);

            // Verify recovery
            var expectedDurationLeft = initialDurationLeft - (currentTimestamp - lastBreachedTimestamp);
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should be reduced by total degraded time");
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should change to HEALTHY");
            assertEquals(1, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Up incident count should increment");

            LOGGER.info(String.format("%s: Recovery from degraded state successful. Total degraded time: %d seconds",
                testInfo.getTestMethod().get().getName(), currentTimestamp - lastBreachedTimestamp));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Event Bus Integration Tests ========================

    /**
     * Test SLO update event processing for performance SLO
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testSLOUpdateEventProcessingPerformance(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("monitorPerformanceId");
            var objectId = 77789039783L; // Test object ID
            var instance = EMPTY_VALUE;
            var currentTimestamp = DateTimeUtil.currentSeconds();

            // Setup test data in sloDetails map
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            var instanceMap = new java.util.HashMap<String, JsonObject>();
            var testData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, currentTimestamp - 300)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 1)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0);

            instanceMap.put(objectId.toString(), testData);
            sloDetailsMap.put(sloId, instanceMap);

            // Setup sloCycles map
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            sloCyclesMap.put(sloId, 1L);

            // Create performance SLO update event (CRITICAL severity)
            var inspectedResult = new JsonObject()
                    .put(SEVERITY, GlobalConstants.Severity.CRITICAL.name())
                    .put("entity.id", objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp)
                    .put(ID, sloId);

            // Listen for SLO update processing
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_SLO_UPDATE, message -> testContext.verify(() ->
            {
                try
                {
                    // Verify the event was processed
                    var updatedData = sloDetailsMap.get(sloId).get(objectId.toString());
                    assertNotNull(updatedData, "SLO data should exist after processing");

                    // For CRITICAL severity, status should change to DEGRADED
                    assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), updatedData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(),
                        "Status should change to DEGRADED for CRITICAL severity");

                    assertEquals(1, updatedData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(),
                        "Down incident count should increment");

                    LOGGER.info(String.format("%s: Performance SLO update event processed successfully", testInfo.getTestMethod().get().getName()));

                    messageConsumer.unregister();
                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                    testContext.failNow(exception.getMessage());
                }
            }));

            // Send the event
            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, inspectedResult);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO update event processing for availability SLO
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testSLOUpdateEventProcessingAvailability(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("monitorAvailabilityId");
            var objectId = 77789039783L;
            var instance = EMPTY_VALUE;
            var currentTimestamp = DateTimeUtil.currentSeconds();

            // Setup availability SLO profiles list
            var availabilitySLOProfilesList = (java.util.List<Long>) availabilitySLOProfilesField.get(null);
            if (!availabilitySLOProfilesList.contains(sloId))
            {
                availabilitySLOProfilesList.add(sloId);
            }

            // Setup test data
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            var instanceMap = new java.util.HashMap<String, JsonObject>();
            var testData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.LAST_TIMESTAMP, currentTimestamp - 300)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 1)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0);

            instanceMap.put(objectId.toString(), testData);
            sloDetailsMap.put(sloId, instanceMap);

            // Setup sloCycles map
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            sloCyclesMap.put(sloId, 1L);

            // Create availability SLO update event (DOWN status)
            var inspectedResult = new JsonObject()
                    .put(SEVERITY, GlobalConstants.Severity.DOWN.name())
                    .put("entity.id", objectId)
                    .put(INSTANCE, instance)
                    .put(EventBusConstants.EVENT_TIMESTAMP, currentTimestamp);
            // Note: No ID field for availability SLO events

            // Listen for SLO update processing
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_SLO_UPDATE, message -> testContext.verify(() ->
            {
                try
                {
                    // Verify the event was processed for availability SLO
                    var updatedData = sloDetailsMap.get(sloId).get(objectId.toString());
                    assertNotNull(updatedData, "SLO data should exist after processing");

                    // For DOWN severity, status should change to DEGRADED
                    assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), updatedData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(),
                        "Status should change to DEGRADED for DOWN severity");

                    assertEquals(1, updatedData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(),
                        "Down incident count should increment");

                    LOGGER.info(String.format("%s: Availability SLO update event processed successfully", testInfo.getTestMethod().get().getName()));

                    messageConsumer.unregister();
                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                    testContext.failNow(exception.getMessage());
                }
            }));

            // Send the event
            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SLO_UPDATE, inspectedResult);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Edge Cases and Error Handling Tests ========================

    /**
     * Test calculation with zero total duration (edge case)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testCalculationWithZeroTotalDuration(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 0L);

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 0L); // Zero duration

            // Test achieved percentage with zero duration
            var achievedPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0.0f, achievedPercentage, 0.01f, "Achieved percentage should be 0 for zero duration");

            // Test violated seconds with zero duration
            var violatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0L, violatedSeconds.longValue(), "Violated seconds should be 0 for zero duration");

            LOGGER.info(String.format("%s: Zero duration edge case handled correctly", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test calculation with negative duration left (edge case)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testCalculationWithNegativeDurationLeft(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, -1000L); // Negative duration left

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L);

            // Test achieved percentage with negative duration
            var achievedPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            assertTrue(achievedPercentage <= 0.0f, "Achieved percentage should be <= 0 for negative duration left");

            // Test violated seconds with negative duration
            var violatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(87400L, violatedSeconds.longValue(), "Violated seconds should be total + abs(negative duration)");

            LOGGER.info(String.format("%s: Negative duration left edge case handled correctly", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test error budget calculation when fully exhausted
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testErrorBudgetFullyExhausted(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 77760L); // 8640 seconds violated (exactly error budget)

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            // Error budget should be 0%
            var errorBudgetLeftPercentage = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0.0f, errorBudgetLeftPercentage, 0.01f, "Error budget should be 0% when fully exhausted");

            // Error budget seconds should be 0
            var errorBudgetLeftSeconds = (Long) getSLOErrorBudgetLeftSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0L, errorBudgetLeftSeconds.longValue(), "Error budget seconds should be 0 when fully exhausted");

            LOGGER.info(String.format("%s: Fully exhausted error budget handled correctly", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test error budget calculation when over-exhausted (negative)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testErrorBudgetOverExhausted(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 70000L); // 16400 seconds violated (more than error budget)

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L) // 24 hours
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            // Error budget should be negative percentage
            var errorBudgetLeftPercentage = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            assertTrue(errorBudgetLeftPercentage < 0.0f, "Error budget should be negative when over-exhausted");

            // Error budget seconds should be negative
            var errorBudgetLeftSeconds = (Long) getSLOErrorBudgetLeftSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertTrue(errorBudgetLeftSeconds < 0L, "Error budget seconds should be negative when over-exhausted");

            LOGGER.info(String.format("%s: Over-exhausted error budget handled correctly. Percentage: %.2f%%, Seconds: %d",
                testInfo.getTestMethod().get().getName(), errorBudgetLeftPercentage, errorBudgetLeftSeconds));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Data Structure Validation Tests ========================

    /**
     * Test SLO details map structure and data integrity
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testSLODetailsMapStructure(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloDetailsMap = (Map<Long, Map<String, JsonObject>>) sloDetailsField.get(null);
            assertNotNull(sloDetailsMap, "SLO details map should be initialized");

            // Add test data to verify structure
            var sloId = testContext.getLong("monitorAvailabilityId");
            var objectId = "test-object-123";
            var instanceMap = new java.util.HashMap<String, JsonObject>();

            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L)
                    .put(SLOConstants.LAST_SLO_STATUS, SLOConstants.SLOFlapStatus.HEALTHY.ordinal())
                    .put(SLOConstants.UP_INCIDENT_COUNT, 1)
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 0);

            instanceMap.put(objectId, sloData);
            sloDetailsMap.put(sloId, instanceMap);

            // Verify structure
            assertTrue(sloDetailsMap.containsKey(sloId), "SLO details map should contain the SLO ID");
            assertTrue(sloDetailsMap.get(sloId).containsKey(objectId), "Instance map should contain the object ID");

            var retrievedData = sloDetailsMap.get(sloId).get(objectId);
            assertEquals(86400L, retrievedData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Duration left should match");
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), retrievedData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should match");

            LOGGER.info(String.format("%s: SLO details map structure validated successfully", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO cycles map structure and data integrity
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testSLOCyclesMapStructure(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloCyclesMap = (Map<Long, Long>) sloCyclesField.get(null);
            assertNotNull(sloCyclesMap, "SLO cycles map should be initialized");

            // Add test data
            var sloId = testContext.getLong("monitorPerformanceId");
            var cycleId = 12345L;
            sloCyclesMap.put(sloId, cycleId);

            // Verify structure
            assertTrue(sloCyclesMap.containsKey(sloId), "SLO cycles map should contain the SLO ID");
            assertEquals(cycleId, sloCyclesMap.get(sloId).longValue(), "Cycle ID should match");

            LOGGER.info(String.format("%s: SLO cycles map structure validated successfully", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO events map structure for availability SLOs
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testSLOEventsMapStructure(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloEventsMap = (Map<Long, TreeMap<Long, Integer>>) sloEventsField.get(null);
            assertNotNull(sloEventsMap, "SLO events map should be initialized");

            // Add test data
            var sloId = testContext.getLong("instanceAvailabilityId");
            var timestamp = DateTimeUtil.currentSeconds();
            var eventCount = 5;

            var timeEventMap = new TreeMap<Long, Integer>();
            timeEventMap.put(timestamp, eventCount);
            sloEventsMap.put(sloId, timeEventMap);

            // Verify structure
            assertTrue(sloEventsMap.containsKey(sloId), "SLO events map should contain the SLO ID");
            assertTrue(sloEventsMap.get(sloId).containsKey(timestamp), "Time event map should contain the timestamp");
            assertEquals(eventCount, sloEventsMap.get(sloId).get(timestamp).intValue(), "Event count should match");

            LOGGER.info(String.format("%s: SLO events map structure validated successfully", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test availability SLO profiles list structure
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testAvailabilitySLOProfilesList(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var availabilitySLOProfilesList = (java.util.List<Long>) availabilitySLOProfilesField.get(null);
            assertNotNull(availabilitySLOProfilesList, "Availability SLO profiles list should be initialized");

            // Add test data
            var availabilitySloId = testContext.getLong("monitorAvailabilityId");
            if (!availabilitySLOProfilesList.contains(availabilitySloId))
            {
                availabilitySLOProfilesList.add(availabilitySloId);
            }

            // Verify structure
            assertTrue(availabilitySLOProfilesList.contains(availabilitySloId), "List should contain availability SLO ID");

            // Verify performance SLO is not in availability list
            var performanceSloId = testContext.getLong("monitorPerformanceId");
            assertFalse(availabilitySLOProfilesList.contains(performanceSloId), "List should not contain performance SLO ID");

            LOGGER.info(String.format("%s: Availability SLO profiles list validated successfully", testInfo.getTestMethod().get().getName()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    // ======================== Complex Scenario Tests ========================

    /**
     * Test complete SLO lifecycle with multiple status changes
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testCompleteSLOLifecycle(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var sloId = testContext.getLong("instancePerformanceId");
            var objectId = 99999L;
            var instance = "lifecycle-test-instance";
            var cycleId = 2L;
            var baseTimestamp = DateTimeUtil.currentSeconds() - 7200; // Start 2 hours ago

            // Initialize SLO data
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 86400L); // 24 hours initially

            // Scenario 1: First poll - HEALTHY
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, baseTimestamp, objectId, instance, sloId, cycleId, false);
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Initial status should be HEALTHY");
            assertEquals(1, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Initial up count should be 1");

            // Scenario 2: Status change to DEGRADED (1 hour later)
            var degradedTimestamp = baseTimestamp + 3600;
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, degradedTimestamp, objectId, instance, sloId, cycleId, false);
            assertEquals(SLOConstants.SLOFlapStatus.DEGRADED.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should change to DEGRADED");
            assertEquals(1, sloData.getInteger(SLOConstants.DOWN_INCIDENT_COUNT).intValue(), "Down count should increment");

            // Scenario 3: Continue in DEGRADED state (30 minutes later)
            var continueDegradedTimestamp = degradedTimestamp + 1800;
            var durationBeforeContinue = sloData.getLong(SLOConstants.DURATION_LEFT);
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.DEGRADED, continueDegradedTimestamp, objectId, instance, sloId, cycleId, false);
            var durationAfterContinue = sloData.getLong(SLOConstants.DURATION_LEFT);
            assertTrue(durationAfterContinue < durationBeforeContinue, "Duration should decrease while in DEGRADED state");

            // Scenario 4: Recovery to HEALTHY (15 minutes later)
            var recoveryTimestamp = continueDegradedTimestamp + 900;
            var durationBeforeRecovery = sloData.getLong(SLOConstants.DURATION_LEFT);
            updateSLOMethod.invoke(sloProcessor, sloData, SLOConstants.SLOFlapStatus.HEALTHY, recoveryTimestamp, objectId, instance, sloId, cycleId, false);
            assertEquals(SLOConstants.SLOFlapStatus.HEALTHY.ordinal(), sloData.getInteger(SLOConstants.LAST_SLO_STATUS).intValue(), "Status should recover to HEALTHY");
            assertEquals(2, sloData.getInteger(SLOConstants.UP_INCIDENT_COUNT).intValue(), "Up count should increment on recovery");

            // Calculate total degraded time: 1800 + 900 = 2700 seconds (45 minutes)
            var expectedDurationLeft = 86400L - 2700L;
            assertEquals(expectedDurationLeft, sloData.getLong(SLOConstants.DURATION_LEFT).longValue(), "Final duration should reflect total degraded time");

            // Test calculations with final state
            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 86400L)
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 8640L); // 10% error budget

            var achievedPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            var expectedAchievedPercentage = (expectedDurationLeft * 100.0f) / 86400L;
            assertEquals(expectedAchievedPercentage, achievedPercentage, 0.01f, "Achieved percentage should match calculated value");

            var mttr = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);
            var expectedMTTR = 2700L / 1; // Total degraded time / down incidents
            assertEquals(expectedMTTR, mttr.longValue(), "MTTR should match calculated value");

            LOGGER.info(String.format("%s: Complete SLO lifecycle test successful. Final achieved: %.2f%%, MTTR: %d seconds",
                testInfo.getTestMethod().get().getName(), achievedPercentage, mttr));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    /**
     * Test SLO calculations with high precision requirements
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testHighPrecisionSLOCalculations(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            // Test with very small durations for precision
            var sloData = new JsonObject()
                    .put(SLOConstants.DURATION_LEFT, 999999L) // 999.999 seconds left
                    .put(SLOConstants.DOWN_INCIDENT_COUNT, 3)
                    .put(SLOConstants.UP_INCIDENT_COUNT, 7)
                    .put(SLOConstants.LAST_VIOLATED_SECONDS, 1L);

            var sloCycle = new JsonObject()
                    .put(SLOCycle.SLO_CYCLE_TOTAL_DURATION, 1000000L) // 1 million seconds
                    .put(SLOCycle.SLO_CYCLE_ACCEPTABLE_VIOLATION_TIME, 50000L); // 5% error budget

            // Test achieved percentage precision
            var achievedPercentage = (Float) getSLOAchievedPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            var expectedAchievedPercentage = 99.9999f; // (999999 * 100) / 1000000
            assertEquals(expectedAchievedPercentage, achievedPercentage, 0.0001f, "High precision achieved percentage failed");

            // Test violated seconds precision
            var violatedSeconds = (Long) getSLOViolatedSecondsMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(1L, violatedSeconds.longValue(), "Violated seconds should be exactly 1");

            // Test MTTR precision
            var mttr = (Long) getSLOMeanTimeToResolveMethod.invoke(sloProcessor, sloData, sloCycle);
            assertEquals(0L, mttr.longValue(), "MTTR should be 0 for 1 second violation across 3 incidents");

            // Test error budget precision
            var errorBudgetLeftPercentage = (Float) getSLOErrorBudgetLeftPercentageMethod.invoke(sloProcessor, sloData, sloCycle);
            var expectedErrorBudgetLeft = 100.0f - ((1.0f * 100.0f) / 50000.0f); // 100 - (1*100/50000) = 99.998%
            assertEquals(expectedErrorBudgetLeft, errorBudgetLeftPercentage, 0.001f, "High precision error budget calculation failed");

            LOGGER.info(String.format("%s: High precision calculations successful. Achieved: %.4f%%, Error Budget: %.3f%%",
                testInfo.getTestMethod().get().getName(), achievedPercentage, errorBudgetLeftPercentage));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        try
        {
            // Clean up any test data if needed
            LOGGER.info("SLO Processor tests completed successfully");
            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception.getMessage());
        }
    }
}
