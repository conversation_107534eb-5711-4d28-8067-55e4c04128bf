/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.rediscovery;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@Timeout(180 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestApplicationRediscovery
{
    public static final JsonObject DISCOVERY_PROFILES = new JsonObject();
    public static final Map<Long, VertxTestContext> DISCOVERY_ITEMS = new HashMap<>();
    private static final Logger LOGGER = new Logger(TestCloudRediscovery.class, MOTADATA_NMS, "Application Rediscovery Test");
    private static MessageConsumer<JsonObject> messageConsumer = null;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            assertReDiscoveryConsumer();

            TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "application-discovery-parameters.json");

            if (file.exists())
            {
                DISCOVERY_PROFILES.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.awaitCompletion(2000, TimeUnit.MILLISECONDS);

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    public static void assertReDiscoveryConsumer()
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().getBinary(EVENT_CONTEXT) != null && message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                if (eventContext.containsKey(EVENT_NAME) && eventContext.getString(EVENT_NAME).equalsIgnoreCase("discovery"))
                {
                    var context = eventContext.getJsonObject(EVENT_CONTEXT);

                    if (context.containsKey(NMSConstants.REDISCOVER_JOB) && context.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase("application"))
                    {
                        DISCOVERY_ITEMS.get(context.getLong("event.discovery.id")).verify(() ->
                        {
                            if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                DISCOVERY_ITEMS.remove(context.getLong("event.discovery.id")).completeNow();
                            }
                            else
                            {
                                DISCOVERY_ITEMS.get(context.getLong("event.discovery.id")).failNow(new Exception("Failed to discover application..."));
                            }
                        });
                    }
                }
            }
        });
    }

    private static void runAppDiscoveryTest(VertxTestContext testContext, JsonObject discoveryParameters)
    {
        try
        {
            DISCOVERY_ITEMS.put(discoveryParameters.getLong("event.discovery.id"), testContext);

            if (discoveryParameters.getValue(Discovery.DISCOVERY_TARGET) instanceof Long)
            {
                discoveryParameters.put(Discovery.DISCOVERY_TARGET, new JsonArray(new ArrayList(1)).add(discoveryParameters.getLong(Discovery.DISCOVERY_TARGET)));
            }

            if (discoveryParameters.getJsonArray(Discovery.DISCOVERY_TARGET) != null)
            {
                discoveryParameters.getJsonArray(Discovery.DISCOVERY_TARGET).forEach(object -> ObjectStatusCacheStore.getStore().updateItem(CommonUtil.getLong(object), STATUS_UP, DateTimeUtil.currentSeconds()));
            }

            var object = ObjectConfigStore.getStore().getItem(discoveryParameters.getJsonArray(Discovery.DISCOVERY_TARGET).getLong(0));

            if (discoveryParameters.containsKey(Metric.METRIC_TYPE) && object != null)
            {
                var objects = SystemProcessConfigStore.getStore().getItemsByValue(SystemProcess.SYSTEM_PROCESS_APP_TYPE, discoveryParameters.getString(Metric.METRIC_TYPE))
                        .stream().filter(item -> JsonObject.mapFrom(item).containsKey(SystemProcess.SYSTEM_PROCESS_OS) && JsonObject.mapFrom(item).getString(SystemProcess.SYSTEM_PROCESS_OS).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_TYPE)))
                        .map(JsonObject::mapFrom)
                        .findFirst().orElse(null);

                if (objects != null)
                {
                    var metric = MetricConfigStore.getStore().getItemsByObject(discoveryParameters.getJsonArray(Discovery.DISCOVERY_TARGET).getLong(0))
                            .stream().filter(item -> NMSConstants.isSystemProcessMetric(item.getString(Metric.METRIC_PLUGIN)))
                            .map(JsonObject::mapFrom).findFirst().orElse(null);

                    if (metric != null && metric.getJsonObject(Metric.METRIC_CONTEXT) != null && metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS) != null)
                    {
                        var processes = metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS)
                                .stream().map(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_NAME)).toList();

                        var process = processes.stream()
                                .filter(item -> item.contains(objects.getString(SystemProcess.SYSTEM_PROCESS)))
                                .map(String::trim)
                                .findFirst().orElse(null);

                        if (process != null)
                        {
                            objects.put(SystemProcess.SYSTEM_PROCESS, process);

                            discoveryParameters.put(NMSConstants.OBJECTS, objects);
                        }
                    }
                }
            }

            var scheduler = object != null ? SchedulerConfigStore.getStore().getItemsByMapValueField(Scheduler.SCHEDULER_CONTEXT, NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.APP.getName())
                    .stream().filter(item -> JsonObject.mapFrom(item).getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_SYSTEM))
                    .map(JsonObject::mapFrom).findFirst().orElse(null) : null;

            var promise = Promise.<JsonObject>promise();

            if (scheduler != null)
            {
                if (scheduler.containsKey(Scheduler.SCHEDULER_CONTEXT))
                {
                    scheduler.mergeIn(scheduler.getJsonObject(Scheduler.SCHEDULER_CONTEXT));
                }

                discoveryParameters.put(EVENT_SCHEDULER, scheduler.getLong(ID));

                var items = new JsonArray()
                        .add(new JsonObject().put(STATUS, "pending")
                                .put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP))
                                .put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME))
                                .put(Metric.METRIC_TYPE, discoveryParameters.getString(Metric.METRIC_TYPE)));

                scheduler.put(RESULT, items);

                Bootstrap.configDBService().update(DBConstants.TBL_SCHEDULER,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, scheduler.getLong(ID)),
                        scheduler, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                        handler ->
                                SchedulerConfigStore.getStore().updateItem(scheduler.getLong(ID)).onComplete(result ->
                                {
                                    scheduler.remove(ID);

                                    discoveryParameters.mergeIn(scheduler);

                                    promise.complete(discoveryParameters);
                                }));
            }
            else
            {
                promise.complete(discoveryParameters);
            }

            promise.future().onComplete(result ->
            {
                LOGGER.trace(String.format("Request for Application Discovery: %s, Metric Type: %s", discoveryParameters.getString(Discovery.DISCOVERY_NAME), discoveryParameters.getString(Metric.METRIC_TYPE)));

                TestAPIUtil.post(TestAPIConstants.APPLICATION_DISCOVERY_API_ENDPOINT, result.result(), testContext.succeeding(discoveryResponse ->
                        testContext.verify(() ->
                        {
                            assertEquals(InfoMessageConstants.DISCOVERY_APP_START_SUCCEEDED, discoveryResponse.bodyAsJsonObject().getString(MESSAGE));

                            assertEquals(SC_OK, discoveryResponse.statusCode());

                            assertEquals(SC_OK, discoveryResponse.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                            assertEquals(STATUS_SUCCEED, discoveryResponse.bodyAsJsonObject().getString(STATUS));

                        })));
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getCause());
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext) throws InterruptedException
    {

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        messageConsumer.unregister(result -> testContext.completeNow());
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(1000, TimeUnit.MILLISECONDS);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testMariaDBDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.MARIADB.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testWildflyDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo) throws InterruptedException
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()).put("event.discovery.id", CommonUtil.newEventId())).put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.WILDFLY.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testTomcatDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo) throws InterruptedException
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.APACHE_TOMCAT.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testNginxDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo) throws InterruptedException
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.NGINX.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testApacheDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo) throws InterruptedException
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.APACHE_HTTP.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testLightHTTPDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.LIGHTTPD.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testBind9Discovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.BIND9.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testIBMWebsphereDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.IBM_WEBSPHERE.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testRabbitMQDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo) throws InterruptedException
    {

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.RABBITMQ.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testPostgreSQLDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.POSTGRESQL.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testIBMDB2Discovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.IBM_DB2.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testMSSQLDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.SQL_SERVER.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testMySQLDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.MYSQL.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testOracleDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.ORACLE_DATABASE.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testSybaseDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.SYBASE.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testSAPMaxDBDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.SAP_MAXDB.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testSAPHANADiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.SAP_HANA.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testLinuxDHCPDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.LINUX_DHCP.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testHAProxyDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.HA_PROXY.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testActiveDirectoryDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.ACTIVE_DIRECTORY.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testIISDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.MICROSOFT_IIS.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testWindowsDHCPDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.WINDOWS_DHCP.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testWindowsDNSDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.WINDOWS_DNS.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testWindowsRDPDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.WINDOWS_RDP.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testMSMQDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.remove(Discovery.DISCOVERY_CREDENTIAL_PROFILES);

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testExchangeClientAccessRoleDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.EXCHANGE_CLIENT_ACCESS_ROLE.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testExchangeMailBoxRoleDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.EXCHANGE_MAILBOX_ROLE.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testExchangeEdgeTransportRoleDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.EXCHANGE_EDGE_TRANSPORT_ROLE.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName())).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testMongoDBDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.MONGODB.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testZimbraDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.ZIMBRA.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testWebLogicDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_TARGET, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_TARGET)).copy();

        context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, context.getJsonObject("application.discovery.credential.profile.context").getJsonObject(NMSConstants.MetricPlugin.ORACLE_WEBLOGIC.getName()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)).getLong(GlobalConstants.ID)));

        runAppDiscoveryTest(vertxTestContext, context.mergeIn(DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).put(Discovery.DISCOVERY_TARGET, context.getLong(GlobalConstants.ID)).put("event.discovery.id", CommonUtil.newEventId())));

    }

}
