/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import io.vertx.core.json.JsonObject;

import static com.mindarray.eventbus.EventBusConstants.AUTO_RECONNECT;
import static com.mindarray.eventbus.EventBusConstants.TRANSPORT_PROTOCOL;

/**
 * Factory class for creating transport server and client instances based on configuration.
 *
 * <p>This class provides factory methods to create appropriate transport implementations
 * based on the specified configuration. It abstracts the creation details and allows
 * the application to work with the transport interfaces without knowing the specific
 * implementation details.</p>
 *
 * <p>The TransportProvider supports various transport protocols defined in
 * {@link EventBusConstants.TransportProtocol}, including:</p>
 * <ul>
 *   <li>ZMQ (ZeroMQ) - High-performance asynchronous messaging library</li>
 *   <li>TCP - Standard TCP socket-based transport</li>
 *   <li>UDP - Connectionless UDP transport</li>
 *   <li>HTTP2 - HTTP/2 protocol-based transport</li>
 *   <li>HTTP3 - HTTP/3 protocol-based transport</li>
 * </ul>
 *
 * <p>The factory methods examine the configuration to determine which transport implementation
 * to instantiate, and handle any additional configuration such as auto-reconnection for clients.</p>
 *
 * <p>Example usage:</p>
 * <pre>{@code
 * // Create a ZMQ server
 * JsonObject serverConfig = new JsonObject()
 *     .put(TRANSPORT_PROTOCOL, TransportProtocol.ZMQ.getValue())
 *     .put("host", "*")
 *     .put("port", 5555)
 *     .put("socket_mode", SocketMode.PUB);
 * TransportServer server = TransportProvider.getServer(serverConfig);
 *
 * // Create a ZMQ client with auto-reconnect
 * JsonObject clientConfig = new JsonObject()
 *     .put(TRANSPORT_PROTOCOL, TransportProtocol.ZMQ.getValue())
 *     .put(AUTO_RECONNECT, true)
 *     .put("host", "localhost")
 *     .put("port", 5555)
 *     .put("socket_mode", SocketMode.SUB);
 * TransportClient client = TransportProvider.getClient(clientConfig);
 * }</pre>
 *
 * <AUTHOR> Engineering Team
 * @version 1.0
 * @since 1.0
 * @see TransportServer
 * @see TransportClient
 * @see EventBusConstants.TransportProtocol
 */
public class TransportProvider
{
    /**
     * Creates and returns a transport server implementation based on the provided configuration.
     *
     * <p>This factory method examines the configuration to determine which transport server
     * implementation to instantiate. The transport protocol is specified in the configuration
     * using the {@link EventBusConstants#TRANSPORT_PROTOCOL} key.</p>
     *
     * <p>Currently supported transport server implementations:</p>
     * <ul>
     *   <li>{@link ZMQServer} - For ZeroMQ transport protocol</li>
     * </ul>
     *
     * <p>The configuration object should contain all necessary parameters for the specific
     * transport implementation, such as:</p>
     * <ul>
     *   <li>Host address to bind to</li>
     *   <li>Port number</li>
     *   <li>Socket mode (PUB, PULL, etc.)</li>
     *   <li>Protocol-specific options (high water mark, etc.)</li>
     * </ul>
     *
     * <p>Example configuration for ZMQ server:</p>
     * <pre>{@code
     * JsonObject config = new JsonObject()
     *     .put(TRANSPORT_PROTOCOL, TransportProtocol.ZMQ.getValue())
     *     .put("host", "*")
     *     .put("port", 5555)
     *     .put("socket_mode", SocketMode.PUB);
     * }</pre>
     *
     * @param config a JsonObject containing the transport server configuration
     * @return a TransportServer implementation based on the specified protocol
     * @throws IllegalArgumentException if the specified protocol is not supported or if the configuration is invalid
     * @see TransportServer
     * @see ZMQServer
     * @see EventBusConstants.TransportProtocol
     */
    public static TransportServer getServer(JsonObject config) throws IllegalArgumentException
    {
        return switch (EventBusConstants.TransportProtocol.valueOfName(config.getString(TRANSPORT_PROTOCOL)))
        {
            case EventBusConstants.TransportProtocol.ZMQ -> new ZMQServer(config);

            default -> throw new IllegalArgumentException("Unsupported protocol for server " + config.getString(TRANSPORT_PROTOCOL));
        };
    }

    /**
     * Creates and returns a transport client implementation based on the provided configuration.
     *
     * <p>This factory method examines the configuration to determine which transport client
     * implementation to instantiate. The transport protocol is specified in the configuration
     * using the {@link EventBusConstants#TRANSPORT_PROTOCOL} key.</p>
     *
     * <p>Currently supported transport client implementations:</p>
     * <ul>
     *   <li>{@link ZMQClient} - For ZeroMQ transport protocol</li>
     * </ul>
     *
     * <p>The method also checks for the {@link EventBusConstants#AUTO_RECONNECT} flag in the
     * configuration. If set to true, the returned client will be wrapped in an
     * {@link TransportReconnectClient} to provide automatic reconnection capabilities.</p>
     *
     * <p>The configuration object should contain all necessary parameters for the specific
     * transport implementation, such as:</p>
     * <ul>
     *   <li>Host address to connect to</li>
     *   <li>Port number</li>
     *   <li>Socket mode (SUB, PUSH, etc.)</li>
     *   <li>Protocol-specific options (high water mark, etc.)</li>
     *   <li>Auto-reconnect flag and parameters</li>
     * </ul>
     *
     * <p>Example configuration for ZMQ client with auto-reconnect:</p>
     * <pre>{@code
     * JsonObject config = new JsonObject()
     *     .put(TRANSPORT_PROTOCOL, TransportProtocol.ZMQ.getValue())
     *     .put(AUTO_RECONNECT, true)
     *     .put("host", "localhost")
     *     .put("port", 5555)
     *     .put("socket_mode", SocketMode.SUB);
     * }</pre>
     *
     * @param config a JsonObject containing the transport client configuration
     * @return a TransportClient implementation based on the specified protocol,
     *         optionally wrapped in an AutoReconnectClient if auto-reconnect is enabled
     * @throws IllegalArgumentException if the specified protocol is not supported or if the configuration is invalid
     * @see TransportClient
     * @see ZMQClient
     * @see TransportReconnectClient
     * @see EventBusConstants.TransportProtocol
     */
    public static TransportClient getClient(JsonObject config) throws IllegalArgumentException
    {
        TransportClient base = switch (EventBusConstants.TransportProtocol.valueOfName(config.getString(TRANSPORT_PROTOCOL)))
        {
            case EventBusConstants.TransportProtocol.ZMQ -> new ZMQClient(config);

            default -> throw new IllegalArgumentException("Unsupported protocol for client " + config.getString(TRANSPORT_PROTOCOL));
        };

        if (config.getBoolean(AUTO_RECONNECT, false))
        {
            base = new TransportReconnectClient(base,config);
        }

        return base;
    }
}