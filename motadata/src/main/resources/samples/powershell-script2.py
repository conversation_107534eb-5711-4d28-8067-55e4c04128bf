#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode

from motadatasdk.constants import Constant
from motadatasdk.util import Util

# COMMAND="repadmin /showrepl"


if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    output = Util.load_plugin_context(str(args.context)).get(Constant.RESULT)

    result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

    destination = None

    replication = {}

    tokens = output.strip().split("\r\n\r\n\r\n")

    if tokens is not None and len(tokens) > 0:

        for token in tokens:

            if token.strip().lower().startswith("repadmin:"):

                destination = token.strip().split("\r\n\r\n")[1].strip()

            else:

                token = token.strip().split("\r\n\r\n")

                if len(token) > 3 and token[2].strip().lower().startswith('dsa object guid:'):

                    source = token[1].strip()

                    if replication.get(source) is None:
                        replication[source] = {
                            'ad.replication': source,
                            'ad.replication.destination': destination
                        }

                    replication[source][token[0].strip()] = "succeed" \
                        if ' successful' in token[3].strip().lower() else "fail"

                if len(token) > 2 and token[0].strip().lower().startswith("naming context:"):

                    source = token[1].strip().split("Source:")[1].strip()

                    if replication.get(source) is None:
                        replication[source] = {
                            'ad.replication': source,
                            'ad.replication.destination': destination
                        }

                    replication[source][token[0].strip().split('Naming Context:')[1].strip()] = Constant.STATUS_SUCCEED \
                        if ' successful' in token[2].strip().lower() else Constant.STATUS_FAIL

    if len(replication) > 0:

        result[Constant.STATUS] = Constant.STATUS_SUCCEED

        result[Constant.RESULT]['ad.replication'] = list(replication.values())

    print(b64encode(json.dumps(result).encode()).decode(), end=Constant.BLANK_STRING)