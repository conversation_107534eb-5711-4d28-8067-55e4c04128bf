/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.datastore;

import com.mindarray.Bootstrap;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.ha.HAConstants;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.util.PortUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.UPDATE_CACHE;

public class ConfigDatastore extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ConfigDatastore.class, MOTADATA_DATASTORE, "Config Datastore");
    private final String uuid;
    private boolean flag = false;
    private DatastoreEngine eventEngine;

    public ConfigDatastore(String uuid)
    {
        this.uuid = uuid;
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        eventEngine = new DatastoreEngine().setEventType(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + uuid)
                .setPersistEventOffset(true).setStatus(false).setLogger(LOGGER).setEventHandler(this::publish).start(vertx, promise);

        vertx.eventBus().<Void>localConsumer(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + uuid + ".inactive", message -> eventEngine.setStatus(false));

        // this timer will run till all the required failover or slave node registered.
        vertx.setPeriodic(30 * 1000L, timer ->
        {
            if (RemoteEventProcessorConfigStore.getStore().getItemsByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name()).size() == MotadataConfigUtil.getHAFailoverNodes())
            {
                flag = true;

                vertx.cancelTimer(timer);

                LOGGER.info(String.format("%s item registered. now app can send the data to observer to write data with respect to uuid", MotadataConfigUtil.getHAFailoverNodes()));
            }
        });

        //send a request to check if the cache file needs updated. If an update file then read the file and send the response back.
        vertx.setPeriodic(MotadataConfigUtil.getCachePollTimerSeconds(), timer ->
        {
            if (flag)
            {
                if (PortUtil.isConnected(MotadataConfigUtil.getRemoteEventObserverSender(), MotadataConfigUtil.getMotadataObserverEventSenderPort(), 3))
                {
                    vertx.eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, UPDATE_CACHE.name()));
                }
                else
                {
                    LOGGER.warn(String.format("observer is down , address : %s ", MotadataConfigUtil.getRemoteEventObserverSender() + ":" + MotadataConfigUtil.getMotadataObserverEventSenderPort()));
                }
            }
        });

        vertx.setPeriodic(MotadataConfigUtil.getConfigDBPollTimerSeconds(), timer ->
        {
            if (flag)
            {
                if (PortUtil.isConnected(MotadataConfigUtil.getRemoteEventObserverSender(), MotadataConfigUtil.getMotadataObserverEventSenderPort(), 3))
                {
                    vertx.eventBus().publish(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + Bootstrap.getRegistrationId() + ".active", Bootstrap.getRegistrationId());
                }
                else
                {
                    LOGGER.warn(String.format("Observer is down , address : %s ", MotadataConfigUtil.getRemoteEventObserverSender() + ":" + MotadataConfigUtil.getMotadataObserverEventSenderPort()));
                }
            }
        });
    }

    private void publish(JsonObject event)
    {
        vertx.eventBus().send(EVENT_HA_OBSERVER, event.put(HAConstants.HA_SYNC_OPERATION, HAConstants.HASyncOperation.SAVE.getName()).put(EVENT_TOPIC, EVENT_HA_OBSERVER).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).put(EVENT_TYPE, EVENT_HA_CONFIG_OBSERVER_SYNC));

        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("event sent to motadata observer %s ", uuid));

        }

        if (MotadataConfigUtil.devMode())
        {
            vertx.eventBus().send("test.config.datastore.manager", event);
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        try
        {
            if (eventEngine != null)
            {
                eventEngine.stop(vertx, promise);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}

