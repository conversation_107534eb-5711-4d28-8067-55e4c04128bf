/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  17-APR-2025     Chopra Deven        MOTADATA-5827 : added isInstanceTagExist method to check existence of (object.id + instance.type)
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_ID;
import static com.mindarray.api.AIOpsObject.OBJECT_TAGS;

public class TagCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(TagCacheStore.class, GlobalConstants.MOTADATA_STORE, "Tag Cache Store");

    private static final TagCacheStore STORE = new TagCacheStore();

    private final Map<String, JsonArray> tagsByInstanceType = new ConcurrentHashMap<>(); //key ->  objectId + instanceType + tag , value-> [objectId ### instance.name]

    private final Map<String, JsonArray> tagsByTagKey = new ConcurrentHashMap<>(); //key-> objectId +instanceType+ tag, value -> [tags]

    private final Map<String, JsonObject> instanceTags = new ConcurrentHashMap<>(); // key ->  objectId + instanceType, value-> { tag : [objectId ### instance.name] }

    private final Map<String, JsonArray> tagsByObject = new ConcurrentHashMap<>(); //key-> objectId + tag, value -> [tags]

    private final Map<String, JsonArray> tagsByInstance = new ConcurrentHashMap<>(); //key-> objectId + +instanceType+ tag+ instance, value -> [tags]

    private final Map<String, JsonArray> tagsByInstanceName = new ConcurrentHashMap<>(); // key -> objectId + instanceType + instance.name -> [tag.ids]

    private TagCacheStore()
    {
    }

    public static TagCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (GlobalConstants.BootstrapType.COLLECTOR == Bootstrap.bootstrapType())
        {
            promise.fail("remote event processor can not init store... it must be master or default boot sequence...");
        }

        else
        {
            try
            {
                var items = MetricConfigStore.getStore().getItems();

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    var context = item.getJsonObject(Metric.METRIC_CONTEXT);

                    if (context.containsKey(NMSConstants.OBJECTS))
                    {
                        var objectId = ObjectConfigStore.getStore().getObjectId(MetricConfigStore.getStore().getMetricObjectId(item.getLong(ID)));

                        var objects = context.getJsonArray(NMSConstants.OBJECTS);

                        if (objects != null && !objects.isEmpty())
                        {
                            var instanceType = NMSConstants.INSTANCE_TYPES.getOrDefault(item.getString(Metric.METRIC_PLUGIN), objects.getJsonObject(0).getString(AIOpsObject.OBJECT_TYPE));

                            var instanceKey = objectId + KEY_SEPARATOR + instanceType; //key: object.id + instance.type

                            var instancesByTag = new JsonObject();

                            for (var i = 0; i < objects.size(); i++)
                            {
                                var object = objects.getJsonObject(i);

                                if (object.containsKey(INSTANCE_TAGS))
                                {
                                    var instanceName = item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()) ? object.getString(NMSConstants.INTERFACE) : object.getString(AIOpsObject.OBJECT_NAME);

                                    TagConfigStore.getStore().getItems(object.getJsonArray(INSTANCE_TAGS)).forEach(tag ->
                                    {

                                        if (CommonUtil.getString(tag).contains(COLON_SEPARATOR))
                                        {
                                            var tokens = CommonUtil.getString(tag).split(COLON_SEPARATOR);

                                            if (tokens.length > 1 && !tokens[0].trim().isEmpty() && !tokens[1].trim().isEmpty())//negative case can happen in which user or someone adds tag as key: or key:EMPTY_SPACES or EMPTY_SPACES:value so will not be considering key value tag
                                            {
                                                var key = objectId + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tokens[0];

                                                tagsByTagKey.computeIfAbsent(key, value -> new JsonArray());

                                                if (!tagsByTagKey.get(key).contains(CommonUtil.getString(tag)))
                                                {
                                                    tagsByTagKey.get(key).add(CommonUtil.getString(tag));
                                                }

                                                key = objectId + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tokens[0] + KEY_SEPARATOR + instanceName;

                                                tagsByInstance.computeIfAbsent(key, value -> new JsonArray());

                                                if (!tagsByInstance.get(key).contains(CommonUtil.getString(tag)))
                                                {
                                                    tagsByInstance.get(key).add(tokens[1]);
                                                }
                                            }
                                        }

                                        var key = objectId + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + (instanceType.equalsIgnoreCase(NMSConstants.INTERFACE) ? instanceName.split(DASH_SEPARATOR)[1] : instanceName);

                                        var tags = tagsByInstanceName.computeIfAbsent(key, value -> new JsonArray());

                                        var tagId = TagConfigStore.getStore().getTag(CommonUtil.getString(tag));

                                        if (!tags.contains(tagId))
                                        {
                                            tags.add(tagId);
                                        }

                                        key = instanceKey + KEY_SEPARATOR + CommonUtil.getString(tag); //key: objectId # instanceType # tag

                                        this.tagsByInstanceType.computeIfAbsent(key, value -> new JsonArray());

                                        this.tagsByInstanceType.get(key).add(objectId + GROUP_SEPARATOR + instanceName);

                                        instancesByTag.put(CommonUtil.getString(tag), instancesByTag.getJsonArray(CommonUtil.getString(tag), new JsonArray()).add(objectId + GROUP_SEPARATOR + instanceName));
                                    });
                                }
                            }

                            if (!instancesByTag.isEmpty())
                            {
                                this.instanceTags.put(instanceKey, instancesByTag);
                            }
                        }
                    }
                }

                LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

                promise.complete();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        return promise.future();
    }

    /*
    Object Config Store ->  whenever tag is updated or created, it will init this method
     */
    public void init(JsonObject item)
    {
        try
        {
            //Removed from the map, so that when new entry will come it will override the Tags and setup new, would be helpful in case if user has removed the tags.
            tagsByObject.entrySet().removeIf(entry -> entry.getKey().split(KEY_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(item.getLong(OBJECT_ID))));

            if ((item.containsKey(AIOpsObject.OBJECT_TAGS) && item.getJsonArray(OBJECT_TAGS) != null))
            {
                for (var tag : TagConfigStore.getStore().getItems(item.getJsonArray(AIOpsObject.OBJECT_TAGS)))
                {
                    updateObjectByTags(item, tag);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    public void deleteTagsByMonitor(long objectId)
    {
        tagsByObject.entrySet().removeIf(entry -> entry.getKey().split(KEY_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(objectId)));
    }

    public void deleteTags(long objectId)
    {
        tagsByTagKey.entrySet().removeIf(entry -> entry.getKey().split(KEY_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(objectId)));

        tagsByInstanceType.entrySet().removeIf(entry -> entry.getKey().split(KEY_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(objectId)));

        instanceTags.entrySet().removeIf(entry -> entry.getKey().split(KEY_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(objectId)));

        tagsByInstance.entrySet().removeIf(entry -> entry.getKey().split(KEY_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(objectId)));

        tagsByInstanceName.entrySet().removeIf(entry -> entry.getKey().split(KEY_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(objectId)));
    }

    /*
     * method will update cache: instanceByTag, instanceByType and instanceByKeyGroup
     *
     * instanceByTag : key ->  objectId + instanceType + tag
     *                 value-> [objectId ### instance.name]
     *
     * instanceByType : key ->  objectId + instanceType
     *                  value-> { tag : [objectId ### instance.name] }
     *
     * instanceByGroup : key -> objectId + KeyTag
     *                   value -> [Tags]
     * ex. instanceType(esxi.vm, interface, access.point)
     * */
    public void updateInstanceTags(long id, JsonArray objects, String instanceType)
    {
        try
        {
            var objectId = ObjectConfigStore.getStore().getObjectId(MetricConfigStore.getStore().getMetricObjectId(id));

            var instancesByTag = new JsonObject();

            var instancesByInstanceKey = new HashMap<String, JsonArray>();

            var tagsByKey = new HashMap<String, JsonArray>();

            var instanceKey = objectId + KEY_SEPARATOR + instanceType;

            this.tagsByInstance.entrySet().removeIf(entry -> entry.getKey().startsWith(CommonUtil.getString(objectId) + KEY_SEPARATOR + instanceType));

            for (var index = 0; index < objects.size(); index++)
            {
                var object = objects.getJsonObject(index);

                if (object.containsKey(INSTANCE_TAGS))
                {
                    var instanceName = instanceType.equalsIgnoreCase(NMSConstants.INTERFACE) ? object.getString(NMSConstants.INTERFACE) : object.getString(AIOpsObject.OBJECT_NAME);

                    TagConfigStore.getStore().getItems(object.getJsonArray(INSTANCE_TAGS)).forEach(tag ->
                    {
                        if (CommonUtil.getString(tag).contains(COLON_SEPARATOR))
                        {
                            var tokens = CommonUtil.getString(tag).split(COLON_SEPARATOR);

                            if (tokens.length > 1 && !tokens[0].trim().isEmpty() && !tokens[1].trim().isEmpty())//negative case can happen in which user or someone adds tag as key: or key:EMPTY_SPACES or EMPTY_SPACES:value so will not be considering key value tag
                            {
                                var tagKey = objectId + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tokens[0];

                                tagsByKey.computeIfAbsent(tagKey, value -> new JsonArray());

                                if (!tagsByKey.get(tagKey).contains(CommonUtil.getString(tag)))
                                {
                                    tagsByKey.get(tagKey).add(CommonUtil.getString(tag));
                                }

                                tagKey = objectId + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tokens[0] + KEY_SEPARATOR + instanceName;

                                tagsByInstance.computeIfAbsent(tagKey, value -> new JsonArray());

                                tagsByInstance.get(tagKey).add(tokens[1]);
                            }
                        }

                        var key = objectId + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + (instanceType.equalsIgnoreCase(NMSConstants.INTERFACE) ? instanceName.split(DASH_SEPARATOR)[1] : instanceName);

                        var tags = tagsByInstanceName.computeIfAbsent(key, value -> new JsonArray());

                        var tagId = TagConfigStore.getStore().getTag(CommonUtil.getString(tag));

                        if (!tags.contains(tagId))
                        {
                            tags.add(tagId);
                        }

                        key = instanceKey + KEY_SEPARATOR + CommonUtil.getString(tag); //key: objectId # instanceType # tag

                        instancesByInstanceKey.computeIfAbsent(key, value -> new JsonArray());

                        instancesByInstanceKey.get(key).add(objectId + GROUP_SEPARATOR + instanceName);

                        instancesByTag.put(CommonUtil.getString(tag), instancesByTag.getJsonArray(CommonUtil.getString(tag), new JsonArray()).add(objectId + GROUP_SEPARATOR + instanceName));
                    });
                }
            }

            if (!instancesByTag.isEmpty())
            {
                this.instanceTags.put(instanceKey, instancesByTag);

                this.tagsByInstanceType.entrySet().removeIf(entry -> entry.getKey().startsWith(objectId + KEY_SEPARATOR + instanceType));

                this.tagsByInstanceType.putAll(instancesByInstanceKey);

                this.tagsByTagKey.entrySet().removeIf(entry -> entry.getKey().startsWith(CommonUtil.getString(objectId) + KEY_SEPARATOR + instanceType));

                this.tagsByTagKey.putAll(tagsByKey);
            }
           /* else
            {
                //when all tags are removed from all instances, we have to remove all the tags
                this.instanceTags.remove(instanceKey);

                this.tagsByInstanceType.entrySet().removeIf(entry -> entry.getKey().startsWith(objectId + KEY_SEPARATOR + instanceType));

                this.tagsByTagKey.entrySet().removeIf(entry -> entry.getKey().startsWith(CommonUtil.getString(objectId) + KEY_SEPARATOR + instanceType));

                this.tagsByInstanceName.entrySet().removeIf(entry -> entry.getKey().startsWith(CommonUtil.getString(objectId) + KEY_SEPARATOR + instanceType));
            }*/

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public JsonArray getObjectTagsByTagKey(int id, String tagKey)
    {
        return tagsByObject.getOrDefault((CommonUtil.getString(id) + KEY_SEPARATOR + tagKey), null);
    }

    public Boolean existTag(int id, String instanceType)
    {
        return instanceTags.containsKey(id + KEY_SEPARATOR + instanceType);
    }

    public Map<String, JsonArray> getInstanceTags(JsonArray ids, String instanceType)
    {
        var items = new HashMap<String, JsonArray>();

        for (var id : ids)
        {
            if (instanceTags.containsKey(id + KEY_SEPARATOR + instanceType))
            {
                for (var entry : instanceTags.get(id + KEY_SEPARATOR + instanceType).getMap().entrySet())
                {
                    var instanceKey = CommonUtil.getString(entry.getKey());

                    if (items.containsKey(instanceKey))
                    {
                        for (var instanceValue : (JsonArray) entry.getValue())
                        {
                            items.get(instanceKey).add(instanceValue);
                        }
                    }
                    else
                    {
                        var tags = new JsonArray();

                        for (var instanceValue : (JsonArray) entry.getValue())
                        {
                            tags.add(instanceValue);
                        }

                        items.put(instanceKey, tags);
                    }
                }
            }
        }

        return items;
    }

    public Map<String, JsonArray> getInstanceTags(JsonArray ids, JsonArray instanceTypes)
    {
        var items = new HashMap<String, JsonArray>();

        for (var id : ids)
        {
            for (var instanceType : instanceTypes)
            {
                if (instanceTags.containsKey(id + KEY_SEPARATOR + instanceType))
                {
                    for (var entry : instanceTags.get(id + KEY_SEPARATOR + instanceType).getMap().entrySet())
                    {
                        var instanceKey = CommonUtil.getString(entry.getKey());

                        if (items.containsKey(instanceKey))
                        {
                            for (var instanceValue : (JsonArray) entry.getValue())
                            {
                                items.get(instanceKey).add(instanceValue);
                            }
                        }
                        else
                        {
                            var tags = new JsonArray();

                            for (var instanceValue : (JsonArray) entry.getValue())
                            {
                                tags.add(instanceValue);
                            }

                            items.put(instanceKey, tags);
                        }
                    }
                }
            }
        }

        return items;
    }

    public JsonArray getInstanceIdsByTags(JsonArray tags)
    {
        return new JsonArray(tagsByInstanceType.keySet().stream()
                .filter(key -> tags.contains(key.split(KEY_SEPARATOR)[2]))
                .map(key -> key.split(KEY_SEPARATOR)[0])
                .distinct().collect(Collectors.toList()));
    }

    public JsonArray getInstanceIdsByTags(String instance, JsonArray tags)
    {
        return new JsonArray(tagsByInstanceType.keySet().stream()
                .filter(key -> tags.contains(key.split(KEY_SEPARATOR)[2]))
                .filter(key -> instance.equalsIgnoreCase(key.split(KEY_SEPARATOR)[1]))
                .map(key -> key.split(KEY_SEPARATOR)[0])
                .distinct().collect(Collectors.toList()));
    }

    public JsonObject getInstanceTagEntities(Set<Integer> items, JsonArray tags, String instance)
    {
        var entities = new JsonObject();

        entities.put(ID, new JsonArray()).put(INSTANCE, new JsonArray()).put(TAGS, new JsonArray());

        for (var item : items)
        {
            for (var tag : tags)
            {
                if (tagsByInstanceType.containsKey(item + KEY_SEPARATOR + instance + KEY_SEPARATOR + tag))
                {
                    for (var entry : tagsByInstanceType.get(item + KEY_SEPARATOR + instance + KEY_SEPARATOR + tag))
                    {
                        var tokens = CommonUtil.getString(entry).split(GROUP_SEPARATOR);

                        entities.getJsonArray(ID).add(tokens[0]);

                        entities.getJsonArray(INSTANCE).add(tokens[1]);

                        entities.getJsonArray(TAGS).add(CommonUtil.getString(tag));
                    }
                }

            }
        }


        return entities;
    }

    public JsonArray getInstanceTags()
    {
        var items = new JsonArray();

        for (var instance : instanceTags.entrySet())
        {
            instance.getValue().getMap().forEach((key, value) -> items.add(key));
        }

        return items;
    }

    public Map<String, JsonArray> getTagsByGroupInstanceType(JsonArray ids, String instanceType, String tagKey)
    {
        var items = new HashMap<String, JsonArray>();

        for (var id : ids)
        {
            if (tagsByTagKey.containsKey(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tagKey))
            {
                for (var entry : tagsByTagKey.get(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tagKey))
                {
                    var instance = CommonUtil.getString(entry);

                    if (tagsByInstanceType.containsKey(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + instance))
                    {
                        if (items.containsKey(instance))
                        {
                            tagsByInstanceType.get(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + entry).forEach(items.get(CommonUtil.getString(entry))::add);
                        }
                        else
                        {
                            var tags = new JsonArray();

                            tagsByInstanceType.get(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + entry).forEach(tags::add);

                            items.put(CommonUtil.getString(entry), tags);
                        }
                    }
                }
            }
        }

        return items;
    }

    public JsonArray getInstanceTagKeys(Integer id, String tagKey, String instance, String instanceType)
    {
        return tagsByInstance.getOrDefault((CommonUtil.getString(id) + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tagKey + KEY_SEPARATOR + instance), null);
    }

    public JsonArray getTagsByTagKey(Integer id, String instanceType, String tagKey)
    {
        return tagsByTagKey.getOrDefault((CommonUtil.getString(id) + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tagKey), null);
    }

    public Map<String, JsonArray> getTagsByGroupInstanceTypes(JsonArray ids, JsonArray instanceTypes, String tagKey)
    {
        var items = new HashMap<String, JsonArray>();

        for (var id : ids)
        {
            for (var instanceType : instanceTypes)
            {
                if (tagsByTagKey.containsKey(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tagKey))
                {
                    for (var entry : tagsByTagKey.get(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + tagKey))
                    {
                        var instance = CommonUtil.getString(entry);

                        if (tagsByInstanceType.containsKey(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + instance))
                        {
                            if (items.containsKey(instance))
                            {
                                tagsByInstanceType.get(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + entry).forEach(items.get(CommonUtil.getString(entry))::add);
                            }
                            else
                            {
                                var tags = new JsonArray();

                                tagsByInstanceType.get(id + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + entry).forEach(tags::add);

                                items.put(CommonUtil.getString(entry), tags);
                            }
                        }
                    }
                }
            }
        }

        return items;
    }

    private void updateObjectByTags(JsonObject items, Object tag)
    {
        if (CommonUtil.getString(tag).contains(COLON_SEPARATOR))
        {
            var tokens = CommonUtil.getString(tag).split(COLON_SEPARATOR);

            if (tokens.length > 1 && !tokens[0].trim().isEmpty() && !tokens[1].trim().isEmpty())//negative case can happen in which user or someone adds tag as key: or key:EMPTY_SPACES or EMPTY_SPACES:value so will not be considering key value tag
            {
                var key = items.getInteger(OBJECT_ID) + KEY_SEPARATOR + tokens[0];

                tagsByObject.computeIfAbsent(key, value -> new JsonArray());

                if (!tagsByObject.get(key).contains(tokens[1]))
                {
                    tagsByObject.get(key).add(tokens[1]);
                }
            }
        }
    }

    public JsonArray getTags(String key)
    {
        return tagsByInstanceName.getOrDefault(key, null);
    }

    // it will return map of specific objectId -> tags (all instance types)
    public JsonArray getInstanceTagsByObjectId(int objectId)
    {
        var tags = new JsonArray();

        for (var entry : instanceTags.entrySet())
        {
            if (entry.getKey().startsWith(CommonUtil.getString(objectId)))
            {
                tags.addAll(TagConfigStore.getStore().getIdsByItems(new JsonArray(entry.getValue().getMap().keySet().stream().toList())));
            }
        }

        return tags;
    }

    // it will return map of all objectId -> tags (all instance types)
    public Map<Integer, JsonArray> getInstanceTagIds()
    {
        var items = new HashMap<Integer, JsonArray>();

        for (var entry : instanceTags.entrySet())
        {
            items.put(CommonUtil.getInteger(entry.getKey().split(KEY_SEPARATOR)[0]), new JsonArray(entry.getValue().getMap().keySet().stream().toList()));
        }

        return items;
    }
}
