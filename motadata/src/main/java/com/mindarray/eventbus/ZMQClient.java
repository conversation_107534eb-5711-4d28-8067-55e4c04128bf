/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Consumer;

import static com.mindarray.GlobalConstants.HOST;
import static com.mindarray.GlobalConstants.PORT;
import static com.mindarray.eventbus.EventBusConstants.SOCKET_MODE;

/**
 * ZeroMQ (ZMQ) client implementation for the Motadata transport layer.
 *
 * <p>This class provides a ZeroMQ-based transport client that supports multiple socket modes
 * including SUB (subscriber), PUSH, and PULL patterns. It implements the {@link TransportClient}
 * interface to provide a standardized transport abstraction layer.</p>
 *
 * <p>The client supports different ZeroMQ socket patterns:</p>
 * <ul>
 *   <li><b>SUB (Subscriber)</b>: Receives messages from PUB sockets, supports topic filtering</li>
 *   <li><b>PUSH</b>: Sends messages to PULL sockets in a load-balanced manner</li>
 *   <li><b>PULL</b>: Receives messages from PUSH sockets in a round-robin fashion</li>
 * </ul>
 *
 * <p>Key features:</p>
 * <ul>
 *   <li>Configurable High Water Mark (HWM) for message queuing</li>
 *   <li>TCP keep-alive support for connection reliability</li>
 *   <li>Topic-based message filtering for SUB sockets</li>
 *   <li>Thread-safe message handling with atomic operations</li>
 *   <li>Integration with Vert.x event bus for message routing</li>
 * </ul>
 *
 * <p>Configuration example:</p>
 * <pre>{@code
 * JsonObject config = new JsonObject()
 *     .put("SOCKET_MODE", "SUB")
 *     .put("HOST", "localhost")
 *     .put("PORT", 5555)
 *     .put("EVENT_TYPE", "remote.events");
 *
 * ZMQClient client = new ZMQClient(config);
 * client.registerMessageHandler(message -> {
 *     // Handle received message
 * });
 * client.connect().onSuccess(v -> {
 *     client.receive(); // Start receiving messages
 * });
 * }</pre>
 *
 * <AUTHOR> Engineering Team
 * @version 1.0
 * @since 1.0
 * @see TransportClient
 * @see ZMQServer
 */
public class ZMQClient implements TransportClient
{
    /**
     * The underlying ZeroMQ socket instance used for communication.
     * This socket is configured based on the socket mode specified in the configuration.
     */
    private ZMQ.Socket client;

    /**
     * Configuration object containing connection parameters such as host, port, socket mode, and event type.
     * This configuration is used to establish the ZMQ connection and determine behavior.
     */
    private final JsonObject config;

    /**
     * Atomic boolean flag indicating whether the client should continue processing events.
     * Used to control the message receiving loop in a thread-safe manner.
     */
    private final AtomicBoolean hasMoreEvent = new AtomicBoolean(false);

    /**
     * Logger instance for this ZMQ client, configured with appropriate context and component name.
     */
    private static final Logger LOGGER = new Logger(ZMQClient.class, GlobalConstants.MOTADATA_EVENT_BUS, "ZMQ Client");

    /**
     * The event type identifier used for routing messages to the appropriate event bus consumers.
     * Extracted from the configuration during initialization.
     */
    private final String eventType;

    /**
     * Message handler consumer that processes received messages.
     * Can be customized via {@link #registerMessageHandler(Consumer)} method.
     */
    private Consumer<byte[]> consumer;

    private Thread thread;

    /**
     * Constructs a new ZMQClient with the specified configuration.
     *
     * <p>The configuration object must contain the following required parameters:</p>
     * <ul>
     *   <li><b>SOCKET_MODE</b>: The ZMQ socket mode (SUB, PUSH, or PULL)</li>
     *   <li><b>HOST</b>: The target host address to connect to</li>
     *   <li><b>PORT</b>: The target port number</li>
     *   <li><b>EVENT_TYPE</b>: The event type for message routing</li>
     * </ul>
     *
     * <p>The constructor initializes the default message handler to send messages
     * to the Vert.x event bus using the specified event type.</p>
     *
     * @param config the configuration object containing connection parameters and settings.
     *               Must not be null and should contain all required parameters.
     * @throws NullPointerException if config is null
     * @throws IllegalArgumentException if required configuration parameters are missing
     */
    public ZMQClient(JsonObject config)
    {
        this.config = config;

        eventType = config.getString(EventBusConstants.EVENT_TYPE);

        consumer = bytes -> Bootstrap.vertx().eventBus().send(eventType, bytes);
    }

    /**
     * Registers a custom message handler for processing received messages.
     *
     * <p>This method allows customization of how received messages are processed.
     * The provided consumer will be invoked for each message received by this client.
     * If no custom handler is registered, messages are sent to the Vert.x event bus
     * using the configured event type.</p>
     *
     * <p>The message handler is called from the receiving thread, so implementations
     * should be thread-safe and avoid blocking operations. For heavy processing,
     * consider delegating to a separate thread or using Vert.x's executeBlocking.</p>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * client.registerMessageHandler(message -> {
     *     // Custom message processing
     *     processMessage(message);
     *     // Forward to event bus if needed
     *     vertx.eventBus().send("custom.topic", message);
     * });
     * }</pre>
     *
     * @param consumer the message handler consumer that will process received messages.
     *                 Must not be null. The consumer receives the raw message bytes.
     * @throws NullPointerException if consumer is null
     * @see #receive()
     */
    @Override
    public void registerMessageHandler(Consumer<byte[]> consumer)
    {
        this.consumer = consumer;
    }

    /**
     * Establishes a connection to the ZeroMQ server based on the configured socket mode.
     *
     * <p>This method creates and configures the appropriate ZMQ socket type based on the
     * SOCKET_MODE specified in the configuration. The supported socket modes are:</p>
     * <ul>
     *   <li><b>SUB</b>: Creates a subscriber socket that connects to a publisher and subscribes to the remote event topic</li>
     *   <li><b>PUSH</b>: Creates a push socket that connects to a pull socket for load-balanced message sending</li>
     *   <li><b>PULL</b>: Creates a pull socket that binds to a port and waits for push connections</li>
     * </ul>
     *
     * <p>The connection process includes:</p>
     * <ul>
     *   <li>Socket creation with appropriate type</li>
     *   <li>High Water Mark (HWM) configuration for message queuing</li>
     *   <li>TCP keep-alive settings for connection reliability</li>
     *   <li>Topic subscription (for SUB sockets)</li>
     *   <li>Connection establishment to the specified host and port</li>
     * </ul>
     *
     * <p>For SUB sockets, the client automatically subscribes to the remote event topic
     * determined by the bootstrap type. For PULL sockets, the client binds to the
     * specified port and listens for incoming connections.</p>
     *
     * @return a Future that completes successfully when the connection is established,
     *         or fails with an exception if the connection cannot be established
     * @throws IllegalArgumentException if the socket mode is not supported
     * @see #receive()
     * @see #send(byte[])
     */
    @Override
    public Future<Void> connect()
    {
        LOGGER.info(String.format("%s Connecting ZMQ Client with mode : %s ",Bootstrap.bootstrapType(),config.getString(SOCKET_MODE)));

        return switch (EventBusConstants.SocketMode.valueOf(config.getString(SOCKET_MODE)))
        {
            case SUB ->
            {
                var promise = Promise.<Void>promise();

                try
                {
                    client = Bootstrap.zcontext().socket(SocketType.SUB);

                    client.setHWM(CommonUtil.getEventQueueSize());

                    client.setSndHWM(CommonUtil.getEventQueueSize());

                    client.subscribe(EventBusConstants.getRemoteEventTopic(Bootstrap.bootstrapType()));

                    client.setReconnectIVL(MotadataConfigUtil.getEventRemoteReconnectIntervalSeconds());

                    client.setReconnectIVLMax(MotadataConfigUtil.getEventRemoteReconnectIntervalMaxSeconds());

                    client.connect("tcp://" + config.getString(HOST) + ":" + config.getString(PORT));

                    hasMoreEvent.set(true);

                    LOGGER.info(String.format("successfully connected with port %s", config.getString(HOST) + ":" + config.getString(PORT)));

                    promise.complete();
                }
                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn(String.format("failed connected with port %s", config.getString(HOST) + ":" + config.getString(PORT)));

                    LOGGER.error(exception);
                }

                yield promise.future();
            }

            case PUSH ->
            {
                var promise = Promise.<Void>promise();

                try
                {
                    client = Bootstrap.zcontext().socket(SocketType.PUSH);

                    client.setHWM(CommonUtil.getEventQueueSize());

                    client.setSndHWM(CommonUtil.getEventQueueSize());

                    client.setSendTimeOut(0);

                    client.setTCPKeepAlive(1);

                    client.setLinger(0);

                    client.setReconnectIVL(MotadataConfigUtil.getEventRemoteReconnectIntervalSeconds());

                    client.setReconnectIVLMax(MotadataConfigUtil.getEventRemoteReconnectIntervalMaxSeconds());

                    client.connect("tcp://" + config.getString(HOST) + ":" + config.getString(PORT));

                    hasMoreEvent.set(true);

                    LockSupport.parkNanos(Duration.ofMillis(100).toNanos()); // Eliminate slow subscriber problem

                    LOGGER.info(String.format("successfully connected with port %s", config.getString(HOST) + ":" + config.getString(PORT)));

                    promise.complete();
                }
                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn(String.format("failed connected with port %s", config.getString(HOST) + ":" + config.getString(PORT)));

                    LOGGER.error(exception);
                }

                yield promise.future();
            }

            case PULL ->
            {
                var promise = Promise.<Void>promise();

                try
                {
                    client = Bootstrap.zcontext().socket(SocketType.PULL);

                    client.setHWM(MotadataConfigUtil.getEventBacklogSize());

                    client.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

                    client.setReconnectIVL(MotadataConfigUtil.getEventRemoteReconnectIntervalSeconds());

                    client.setReconnectIVLMax(MotadataConfigUtil.getEventRemoteReconnectIntervalMaxSeconds());

                    client.connect("tcp://" + config.getString(HOST) + ":" + config.getString(PORT));

                    hasMoreEvent.set(true);

                    LOGGER.info(String.format("successfully connected with port %s", config.getString(HOST) + ":" + config.getString(PORT)));

                    promise.complete();
                }
                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn(String.format("failed connected with port %s", config.getString(HOST) + ":" + config.getString(PORT)));

                    LOGGER.error(exception);
                }

                yield promise.future();
            }

            default -> Future.failedFuture("Unsupported ZMQ client mode");
        };
    }

    /**
     * Sends a message through the ZeroMQ socket.
     *
     * <p>This method sends the provided byte array as a message through the established
     * ZMQ connection. The behavior depends on the socket mode:</p>
     * <ul>
     *   <li><b>PUSH</b>: Sends the message to connected PULL sockets in a load-balanced manner</li>
     *   <li><b>SUB</b>: Not typically used for sending (subscribers usually only receive)</li>
     *   <li><b>PULL</b>: Not typically used for sending (pull sockets usually only receive)</li>
     * </ul>
     *
     * <p>The method is synchronous and will block until the message is queued for sending.
     * If the High Water Mark (HWM) is reached, the behavior depends on the socket configuration.</p>
     *
     * <p><b>Note:</b> This method should only be called after a successful connection has been
     * established via {@link #connect()}. Calling this method before connecting may result
     * in undefined behavior or exceptions.</p>
     *
     * @param bytes the message data to send as a byte array. Must not be null.
     * @throws IllegalStateException if the client is not connected
     * @throws org.zeromq.ZMQException if there's an error sending the message
     * @throws NullPointerException if bytes is null
     * @see #connect()
     */
    @Override
    public void send(byte[] bytes)
    {
        client.send(bytes);
    }

    /**
     * Starts receiving messages from the ZeroMQ socket in a separate thread.
     *
     * <p>This method creates a new thread that continuously listens for incoming messages
     * on the ZMQ socket. The receiving behavior depends on the socket mode:</p>
     * <ul>
     *   <li><b>SUB</b>: Receives messages from publishers that match the subscribed topic</li>
     *   <li><b>PULL</b>: Receives messages from push sockets in a round-robin fashion</li>
     *   <li><b>PUSH</b>: Not typically used for receiving (push sockets usually only send)</li>
     * </ul>
     *
     * <p>The receiving process:</p>
     * <ol>
     *   <li>Creates a dedicated thread for message reception</li>
     *   <li>Continuously polls the socket for new messages</li>
     *   <li>For SUB sockets, receives both topic and message parts</li>
     *   <li>Processes received messages through the registered message handler</li>
     *   <li>Continues until {@link #close()} is called</li>
     * </ol>
     *
     * <p>For SUB sockets, messages are received as two parts: the topic and the actual message.
     * The method combines these parts into a single buffer with the topic length prefix.</p>
     *
     * <p><b>Thread Safety:</b> This method is thread-safe and can be called multiple times,
     * but only one receiving thread will be active per client instance.</p>
     *
     * <p><b>Error Handling:</b> If an error occurs during message reception, it is logged
     * and the receiving loop continues. The thread will only stop when the client is closed.</p>
     *
     * @return a Future that completes immediately after starting the receiving thread.
     *         The Future completion does not indicate message reception status.
     * @throws IllegalStateException if the client is not connected
     * @see #registerMessageHandler(Consumer)
     * @see #connect()
     * @see #close()
     */
    @Override
    public Future<Void> receive()
    {
        if (EventBusConstants.SocketMode.valueOf(config.getString(SOCKET_MODE)) != EventBusConstants.SocketMode.PUSH)
        {
            thread = new Thread(() ->
            {
                byte[] message;

                byte[] topic;

                while (hasMoreEvent.get())
                {
                    try
                    {
                        topic = client.recv();

                        message = client.recv();

                        if (message.length > 0)
                        {
                            consumer.accept(Buffer.buffer().appendShortLE(CommonUtil.getShort(topic.length)).appendBytes(topic).appendBytes(message).getBytes());

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format(" %s received %s bytes",Bootstrap.bootstrapType(), message.length));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.warn(String.format("error while sending event to : %s , thread : %s  ",eventType,Thread.currentThread().getName()));

                        LOGGER.error(exception);
                    }
                }

                LOGGER.warn("client disconnected....");

            }, String.format("ZMQ Client port : %s event.type : %s ",config.getInteger(PORT),eventType));

            thread.start();
        }

        return Future.succeededFuture();
    }

    /**
     * Closes the ZeroMQ server and releases all associated resources.
     *
     * <p>This method performs a graceful shutdown of the ZMQ client by:</p>
     * <ol>
     *   <li>Logging the client closure with host and port information</li>
     *   <li>Setting the hasMoreEvent flag to false to stop the receiving loop</li>
     *   <li>Closing the underlying ZMQ socket</li>
     *   <li>Releasing any system resources associated with the client</li>
     * </ol>
     *
     * <p>After calling this method:</p>
     * <ul>
     *   <li>The receiving thread will terminate gracefully</li>
     *   <li>No more messages can be sent or received</li>
     *   <li>The client socket will be unbound from its port</li>
     *   <li>The client cannot be reused and a new instance must be created for restart</li>
     * </ul>
     *
     * <p><b>Thread Safety:</b> This method is thread-safe and can be called from any thread.
     * Multiple calls to close() are safe and will have no additional effect.</p>
     *
     * <p><b>Graceful Shutdown:</b> The method ensures that any ongoing receive operations
     * complete before the socket is closed, preventing data loss or corruption.</p>
     *
     * <p><b>Best Practice:</b> Always call this method when the client is no longer needed
     * to prevent resource leaks and ensure proper cleanup. This is especially important
     * in long-running applications.</p>
     *
     * @see #connect() ()
     * @see #receive()
     */
    @Override
    public Future<Void> close()
    {
        try
        {
            LOGGER.info(String.format(" %s client port : %s event.type : %s is closed ...",Bootstrap.bootstrapType(),config.getInteger(PORT),eventType));

            hasMoreEvent.set(false);

            client.close();

            if (thread != null)
            {
                thread.join();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture();
    }
}
