/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  26-May-2025		<PERSON>itas Shah		MOTADATA-6242: The Report executable (.exe) is not functioning in the Windows-based Motadata AIOps environment.
 *  10-Jun-2025     Bharat Chaudhari    MOTADATA-5883: Revamp Reporting Engine to Backend with Support for Go
 */
package com.mindarray.report;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.ReportCacheStore;
import com.mindarray.store.ReportConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.netty.handler.codec.http.HttpScheme;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.text.StringEscapeUtils;
import com.mindarray.store.RebrandingConfigStore;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.REPORT_NOTIFICATION_SUBJECT;
import static com.mindarray.api.APIConstants.AUTH_ACCESS_TOKEN;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.APIConstants.CLIENT_ID;
import static com.mindarray.api.APIConstants.UI_CLIENT_ID;
import static com.mindarray.api.Report.REPORT_NAME;
import static com.mindarray.api.Report.REPORT_TYPE;
import static com.mindarray.api.Scheduler.SCHEDULER_EMAIL_RECIPIENTS;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.api.User.USER_PREFERENCES;
import static com.mindarray.api.User.USER_PREFERENCE_DATE_TIME_FORMAT;
import static com.mindarray.api.User.USER_PREFERENCE_TIME_ZONE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.report.ReportConstants.*;

public class ReportingEngine extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ReportingEngine.class, MOTADATA_REPORTING, "Reporting Engine");

    private static final int TIMEOUT_SECONDS = MotadataConfigUtil.getReportEngineTimeoutSeconds();

    private static final String BROWSER_PATH = MotadataConfigUtil.getBrowserBinPath();

    private static final int SOCKET_TIMEOUT_MILLIS = MotadataConfigUtil.getReportSocketTimeoutSeconds() * 1000;

    private static final SimpleDateFormat LOG_DATE_FORMAT = new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT);

    private final Map<Long, Long> tickers = new ConcurrentHashMap<>();

    @Override
    public void start(Promise<Void> promise) throws Exception {
        vertx.eventBus().localConsumer(config().getString(EVENT_TYPE), this::process).exceptionHandler(LOGGER::error);

        vertx.eventBus().localConsumer(EVENT_REPORT_EXPORT, this::process).exceptionHandler(LOGGER::error);

        LOGGER.debug(config().getString(EventBusConstants.EVENT_TYPE) + " started successfully!!!");

        // clean up job
        vertx.setPeriodic(MotadataConfigUtil.devMode() ? 5000 : 30000, timer ->
        {
            try
            {
                var iterator = tickers.entrySet().iterator();

                while (iterator.hasNext())
                {
                    var item = iterator.next();

                    if (item.getValue() + (MotadataConfigUtil.devMode() ? 60 : 600) <= DateTimeUtil.currentSeconds()) // 10 minutes
                    {
                        iterator.remove();

                        ReportCacheStore.getStore().removeItem(item.getKey());

                        LOGGER.warn(String.format("%s is cleaned up due to timeout", item.getKey()));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        promise.complete();
    }

    /**
     * MOTADATA-1454, date format is shown as per default admin user and not by format selected by specific user.
     * If report request is received from UI, fetch the user details from id and passed for headless authentication
     * -- Changed method to update report progress on UI. Before, It is not showing 40%, 50% and directly getting exporting..
     *
     * @param message Message
     */
    private void process(Message<JsonObject> message)
    {
        try
        {
            var event = message.body();

            var item = ReportConfigStore.getStore().getItem(event.getLong(ID));

            if (item != null)
            {
                try
                {
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("%s> %s report generation request received for event %s", item.getLong(ID), item.getString(REPORT_NAME),event));
                    }

                    ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject().put(PROGRESS, 10).put(MESSAGE, "Request queued"));

                    tickers.put(item.getLong(ID), DateTimeUtil.currentSeconds());

                    var userId = item.getLong(Report.REPORT_USER);

                    var user = UserConfigStore.getStore().getItemByValue(USER_NAME, event.getString(USER_NAME,UserConfigStore.getStore().getItem(userId) != null ? UserConfigStore.getStore().getItem(userId).getString(USER_NAME) : DEFAULT_USER));

                    var rebrandingItem = RebrandingConfigStore.getStore().getItem();

                    if (EVENT_REPORT_EXPORT.equalsIgnoreCase(event.getString(EVENT_TYPE)))
                    {
                        userId = user.getLong(ID);
                    }

                    event.put(USER_ID, userId);

                    LOGGER.debug(String.format("%s>Exporting report for : %s", item.getLong(ID), userId));

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_AUTH_TOKEN_CREATE, new JsonObject().put(ID, userId))
                            .onComplete(reply ->
                            {
                                if (reply.succeeded())
                                {
                                    String fileName;

                                    if (event.containsKey(Report.REPORT_FORMAT))
                                    {
                                        item.put(Report.REPORT_FORMAT, event.getString(Report.REPORT_FORMAT));
                                    }

                                    if (item.containsKey(Report.REPORT_FORMAT) && !item.getString(Report.REPORT_FORMAT).equalsIgnoreCase("pdf"))
                                    {
                                        fileName = item.getString(REPORT_NAME).replaceAll("-", "").replaceAll(" +", "_") + "_" + DateTimeUtil.currentSeconds() + ".xlsx";
                                    }
                                    else
                                    {
                                        fileName = item.getString(REPORT_NAME).replaceAll("-", "").replaceAll(" +", "_") + "_" + DateTimeUtil.currentSeconds() + ".pdf";
                                    }

                                    event.put(EVENT_ID, CommonUtil.newEventId());

                                    vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID))
                                            .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                                            .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_ADD)
                                            .put(EventBusConstants.EVENT_TYPE, EVENT_REPORT));

                                    vertx.<Integer>executeBlocking(future ->
                                    {
                                        var result = reply.result().body();

                                        try
                                        {
                                            ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject().put(PROGRESS, 20).put(MESSAGE, "Auth token generated"));

                                            FileUtils.deleteQuietly(new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + REPORTS + PATH_SEPARATOR + fileName));    // delete file if exists...

                                            //check for the report type that is supported by go engine and exclude Performance Type of report by special condition...
                                            if (GO_ENGINE_REPORTS.contains(item.getString(REPORT_TYPE, EMPTY_VALUE))
                                                    && !(item.getString(REPORT_TYPE, EMPTY_VALUE).equalsIgnoreCase("metric") && (item.getJsonArray(Report.REPORT_WIDGETS).getJsonObject(0).getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.HISTOGRAM.getName()) || item.getJsonArray(Report.REPORT_WIDGETS).getJsonObject(0).getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.TOP_N.getName()))))
                                            {
                                                var arguments = new ArrayList<String>();

                                                var exitCode = DUMMY_NUMERIC_VALUE;

                                                arguments.add(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GO_PLUGIN_ENGINE_DIR + PATH_SEPARATOR + GO_REPORT_ENGINE_BIN);

                                                arguments.add(event.getString(EVENT_ID, EMPTY_VALUE));

                                                arguments.add(fileName);

                                                var qualifiedContext = enrich(rebrandingItem, user, event, item, fileName);

                                                FileUtils.writeStringToFile(new File(CURRENT_DIR + PATH_SEPARATOR + REPORT_CONTEXT_DIR + PATH_SEPARATOR + event.getLong(EVENT_ID)), Base64.getEncoder().encodeToString(qualifiedContext.encode().getBytes()), StandardCharsets.UTF_8);

                                                var processBuilder = new ProcessBuilder(arguments).redirectErrorStream(true);

                                                ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject().put(PROGRESS, 30).put(MESSAGE, "Exporting report"));

                                                if (CommonUtil.debugEnabled())
                                                {
                                                    LOGGER.debug(String.format("%s> starting report-engine", item.getLong(ID)));
                                                }

                                                var process = processBuilder.start();

                                                WorkerUtil.setupCleanupTimer(TIMEOUT_SECONDS, process, processBuilder, event.getLong(EVENT_ID), null, null);

                                                var output = new StringBuilder(1000);

                                                try (var reader = new BufferedReader(new InputStreamReader(process.getInputStream())))
                                                {
                                                    String line;

                                                    while ((line = reader.readLine()) != null)
                                                    {
                                                        output.append(line);

                                                        try
                                                        {
                                                            if (!output.isEmpty())
                                                            {
                                                                var response = output.toString();

                                                                if (response.contains(VALUE_SEPARATOR))
                                                                {
                                                                    var values = response.split(VALUE_SEPARATOR);

                                                                    var context = new JsonObject(new String(Base64.getDecoder().decode(values[0])));

                                                                    if (context.containsKey(REQUEST_TYPE))
                                                                    {
                                                                        //request type is sent by report engine
                                                                        // auth token type -  requesting for the auth token
                                                                        // process update - requesting to update the process on ui
                                                                        // will add custom script support here as well
                                                                        if (context.getString(REQUEST_TYPE, EMPTY_VALUE).equalsIgnoreCase("auth.token"))
                                                                        {
                                                                            var token = Base64.getEncoder().encodeToString(new JsonObject().put(RESULT, result).encode().getBytes(StandardCharsets.UTF_8)) + VALUE_SEPARATOR;

                                                                            process.getOutputStream().write(token.getBytes(StandardCharsets.UTF_8));

                                                                            process.getOutputStream().flush();
                                                                        }
                                                                        if (context.getString(REQUEST_TYPE, EMPTY_VALUE).equalsIgnoreCase("update.progress"))
                                                                        {
                                                                            // update progress on UI
                                                                            var progress = new JsonObject();

                                                                            ReportCacheStore.getStore().updateItem(context.getLong(REPORT_ID), progress.put(PROGRESS, context.getInteger(PROGRESS, 0)).put(MESSAGE, context.getString(MESSAGE, EMPTY_VALUE)));
                                                                        }

                                                                    }

                                                                    output.setLength(0);
                                                                }

                                                            }

                                                        }
                                                        catch (Exception exception)
                                                        {
                                                            LOGGER.error(new Exception(exception.getMessage()));

                                                            exitCode = NOT_AVAILABLE;
                                                        }
                                                    }
                                                }

                                                if (!process.isAlive())
                                                {
                                                    exitCode = process.exitValue();
                                                }

                                                if (CommonUtil.debugEnabled())
                                                {
                                                    LOGGER.debug(String.format("%s> report engine with PID %s exited with status code %s", item.getLong(ID), process.pid(), exitCode));
                                                }

                                                future.complete(exitCode);
                                            }
                                            else
                                            {
                                                var arguments = new ArrayList<String>();

                                                var exitCode = DUMMY_NUMERIC_VALUE;

                                                arguments.add(CURRENT_DIR + PATH_SEPARATOR + REPORT_ENGINE_BIN);

                                                // TODO: Once the report engine is refactored this will be removed.
                                                // Currently on Windows the JSON string isn’t parsed directly, so it must be escaped.
                                                if (OS_WINDOWS)
                                                {
                                                    arguments.add("--credentials=\"" + StringEscapeUtils.escapeJava(new JsonObject().put("auth.token", new JsonObject()
                                                            .put("access_token", result.getString(AUTH_ACCESS_TOKEN))
                                                            .put("session_id", result.getString(SESSION_ID))).encode()) + "\"");
                                                }
                                                else
                                                {
                                                    arguments.add("--credentials=" + new JsonObject().put("auth.token", new JsonObject()
                                                            .put("access_token", result.getString(AUTH_ACCESS_TOKEN))
                                                            .put("session_id", result.getString(SESSION_ID))).encode());
                                                }

                                                arguments.add("--url=https://" + MotadataConfigUtil.getHTTPServerHost() + ":" + MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()) + "/reports/export/" + event.getLong(ID));

                                                arguments.add("--filename=" + fileName);

                                                if (item.containsKey(Report.REPORT_FORMAT) && !item.getString(Report.REPORT_FORMAT).equalsIgnoreCase("pdf"))
                                                {
                                                    arguments.add("--xlsx");      // for xlsx format & file name should end with xlsx
                                                }

                                                arguments.add("--output=" + CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + REPORTS);

                                                arguments.add("--browser=" + BROWSER_PATH);

                                                arguments.add("--http-port=" + PortUtil.getAvailablePort());

                                                arguments.add("--socket-connection-timeout=" + SOCKET_TIMEOUT_MILLIS);

                                                arguments.add("--socket-event-timeout=" + SOCKET_TIMEOUT_MILLIS);

                                                arguments.add("--log-path=" + CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + MOTADATA_REPORTING);

                                                arguments.add("--template-path=" + CURRENT_DIR + PATH_SEPARATOR + "templates" + PATH_SEPARATOR + REPORTS);

                                                arguments.add("--log-prefix=" + LOG_DATE_FORMAT.format(new Date()) + "-Reporting Engine-" + item.getLong(ID));

//                                            arguments.add("--open-browser"); // for debug purpose ONLY

                                                var processBuilder = new ProcessBuilder(arguments);

                                                ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject().put(PROGRESS, 30).put(MESSAGE, "Exporting report"));

                                                if (CommonUtil.debugEnabled())
                                                {
                                                    LOGGER.debug(String.format("%s> starting report-engine", item.getLong(ID)));
                                                }

                                                var process = processBuilder.start();

                                                WorkerUtil.setupCleanupTimer(TIMEOUT_SECONDS, process, processBuilder, event.getLong(EVENT_ID), null, null);

                                                try (var reader = new BufferedReader(new InputStreamReader(process.getInputStream())))
                                                {
                                                    String line;

                                                    while ((line = reader.readLine()) != null)
                                                    {
                                                        try
                                                        {
                                                            for (var value : line.trim().split("\n"))
                                                            {
                                                                ReportCacheStore.getStore().updateItem(item.getLong(ID), new JsonObject(new String(Base64.getDecoder().decode(value.trim()))));
                                                            }

                                                        }
                                                        catch (Exception exception)
                                                        {
                                                            LOGGER.error(new Exception(exception.getMessage() + ": " + line));

                                                            exitCode = NOT_AVAILABLE;
                                                        }
                                                    }
                                                }

                                                if (!process.isAlive())
                                                {
                                                    exitCode = process.exitValue();
                                                }

                                                if (CommonUtil.debugEnabled())
                                                {
                                                    LOGGER.debug(String.format("%s> report engine with PID %s exited with status code %s", item.getLong(ID), process.pid(), exitCode));
                                                }

                                                future.complete(exitCode);
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            vertx.eventBus().send(EVENT_FAIL, new JsonObject().put(EVENT_ID, event.getLong(EVENT_ID))
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(MESSAGE, String.format(ErrorMessageConstants.REPORT_GENERATION_FAILED, item.getString(REPORT_NAME), exception.getMessage()))
                                                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));

                                            future.fail(exception);
                                        }

                                    }).onComplete(result ->
                                    {
                                        if (result.succeeded() && result.result() == 0 && vertx.fileSystem().existsBlocking(GlobalConstants.UPLOADS + PATH_SEPARATOR + REPORTS + PATH_SEPARATOR + fileName))
                                        {
                                            // post actions
                                            if (EVENT_REPORT_EXPORT.equalsIgnoreCase(event.getString(EVENT_TYPE))) // if report export from UI
                                            {
                                                EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_CSV_EXPORT_READY, event.put(GlobalConstants.FILE_NAME, URLEncoder.encode(REPORTS + PATH_SEPARATOR + fileName, StandardCharsets.UTF_8)).put(STATUS, STATUS_SUCCEED));

                                                if (CommonUtil.debugEnabled())
                                                {
                                                    LOGGER.debug(String.format("%s> exporting...", item.getLong(ID)));
                                                }
                                            }
                                            else if (EVENT_REPORT.equalsIgnoreCase(event.getString(EVENT_TYPE)))
                                            {
                                                var context = event.getJsonObject(EVENT_CONTEXT);

                                                if (context != null)
                                                {
                                                    Notification.sendEmail(new JsonObject().put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(REPORTS + PATH_SEPARATOR + fileName).add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(REPORT_NOTIFICATION_SUBJECT, context.getString("subject")))
                                                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, context.getJsonArray("to"))
                                                            .put(Notification.EMAIL_NOTIFICATION_SENDER, context.getString("from"))
                                                            .put(Notification.TEMPLATE_NAME, Notification.EMAIL_NOTIFICATION_REPORT_HTML_TEMPLATE)
                                                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(TIME_STAMP, DateTimeUtil.timestamp()).put(REPORT_NAME, item.getString(REPORT_NAME)).put(MESSAGE, context.getString(MESSAGE).replaceAll("\n", "<br>"))));

                                                    if (CommonUtil.debugEnabled())
                                                    {

                                                        LOGGER.debug(String.format("%s> emailing...", item.getLong(ID)));
                                                    }
                                                }
                                            }
                                            else if (event.containsKey(Scheduler.SCHEDULER_CONTEXT))
                                            {
                                                var context = event.getJsonObject(Scheduler.SCHEDULER_CONTEXT);

                                                if (context.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !context.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
                                                {
                                                    Notification.sendEmail(new JsonObject().put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(REPORTS + PATH_SEPARATOR + fileName).add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                            .put(Notification.TEMPLATE_NAME, Notification.EMAIL_NOTIFICATION_REPORT_HTML_TEMPLATE)
                                                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(TIME_STAMP, DateTimeUtil.timestamp()).put(REPORT_NAME, item.getString(REPORT_NAME)).put(MESSAGE, "Hello,<br>Kindly find attached Reports.<br>Thank you"))
                                                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(REPORT_NOTIFICATION_SUBJECT, item.getString(REPORT_NAME)))
                                                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, context.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS)));

                                                    if (CommonUtil.debugEnabled())
                                                    {
                                                        LOGGER.debug(String.format("%s> emailing...", item.getLong(ID)));
                                                    }
                                                }
                                            }

                                            vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID)));
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format(ErrorMessageConstants.REPORT_GENERATION_FAILED, item.getLong(ID), result.failed() ? result.cause().getMessage() : "file not found"));
                                        }

                                        ReportCacheStore.getStore().complete(item.getLong(ID));

                                        this.tickers.remove(item.getLong(ID));
                                    });

                                }
                                else
                                {
                                    LOGGER.warn(String.format(ErrorMessageConstants.REPORT_GENERATION_FAILED, item.getLong(ID), reply.cause().getMessage()));

                                    ReportCacheStore.getStore().complete(item.getLong(ID));   // clear memory

                                    this.tickers.remove(item.getLong(ID));
                                }
                            });


                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    ReportCacheStore.getStore().complete(item.getLong(ID));   // clear memory

                    this.tickers.remove(item.getLong(ID));
                }
            }
            else
            {
                LOGGER.warn(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, event.encode()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private static JsonObject enrich(JsonObject rebrandingItem, JsonObject user, JsonObject event, JsonObject item, String fileName)
    {
        var config = new JsonObject()
                .put(AIOpsObject.OBJECT_IP, MotadataConfigUtil.getHTTPServerHost())
                .put(CLIENT_ID, UI_CLIENT_ID)
                .put(PORT, MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()))
                .put(TIMEOUT, 60)
                .put(URL_PROTOCOL, MotadataConfigUtil.httpsEnabled() ? HttpScheme.HTTPS.toString() : HttpScheme.HTTP.toString())
                .put(Rebranding.REBRANDING_LOGO, (rebrandingItem != null && rebrandingItem.containsKey(Rebranding.REBRANDING_LOGO_NAME) && rebrandingItem.getString(Rebranding.REBRANDING_LOGO_NAME) != null) ? rebrandingItem.getString(Rebranding.REBRANDING_LOGO) : "logo.png")
                .put(USER_PREFERENCE_TIME_ZONE, user.getJsonObject(USER_PREFERENCES).getString(USER_PREFERENCE_TIME_ZONE))
                .put(USER_PREFERENCE_DATE_TIME_FORMAT, user.getJsonObject(USER_PREFERENCES).getString(USER_PREFERENCE_DATE_TIME_FORMAT));


        return new JsonObject().put(SYSTEM_LOG_LEVEL, CommonUtil.getLogLevel()).put(INSTALLATION_TYPE, MotadataConfigUtil.getInstallationType()).put("maximum.cpu.cores", MotadataConfigUtil.getPluginEngineCPUCores()).put(REPORT_CONTEXT, new JsonObject().put(EVENT, event).put(REPORT, item).put(CONFIG_DIR, config).put(FILE_NAME, fileName));
    }

}
