{"************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init splash", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u13:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda1-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop1|[loop1]", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "loop6|[loop6]", "object.type": "system.process"}, {"object.name": "loop7|[loop7]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop9|[loop9]", "object.type": "system.process"}, {"object.name": "loop10|[loop10]", "object.type": "system.process"}, {"object.name": "loop13|[loop13]", "object.type": "system.process"}, {"object.name": "loop15|[loop15]", "object.type": "system.process"}, {"object.name": "loop16|[loop16]", "object.type": "system.process"}, {"object.name": "loop19|[loop19]", "object.type": "system.process"}, {"object.name": "loop20|[loop20]", "object.type": "system.process"}, {"object.name": "loop22|[loop22]", "object.type": "system.process"}, {"object.name": "loop23|[loop23]", "object.type": "system.process"}, {"object.name": "loop24|[loop24]", "object.type": "system.process"}, {"object.name": "loop25|[loop25]", "object.type": "system.process"}, {"object.name": "loop26|[loop26]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/RDP_Server/motadata_rdpwrapper", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "wpa_supp|/sbin/wpa_supplicant -u -s -O /run/wpa_supplicant", "object.type": "system.process"}, {"object.name": "NetworkM|/usr/sbin/NetworkManager --no-daemon", "object.type": "system.process"}, {"object.name": "ModemMan|/usr/sbin/ModemManager --filter-policy=strict", "object.type": "system.process"}, {"object.name": "avahi-da|avahi-daemon: running [motadata8.local]", "object.type": "system.process"}, {"object.name": "acpid|/usr/sbin/acpid", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "avahi-da|avahi-daemon: chroot helper", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "vsftpd|/usr/sbin/vsftpd /etc/vsftpd.conf", "object.type": "system.process"}, {"object.name": "anydesk|/usr/bin/anydesk --service", "object.type": "system.process"}, {"object.name": "dhclient|/sbin/dhclient -d -q -sf /usr/lib/NetworkManager/nm-dhcp-helper -pf /run/dhclient-ens160.pid -lf /var/lib/NetworkManager/dhclient-f4b74d97-55b5-39c3-a1b4-93335605ebc1-ens160.lease -cf /var/lib/NetworkManager/dhclient-ens160.conf ens160", "object.type": "system.process"}, {"object.name": "gdm3|/usr/sbin/gdm3", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "gdm-sess|gdm-session-worker [pam/gdm-launch-environment]", "object.type": "system.process"}, {"object.name": "ntpd|/usr/sbin/ntpd -p /var/run/ntpd.pid -g -u 125:129", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "whoopsie|/usr/bin/whoopsie -f", "object.type": "system.process"}, {"object.name": "kerneloo|/usr/sbin/kerneloops --test", "object.type": "system.process"}, {"object.name": "kerneloo|/usr/sbin/kerneloops", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "Xvnc4|Xvnc4 :1 -desktop motadata8:1 (root) -auth /root/.Xauthority -geometry 1280x800 -depth 24 -rfbwait 30000 -rfbauth /root/.vnc/passwd -rfbport 5901 -pn -fp /usr/X11R6/lib/X11/fonts/Type1/,/usr/X11R6/lib/X11/fonts/Speedo/,/usr/X11R6/lib/X11/fonts/misc/,/usr/X11R6/lib/X11/fonts/75dpi/,/usr/X11R6/lib/X11/fonts/100dpi/,/usr/share/fonts/X11/misc/,/usr/share/fonts/X11/Type1/,/usr/share/fonts/X11/75dpi/,/usr/share/fonts/X11/100dpi/ -co /etc/X11/rgb", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "gdm-wayl|/usr/lib/gdm3/gdm-wayland-session gnome-session --autostart /usr/share/gdm/greeter/autostart", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --session --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "gnome-se|/usr/lib/gnome-session/gnome-session-binary --autostart /usr/share/gdm/greeter/autostart", "object.type": "system.process"}, {"object.name": "gnome-sh|/usr/bin/gnome-shell", "object.type": "system.process"}, {"object.name": "sh|/bin/sh /etc/xdg/xfce4/xinitrc -- /etc/X11/xinit/xserverrc", "object.type": "system.process"}, {"object.name": "upowerd|/usr/lib/upower/upowerd", "object.type": "system.process"}, {"object.name": "uuidd|/usr/sbin/uuidd --socket-activation", "object.type": "system.process"}, {"object.name": "xfce4-se|xfce4-session", "object.type": "system.process"}, {"object.name": "dbus-lau|/usr/bin/dbus-launch --sh-syntax --exit-with-session xfce4-session", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --syslog --fork --print-pid 5 --print-address 7 --session", "object.type": "system.process"}, {"object.name": "xfconfd|/usr/lib/x86_64-linux-gnu/xfce4/xfconf/xfconfd", "object.type": "system.process"}, {"object.name": "ssh-agen|/usr/bin/ssh-agent -s", "object.type": "system.process"}, {"object.name": "gpg-agen|/usr/bin/gpg-agent --sh --daemon --write-env-file /root/.cache/gpg-agent-info", "object.type": "system.process"}, {"object.name": "xfwm4|xfwm4", "object.type": "system.process"}, {"object.name": "xfce4-pa|xfce4-panel", "object.type": "system.process"}, {"object.name": "T<PERSON><PERSON>|Thunar --daemon", "object.type": "system.process"}, {"object.name": "xfdeskto|xfdesktop", "object.type": "system.process"}, {"object.name": "xfsettin|xfsettingsd", "object.type": "system.process"}, {"object.name": "nm-apple|nm-applet", "object.type": "system.process"}, {"object.name": "deja-dup|/usr/lib/deja-dup/deja-dup-monitor", "object.type": "system.process"}, {"object.name": "applet.p|/usr/bin/python3 /usr/share/system-config-printer/applet.py", "object.type": "system.process"}, {"object.name": "at-spi-b|/usr/lib/at-spi2-core/at-spi-bus-launcher", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --config-file=/usr/share/defaults/at-spi2/accessibility.conf --nofork --print-address 3", "object.type": "system.process"}, {"object.name": "at-spi2-|/usr/lib/at-spi2-core/at-spi2-registryd --use-gnome-session", "object.type": "system.process"}, {"object.name": "gvfsd|/usr/lib/gvfs/gvfsd", "object.type": "system.process"}, {"object.name": "gvfsd-fu|/usr/lib/gvfs/gvfsd-fuse /root/.gvfs -f -o big_writes", "object.type": "system.process"}, {"object.name": "xfce4-po|xfce4-power-manager", "object.type": "system.process"}, {"object.name": "xfce4-no|/usr/lib/x86_64-linux-gnu/xfce4/notifyd/xfce4-notifyd", "object.type": "system.process"}, {"object.name": "Xwayland|/usr/bin/Xwayland :1024 -rootless -terminate -accessx -core -listen 4 -listen 5 -displayfd 6", "object.type": "system.process"}, {"object.name": "gvfs-udi|/usr/lib/gvfs/gvfs-udisks2-volume-monitor", "object.type": "system.process"}, {"object.name": "gvfs-mtp|/usr/lib/gvfs/gvfs-mtp-volume-monitor", "object.type": "system.process"}, {"object.name": "gvfs-goa|/usr/lib/gvfs/gvfs-goa-volume-monitor", "object.type": "system.process"}, {"object.name": "goa-daem|/usr/lib/gnome-online-accounts/goa-daemon", "object.type": "system.process"}, {"object.name": "goa-iden|/usr/lib/gnome-online-accounts/goa-identity-service", "object.type": "system.process"}, {"object.name": "gvfs-gph|/usr/lib/gvfs/gvfs-gphoto2-volume-monitor", "object.type": "system.process"}, {"object.name": "pulseaud|/usr/bin/pulseaudio --daemonize=no", "object.type": "system.process"}, {"object.name": "panel-6-|/usr/lib/x86_64-linux-gnu/xfce4/panel/wrapper-1.0 /usr/lib/x86_64-linux-gnu/xfce4/panel/plugins/libsystray.so 6 ******** systray Notification Area Area where notification icons appear", "object.type": "system.process"}, {"object.name": "gvfs-afc|/usr/lib/gvfs/gvfs-afc-volume-monitor", "object.type": "system.process"}, {"object.name": "panel-2-|/usr/lib/x86_64-linux-gnu/xfce4/panel/wrapper-1.0 /usr/lib/x86_64-linux-gnu/xfce4/panel/plugins/libactions.so 2 ******** actions Action Buttons Log out, lock or other system actions", "object.type": "system.process"}, {"object.name": "rtkit-da|/usr/lib/rtkit/rtkit-daemon", "object.type": "system.process"}, {"object.name": "gvfsd-tr|/usr/lib/gvfs/gvfsd-trash --spawner :1.12 /org/gtk/gvfs/exec_spaw/0", "object.type": "system.process"}, {"object.name": "gvfsd-me|/usr/lib/gvfs/gvfsd-metadata", "object.type": "system.process"}, {"object.name": "ibus-dae|ibus-daemon --xim --panel disable", "object.type": "system.process"}, {"object.name": "ibus-dco|/usr/lib/ibus/ibus-dconf", "object.type": "system.process"}, {"object.name": "ibus-x11|/usr/lib/ibus/ibus-x11 --kill-daemon", "object.type": "system.process"}, {"object.name": "ibus-por|/usr/lib/ibus/ibus-portal", "object.type": "system.process"}, {"object.name": "xdg-perm|/usr/libexec/xdg-permission-store", "object.type": "system.process"}, {"object.name": "boltd|/usr/lib/x86_64-linux-gnu/boltd", "object.type": "system.process"}, {"object.name": "gsd-xset|/usr/lib/gnome-settings-daemon/gsd-xsettings", "object.type": "system.process"}, {"object.name": "gsd-a11y|/usr/lib/gnome-settings-daemon/gsd-a11y-settings", "object.type": "system.process"}, {"object.name": "packagek|/usr/lib/packagekit/packagekitd", "object.type": "system.process"}, {"object.name": "gsd-clip|/usr/lib/gnome-settings-daemon/gsd-clipboard", "object.type": "system.process"}, {"object.name": "gsd-colo|/usr/lib/gnome-settings-daemon/gsd-color", "object.type": "system.process"}, {"object.name": "gsd-date|/usr/lib/gnome-settings-daemon/gsd-datetime", "object.type": "system.process"}, {"object.name": "gsd-hous|/usr/lib/gnome-settings-daemon/gsd-housekeeping", "object.type": "system.process"}, {"object.name": "gsd-keyb|/usr/lib/gnome-settings-daemon/gsd-keyboard", "object.type": "system.process"}, {"object.name": "gsd-medi|/usr/lib/gnome-settings-daemon/gsd-media-keys", "object.type": "system.process"}, {"object.name": "gsd-mous|/usr/lib/gnome-settings-daemon/gsd-mouse", "object.type": "system.process"}, {"object.name": "gsd-powe|/usr/lib/gnome-settings-daemon/gsd-power", "object.type": "system.process"}, {"object.name": "gsd-prin|/usr/lib/gnome-settings-daemon/gsd-print-notifications", "object.type": "system.process"}, {"object.name": "gsd-rfki|/usr/lib/gnome-settings-daemon/gsd-rfkill", "object.type": "system.process"}, {"object.name": "gsd-scre|/usr/lib/gnome-settings-daemon/gsd-screensaver-proxy", "object.type": "system.process"}, {"object.name": "gsd-shar|/usr/lib/gnome-settings-daemon/gsd-sharing", "object.type": "system.process"}, {"object.name": "gsd-smar|/usr/lib/gnome-settings-daemon/gsd-smartcard", "object.type": "system.process"}, {"object.name": "gsd-soun|/usr/lib/gnome-settings-daemon/gsd-sound", "object.type": "system.process"}, {"object.name": "gsd-waco|/usr/lib/gnome-settings-daemon/gsd-wacom", "object.type": "system.process"}, {"object.name": "colord|/usr/lib/colord/colord", "object.type": "system.process"}, {"object.name": "ibus-eng|/usr/lib/ibus/ibus-engine-simple", "object.type": "system.process"}, {"object.name": "gdm-sess|gdm-session-worker [pam/gdm-password]", "object.type": "system.process"}, {"object.name": "gnome-ke|/usr/bin/gnome-keyring-daemon --daemonize --login", "object.type": "system.process"}, {"object.name": "gdm-x-se|/usr/lib/gdm3/gdm-x-session --run-script env GNOME_SHELL_SESSION_MODE=ubuntu gnome-session --session=ubuntu", "object.type": "system.process"}, {"object.name": "Xorg|/usr/lib/xorg/Xorg vt2 -displayfd 3 -auth /run/user/1000/gdm/Xauthority -background none -noreset -keeptty -verbose 3", "object.type": "system.process"}, {"object.name": "gnome-se|/usr/lib/gnome-session/gnome-session-binary --session=ubuntu", "object.type": "system.process"}, {"object.name": "ssh-agen|/usr/bin/ssh-agent /usr/bin/im-launch env GNOME_SHELL_SESSION_MODE=ubuntu gnome-session --session=ubuntu", "object.type": "system.process"}, {"object.name": "gvfsd-fu|/usr/lib/gvfs/gvfsd-fuse /run/user/1000/gvfs -f -o big_writes", "object.type": "system.process"}, {"object.name": "pulseaud|/usr/bin/pulseaudio --start --log-target=syslog", "object.type": "system.process"}, {"object.name": "gnome-sh|/usr/lib/gnome-shell/gnome-shell-calendar-server", "object.type": "system.process"}, {"object.name": "evolutio|/usr/lib/evolution/evolution-source-registry", "object.type": "system.process"}, {"object.name": "dconf-se|/usr/lib/dconf/dconf-service", "object.type": "system.process"}, {"object.name": "gsd-prin|/usr/lib/gnome-settings-daemon/gsd-printer", "object.type": "system.process"}, {"object.name": "vino-ser|/usr/lib/vino/vino-server", "object.type": "system.process"}, {"object.name": "nautilus|nautilus-desktop", "object.type": "system.process"}, {"object.name": "gsd-disk|/usr/lib/gnome-disk-utility/gsd-disk-utility-notify", "object.type": "system.process"}, {"object.name": "anydesk|/usr/bin/anydesk --tray", "object.type": "system.process"}, {"object.name": "gvfsd-tr|/usr/lib/gvfs/gvfsd-trash --spawner :1.22 /org/gtk/gvfs/exec_spaw/0", "object.type": "system.process"}, {"object.name": "evolutio|/usr/lib/evolution/evolution-calendar-factory", "object.type": "system.process"}, {"object.name": "evolutio|/usr/lib/evolution/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2153x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2153/2", "object.type": "system.process"}, {"object.name": "evolutio|/usr/lib/evolution/evolution-addressbook-factory", "object.type": "system.process"}, {"object.name": "evolutio|/usr/lib/evolution/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2191x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2191/2", "object.type": "system.process"}, {"object.name": "gnome-so|/usr/bin/gnome-software --gapplication-service", "object.type": "system.process"}, {"object.name": "gnome-te|/usr/lib/gnome-terminal/gnome-terminal-server", "object.type": "system.process"}, {"object.name": "bash|bash", "object.type": "system.process"}, {"object.name": "update-n|update-notifier", "object.type": "system.process"}, {"object.name": "deja-dup|deja-dup --prompt", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u12:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u12:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u12:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u12:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "x11vnc|/usr/bin/x11vnc -rfbport 5901", "object.type": "system.process"}, {"object.name": "sshd|sshd: root@notty", "object.type": "system.process"}, {"object.name": "loop11|[loop11]", "object.type": "system.process"}, {"object.name": "loop18|[loop18]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "loop27|[loop27]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:5]", "object.type": "system.process"}, {"object.name": "loop28|[loop28]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "loop14|[loop14]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:7]", "object.type": "system.process"}, {"object.name": "loop8|[loop8]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "update-m|/usr/bin/python3 /usr/bin/update-manager --no-update --no-focus-on-map", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop17|[loop17]", "object.type": "system.process"}, {"object.name": "loop12|[loop12]", "object.type": "system.process"}, {"object.name": "xfsalloc|[xfsalloc]", "object.type": "system.process"}, {"object.name": "xfs_mru_|[xfs_mru_cache]", "object.type": "system.process"}, {"object.name": "jfsIO|[jfsIO]", "object.type": "system.process"}, {"object.name": "jfsCommi|[jfsCommit]", "object.type": "system.process"}, {"object.name": "jfsSync|[jfsSync]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop21|[loop21]", "object.type": "system.process"}, {"object.name": "cupsd|/usr/sbin/cupsd -l", "object.type": "system.process"}, {"object.name": "cups-bro|/usr/sbin/cups-browsed", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "object.type": "system.process"}], "password": "Mind@123", "port": 22, "status": "succeed", "username": "root"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "perf|[perf]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "vmstat|[vmstat]", "object.type": "system.process"}, {"object.name": "fsnotify|[fsnotify_mark]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "deferwq|[deferwq]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_3]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_3]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_4]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_4]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_5]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_5]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_6]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_6]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_7]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_7]", "object.type": "system.process"}, {"object.name": "kpsmouse|[kpsmoused]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_8]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_8]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_9]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_9]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_10]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_10]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_11]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_11]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_12]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_12]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_13]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_13]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_14]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_14]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_15]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_15]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_16]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_16]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_17]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_17]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_18]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_18]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_19]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_19]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_20]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_20]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_21]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_21]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_22]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_22]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_23]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_23]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_24]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_24]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_25]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_25]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_26]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_26]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_27]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_27]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_28]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_28]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_29]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_29]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_30]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_30]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_31]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_31]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_32]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_32]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "ib_addr|[ib_addr]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "ib_cm|[ib_cm]", "object.type": "system.process"}, {"object.name": "iw_cm_wq|[iw_cm_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation", "object.type": "system.process"}, {"object.name": "dhclient|/sbin/dhclient -1 -v -pf /run/dhclient.ens160.pid -lf /var/lib/dhcp/dhclient.ens160.leases -I -df /var/lib/dhcp/dhclient6.ens160.leases ens160", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "acpid|/usr/sbin/acpid", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "mdadm|/sbin/mdadm --monitor --pid-file /run/mdadm/monitor.pid --daemonise --scan --syslog", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "iscsid|/sbin/iscsid", "object.type": "system.process"}, {"object.name": "irqbalan|/usr/sbin/irqbalance --pid=/var/run/irqbalance.pid", "object.type": "system.process"}, {"object.name": "login|/bin/login --", "object.type": "system.process"}, {"object.name": "apache2|/usr/sbin/apache2 -k start", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "bash|-bash", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "uuidd|/usr/sbin/uuidd --socket-activation", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/RDP_Server/motadata_rdpwrapper", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u4:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: root@notty", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}], "password": "Mind@123", "port": 22, "status": "succeed", "username": "root"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "***********": {"metric.timeout": 10, "object.ip": "***********", "objects": [{"object.name": "systemd|/usr/lib/systemd/systemd --switched-root --system --deserialize 24", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "perf|[perf]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "vmstat|[vmstat]", "object.type": "system.process"}, {"object.name": "fsnotify|[fsnotify_mark]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "kpsmouse|[kpsmoused]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "printk|[printk]", "object.type": "system.process"}, {"object.name": "deferwq|[deferwq]", "object.type": "system.process"}, {"object.name": "kaluad|[kaluad]", "object.type": "system.process"}, {"object.name": "kaluad_s|[kaluad_sync]", "object.type": "system.process"}, {"object.name": "kmpath_r|[kmpath_rdacd]", "object.type": "system.process"}, {"object.name": "kmpathd|[kmpathd]", "object.type": "system.process"}, {"object.name": "kmpath_h|[kmpath_handlerd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "vmw_pvsc|[vmw_pvscsi_wq_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "btrfs-wo|[btrfs-worker]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u3:0]", "object.type": "system.process"}, {"object.name": "btrfs-wo|[btrfs-worker-hi]", "object.type": "system.process"}, {"object.name": "btrfs-de|[btrfs-del<PERSON><PERSON>]", "object.type": "system.process"}, {"object.name": "btrfs-fl|[btrfs-flush_del]", "object.type": "system.process"}, {"object.name": "btrfs-ca|[btrfs-cache]", "object.type": "system.process"}, {"object.name": "btrfs-su|[btrfs-submit]", "object.type": "system.process"}, {"object.name": "btrfs-fi|[btrfs-fixup]", "object.type": "system.process"}, {"object.name": "btrfs-en|[btrfs-endio]", "object.type": "system.process"}, {"object.name": "btrfs-en|[btrfs-endio-met]", "object.type": "system.process"}, {"object.name": "btrfs-en|[btrfs-endio-rai]", "object.type": "system.process"}, {"object.name": "btrfs-en|[btrfs-endio-rep]", "object.type": "system.process"}, {"object.name": "btrfs-rm|[btrfs-rmw]", "object.type": "system.process"}, {"object.name": "btrfs-en|[btrfs-endio-wri]", "object.type": "system.process"}, {"object.name": "btrfs-fr|[btrfs-freespace]", "object.type": "system.process"}, {"object.name": "btrfs-de|[btrfs-delayed-m]", "object.type": "system.process"}, {"object.name": "btrfs-re|[btrfs-readahead]", "object.type": "system.process"}, {"object.name": "btrfs-qg|[btrfs-qgroup-re]", "object.type": "system.process"}, {"object.name": "btrfs-ex|[btrfs-extent-re]", "object.type": "system.process"}, {"object.name": "btrfs-cl|[btrfs-cleaner]", "object.type": "system.process"}, {"object.name": "btrfs-tr|[btrfs-transacti]", "object.type": "system.process"}, {"object.name": "systemd-|/usr/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "haveged|/usr/sbin/haveged -w 1024 -v 0 -F", "object.type": "system.process"}, {"object.name": "systemd-|/usr/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "nfit|[nfit]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "xfsalloc|[xfsalloc]", "object.type": "system.process"}, {"object.name": "xfs_mru_|[xfs_mru_cache]", "object.type": "system.process"}, {"object.name": "xfs-buf/|[xfs-buf/sda3]", "object.type": "system.process"}, {"object.name": "xfs-data|[xfs-data/sda3]", "object.type": "system.process"}, {"object.name": "xfs-conv|[xfs-conv/sda3]", "object.type": "system.process"}, {"object.name": "xfs-cil/|[xfs-cil/sda3]", "object.type": "system.process"}, {"object.name": "xfs-recl|[xfs-reclaim/sda]", "object.type": "system.process"}, {"object.name": "xfs-log/|[xfs-log/sda3]", "object.type": "system.process"}, {"object.name": "xfs-eofb|[xfs-eofblocks/s]", "object.type": "system.process"}, {"object.name": "xfsaild/|[xfsaild/sda3]", "object.type": "system.process"}, {"object.name": "dbus-dae|/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService -s", "object.type": "system.process"}, {"object.name": "wickedd-|/usr/lib/wicked/bin/wickedd-auto4 --systemd --foreground", "object.type": "system.process"}, {"object.name": "wickedd-|/usr/lib/wicked/bin/wickedd-dhcp4 --systemd --foreground", "object.type": "system.process"}, {"object.name": "wickedd-|/usr/lib/wicked/bin/wickedd-dhcp6 --systemd --foreground", "object.type": "system.process"}, {"object.name": "nscd|/usr/sbin/nscd", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "systemd-|/usr/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "wickedd|/usr/sbin/wickedd --systemd --foreground", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "wickedd-|/usr/sbin/wickedd-nanny --systemd --foreground", "object.type": "system.process"}, {"object.name": "gdm|/usr/sbin/gdm", "object.type": "system.process"}, {"object.name": "gdm-simp|/usr/lib/gdm/gdm-simple-slave --display-id /org/gnome/DisplayManager/Displays/_0", "object.type": "system.process"}, {"object.name": "Xorg|/usr/bin/Xorg :0 -background none -verbose -auth /run/gdm/auth-for-gdm-PcJr7G/database -seat seat0 vt7", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accounts-daemon", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/polkit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "master|/usr/lib/postfix/master -w", "object.type": "system.process"}, {"object.name": "qmgr|qmgr -l -t fifo -u", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -n", "object.type": "system.process"}, {"object.name": "gdm-sess|gdm-session-worker [pam/gdm-launch-environment]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "systemd|/usr/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "gnome-se|/usr/lib/gnome-session-binary --autostart /usr/share/gdm/greeter/autostart", "object.type": "system.process"}, {"object.name": "dbus-lau|/usr/bin/dbus-launch --exit-with-session /usr/bin/gnome-session --autostart /usr/share/gdm/greeter/autostart", "object.type": "system.process"}, {"object.name": "dbus-dae|/bin/dbus-daemon --fork --print-pid 5 --print-address 7 --session", "object.type": "system.process"}, {"object.name": "at-spi-b|/usr/lib/at-spi2/at-spi-bus-launcher", "object.type": "system.process"}, {"object.name": "dbus-dae|/bin/dbus-daemon --config-file=/usr/share/defaults/at-spi2/accessibility.conf --nofork --print-address 3", "object.type": "system.process"}, {"object.name": "at-spi2-|/usr/lib/at-spi2/at-spi2-registryd --use-gnome-session", "object.type": "system.process"}, {"object.name": "gnome-se|/usr/lib/gnome-settings-daemon-3.0/gnome-settings-daemon", "object.type": "system.process"}, {"object.name": "upowerd|/usr/lib/upower/upowerd", "object.type": "system.process"}, {"object.name": "gnome-sh|gnome-shell --mode=gdm", "object.type": "system.process"}, {"object.name": "pulseaud|/usr/bin/pulseaudio --start --log-target=syslog", "object.type": "system.process"}, {"object.name": "rtkit-da|/usr/lib/rtkit/rtkit-daemon", "object.type": "system.process"}, {"object.name": "dconf-se|/usr/lib/dconf-service", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "pickup|pickup -l -t fifo -u", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u2:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:6]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/8]", "object.type": "system.process"}, {"object.name": "migratio|[migration/8]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/8]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/8:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/9]", "object.type": "system.process"}, {"object.name": "migratio|[migration/9]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/9]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/9:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/10]", "object.type": "system.process"}, {"object.name": "migratio|[migration/10]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/10]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/10:0H]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/11]", "object.type": "system.process"}, {"object.name": "migratio|[migration/11]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/11]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/11:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "perf|[perf]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "vmstat|[vmstat]", "object.type": "system.process"}, {"object.name": "fsnotify|[fsnotify_mark]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "deferwq|[deferwq]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_3]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_3]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_4]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_4]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_5]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_5]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_6]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_6]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_7]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_7]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_8]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_8]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_9]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_9]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_10]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_10]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_11]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_11]", "object.type": "system.process"}, {"object.name": "kpsmouse|[kpsmoused]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_12]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_12]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_13]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_13]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_14]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_14]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_15]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_15]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_16]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_16]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_17]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_17]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_18]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_18]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_19]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_19]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_20]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_20]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_21]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_21]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_22]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_22]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_23]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_23]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_24]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_24]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_25]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_25]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_26]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_26]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_27]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_27]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_28]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_28]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_29]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_29]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_30]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_30]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_31]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_31]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_32]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_32]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/10:0]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/10:1H]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "ib_addr|[ib_addr]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "ib_cm|[ib_cm]", "object.type": "system.process"}, {"object.name": "iw_cm_wq|[iw_cm_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/8:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/8:3]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/10:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/11:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "acpid|/usr/sbin/acpid", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation", "object.type": "system.process"}, {"object.name": "dhclient|/sbin/dhclient -1 -v -pf /run/dhclient.ens160.pid -lf /var/lib/dhcp/dhclient.ens160.leases -I -df /var/lib/dhcp/dhclient6.ens160.leases ens160", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "mdadm|/sbin/mdadm --monitor --pid-file /run/mdadm/monitor.pid --daemonise --scan --syslog", "object.type": "system.process"}, {"object.name": "containe|/usr/bin/containerd", "object.type": "system.process"}, {"object.name": "dockerd|/usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock", "object.type": "system.process"}, {"object.name": "iscsid|/sbin/iscsid", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "login|/bin/login --", "object.type": "system.process"}, {"object.name": "irqbalan|/usr/sbin/irqbalance --pid=/var/run/irqbalance.pid", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "bash|bash -c /var/db2_setup/lib/backup_cfg.sh >> /tmp/backup_cfg.out 2>&1", "object.type": "system.process"}, {"object.name": "backup_c|/bin/bash /var/db2_setup/lib/backup_cfg.sh", "object.type": "system.process"}, {"object.name": "sleep|sleep 2m", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/8:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "postgres|postgres: postgres postgres *************(52780) idle in transaction", "object.type": "system.process"}, {"object.name": "fix_etc_|/bin/bash /var/db2_setup/lib/fix_etc_host.sh", "object.type": "system.process"}, {"object.name": "sleep|sleep 10", "object.type": "system.process"}, {"object.name": "sleep|sleep 1", "object.type": "system.process"}, {"object.name": "sshd|sshd: root@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/9:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/11:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 5000 -container-ip ********** -container-port 5000", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/cb3b3ab581785a70399aa19ab63528a57f5d1836e3a443028f8c0685425c6a73 -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "entrypoi|/bin/bash /entrypoint.sh", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 55432 -container-ip ********** -container-port 5432", "object.type": "system.process"}, {"object.name": "sh|sh /opt/sybase/ASE-16_0/install/RUN_MYSYBASE", "object.type": "system.process"}, {"object.name": "dataserv|/opt/sybase/ASE-16_0/bin/dataserver -d/opt/sybase/data/master.dat -e/opt/sybase/ASE-16_0/install/MYSYBASE.log -c/opt/sybase/ASE-16_0/MYSYBASE.cfg -M/opt/sybase/ASE-16_0 -N/opt/sybase/ASE-16_0/sysam/MYSYBASE.properties -i/opt/sybase -sMYSYBASE", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/455598b609f31b07a16f0d63dc83d962d579f1ed75646aa22a3e3cb03f78aaf3 -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "postgres|postgres", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 50000 -container-ip ********** -container-port 50000", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/6c9f0c61816f0f4e89b9806d106abc090f2e3a3966621f72b2bf0b51748ba78d -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "setup_db|/bin/bash /var/db2_setup/lib/setup_db2_instance.sh", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 33066 -container-ip ********** -container-port 3306", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/24d4729be6dea1c5dbc513f18862cae776136fdbeb50c2e4916bd4470c2c6255 -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "entrypoi|/bin/bash /entrypoint.sh mysqld", "object.type": "system.process"}, {"object.name": "postgres|postgres: checkpointer process", "object.type": "system.process"}, {"object.name": "postgres|postgres: writer process", "object.type": "system.process"}, {"object.name": "postgres|postgres: wal writer process", "object.type": "system.process"}, {"object.name": "postgres|postgres: autovacuum launcher process", "object.type": "system.process"}, {"object.name": "postgres|postgres: stats collector process", "object.type": "system.process"}, {"object.name": "dio/dm-0|[dio/dm-0]", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 33061 -container-ip ********** -container-port 3306", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/2f8f0d0cee93440d528a2b2bf50f6d9a1ab3561a8a36608430cbaf319815cff0 -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "mysqld|mysqld", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 5656 -container-ip ********** -container-port 5000", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 1936 -container-ip ********** -container-port 1936", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/7c8c5af026d34a1028279f723c4b7e6f891f0c99ae5a2b9ea2c4495925474a3c -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "haproxy|haproxy -W -db -f /usr/local/etc/haproxy/haproxy.cfg", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/83daf4bb7c59f6eba959abef2b617d4ecbc5b8813194b34b9f308e9535c7ea11 -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "python|python hello.py", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 9197 -container-ip ********** -container-port 9997", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 8180 -container-ip ********** -container-port 8080", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/6eb1b45426a846c5a8c79bfd99104390ab4ee610296fecd23ce1f7adbb479411 -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "java|/usr/lib/jvm/java-7-openjdk-amd64/jre/bin/java -Djava.util.logging.config.file=/usr/local/tomcat/conf/logging.properties -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Djava.endorsed.dirs=/usr/local/tomcat/endorsed -classpath /usr/local/tomcat/bin/bootstrap.jar:/usr/local/tomcat/bin/tomcat-juli.jar -Dcatalina.base=/usr/local/tomcat -Dcatalina.home=/usr/local/tomcat -Djava.io.tmpdir=/usr/local/tomcat/temp org.apache.catalina.startup.Bootstrap start", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 7088 -container-ip *********** -container-port 80", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/7a00693533eaceab5d60dce3fd036ac55d43df5274df88a41bada1ed948fb4bc -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "bash|bash", "object.type": "system.process"}, {"object.name": "db2fmp|db2fmp ( ,1,0,0,0,0,0,00000000,0,0,0000000000000000,0000000000000000,00000000,00000000,00000000,00000000,00000000,00000000,0000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,000000035b635000,0000000000000000,0000000000000000,1,0,0,,,,,a89da0,14,1e014,2,0,1,00000000000c0080,0x240000000,0x240000000,1600000,10002,2,4d558049", "object.type": "system.process"}, {"object.name": "uuidd|/usr/sbin/uuidd --socket-activation", "object.type": "system.process"}, {"object.name": "db2syscr|db2wdog 0 [db2inst1]", "object.type": "system.process"}, {"object.name": "db2sysc|db2sysc 0", "object.type": "system.process"}, {"object.name": "db2syscr|db2ckpwd 0", "object.type": "system.process"}, {"object.name": "db2vend|db2vend (PD Vendor Process - 1) 0", "object.type": "system.process"}, {"object.name": "db2fmp|db2acd 0 ,0,0,0,1,0,0,00000000,0,0,0000000000000000,0000000000000000,00000000,00000000,00000000,00000000,00000000,00000000,0000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,000000035b635000,0000000000000000,0000000000000000,1,0,0,,,,,a89da0,14,1e014,2,0,1,0000000000041fc0,0x240000000,0x240000000,1600000,10002,2,100013", "object.type": "system.process"}, {"object.name": "supervis|/usr/bin/python /usr/bin/supervisord -c /etc/supervisord.conf", "object.type": "system.process"}, {"object.name": "tail|tail -f /database/config/db2inst1/sqllib/db2dump/DIAG0000/db2diag.log", "object.type": "system.process"}, {"object.name": "db2fmcd|/opt/ibm/db2/V11.5/bin/db2fmcd", "object.type": "system.process"}, {"object.name": "lighttpd|/usr/sbin/lighttpd -f /etc/lighttpd/lighttpd.conf", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 5500 -container-ip ********** -container-port 8080", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 1521 -container-ip ********** -container-port 1521", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/fec4a6fdc6e17968e1fa9cc86717f9ad0802605dbe47bde1b2fbb83b873b94de -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "tnslsnr|/u01/app/oracle/product/12.2.0/SE/bin/tnslsnr", "object.type": "system.process"}, {"object.name": "ora_pmon|ora_pmon_xe", "object.type": "system.process"}, {"object.name": "ora_clmn|ora_clmn_xe", "object.type": "system.process"}, {"object.name": "ora_psp0|ora_psp0_xe", "object.type": "system.process"}, {"object.name": "ora_vktm|ora_vktm_xe", "object.type": "system.process"}, {"object.name": "ora_gen0|ora_gen0_xe", "object.type": "system.process"}, {"object.name": "ora_mman|ora_mman_xe", "object.type": "system.process"}, {"object.name": "ora_scmn|ora_gen1_xe", "object.type": "system.process"}, {"object.name": "ora_diag|ora_diag_xe", "object.type": "system.process"}, {"object.name": "ora_scmn|ora_ofsd_xe", "object.type": "system.process"}, {"object.name": "ora_dbrm|ora_dbrm_xe", "object.type": "system.process"}, {"object.name": "ora_vkrm|ora_vkrm_xe", "object.type": "system.process"}, {"object.name": "ora_svcb|ora_svcb_xe", "object.type": "system.process"}, {"object.name": "ora_pman|ora_pman_xe", "object.type": "system.process"}, {"object.name": "ora_dia0|ora_dia0_xe", "object.type": "system.process"}, {"object.name": "ora_dbw0|ora_dbw0_xe", "object.type": "system.process"}, {"object.name": "ora_lgwr|ora_lgwr_xe", "object.type": "system.process"}, {"object.name": "ora_ckpt|ora_ckpt_xe", "object.type": "system.process"}, {"object.name": "ora_lg00|ora_lg00_xe", "object.type": "system.process"}, {"object.name": "ora_smon|ora_smon_xe", "object.type": "system.process"}, {"object.name": "ora_lg01|ora_lg01_xe", "object.type": "system.process"}, {"object.name": "ora_smco|ora_smco_xe", "object.type": "system.process"}, {"object.name": "ora_reco|ora_reco_xe", "object.type": "system.process"}, {"object.name": "ora_lreg|ora_lreg_xe", "object.type": "system.process"}, {"object.name": "ora_pxmn|ora_pxmn_xe", "object.type": "system.process"}, {"object.name": "ora_mmon|ora_mmon_xe", "object.type": "system.process"}, {"object.name": "ora_mmnl|ora_mmnl_xe", "object.type": "system.process"}, {"object.name": "ora_d000|ora_d000_xe", "object.type": "system.process"}, {"object.name": "ora_s000|ora_s000_xe", "object.type": "system.process"}, {"object.name": "ora_tmon|ora_tmon_xe", "object.type": "system.process"}, {"object.name": "ora_tt00|ora_tt00_xe", "object.type": "system.process"}, {"object.name": "ora_tt01|ora_tt01_xe", "object.type": "system.process"}, {"object.name": "ora_tt02|ora_tt02_xe", "object.type": "system.process"}, {"object.name": "ora_aqpc|ora_aqpc_xe", "object.type": "system.process"}, {"object.name": "ora_cjq0|ora_cjq0_xe", "object.type": "system.process"}, {"object.name": "ora_qm02|ora_qm02_xe", "object.type": "system.process"}, {"object.name": "ora_q003|ora_q003_xe", "object.type": "system.process"}, {"object.name": "ora_q004|ora_q004_xe", "object.type": "system.process"}, {"object.name": "bash|-bash", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "bash|/bin/bash", "object.type": "system.process"}, {"object.name": "sqlplus|./sqlplus", "object.type": "system.process"}, {"object.name": "oracle_6|oraclexe (DESCRIPTION=(LOCAL=YES)(ADDRESS=(PROTOCOL=beq)))", "object.type": "system.process"}, {"object.name": "motadata|/motadata/motadata-agent/motadata-manager &", "object.type": "system.process"}, {"object.name": "ora_w00d|ora_w00d_xe", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}, {"object.name": "ora_w001|ora_w001_xe", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/RDP_Server/motadata_rdpwrapper", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/10:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/11:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/9:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/8:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 10000 -container-ip ********** -container-port 10000", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 8053 -container-ip ********** -container-port 8053", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto tcp -host-ip 0.0.0.0 -host-port 53 -container-ip ********** -container-port 53", "object.type": "system.process"}, {"object.name": "docker-p|/usr/bin/docker-proxy -proto udp -host-ip 0.0.0.0 -host-port 53 -container-ip ********** -container-port 53", "object.type": "system.process"}, {"object.name": "containe|containerd-shim -namespace moby -workdir /var/lib/containerd/io.containerd.runtime.v1.linux/moby/824beac60656981a3a45cb9885c924c9237c805343ff8d957db154ff78829fa1 -address /run/containerd/containerd.sock -containerd-binary /usr/bin/containerd -runtime-root /var/run/docker/runtime-runc", "object.type": "system.process"}, {"object.name": "named|/usr/sbin/named -u bind -g", "object.type": "system.process"}, {"object.name": "miniserv|/usr/bin/perl /usr/share/webmin/miniserv.pl /etc/webmin/miniserv.conf", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/11:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/8:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u256:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0]", "object.type": "system.process"}, {"object.name": "ora_w009|ora_w009_xe", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/9:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/10:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u256:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/11:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u256:0]", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "root"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "***********": {"metric.timeout": 10, "object.ip": "***********", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "***********": {"metric.timeout": 10, "object.ip": "***********", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "***********": {"metric.timeout": 10, "object.ip": "***********", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "***********": {"metric.timeout": 10, "object.ip": "***********", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "***********": {"metric.timeout": 10, "object.ip": "***********", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 10, "object.ip": "************", "objects": [{"object.name": "systemd|/sbin/init maybe-ubiquity", "object.type": "system.process"}, {"object.name": "kthreadd|[kthreadd]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:0H]", "object.type": "system.process"}, {"object.name": "mm_percp|[mm_percpu_wq]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/0]", "object.type": "system.process"}, {"object.name": "rcu_sche|[rcu_sched]", "object.type": "system.process"}, {"object.name": "rcu_bh|[rcu_bh]", "object.type": "system.process"}, {"object.name": "migratio|[migration/0]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/0]", "object.type": "system.process"}, {"object.name": "cpuhp/0|[cpuhp/0]", "object.type": "system.process"}, {"object.name": "cpuhp/1|[cpuhp/1]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/1]", "object.type": "system.process"}, {"object.name": "migratio|[migration/1]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/2|[cpuhp/2]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/2]", "object.type": "system.process"}, {"object.name": "migratio|[migration/2]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/3|[cpuhp/3]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/3]", "object.type": "system.process"}, {"object.name": "migratio|[migration/3]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/3]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/4|[cpuhp/4]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/4]", "object.type": "system.process"}, {"object.name": "migratio|[migration/4]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/5|[cpuhp/5]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/5]", "object.type": "system.process"}, {"object.name": "migratio|[migration/5]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/5]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/6|[cpuhp/6]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/6]", "object.type": "system.process"}, {"object.name": "migratio|[migration/6]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/6]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0H]", "object.type": "system.process"}, {"object.name": "cpuhp/7|[cpuhp/7]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdog/7]", "object.type": "system.process"}, {"object.name": "migratio|[migration/7]", "object.type": "system.process"}, {"object.name": "ksoftirq|[ksoftirqd/7]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0H]", "object.type": "system.process"}, {"object.name": "kdevtmpf|[kdevtmpfs]", "object.type": "system.process"}, {"object.name": "netns|[netns]", "object.type": "system.process"}, {"object.name": "rcu_task|[rcu_tasks_kthre]", "object.type": "system.process"}, {"object.name": "kauditd|[kauditd]", "object.type": "system.process"}, {"object.name": "khungtas|[khungtaskd]", "object.type": "system.process"}, {"object.name": "oom_reap|[oom_reaper]", "object.type": "system.process"}, {"object.name": "writebac|[writeback]", "object.type": "system.process"}, {"object.name": "kcompact|[kcompactd0]", "object.type": "system.process"}, {"object.name": "ksmd|[ksmd]", "object.type": "system.process"}, {"object.name": "khugepag|[khugepaged]", "object.type": "system.process"}, {"object.name": "crypto|[crypto]", "object.type": "system.process"}, {"object.name": "kintegri|[kintegrityd]", "object.type": "system.process"}, {"object.name": "kblockd|[kblockd]", "object.type": "system.process"}, {"object.name": "ata_sff|[ata_sff]", "object.type": "system.process"}, {"object.name": "md|[md]", "object.type": "system.process"}, {"object.name": "edac-pol|[edac-poller]", "object.type": "system.process"}, {"object.name": "devfreq_|[devfreq_wq]", "object.type": "system.process"}, {"object.name": "watchdog|[watchdogd]", "object.type": "system.process"}, {"object.name": "kswapd0|[kswapd0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u17:0]", "object.type": "system.process"}, {"object.name": "ecryptfs|[ecryptfs-kthrea]", "object.type": "system.process"}, {"object.name": "kthrotld|[kthrotld]", "object.type": "system.process"}, {"object.name": "acpi_the|[acpi_thermal_pm]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_0]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_1]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_1]", "object.type": "system.process"}, {"object.name": "ipv6_add|[ipv6_addrconf]", "object.type": "system.process"}, {"object.name": "kstrp|[kstrp]", "object.type": "system.process"}, {"object.name": "charger_|[charger_manager]", "object.type": "system.process"}, {"object.name": "mpt_poll|[mpt_poll_0]", "object.type": "system.process"}, {"object.name": "mpt/0|[mpt/0]", "object.type": "system.process"}, {"object.name": "scsi_eh_|[scsi_eh_2]", "object.type": "system.process"}, {"object.name": "scsi_tmf|[scsi_tmf_2]", "object.type": "system.process"}, {"object.name": "ttm_swap|[ttm_swap]", "object.type": "system.process"}, {"object.name": "irq/16-v|[irq/16-vmwgfx]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1H]", "object.type": "system.process"}, {"object.name": "raid5wq|[raid5wq]", "object.type": "system.process"}, {"object.name": "kdmflush|[kdmflush]", "object.type": "system.process"}, {"object.name": "bioset|[bioset]", "object.type": "system.process"}, {"object.name": "jbd2/dm-|[jbd2/dm-0-8]", "object.type": "system.process"}, {"object.name": "ext4-rsv|[ext4-rsv-conver]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:1H]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1H]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-journald", "object.type": "system.process"}, {"object.name": "lvmetad|/sbin/lvmetad -f", "object.type": "system.process"}, {"object.name": "iscsi_eh|[iscsi_eh]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-wq]", "object.type": "system.process"}, {"object.name": "ib-comp-|[ib-comp-unb-wq]", "object.type": "system.process"}, {"object.name": "ib_mcast|[ib_mcast]", "object.type": "system.process"}, {"object.name": "ib_nl_sa|[ib_nl_sa_wq]", "object.type": "system.process"}, {"object.name": "rdma_cm|[rdma_cm]", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-udevd", "object.type": "system.process"}, {"object.name": "loop0|[loop0]", "object.type": "system.process"}, {"object.name": "loop2|[loop2]", "object.type": "system.process"}, {"object.name": "loop3|[loop3]", "object.type": "system.process"}, {"object.name": "jbd2/sda|[jbd2/sda2-8]", "object.type": "system.process"}, {"object.name": "VGAuthSe|/usr/bin/VGAuthService", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-timesyncd", "object.type": "system.process"}, {"object.name": "vmtoolsd|/usr/bin/vmtoolsd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-networkd", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-resolved", "object.type": "system.process"}, {"object.name": "systemd-|/lib/systemd/systemd-logind", "object.type": "system.process"}, {"object.name": "udisksd|/usr/lib/udisks2/udisksd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/bin/dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only", "object.type": "system.process"}, {"object.name": "accounts|/usr/lib/accountsservice/accounts-daemon", "object.type": "system.process"}, {"object.name": "lxcfs|/usr/bin/lxcfs /var/lib/lxcfs/", "object.type": "system.process"}, {"object.name": "irq<PERSON><PERSON>|/usr/sbin/irqbalance --foreground", "object.type": "system.process"}, {"object.name": "clckhous|clickhouse-watchdog --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "run-snmp|/bin/sh /snap/net-snmp/27/sbin/run-snmpd", "object.type": "system.process"}, {"object.name": "atd|/usr/sbin/atd -f", "object.type": "system.process"}, {"object.name": "unattend|/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal", "object.type": "system.process"}, {"object.name": "agetty|/sbin/agetty -o -p -- \\u --noclear tty1 linux", "object.type": "system.process"}, {"object.name": "xrdp-ses|/usr/sbin/xrdp-sesman", "object.type": "system.process"}, {"object.name": "sshd|/usr/sbin/sshd -D", "object.type": "system.process"}, {"object.name": "xrdp|/usr/sbin/xrdp", "object.type": "system.process"}, {"object.name": "clickhou|/usr/bin/clickhouse-server --config=/etc/clickhouse-server/config.xml --pid-file=/run/clickhouse-server/clickhouse-server.pid", "object.type": "system.process"}, {"object.name": "snmpd|/snap/net-snmp/27/sbin/snmpd -c /var/snap/net-snmp/27/snmpd.conf -C -f -Lf /var/snap/net-snmp/27/snmpd.log -M /snap/net-snmp/27/share/snmp/mibs -r", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/4:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:2]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/2:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/3:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/6:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/5:0]", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata [priv]", "object.type": "system.process"}, {"object.name": "systemd|/lib/systemd/systemd --user", "object.type": "system.process"}, {"object.name": "(sd-pam)|(sd-pam)", "object.type": "system.process"}, {"object.name": "sshd|sshd: motadata@notty", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/1:2]", "object.type": "system.process"}, {"object.name": "networkd|/usr/bin/python3 /usr/bin/networkd-dispatcher --run-startup-triggers", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron -f", "object.type": "system.process"}, {"object.name": "rsyslogd|/usr/sbin/rsyslogd -n", "object.type": "system.process"}, {"object.name": "loop4|[loop4]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/7:0]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:1]", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/0:1]", "object.type": "system.process"}, {"object.name": "loop5|[loop5]", "object.type": "system.process"}, {"object.name": "snapd|/usr/lib/snapd/snapd", "object.type": "system.process"}, {"object.name": "polkitd|/usr/lib/policykit-1/polkitd --no-debug", "object.type": "system.process"}, {"object.name": "kworker/|[kworker/u16:3]", "object.type": "system.process"}, {"object.name": "motadata|/opt/Motadata/agent_service/motadata_serviceops", "object.type": "system.process"}], "password": "motadata", "port": 22, "status": "succeed", "username": "motadata"}}