{"172.16.8.113": {"result": {"ad.replication": [{"ad.replication": "WIN-RC9AF0C6565", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "CN=Configuration,DC=motadata,DC=in", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:59:17", "ad.replication.error.code": "1908", "ad.replication.error.message": "Could not find the domain controller for this domain."}, {"ad.replication": "WIN-RC9AF0C6565", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "CN=Schema,CN=Configuration,DC=motadata,DC=in", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:59:17", "ad.replication.error.code": "5", "ad.replication.error.message": "Access is denied."}, {"ad.replication": "WIN-RC9AF0C6565", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "DC=ForestDnsZones,DC=motadata,DC=in", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:59:17", "ad.replication.error.code": "1256", "ad.replication.error.message": "The remote system is not available. For information about network troubleshooting, see Windows Help."}, {"ad.replication": "WIN-RC9AF0C6565", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "DC=motadata,DC=in", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:59:17", "ad.replication.error.code": "1256", "ad.replication.error.message": "The remote system is not available. For information about network troubleshooting, see Windows Help."}, {"ad.replication": "WIN-5HFC3SICV2O", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.naming.context": "DC=mind78,DC=local", "ad.replication.status": "fail"}, {"ad.replication": "WIN-5HFC3SICV2O", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.naming.context": "CN=Configuration,DC=motadata,DC=in", "ad.replication.status": "fail"}, {"ad.replication": "WIN-5HFC3SICV2O", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.naming.context": "CN=Schema,CN=Configuration,DC=motadata,DC=in", "ad.replication.status": "fail"}]}, "errors": []}, "172.16.8.205": {"result": {"ad.replication": [{"ad.replication": "WIN-RC9AF0C6565", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "CN=Configuration,DC=motadata,DC=in", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:59:17", "ad.replication.error.code": "1908", "ad.replication.error.message": "Could not find the domain controller for this domain."}, {"ad.replication": "WIN-RC9AF0C6565", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "CN=Schema,CN=Configuration,DC=motadata,DC=in", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:59:17", "ad.replication.error.code": "5", "ad.replication.error.message": "Access is denied."}, {"ad.replication": "WIN-RC9AF0C6565", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "DC=ForestDnsZones,DC=motadata,DC=in", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:59:17", "ad.replication.error.code": "1256", "ad.replication.error.message": "The remote system is not available. For information about network troubleshooting, see Windows Help."}, {"ad.replication": "WIN-RC9AF0C6565", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "DC=motadata,DC=in", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:59:17", "ad.replication.error.code": "1256", "ad.replication.error.message": "The remote system is not available. For information about network troubleshooting, see Windows Help."}, {"ad.replication": "WIN-5HFC3SICV2O", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.naming.context": "DC=mind78,DC=local", "ad.replication.status": "fail"}, {"ad.replication": "WIN-5HFC3SICV2O", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.naming.context": "CN=Configuration,DC=motadata,DC=in", "ad.replication.status": "fail"}, {"ad.replication": "WIN-5HFC3SICV2O", "ad.replication.destination": "WIN-2VQA64TFUK9", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "minder", "ad.replication.naming.context": "CN=Schema,CN=Configuration,DC=motadata,DC=in", "ad.replication.status": "fail"}]}, "errors": []}, "172.16.9.84": {"result": {"ad.replication": [{"ad.replication": "WIN-2GBVTG56", "ad.replication.destination": "WIN-25GC4L7VNK5", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "ad2016", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "DC=ad2016,DC=local", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:30:33", "ad.replication.error.code": "1722", "ad.replication.error.message": "The RPC server is unavailable."}, {"ad.replication": "WIN-2GBVTG56", "ad.replication.destination": "WIN-25GC4L7VNK5", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "ad2016", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "CN=Configuration,DC=ad2016,DC=local", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:29:09", "ad.replication.error.code": "1722", "ad.replication.error.message": "The RPC server is unavailable."}, {"ad.replication": "WIN-2GBVTG56", "ad.replication.destination": "WIN-25GC4L7VNK5", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "ad2016", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "CN=Schema,CN=Configuration,DC=ad2016,DC=local", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:29:51", "ad.replication.error.code": "1722", "ad.replication.error.message": "The RPC server is unavailable."}, {"ad.replication": "WIN-2GBVTG56", "ad.replication.destination": "WIN-25GC4L7VNK5", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "ad2016", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "DC=DomainDnsZones,DC=ad2016,DC=local", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:29:09", "ad.replication.error.code": "1256", "ad.replication.error.message": "The remote system is not available. For information about network troubleshooting, see Windows Help."}, {"ad.replication": "WIN-2GBVTG56", "ad.replication.destination": "WIN-25GC4L7VNK5", "ad.replication.site": "Default-First-Site-Name", "ad.replication.domain.name": "ad2016", "ad.replication.protocol": "RPC", "ad.replication.naming.context": "DC=ForestDnsZones,DC=ad2016,DC=local", "ad.replication.status": "fail", "ad.replication.last.attempted": "2022-06-08 10:29:09", "ad.replication.error.code": "1256", "ad.replication.error.message": "The remote system is not available. For information about network troubleshooting, see Windows Help."}]}, "errors": []}}