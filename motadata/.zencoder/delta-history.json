{"snapshots": {"/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/main/java/com/mindarray/visualization/VisualizationConstants.java": {"filePath": "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/main/java/com/mindarray/visualization/VisualizationConstants.java", "baseContent": "/*\n *   Copyright (c) Motadata 2025. All rights reserved.\n *\n *   This source code is the property of Motadata and constitutes\n *   proprietary and confidential information. Unauthorized copying, distribution,\n *   modification, or use of this file, via any medium, is strictly prohibited\n *   unless prior written permission is obtained from Motadata.\n *\n *   Unauthorized access or use of this software may result in legal action\n *   and/or prosecution to the fullest extent of the law.\n *\n *   This software is provided \"AS IS,\" without warranties of any kind, express\n *   or implied, including but not limited to implied warranties of\n *   merchantability or fitness for a particular purpose. In no event shall\n *   Motadata be held liable for any damages arising from the use\n *   of this software.\n *\n *   For inquiries, contact: <EMAIL>\n *\n */\n\n/* Change Logs:\n *   Date          Author              Notes\n *   2025-02-28   <PERSON><PERSON>       Added Support for Log,trap and Flow in Event Stream.\n *   2025-03-03   Chopra Deven       MOTADATA-4973: Converted long object.id into the small object.id in RUNBOOK_WORKLOG request.\n *   2025-03-25   Chopra Deven       MOTADATA-5299: Added more timelines into VisualizationTimeline for forecast policy.\n *   2025-03-06   Pruthviraj         MOTADATA-5331 : netroute.metric and availability added in datastore types and datasource added for netroute\n *   2025-04-17   Chopra Deven       MOTADATA-5827 : Group and Tag available as Column in case of multiple instance types\n *   2025-03-25   Umang Sharma       Added Support for Status Flap Metric.\n * 9-May-2025\t\tSankalp\t\t\tMOTADATA-5949: Added template for moxa switch\n *   2025-05-08   Umang Sharma       Added Support Raw in Metric Explorer by removing object.id\n *   2025-05-08   Darshan Parmar     MOTADATA-6084:  HPE Primera Template\n *   2025-05-09   Darshan Parmar     MOTADATA-6055 :  HPE 3PAR Template\n *   2025-05-09\t\tSankalp\t\t\t MOTADATA-5949: Added template for moxa switch\n *   2025-05-21   Darshan Parmar     MOTADATA-6184 :  Dell EMC Unity Template\n *   2025-05-26\t\tJenil\t\t\t MOTADATA-6220: Added template for windows snmp and linux snmp\n *   2025-23-05   Umang Sharma       MOTADATA-6344: Add query.start.time to Visualization Query for capturing current timestamp\n *   2025-05-23   Chopra Deven       MOTADATA-6187: merging \"instance.ip\" column for join.type = \"instance.ip\".\n *  June-5-2025     Umang Sharma            Added Support for widget/Alert for Netroute.\n *  June-24-2025    Darshan Parmar   MOTADATA-6502: Added EMAIL_GATEWAY tamplate.\n *  June-25-2025   Umang Sharma      MOTADATA-6585 Added support for instance level tag\n *  June-24-2025    Darshan Parmar   MOTADATA-6591: Added Tanzu Kubernetes tamplate.\n *  June-25-2025   Priyansh Sindhav   MOTADATA-6579 Added support for NSXT\n *  08-July-2025    Priyansh          MOTADATA-6400: MongoDB Application support\n *  June-26-2025   Umang Sharma      MOTADATA6552 Added Priority to drilldown filter if both filters are present.\n *  July-07-2025   Jay Patel         MOTADATA-6675: Added Kubernetes template\n *  10-July-2025    Nikunj          MOTADATA-6659: Handled exception for each row packing.\n *  Jul-04-2025   Jenil Kajavadara   MOTADATA-6380 Added support for Oracle Weblogic application\n\n */\n\npackage com.mindarray.visualization;\n\n/**\n * This class defines constants, enums, and utility methods used throughout the visualization system.\n * <p>\n * VisualizationConstants serves as a central repository for:\n * <ul>\n *   <li>String constants for visualization parameters and properties</li>\n *   <li>Enumeration types for visualization categories, data sources, and result types</li>\n *   <li>Utility methods for processing visualization data</li>\n *   <li>Functions for packing and unpacking visualization results</li>\n *   <li>Methods for filtering and qualifying entities for visualization</li>\n * </ul>\n * <p>\n * This class is used by all components in the visualization package to ensure\n * consistent naming, data processing, and result formatting across the system.\n * <p>\n * The class contains several inner enum types that define specific aspects of visualizations:\n * <ul>\n *   <li>{@link VisualizationJoinType} - Defines how data from different sources is joined</li>\n *   <li>{@link VisualizationGrouping} - Defines grouping options for visualization data</li>\n *   <li>{@link VisualizationDataSource} - Defines available data sources for visualizations</li>\n *   <li>{@link VisualizationCategory} - Defines categories of visualizations</li>\n *   <li>{@link VisualizationResultType} - Defines types of visualization results</li>\n *   <li>{@link VisualizationTimeline} - Defines time ranges for visualizations</li>\n * </ul>\n */\n\nimport com.mindarray.Bootstrap;\nimport com.mindarray.ErrorMessageConstants;\nimport com.mindarray.GlobalConstants;\nimport com.mindarray.api.*;\nimport com.mindarray.config.ConfigConstants;\nimport com.mindarray.datastore.DatastoreConstants;\nimport com.mindarray.db.DBConstants;\nimport com.mindarray.eventbus.EventBusConstants;\nimport com.mindarray.flow.FlowEngineConstants;\nimport com.mindarray.integration.IntegrationEngine;\nimport com.mindarray.log.LogEngineConstants;\nimport com.mindarray.nms.NMSConstants;\nimport com.mindarray.nms.SNMPTrapProcessor;\nimport com.mindarray.policy.PolicyEngineConstants;\nimport com.mindarray.report.ReportConstants;\nimport com.mindarray.store.*;\nimport com.mindarray.util.*;\nimport io.vertx.core.buffer.Buffer;\nimport io.vertx.core.json.JsonArray;\nimport io.vertx.core.json.JsonObject;\nimport org.apache.commons.lang3.StringUtils;\n\nimport java.io.File;\nimport java.math.BigInteger;\nimport java.nio.ByteBuffer;\nimport java.nio.ByteOrder;\nimport java.nio.charset.StandardCharsets;\nimport java.util.*;\nimport java.util.concurrent.atomic.AtomicReference;\nimport java.util.stream.Collectors;\n\nimport static com.mindarray.GlobalConstants.*;\nimport static com.mindarray.api.AIOpsObject.OBJECT_IP;\nimport static com.mindarray.api.AIOpsObject.OBJECT_TARGET;\nimport static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;\nimport static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;\nimport static com.mindarray.datastore.DatastoreConstants.DataType.*;\nimport static com.mindarray.datastore.DatastoreConstants.MAPPER_DATA_CATEGORIES;\nimport static com.mindarray.eventbus.EventBusConstants.*;\nimport static com.mindarray.nms.NMSConstants.*;\nimport static java.util.Map.entry;\n\npublic class VisualizationConstants\n{\n\n    private static final Logger LOGGER = new Logger(VisualizationConstants.class, MOTADATA_VISUALIZATION, \"Visualization Constants\");\n\n    public static final String VISUALIZATION_TYPE = \"visualization.type\";\n\n    public static final String VISUALIZATION_NAME = \"visualization.name\";\n\n    public static final String VISUALIZATION_GROUP_TYPE = \"visualization.group.type\";\n\n    public static final String VISUALIZATION_CATEGORY = \"visualization.category\";\n\n    public static final String VISUALIZATION_TIMELINE = \"visualization.timeline\";\n\n    public static final String VISUALIZATION_TIMEZONE = \"visualization.timezone\";\n\n    public static final String VISUALIZATION_DATA_SOURCES = \"visualization.data.sources\";\n\n    public static final String VISUALIZATION_RESULT_BY = \"visualization.result.by\";\n\n    public static final String QUERY_ID = \"query.id\";\n\n    public static final String SUB_QUERY_ID = \"sub.query.id\";\n\n    public static final String ENTITY_KEYS = \"entity.keys\";\n\n    public static final String INSTANCE_TYPE = \"instance.type\";\n\n    public static final String OBJECT_FILTER = \"object.filter\";\n\n    public static final String ADMIN_ROLE = \"admin.role\";\n\n    public static final String VISUALIZATION_STREAMING = \"visualization.streaming\";\n\n    public static final String FILTER_KEYS = \"filter.keys\";\n\n    public static final String DISCARD_DUMMY_ROWS = \"discard.dummy.rows\";\n\n    public static final String VISUALIZATION_DATA_FILTER_JSON_TEMPLATE = \"{\\\"data.filter\\\":{\\\"operator\\\":\\\"and\\\",\\\"filter\\\":\\\"include\\\",\\\"groups\\\":[{\\\"filter\\\":\\\"include\\\",\\\"operator\\\":\\\"and\\\",\\\"conditions\\\":[]}]}}\";\n\n    public static final String VISUALIZATION_RESULT_FILTER_JSON_TEMPLATE = \"{\\\"result.filter\\\":{\\\"operator\\\":\\\"and\\\",\\\"filter\\\":\\\"include\\\",\\\"conditions\\\":[{\\\"value\\\":[],\\\"operand\\\":\\\"\\\",\\\"operator\\\":\\\"in\\\"}]}}\";\n\n    public static final String VISUALIZATION_DRILL_DOWN_FILTER_JSON_TEMPLATE = \"{\\\"drill.down.filter\\\":{\\\"operator\\\":\\\"and\\\",\\\"filter\\\":\\\"include\\\",\\\"groups\\\":[{\\\"filter\\\":\\\"include\\\",\\\"operator\\\":\\\"and\\\",\\\"conditions\\\":[]}]}}\";\n\n    public static final String VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE = \"{\\\"filter\\\":\\\"include\\\",\\\"operator\\\":\\\"and\\\",\\\"conditions\\\":[]}\";\n\n    public static final String VISUALIZATION_SYSTEM_PROCESS_DATASOURCE_JSON_TEMPLATE = \"{\\\"visualization.data.sources\\\":[{\\\"visualization.result.by\\\":[\\\"monitor\\\"],\\\"type\\\":\\\"availability\\\",\\\"join.type\\\":\\\"all\\\",\\\"join.result\\\":\\\"availability\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[{\\\"data.point\\\":\\\"system.process~uptime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~downtime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~suspendtime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~unknowntime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~maintenancetime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~disabletime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~unreachabletime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]}]},{\\\"visualization.result.by\\\":[\\\"monitor\\\"],\\\"type\\\":\\\"availability\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[{\\\"data.point\\\":\\\"system.process~uptime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~downtime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~suspendtime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~unknowntime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~maintenancetime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~disabletime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~unreachabletime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]}]}]}\";\n\n    public static final String VISUALIZATION_SYSTEM_PROCESS_AVAILABILITY_STATISTICS_DATASOURCE_JSON_TEMPLATE = \"{\\\"visualization.data.sources\\\":[{\\\"visualization.result.by\\\":[\\\"monitor\\\"],\\\"type\\\":\\\"availability\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[{\\\"data.point\\\":\\\"system.process~uptime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~downtime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~suspendtime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~unknowntime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~maintenancetime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~disabletime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.process~unreachabletime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]}]}]}\";\n\n    public static final String VISUALIZATION_SYSTEM_SERVICE_DATASOURCE_JSON_TEMPLATE = \"{\\\"visualization.data.sources\\\":[{\\\"visualization.result.by\\\":[\\\"monitor\\\"],\\\"type\\\":\\\"availability\\\",\\\"join.type\\\":\\\"all\\\",\\\"join.result\\\":\\\"availability\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[{\\\"data.point\\\":\\\"system.service~uptime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~downtime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~suspendtime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~unknowntime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~maintenancetime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~disabletime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~unreachabletime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]}]},{\\\"visualization.result.by\\\":[\\\"monitor\\\"],\\\"type\\\":\\\"availability\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[{\\\"data.point\\\":\\\"system.service~uptime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~downtime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~suspendtime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~unknowntime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~maintenancetime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~disabletime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~unreachabletime.seconds\\\",\\\"aggregator\\\":\\\"sum\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]}]}]}\";\n\n    public static final String VISUALIZATION_SYSTEM_SERVICE_AVAILABILITY_STATISTICS_DATASOURCE_JSON_TEMPLATE = \"{\\\"visualization.data.sources\\\":[{\\\"visualization.result.by\\\":[\\\"monitor\\\"],\\\"type\\\":\\\"availability\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[{\\\"data.point\\\":\\\"system.service~uptime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~downtime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~suspendtime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~unknowntime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~maintenancetime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~disabletime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]},{\\\"data.point\\\":\\\"system.service~unreachabletime.percent\\\",\\\"aggregator\\\":\\\"avg\\\",\\\"entity.type\\\":\\\"Monitor\\\",\\\"entities\\\":[]}]}]}\";\n\n    public static final String QUERY_PROGRESS = \"query.progress\";\n\n    public static final String JOIN_TYPE = \"join.type\";\n\n    public static final Set<String> PASSOVER_INSTANCES = Set.of(\"application\", \"ipsla\", \"cisco.vedge.tunnel\", \"cisco.vedge.tloc\", \"cisco.vedge.interface\");\n\n    public static final String JOIN_BY_COLUMNS = \"join.columns\";\n\n    public static final String JOIN_RESULT = \"join.result\";\n\n    public static final String JOIN_ALIAS = \"join.alias\";\n\n    public static final String SORT_BY = \"sorting.column\";\n\n    public static final String PUBLISH_SUB_QUERY_PROGRESS = \"publish.sub.query.progress\";\n\n    public static final int DATA_TYPE_INT32 = 48;\n\n    public static final int DATA_TYPE_INT64 = 112;\n\n    public static final int DATA_TYPE_FLOAT32 = 160;\n\n    public static final int DATA_TYPE_STRING = 176;\n\n    public static final String VISUALIZATION_TOP_N_CONTEXT = \"{\\\"id\\\":-1,\\\"visualization.timeline\\\":{\\\"relative.timeline\\\":\\\"today\\\"},\\\"visualization.category\\\":\\\"TopN\\\",\\\"visualization.type\\\":\\\"Grid\\\",\\\"visualization.data.sources\\\":[{\\\"type\\\":\\\"flow\\\",\\\"category\\\":\\\"flow\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[]}],\\\"visualization.properties\\\":{\\\"grid\\\":{\\\"searchable\\\":\\\"yes\\\",\\\"column.selection\\\":\\\"no\\\",\\\"header\\\":\\\"yes\\\",\\\"style\\\":{\\\"header.font.size\\\":\\\"small\\\"},\\\"sorting\\\":{\\\"limit\\\":300,\\\"order\\\":\\\"desc\\\",\\\"column\\\":\\\"@@@\\\"}}},\\\"visualization.result.by\\\":[],\\\"granularity\\\":{\\\"value\\\":5,\\\"unit\\\":\\\"m\\\"}}\";\n\n    public static final String VISUALIZATION_CHART_GRID_CONTEXT = \"{\\\"visualization.timeline\\\":{\\\"relative.timeline\\\":\\\"today\\\",\\\"visualization.time.range.inclusive\\\":\\\"no\\\"},\\\"visualization.granularity\\\":\\\"5 m\\\",\\\"visualization.properties\\\":{\\\"grid\\\":{\\\"searchable\\\":\\\"yes\\\",\\\"column.selection\\\":\\\"no\\\",\\\"header\\\":\\\"yes\\\",\\\"style\\\":{\\\"header.font.size\\\":\\\"small\\\"}}},\\\"granularity\\\":{\\\"unit\\\":\\\"m\\\",\\\"value\\\":5},\\\"user.name\\\":\\\"admin\\\",\\\"id\\\":-1,\\\"visualization.category\\\":\\\"Chart\\\",\\\"visualization.data.sources\\\":[{\\\"type\\\":\\\"metric\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[]}],\\\"visualization.type\\\":\\\"Grid\\\"}\";\n\n    public static final String VISUALIZATION_GAUGE_CONTEXT = \"{\\\"id\\\":-1,\\\"visualization.timeline\\\":{\\\"relative.timeline\\\":\\\"today\\\"},\\\"visualization.category\\\":\\\"Gauge\\\",\\\"visualization.type\\\":\\\"MetroTile\\\",\\\"visualization.data.sources\\\":[{\\\"type\\\":\\\"flow\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{}},\\\"data.points\\\":[]}],\\\"visualization.properties\\\":{\\\"gauge\\\":{\\\"style\\\":{\\\"chart.legend\\\":\\\"no\\\",\\\"chart.label\\\":\\\"no\\\",\\\"type\\\":\\\"number\\\",\\\"font.size\\\":\\\"small\\\"}}},\\\"granularity\\\":{\\\"value\\\":5,\\\"unit\\\":\\\"m\\\"}}\";\n\n    public static final String VISUALIZATION_CHART_CONTEXT = \"{\\\"visualization.timeline\\\":{\\\"relative.timeline\\\":\\\"today\\\",\\\"visualization.time.range.inclusive\\\":\\\"no\\\"},\\\"visualization.granularity\\\":\\\"5 m\\\",\\\"visualization.properties\\\":{\\\"chart\\\":{\\\"vertical.legend\\\":\\\"no\\\",\\\"highchart.settings\\\":{},\\\"chart.legend\\\":\\\"yes\\\",\\\"sorting\\\":{\\\"limit\\\":10,\\\"order\\\":\\\"desc\\\"},\\\"chart.label\\\":\\\"no\\\",\\\"rotation.angle\\\":0}},\\\"granularity\\\":{\\\"unit\\\":\\\"m\\\",\\\"value\\\":5},\\\"ui.event.uuid\\\":\\\"5cefd2d9-69c2-43ab-8d85-12b2ef29cfa5\\\",\\\"user.name\\\":\\\"admin\\\",\\\"id\\\":-1,\\\"visualization.category\\\":\\\"Chart\\\",\\\"visualization.data.sources\\\":[],\\\"visualization.type\\\":\\\"Area\\\",\\\"session-id\\\":\\\"3bb9c217-5c87-4652-81e6-5797be89f0a1\\\"}\";\n    public static final String VISUALIZATION_PROPERTIES = \"visualization.properties\";\n    public static final String SORTING = \"sorting\";\n    public static final String QUERY_CONTEXT = \"query.context\";\n    public static final int MAX_BAR_CHART_LIMIT = 150;\n    public static final String PAGINATION_QUERY = \"pagination.query\";\n    public static final String VISUALIZATION_GRANULARITY = \"visualization.granularity\";\n    public static final String RESULT_FILTER = \"result.filter\";\n    public static final String DRILL_DOWN_FILTER = \"drill.down.filter\";\n    public static final String QUERY_PRIORITY = \"query.priority\";\n    public static final Map<String, String> REPLACEABLE_COLUMNS = Map.ofEntries(Map.entry(SNMPTrapProcessor.SNMP_TRAP_OID, \"trap.name\"), Map.entry(PolicyEngineConstants.POLICY_ID, \"policy.name\"), Map.entry(NetRoute.NETROUTE_ID, NetRoute.NETROUTE_NAME));\n    public static final String TYPE = \"type\";\n    public static final String CATEGORY = \"category\";\n    public static final String PLUGINS = \"plugins\";\n    public static final String PLUGIN = \"plugin\";\n    public static final String PLUGIN_ENTITIES = \"plugin.entities\";\n    public static final String DATA_POINTS = \"data.points\";\n    public static final String DATA_POINT = \"data.point\";\n    public static final String DATA_POINT_ALIAS = \"data.point.alias\";\n    public static final String DATA_TYPE = \"data.type\";\n    public static final String DATA_POINT_LENGTH = \"data.point.length\";\n    public static final String VISUALIZATION_ROWS = \"rows\";\n    public static final int MAX_AGGREGATION_FUNCTIONS = 16;\n    public static final int MAX_EVENT_AGGREGATION_FUNCTIONS = 10;\n    public static final Map<String, JsonObject> VISUALIZATION_APP_DATA_SOURCES = Map.ofEntries(\n            Map.entry(NMSConstants.SYSTEM_PROCESS, new JsonObject(VISUALIZATION_SYSTEM_PROCESS_DATASOURCE_JSON_TEMPLATE)),\n            Map.entry(NMSConstants.SYSTEM_SERVICE, new JsonObject(VISUALIZATION_SYSTEM_SERVICE_DATASOURCE_JSON_TEMPLATE)),\n            Map.entry(NMSConstants.SYSTEM_PROCESS_AVAILABILITY_STATISTICS, new JsonObject(VISUALIZATION_SYSTEM_PROCESS_AVAILABILITY_STATISTICS_DATASOURCE_JSON_TEMPLATE)),\n            Map.entry(NMSConstants.SYSTEM_SERVICE_AVAILABILITY_STATISTICS, new JsonObject(VISUALIZATION_SYSTEM_SERVICE_AVAILABILITY_STATISTICS_DATASOURCE_JSON_TEMPLATE)));\n    public static final String RELATIVE_TIMELINE = \"relative.timeline\";\n    public static final String FROM_DATE = \"from.date\";\n    public static final String TO_DATE = \"to.date\";\n    public static final String FROM_TIME = \"from.time\";\n    public static final String TO_TIME = \"to.time\";\n    public static final String FROM_DATETIME = \"from.datetime\";\n    public static final String DUMMY_FIELDS = \"dummy.fields\";\n    public static final String VISUALIZATION_INVENTORY_TYPES = \"visualization.inventory.types\";\n    public static final String VISUALIZATION_EXTRA_COLUMNS = \"visualization.extra.columns\";\n    public static final String VISUALIZATION_DECODE_RESPONSE = \"decode.response\";\n    public static final String VISUALIZATION_RESULT_TYPE = \"visualization.result.type\";\n    public static final Set<String> PASSOVER_FILTER_INSTANCES = Set.of(NETWORK_SERVICE, INTERFACE, SYSTEM_PROCESS, ESXI_VM, SYSTEM_SERVICE, CITRIX_XEN_VM,\n            HYPERV_VM, CISCO_WIRELESS_ACCESS_POINT, ARUBA_WIRELESS_ACCESS_POINT, RUCKUS_WIRELESS_ACCESS_POINT, NUTANIX_VM);\n\n    //common constant\n    public static final String TO_DATETIME = \"to.datetime\";\n    public static final String AGGREGATOR = \"aggregator\";\n    public static final String TEMPLATE_ID = \"template.id\";\n    public static final String RESERVED_METRICS = \"~duration\";\n    public static final String VISUALIZATION_TIME_RANGE_INCLUSIVE = \"visualization.time.range.inclusive\";\n    public static final String GROUP_APP = \"application\";\n    public static final String GROUP_VM = \"vm\";\n    public static final String GROUP_ACCESS_POINT = \"access point\";\n    public static final String GROUP_PROCESS = \"process\";\n    public static final String GROUP_BY = \"group.by\";\n    public static final String MULTI_AVAILABILITY_COLUMNS = \"{\\\"#counter#~uptime.seconds^sum\\\":112,\\\"#counter#~uptime.percent^avg\\\":160,\\\"#counter#~downtime.seconds^sum\\\":112,\\\"#counter#~downtime.percent^avg\\\":160}\";\n    public static final String MAX_RECORDS = \"max.records\";\n    public static final String CONTAINER_TYPE = \"container.type\";\n    public static final long DEFAULT_NETWORK_TEMPLATE = 10000000000155L;\n    public static final String TIMESTAMP = \"Timestamp\";\n    public static final Map<String, String> VISUALIZATION_COMPLIANCE_TABLE_MAPPINGS = Map.ofEntries(\n            Map.entry(VisualizationDataSource.COMPLIANCE_TRAIL.getName(), DBConstants.TABLE_COMPLIANCE_TRAIL),\n            Map.entry(VisualizationDataSource.COMPLIANCE_STATS_ENTITY.getName(), DBConstants.TABLE_COMPLIANCE_STATS_ENTITY),\n            Map.entry(VisualizationDataSource.COMPLIANCE_STATS_POLICY.getName(), DBConstants.TABLE_COMPLIANCE_STATS_POLICY));\n    // container types\n    public static final String CONTAINER_TYPE_REPORT = \"report\";\n    static final String VISUALIZATION_TYPE_APPLICATION_AVAILABILITY = \"Application Availability\";\n    static final String VISUALIZATION_TYPE_APPLICATION_STATUS = \"Application Status\";\n    static final String VISUALIZATION_TYPE_APPLICATION_AVAILABILITY_TIME_SERIES = \"Application Availability Time Series\";\n    static final String VISUALIZATION_TYPE_AVAILABILITY_TIME_SERIES = \"Availability Time Series\";\n    static final String VISUALIZATION_TYPE_TODAY_AVAILABILITY = \"Today's Availability\";\n    static final boolean VISUALIZATION_CACHING_ENABLED = MotadataConfigUtil.cachingEnabled();\n    static final String VISUALIZATION_SESSIONS = \"sessions\";\n    static final String INDEXABLE_COLUMNS = \"indexable.columns\";\n    static final String VISUALIZATION_TAGS = \"visualization.tags\";\n    //Query date and time constant\n    static final String VISUALIZATION_TYPE_CHILD_VISUALIZATION = \"child.visualization\";\n    static final String VISUALIZATION_CHILD_CATEGORIES = \"visualization.child.categories\";\n    static final String VISUALIZATION_DRILL_DOWN = \"drill.down\";\n    static final String QUERY_ABORT_REQUIRED = \"query.abort.required\";\n    static final int INTERVAL_SECONDS = MotadataConfigUtil.getDBQueryAbortTimerSeconds();\n    static final String ACTIVE_POLICY_DATA_POINTS = \"{\\\"data.points\\\":[{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"severity\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"policy.name\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"instance\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"policy.type\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"policy.id\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"metric\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"value\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"object.id\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"duration\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"previous.flap.timestamp\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"policy.note\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"acknowledge\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"policy.acknowledge.by\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"policy.acknowledge.time\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"event.timestamp\\\"},{\\\"aggregator\\\":\\\"last\\\",\\\"data.point\\\":\\\"policy.tags\\\"}]}\";\n    static final String CUMULATIVE_STATUS_FLAP_DATA_POINTS = \"{\\\"data.points\\\":[{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"duration\\\"},{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"status.flap.history\\\"},{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"object.id\\\"}]}\";\n    static final String HOURLY_STATUS_FLAP_DATA_POINTS = \"{\\\"data.points\\\":[{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"duration\\\"},{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"status.flap.history\\\"}]}\";\n    static final String INSTANCE_STATUS_FLAP_DATA_POINTS = \"{\\\"data.points\\\":[{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"duration\\\"},{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"status.flap.history\\\"},{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"object.id\\\"},{\\\"aggregator\\\":\\\"\\\",\\\"data.point\\\":\\\"instance\\\"}]}\";\n    static final String AVAILABILITY_DATA_POINTS = \"{\\\"data.points\\\":[{\\\"aggregator\\\":\\\"avg\\\",\\\"data.point\\\":\\\"#counter#uptime.percent\\\"},{\\\"aggregator\\\":\\\"avg\\\",\\\"data.point\\\":\\\"#counter#downtime.percent\\\"},{\\\"aggregator\\\":\\\"avg\\\",\\\"data.point\\\":\\\"#counter#maintenancetime.percent\\\"},{\\\"aggregator\\\":\\\"avg\\\",\\\"data.point\\\":\\\"#counter#unreachabletime.percent\\\"},{\\\"aggregator\\\":\\\"avg\\\",\\\"data.point\\\":\\\"#counter#disabletime.percent\\\"},{\\\"aggregator\\\":\\\"avg\\\",\\\"data.point\\\":\\\"#counter#suspendtime.percent\\\"},{\\\"aggregator\\\":\\\"avg\\\",\\\"data.point\\\":\\\"#counter#unknowntime.percent\\\"},{\\\"aggregator\\\":\\\"sum\\\",\\\"data.point\\\":\\\"#counter#uptime.seconds\\\"},{\\\"aggregator\\\":\\\"sum\\\",\\\"data.point\\\":\\\"#counter#downtime.seconds\\\"},{\\\"aggregator\\\":\\\"sum\\\",\\\"data.point\\\":\\\"#counter#maintenancetime.seconds\\\"},{\\\"aggregator\\\":\\\"sum\\\",\\\"data.point\\\":\\\"#counter#unreachabletime.seconds\\\"},{\\\"aggregator\\\":\\\"sum\\\",\\\"data.point\\\":\\\"#counter#disabletime.seconds\\\"},{\\\"aggregator\\\":\\\"sum\\\",\\\"data.point\\\":\\\"#counter#suspendtime.seconds\\\"},{\\\"aggregator\\\":\\\"sum\\\",\\\"data.point\\\":\\\"#counter#unknowntime.seconds\\\"}]}\";\n    static final JsonArray VM_DATA_POINTS = new JsonArray().add(NMSConstants.CITRIX_XEN_VM + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.ESXI_VM + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.HYPERV_VM + INSTANCE_SEPARATOR + STATUS).add(NUTANIX_VM + INSTANCE_SEPARATOR + STATUS);\n    static final JsonArray ACCESS_POINT_DATA_POINTS = new JsonArray().add(NMSConstants.CISCO_WIRELESS_ACCESS_POINT + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.RUCKUS_WIRELESS_ACCESS_POINT + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.ARUBA_WIRELESS_ACCESS_POINT + INSTANCE_SEPARATOR + STATUS);\n    static final JsonArray APPLICATION_DATA_POINTS = new JsonArray().add(NMSConstants.SYSTEM_PROCESS + INSTANCE_SEPARATOR + STATUS).add(NMSConstants.SYSTEM_SERVICE + INSTANCE_SEPARATOR + STATUS);\n    static final String END_TIME = \"end.time\";\n    static final String START_TIME = \"start.time\";\n    static final String COMPOSITE_QUERY_SIZE = \"composite.query.size\";\n    static final String QUERY_CREATION_TIME = \"query.creation.time\"; //This is only to send start.time to DB\n    private static final Map<String, Integer> DATASTORE_TYPES = Map.ofEntries(Map.entry(VisualizationDataSource.PERFORMANCE_METRIC.getName(), DatastoreConstants.DatastoreType.PERFORMANCE_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.OBJECT_AVAILABILITY.getName(), DatastoreConstants.DatastoreType.OBJECT_STATUS_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.LOG.getName(), DatastoreConstants.DatastoreType.LOG.ordinal()),\n            Map.entry(VisualizationDataSource.SLO_INSTANCE.getName(), DatastoreConstants.DatastoreType.SLO_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.SLO.getName(), DatastoreConstants.DatastoreType.SLO_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.SLO_INSTANCE_FLAP.getName(), DatastoreConstants.DatastoreType.SLO_FLAP_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.SLO_FLAP.getName(), DatastoreConstants.DatastoreType.SLO_FLAP_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.FLOW.getName(), DatastoreConstants.DatastoreType.FLOW.ordinal()),\n            Map.entry(VisualizationDataSource.TRAP.getName(), DatastoreConstants.DatastoreType.TRAP.ordinal()),\n            Map.entry(VisualizationDataSource.USER_NOTIFICATION.getName(), DatastoreConstants.DatastoreType.NOTIFICATION.ordinal()),\n            Map.entry(VisualizationDataSource.AUDIT.getName(), DatastoreConstants.DatastoreType.AUDIT.ordinal()),\n            Map.entry(VisualizationDataSource.RUNBOOK_WORKLOG.getName(), DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal()),\n            Map.entry(VisualizationDataSource.POLICY_RESULT.getName(), DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()),\n            Map.entry(VisualizationDataSource.TRAP_ACKNOWLEDGEMENT.getName(), DatastoreConstants.DatastoreType.TRAP_ACKNOWLEDGEMENT.ordinal()),\n            Map.entry(VisualizationDataSource.POLICY_FLAP.getName(), DatastoreConstants.DatastoreType.METRIC_POLICY_FLAP_HISTORY.ordinal()),\n            Map.entry(VisualizationDataSource.TRAP_FLAP.getName(), DatastoreConstants.DatastoreType.TRAP_FLAP_HISTORY.ordinal()),\n            Map.entry(VisualizationDataSource.HEALTH_METRIC.getName(), DatastoreConstants.DatastoreType.HEALTH_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.CORRELATED_METRIC.getName(), DatastoreConstants.DatastoreType.CORRELATED_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName(), DatastoreConstants.DatastoreType.OBJECT_STATUS_FLAP_HISTORY.ordinal()),\n            Map.entry(VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName(), DatastoreConstants.DatastoreType.OBJECT_STATUS_FLAP_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.COMPLIANCE.getName(), DatastoreConstants.DatastoreType.COMPLIANCE.ordinal()),\n            Map.entry(VisualizationDataSource.STATIC_METRIC.getName(), DatastoreConstants.DatastoreType.STATIC_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.NETROUTE_METRIC.getName(), DatastoreConstants.DatastoreType.NETROUTE_METRIC.ordinal()),\n            Map.entry(VisualizationDataSource.NETROUTE_AVAILABILITY.getName(), DatastoreConstants.DatastoreType.NETROUTE_STATUS_METRIC.ordinal()));\n    private static final Map<String, JsonObject> VISUALIZATION_INVENTORY_WIDGETS = Map.ofEntries(Map.entry(\"Service Check Inventory Summary\", new JsonObject().put(CATEGORY, NMSConstants.Category.SERVICE_CHECK.getName()).put(\"queryCustomMonitoringFields\", true).put(\"queryActiveAlerts\", true).put(\"instance\", EMPTY_VALUE)),\n            Map.entry(\"Server Inventory Summary\", new JsonObject().put(CATEGORY, Category.SERVER.getName()).put(\"queryCustomMonitoringFields\", true).put(\"queryActiveAlerts\", true).put(\"instance\", EMPTY_VALUE)),\n            Map.entry(\"System Service Inventory Summary\", new JsonObject().put(CATEGORY, Category.SERVER.getName()).put(\"queryCustomMonitoringFields\", false).put(\"queryActiveAlerts\", false).put(\"instance\", SYSTEM_SERVICE)),\n            Map.entry(\"System Process Inventory Summary\", new JsonObject().put(CATEGORY, Category.SERVER.getName()).put(\"queryCustomMonitoringFields\", false).put(\"queryActiveAlerts\", false).put(\"instance\", SYSTEM_PROCESS)),\n            Map.entry(\"Network Inventory Summary\", new JsonObject().put(CATEGORY, Category.NETWORK.getName()).put(\"queryCustomMonitoringFields\", true).put(\"queryActiveAlerts\", true).put(\"instance\", EMPTY_VALUE)),\n            Map.entry(\"Virtualization Inventory Summary\", new JsonObject().put(CATEGORY, Category.VIRTUALIZATION.getName()).put(\"queryCustomMonitoringFields\", true).put(\"queryActiveAlerts\", true).put(\"instance\", EMPTY_VALUE)),\n            Map.entry(\"Other Inventory Summary\", new JsonObject().put(CATEGORY, Category.OTHER.getName()).put(\"queryCustomMonitoringFields\", true).put(\"queryActiveAlerts\", true).put(\"instance\", EMPTY_VALUE)),\n            Map.entry(\"WAN Link Inventory Summary\", new JsonObject().put(CATEGORY, Category.NETWORK.getName()).put(\"queryCustomMonitoringFields\", false).put(\"queryActiveAlerts\", false).put(\"instance\", IPSLA)),\n            Map.entry(\"HCI Inventory Summary\", new JsonObject().put(CATEGORY, Category.HCI.getName()).put(\"queryCustomMonitoringFields\", true).put(\"queryActiveAlerts\", true).put(\"instance\", EMPTY_VALUE)),\n            Map.entry(\"SDN Inventory Summary\", new JsonObject().put(CATEGORY, Category.SDN.getName()).put(\"queryCustomMonitoringFields\", true).put(\"queryActiveAlerts\", true).put(\"instance\", EMPTY_VALUE)),\n            Map.entry(\"Container Inventory Summary\", new JsonObject().put(CATEGORY, Category.SERVER.getName()).put(\"queryCustomMonitoringFields\", false).put(\"queryActiveAlerts\", false).put(\"instance\", \"docker.container\")),\n            Map.entry(\"Container Orchestration Inventory Summary\", new JsonObject().put(CATEGORY, Category.CONTAINER_ORCHESTRATION.getName()).put(\"queryCustomMonitoringFields\", true).put(\"queryActiveAlerts\", true).put(\"instance\", EMPTY_VALUE)),\n            Map.entry(\"Interface Inventory Summary\", new JsonObject().put(CATEGORY, Category.NETWORK.getName()).put(\"queryCustomMonitoringFields\", false).put(\"queryActiveAlerts\", false).put(\"instance\", INTERFACE))\n    );\n\n    public static JsonObject unpack(Buffer buffer, Logger logger, boolean alias, JsonObject query, boolean removeDummyRow, boolean join)\n    {\n        var result = new JsonObject();\n\n        try\n        {\n            //as of now not decoded sparkline as requirement not there\n            if (buffer.length() > 0)\n            {\n                var instance = EMPTY_VALUE;\n\n                var valid = true;\n\n                var position = 0;\n\n                result.put(QUERY_ID, buffer.getLongLE(position));\n                position = position + 8; // 8 byte parent query id\n\n                result.put(SUB_QUERY_ID, buffer.getLongLE(position));\n                position = position + 8; // 8 byte current query id\n\n                result.put(QUERY_PROGRESS, CommonUtil.getShort(buffer.getUnsignedByte(position)));\n                position = position + 1; // 1 byte for query progress up to 100%\n\n                result.put(\"rows\", buffer.getLongLE(position));\n                position = position + 8; // 1 byte for query progress up to 100%\n\n                result.put(\"response.time.ms\", buffer.getLongLE(position));\n                position = position + 8; // 1 byte for query progress up to 100%\n\n                var status = CommonUtil.getShort(buffer.getUnsignedByte(position));\n\n                position = position + 1;\n\n                if (status == 0 || status == 2)//if query failed status recieved from DB is either 0 and in case of abort from DB is status 2\n                {\n                    position = 38 + buffer.getIntLE(34);\n\n                    if (buffer.length() < position + 1)\n                    {\n                        valid = false;\n                    }\n\n                    result.put(ERROR, buffer.getString(38, 38 + buffer.getIntLE(34)));\n                }\n\n                if (valid)\n                {\n                    result.put(GlobalConstants.STATUS, status); // 1 byte query status success or failed (0/1 value)\n\n                    result.put(CATEGORY, CommonUtil.getShort(buffer.getUnsignedByte(position)));\n                    position = position + 1; // 1 byte visualization category (Gauge =0 ,TopNChart =1 ,TopNGrid =2 ,Histogram =3, Grid = 4)\n\n                    result.put(DATA_POINTS, CommonUtil.getShort(buffer.getUnsignedByte(position)));\n                    position = position + 1; // 1 byte number of columns\n\n                    if (buffer.length() > position)\n                    {\n                        result.put(VISUALIZATION_ROWS, buffer.getIntLE(position));\n                        position = position + 4; // 4 byte for number of rows counts\n\n                        if (query != null && query.containsKey(VISUALIZATION_CATEGORY) && query.getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.FORECAST.getName()) && !query.getString(VISUALIZATION_TYPE, VisualizationCategory.HISTOGRAM.getName()).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))\n                        {\n                            result.put(\"prediction.index\", buffer.getShortLE(position));\n                            position = position + 2; // 2 byte for predicted row index\n                        }\n\n                        var rows = new ArrayList<JsonObject>();\n\n                        for (var i = 0; i < result.getInteger(VISUALIZATION_ROWS); i++)\n                        {\n\n                            rows.add(new JsonObject());\n                        }\n\n                        var dummyRows = new HashSet<Integer>();\n\n                        for (var i = 0; i < result.getInteger(DATA_POINTS); i++)\n                        {\n                            var context = new JsonObject();\n\n                            result.put(\"total.bytes\", buffer.getUnsignedIntLE(position));\n                            position = position + 4; // 4 byte for number of rows counts\n\n                            context.put(AGGREGATOR, buffer.getUnsignedByte(position));\n                            position = position + 1; // 1 byte column aggregation type\n\n                            context.put(DATA_TYPE, buffer.getUnsignedByte(position));\n                            position = position + 1; // 1 byte column datatype\n\n                            context.put(DATA_POINT_LENGTH, buffer.getShortLE(position));\n                            position = position + 2; // 2 byte column length\n\n                            context.put(DATA_POINT, CommonUtil.getString(buffer.getString(position, position + context.getInteger(DATA_POINT_LENGTH))));\n                            position = position + context.getInteger(DATA_POINT_LENGTH); //  moving bytes' length of column name size\n\n                            context.put(\"alias.name.length\", buffer.getShortLE(position));\n                            position = position + 2; // 2 byte alias name length\n\n                            context.put(DATA_POINT_ALIAS, CommonUtil.getString(buffer.getString(position, position + context.getInteger(\"alias.name.length\"))));\n                            position = position + context.getInteger(\"alias.name.length\"); //  moving bytes' length of column name size\n\n                            var dataType = CommonUtil.getInteger(context.getValue(DATA_TYPE));\n\n                            if (instance.isEmpty() && context.getString(DATA_POINT).contains(GlobalConstants.INSTANCE_SEPARATOR))\n                            {\n                                instance = context.getString(DATA_POINT).split(GlobalConstants.INSTANCE_SEPARATOR)[0];\n                            }\n\n                            if (CommonUtil.getInteger(context.getValue(AGGREGATOR)) == DatastoreConstants.AggregationType.SPARKLINE.ordinal())//sparkline\n                            {\n                                var sparklinePoints = buffer.getShortLE(position);\n\n                                position = position + 2;  //2 byte (size of the sparkline points)\n\n                                for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                {\n                                    var values = new JsonObject();\n\n                                    var timestamps = new ArrayList<>();\n\n                                    if (dataType == DATA_TYPE_INT32)\n                                    {\n                                        for (var idx = 0; idx < sparklinePoints; idx++)\n                                        {\n                                            timestamps.add(buffer.getLongLE(position)); //Adding Interval/Timestamp\n\n                                            position = position + 8; //  moving 8 bytes' each value\n                                        }\n\n                                        for (var idx = 0; idx < sparklinePoints; idx++)\n                                        {\n                                            values.put(CommonUtil.getString(timestamps.get(idx)), buffer.getIntLE(position)); //Adding Value\n\n                                            position = position + 4; //  moving 4 bytes' each value\n                                        }\n\n                                    }\n                                    else if (dataType == DATA_TYPE_INT64)\n                                    {\n                                        for (var idx = 0; idx < sparklinePoints; idx++)\n                                        {\n                                            timestamps.add(buffer.getLongLE(position)); //Adding Interval/Timestamp\n\n                                            position = position + 8; //  moving 8 bytes' each value\n                                        }\n\n                                        for (var idx = 0; idx < sparklinePoints; idx++)\n                                        {\n                                            values.put(CommonUtil.getString(timestamps.get(idx)), buffer.getLongLE(position));//Adding Value\n\n                                            position = position + 8; //  moving 8 bytes' each value\n                                        }\n                                    }\n                                    else if (dataType == DATA_TYPE_FLOAT32)\n                                    {\n                                        for (var idx = 0; idx < sparklinePoints; idx++)\n                                        {\n                                            timestamps.add(buffer.getLongLE(position));\n\n                                            position = position + 8; // moving 8 byte value\n                                        }\n\n                                        for (var idx = 0; idx < sparklinePoints; idx++)\n                                        {\n                                            values.put(CommonUtil.getString(timestamps.get(idx)), ByteBuffer.wrap(buffer.getBytes(position, position + 8)).order(ByteOrder.LITTLE_ENDIAN).getDouble()); //wrap float into Little Endian and move 8 bytes\n\n                                            position = position + 8; //  moving 8 bytes' each value\n                                        }\n                                    }\n\n                                    rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), values);\n                                }\n                            }\n                            else\n                            {\n                                if (dataType == DATA_TYPE_INT32)\n                                {\n                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                    {\n                                        var value = buffer.getIntLE(position);\n\n                                        if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT).equalsIgnoreCase(\"monitor\"))\n                                        {\n                                            rows.get(rowIndex).put(\"monitor\", ObjectConfigStore.getStore().getObjectName(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(value))));\n                                        }\n                                        else\n                                        {\n                                            rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);\n                                        }\n\n                                        position = position + 4; //  moving 4 bytes' each value\n                                    }\n                                }\n                                else if (dataType == DATA_TYPE_INT64)\n                                {\n                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                    {\n                                        var value = buffer.getLongLE(position);\n\n                                        if (removeDummyRow)\n                                        {\n                                            if (value != Long.MIN_VALUE)\n                                            {\n                                                if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT).equalsIgnoreCase(\"monitor\"))\n                                                {\n                                                    rows.get(rowIndex).put(\"monitor\", ObjectConfigStore.getStore().getObjectName(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(value))));\n                                                }\n                                                else\n                                                {\n                                                    rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);\n                                                }\n                                            }\n\n                                            else\n                                            {\n                                                dummyRows.add(rowIndex);\n                                            }\n                                        }\n\n                                        else\n                                        {\n                                            if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT).equalsIgnoreCase(\"monitor\"))\n                                            {\n                                                rows.get(rowIndex).put(\"monitor\", ObjectConfigStore.getStore().getObjectName(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(value))));\n                                            }\n                                            else\n                                            {\n                                                rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);\n                                            }\n                                        }\n\n\n                                        position = position + 8; //  moving 8 bytes' each value\n                                    }\n                                }\n                                else if (dataType == DATA_TYPE_FLOAT32)\n                                {\n                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                    {\n                                        var value = ByteBuffer.wrap(buffer.getBytes(position, position + 8)).order(ByteOrder.LITTLE_ENDIAN).getDouble();\n\n                                        if (removeDummyRow)\n                                        {\n                                            if (value != -Float.MAX_VALUE)\n                                            {\n                                                rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);\n                                            }\n\n                                            else\n                                            {\n                                                dummyRows.add(rowIndex);\n                                            }\n\n                                        }\n\n                                        else\n                                        {\n                                            rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);\n                                        }\n\n                                        position = position + 8; //  moving 8 bytes' each value\n                                    }\n                                }\n\n                                else if (dataType == DATA_TYPE_STRING)\n                                {\n                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                    {\n                                        var length = CommonUtil.getInteger(buffer.getUnsignedIntLE(position));\n\n                                        position = position + 4;\n\n                                        var value = buffer.getString(position, position + length);\n\n                                        if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT).equalsIgnoreCase(\"monitor\"))\n                                        {\n                                            if (value != null)\n                                            {\n                                                rows.get(rowIndex).put(\"monitor\", ObjectConfigStore.getStore().getObjectName(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(value))));\n                                            }\n                                        }\n                                        else\n                                        {\n                                            rows.get(rowIndex).put(context.getString(alias ? DATA_POINT_ALIAS : DATA_POINT), value);\n                                        }\n\n                                        position = position + length; //  moving length of bytes' each value\n                                    }\n                                }\n                            }\n                        }\n\n                        if (removeDummyRow && !dummyRows.isEmpty())\n                        {\n                            var indices = new ArrayList<>(dummyRows);\n\n                            for (var i = indices.size() - 1; i >= 0; i--)\n                            {\n                                var index = indices.get(i);\n\n                                if (index >= 0 && index < rows.size())\n                                {\n                                    rows.remove(index.intValue());\n                                }\n                            }\n                        }\n\n                        if (join && query != null && query.containsKey(JOIN_TYPE) && query.getString(JOIN_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationJoinType.JOIN_TYPE_ANY.getName()))\n                        {\n                            var records = new HashMap<String, List<JsonObject>>();\n\n                            var columns = query.getJsonArray(JOIN_BY_COLUMNS);\n\n                            var builder = new StringBuilder();\n\n                            for (var row : rows)\n                            {\n                                valid = true;\n\n                                for (var column : columns)\n                                {\n                                    if (row.containsKey(CommonUtil.getString(column)))\n                                    {\n                                        builder.append(row.getValue(CommonUtil.getString(column))).append(GlobalConstants.COLUMN_SEPARATOR);\n                                    }\n\n                                    else\n                                    {\n                                        valid = false;\n\n                                        break;\n                                    }\n                                }\n\n                                if (valid)\n                                {\n                                    records.computeIfAbsent(CommonUtil.getString(builder.deleteCharAt(builder.length() - 1)), value -> new ArrayList<>()).add(row);\n                                }\n\n                                builder.setLength(0);\n                            }\n\n                            result.put(GlobalConstants.RESULT, records);\n                        }\n\n                        else\n                        {\n\n                            result.put(GlobalConstants.RESULT, rows);\n                        }\n\n                        result.put(GlobalConstants.INSTANCE, instance);\n                    }\n\n                    else\n                    {\n                        if (CommonUtil.debugEnabled())\n                        {\n                            logger.debug(\"invalid query result:\" + query);\n                        }\n                    }\n                }\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n\n            if (query != null)\n            {\n                logger.warn(\"exception query context:\" + query);\n            }\n        }\n\n        return result;\n    }\n\n    // this method is separate from existing unpack method\n    // which will be used to manipulate result to show in API in readable format.\n    // if we have any further requirement/improvement/manipulation for API, we can do it here.\n    public static JsonObject unpack(Buffer buffer, Logger logger, JsonObject query, boolean removeDummyRow)\n    {\n        var result = new JsonObject();\n\n        var category = query != null ? query.getString(VisualizationConstants.VISUALIZATION_CATEGORY) : EMPTY_VALUE;\n\n        try\n        {\n            if (buffer.length() > 0)\n            {\n                var instance = EMPTY_VALUE;\n\n                var valid = true;\n\n                var position = 0;\n\n                result.put(QUERY_ID, buffer.getLongLE(position));\n                position = position + 8; // 8 byte parent query id\n\n                result.put(SUB_QUERY_ID, buffer.getLongLE(position));\n                position = position + 8; // 8 byte current query id\n\n                result.put(QUERY_PROGRESS, CommonUtil.getShort(buffer.getUnsignedByte(position)));\n                position = position + 1; // 1 byte for query progress up to 100%\n\n                result.put(\"rows\", buffer.getLongLE(position));\n                position = position + 8; // 1 byte for query progress up to 100%\n\n                result.put(\"response.time.ms\", buffer.getLongLE(position));\n                position = position + 8; // 1 byte for query progress up to 100%\n\n                var status = CommonUtil.getShort(buffer.getUnsignedByte(position));\n\n                position = position + 1;\n\n                if (status == 0)//if query failed\n                {\n                    position = 38 + buffer.getIntLE(34);\n\n                    if (buffer.length() < position + 1)\n                    {\n                        valid = false;\n                    }\n\n                    result.put(ERROR, buffer.getString(38, 38 + buffer.getIntLE(34)));\n                }\n\n                if (valid)\n                {\n                    result.put(GlobalConstants.STATUS, status); // 1 byte query status success or failed (0/1 value)\n\n                    result.put(CATEGORY, CommonUtil.getShort(buffer.getUnsignedByte(position)));\n\n                    position = position + 1; // 1 byte visualization category (Gauge =0 ,TopNChart =1 ,TopNGrid =2 ,Histogram =3, Grid = 4)\n\n                    result.put(DATA_POINTS, CommonUtil.getShort(buffer.getUnsignedByte(position)));\n\n                    position = position + 1; // 1 byte number of columns\n\n                    if (buffer.length() > position)\n                    {\n                        result.put(VISUALIZATION_ROWS, buffer.getIntLE(position));\n\n                        position = position + 4; // 4 byte for number of rows counts\n\n                        if (query != null && query.containsKey(VISUALIZATION_CATEGORY) && query.getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.FORECAST.getName()) && !query.getString(VISUALIZATION_TYPE, VisualizationCategory.HISTOGRAM.getName()).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))\n                        {\n                            result.put(\"prediction.index\", buffer.getShortLE(position));\n\n                            position = position + 2; // 2 byte for predicted row index\n                        }\n\n                        var rows = new ArrayList<JsonObject>();\n\n                        for (var i = 0; i < result.getInteger(VISUALIZATION_ROWS); i++)\n                        {\n\n                            rows.add(new JsonObject());\n                        }\n\n                        var dummyRows = new HashSet<Integer>();\n\n                        for (var i = 0; i < result.getInteger(DATA_POINTS); i++)\n                        {\n                            try\n                            {\n\n                                var context = new JsonObject();\n\n                                result.put(\"total.bytes\", buffer.getUnsignedIntLE(position));\n\n                                position = position + 4; // 4 byte for number of rows counts\n\n                                context.put(AGGREGATOR, buffer.getUnsignedByte(position));\n                                position = position + 1; // 1 byte column aggregation type\n\n                                context.put(DATA_TYPE, buffer.getUnsignedByte(position));\n\n                                position = position + 1; // 1 byte column datatype\n\n                                context.put(DATA_POINT_LENGTH, buffer.getShortLE(position));\n\n                                position = position + 2; // 2 byte column length\n\n                                context.put(DATA_POINT, CommonUtil.getString(buffer.getString(position, position + context.getInteger(DATA_POINT_LENGTH))));\n\n                                position = position + context.getInteger(DATA_POINT_LENGTH); //  moving bytes' length of column name size\n\n                                context.put(\"alias.name.length\", buffer.getShortLE(position));\n\n                                position = position + 2; // 2 byte column length\n\n                                context.put(DATA_POINT_ALIAS, CommonUtil.getString(buffer.getString(position, position + context.getInteger(\"alias.name.length\"))));\n\n                                position = position + context.getInteger(\"alias.name.length\"); //  moving bytes' length of column name size\n\n                                var dataType = CommonUtil.getInteger(context.getValue(DATA_TYPE));\n\n                                if (instance.isEmpty() && context.getString(DATA_POINT).contains(GlobalConstants.INSTANCE_SEPARATOR))\n                                {\n                                    instance = context.getString(DATA_POINT).split(GlobalConstants.INSTANCE_SEPARATOR)[0];\n                                }\n\n                                if (dataType == DATA_TYPE_INT32)\n                                {\n                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                    {\n                                        var value = buffer.getIntLE(position);\n\n                                        if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && (context.getString(DATA_POINT).equalsIgnoreCase(\"monitor\") || context.getString(DATA_POINT).equalsIgnoreCase(AIOpsObject.OBJECT_ID)))\n                                        {\n                                            var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(value));\n\n                                            rows.get(rowIndex).put(\"monitor\", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(value));\n                                        }\n                                        else\n                                        {\n                                            rows.get(rowIndex).put(context.getString(DATA_POINT), value);\n                                        }\n\n                                        position = position + 4; //  moving 4 bytes' each value\n                                    }\n                                }\n                                else if (dataType == DATA_TYPE_INT64)\n                                {\n                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                    {\n                                        var value = buffer.getLongLE(position);\n\n                                        if (removeDummyRow)\n                                        {\n                                            if (value != Long.MIN_VALUE)\n                                            {\n                                                if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && (context.getString(DATA_POINT).equalsIgnoreCase(\"monitor\") || context.getString(DATA_POINT).equalsIgnoreCase(AIOpsObject.OBJECT_ID)))\n                                                {\n                                                    var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(value));\n\n                                                    rows.get(rowIndex).put(\"monitor\", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(value));\n                                                }\n                                                else\n                                                {\n                                                    rows.get(rowIndex).put(context.getString(DATA_POINT), value);\n                                                }\n                                            }\n\n                                            else\n                                            {\n                                                dummyRows.add(rowIndex);\n                                            }\n                                        }\n                                        else\n                                        {\n                                            if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && (context.getString(DATA_POINT).equalsIgnoreCase(\"monitor\") || context.getString(DATA_POINT).equalsIgnoreCase(AIOpsObject.OBJECT_ID)))\n                                            {\n                                                var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(value));\n\n                                                rows.get(rowIndex).put(\"monitor\", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(value));\n                                            }\n                                            else\n                                            {\n                                                rows.get(rowIndex).put(context.getString(DATA_POINT), value);\n                                            }\n                                        }\n\n                                        position = position + 8; //  moving 8 bytes' each value\n                                    }\n                                }\n                                else if (dataType == DATA_TYPE_FLOAT32)\n                                {\n                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                    {\n                                        var value = ByteBuffer.wrap(buffer.getBytes(position, position + 8)).order(ByteOrder.LITTLE_ENDIAN).getDouble();\n\n                                        if (removeDummyRow)\n                                        {\n                                            if (value != -Float.MAX_VALUE)\n                                            {\n                                                rows.get(rowIndex).put(context.getString(DATA_POINT), value);\n                                            }\n                                            else\n                                            {\n                                                dummyRows.add(rowIndex);\n                                            }\n                                        }\n                                        else\n                                        {\n                                            rows.get(rowIndex).put(context.getString(DATA_POINT), value);\n                                        }\n\n                                        position = position + 8; //  moving 8 bytes' each value\n                                    }\n                                }\n                                else if (dataType == DATA_TYPE_STRING)\n                                {\n                                    for (var rowIndex = 0; rowIndex < result.getInteger(VISUALIZATION_ROWS); rowIndex++)\n                                    {\n                                        var length = CommonUtil.getInteger(buffer.getUnsignedIntLE(position));\n\n                                        position = position + 4;\n\n                                        var value = buffer.getString(position, position + length);\n\n                                        if (query != null && query.containsKey(VISUALIZATION_DECODE_RESPONSE) && (context.getString(DATA_POINT).equalsIgnoreCase(\"monitor\") || context.getString(DATA_POINT).equalsIgnoreCase(AIOpsObject.OBJECT_ID)))\n                                        {\n                                            if (value != null)\n                                            {\n                                                var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(value));\n\n                                                rows.get(rowIndex).put(\"monitor\", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(value));\n                                            }\n                                        }\n                                        else\n                                        {\n                                            rows.get(rowIndex).put(context.getString(DATA_POINT), value);\n                                        }\n\n                                        position = position + length; //  moving length of bytes' each value\n                                    }\n                                }\n                            }\n                            catch (Exception exception)\n                            {\n                                logger.error(exception);\n                            }\n                        }\n\n                        if (category.equalsIgnoreCase(VisualizationCategory.HISTOGRAM.getName()))\n                        {\n                            result.put(GlobalConstants.RESULT, setHistogramResult(rows, query, logger));\n                        }\n                        else\n                        {\n                            if (removeDummyRow && !dummyRows.isEmpty())\n                            {\n                                var indices = new ArrayList<>(dummyRows);\n\n                                for (var i = indices.size() - 1; i >= 0; i--)\n                                {\n                                    var index = indices.get(i);\n\n                                    if (index >= 0 && index < rows.size())\n                                    {\n                                        rows.remove(index.intValue());\n                                    }\n                                }\n                            }\n\n                            result.put(GlobalConstants.RESULT, rows);\n\n                            result.put(GlobalConstants.INSTANCE, instance);\n                        }\n                    }\n\n                    else\n                    {\n                        if (CommonUtil.debugEnabled())\n                        {\n                            logger.debug(\"invalid query result:\" + query);\n                        }\n                    }\n                }\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n\n            if (query != null)\n            {\n                logger.warn(\"exception query context:\" + query);\n            }\n        }\n\n        return result;\n    }\n\n    // this method is to manipulate result of histogram coming from datastore to show in API\n    private static JsonArray setHistogramResult(List<JsonObject> rows, JsonObject query, Logger logger)\n    {\n        var records = new JsonArray();\n\n        try\n        {\n            var iterator = rows.iterator();\n\n            while (iterator.hasNext())\n            {\n                try\n                {\n                    var row = iterator.next();\n\n                    var fields = row.fieldNames();\n\n                    // need to remove single row with timestamp , in chart we have many dummy rows\n                    if (fields.size() > 1)\n                    {\n                        for (var field : fields)\n                        {\n                            // field : 1^ping.sent.packets^avg\n                            if (field.contains(CARET_SEPARATOR))\n                            {\n                                var record = new JsonObject();\n\n                                var tokens = field.split(CARET_SEPARATOR_WITH_ESCAPE);\n\n                                if (tokens.length > 2)\n                                {\n                                    var instanceName = EMPTY_VALUE;\n\n                                    var token = tokens[0];\n\n                                    // instance : 1###instance.name^metric.name^aggregator\n                                    if (token.contains(GROUP_SEPARATOR))\n                                    {\n                                        instanceName = token.split(GROUP_SEPARATOR)[1];\n\n                                        token = token.split(GROUP_SEPARATOR)[0];\n                                    }\n\n                                    if (query.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY))\n                                    {\n                                        var groupingColumns = query.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY);\n\n                                        if (groupingColumns.contains(\"severity\"))\n                                        {\n                                            record.put(SEVERITY, token);\n                                        }\n                                        else if (groupingColumns.contains(\"policy.id\"))\n                                        {\n                                            record.put(\"policy\", token);\n                                        }\n                                        else\n                                        {\n                                            if (groupingColumns.contains(\"group\"))\n                                            {\n                                                record.put(\"group\", token);\n                                            }\n                                            else if (groupingColumns.contains(\"tag\"))\n                                            {\n                                                record.put(\"tag\", token);\n                                            }\n                                            else if (groupingColumns.contains(\"monitor\") || groupingColumns.contains(\"object.id\") || !instanceName.equalsIgnoreCase(EMPTY_VALUE))          // last instance condition for result by instance\n                                            {\n                                                var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(token));\n\n                                                record.put(\"monitor\", object.getString(AIOpsObject.OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(ID, object.getLong(ID)).put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(token));\n                                            }\n\n                                            record.put(INSTANCE_NAME, instanceName);\n\n                                            record.put(METRIC, tokens[1]);\n\n                                            record.put(AGGREGATOR, tokens[2]);\n                                        }\n                                    }\n\n                                    record.put(VALUE, row.getValue(field));\n\n                                    record.put(TIMESTAMP, row.getValue(TIMESTAMP));\n                                }\n                                // for multiple datapoint, we don't have group by. so we can directly set values\n                                // ex : ping.received.packets^avg , ping.sent.packets^avg\n                                else if (tokens.length > 1)\n                                {\n                                    record.put(METRIC, tokens[0]);\n\n                                    record.put(AGGREGATOR, tokens[1]);\n\n                                    record.put(VALUE, row.getValue(field));\n\n                                    record.put(TIMESTAMP, row.getValue(TIMESTAMP));\n                                }\n\n                                records.add(record);\n                            }\n                        }\n                    }\n                    else\n                    {\n                        iterator.remove();\n                    }\n                }\n                catch (Exception exception)\n                {\n                    logger.error(exception);\n                }\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n\n        return records;\n    }\n\n    public static Buffer packGridResult(JsonObject result, int visualizationType, long queryId, long subQueryId)\n    {\n        var buffer = Buffer.buffer();\n\n        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(result.containsKey(QUERY_PROGRESS) ? result.remove(QUERY_PROGRESS) : 100), CommonUtil.getShort(1));\n\n        buffer.setUnsignedByte(34, CommonUtil.getShort(visualizationType));\n\n        buffer.setUnsignedByte(35, CommonUtil.getShort(result.size()));\n\n        result.getMap().forEach((key, value) ->\n        {\n            var records = (JsonArray) value;\n\n            if (!records.isEmpty())\n            {\n                var dataType = new AtomicReference<>(STRING);\n\n                var sparkline = key.contains(\"sparkline\");\n\n                var category = DatastoreConstants.getDataCategory(true, key.split(CARET_SEPARATOR_WITH_ESCAPE)[0], CommonUtil.getString(records.getValue(0)));\n\n                if (sparkline)\n                {\n                    //For Sparkline, set data as per values...\n                    var row = records.getJsonObject(0);\n\n                    for (var entry : row.getMap().entrySet())\n                    {\n                        var column = row.getValue(CommonUtil.getString(entry.getKey()));\n\n                        if (column instanceof Float || column instanceof Double)\n                        {\n                            dataType.set(FLOAT32);\n\n                            break;\n                        }\n                        // in case of result received from custom script (GoLang), value converted from Long to BigInteger\n                        else if (column instanceof Long || column instanceof BigInteger)\n                        {\n                            dataType.set(INT64);\n\n                            break;\n                        }\n                        else if (column instanceof Integer)\n                        {\n                            dataType.set(INT32);\n\n                            break;\n                        }\n                    }\n                }\n                else\n                {\n                    if (category == DatastoreConstants.DataCategory.FLOAT.getName() || records.getValue(0) instanceof Double || records.getValue(0) instanceof Float)\n                    {\n                        dataType.set(FLOAT32);\n                    }\n\n                    // in case of result received from custom script (GoLang), value converted from Long to BigInteger\n                    else if (records.getValue(0) instanceof Long || records.getValue(0) instanceof BigInteger)\n                    {\n                        dataType.set(INT64);\n                    }\n\n                    else if (records.getValue(0) instanceof Integer)\n                    {\n                        dataType.set(INT32);\n\n                        // In the custom script report when reading response from script (python, golang, node js), getting mix array of integer and Long like [12,4,6,3,5,-9223372036854775808]. So, It is\n                        // throwing ClassCastException so, have to assign category by checking each value\n                        for (var index = 0; index < records.size(); index++)\n                        {\n                            if (records.getValue(index) instanceof Long || CommonUtil.getLong(records.getValue(index)) == Long.MIN_VALUE)\n                            {\n                                dataType.set(INT64);\n\n                                break;\n                            }\n                        }\n                    }\n\n                }\n\n                buffer.setIntLE(36, records.size());\n\n                buffer.setIntLE(buffer.length(), 0);//extra bytes\n\n                if (sparkline)\n                {\n                    //Adding Aggregation Ordinal\n                    buffer.setUnsignedByte(buffer.length(), CommonUtil.getShort(DatastoreConstants.AggregationType.SPARKLINE.ordinal()));\n                }\n                else\n                {\n                    buffer.setUnsignedByte(buffer.length(), STRING.equals(dataType.get()) ? CommonUtil.getShort(DatastoreConstants.AggregationType.COUNT.ordinal()) : CommonUtil.getShort(DatastoreConstants.AggregationType.LAST.ordinal()));\n                }\n\n                buffer.setUnsignedByte(buffer.length(), dataType.get().getSize());\n\n                buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));\n\n                buffer.setString(buffer.length(), key);\n\n                buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));\n\n                buffer.setString(buffer.length(), key);\n\n                if (sparkline)\n                {\n                    //Adding length of the whole rows to buffer...\n                    buffer.setShortLE(buffer.length(), CommonUtil.getShort(records.getJsonObject(0).size()));\n                }\n\n                for (var i = 0; i < records.size(); i++)\n                {\n                    switch (dataType.get())\n                    {\n                        case INT32 ->\n                        {\n                            if (sparkline)\n                            {\n                                for (var row : records.getJsonObject(i).getMap().entrySet())\n                                {\n                                    try\n                                    {\n                                        buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));\n                                    }\n                                    catch (Exception exception)\n                                    {\n                                        LOGGER.error(exception);\n\n                                        buffer.setLongLE(buffer.length(), DUMMY_ID);\n                                    }\n                                }\n\n                                for (var row : records.getJsonObject(i).getMap().entrySet())\n                                {\n                                    try\n                                    {\n                                        buffer.setIntLE(buffer.length(), CommonUtil.getInteger(row.getValue()));\n                                    }\n                                    catch (Exception exception)\n                                    {\n                                        LOGGER.error(exception);\n\n                                        buffer.setIntLE(buffer.length(), DUMMY_NUMERIC_VALUE);\n                                    }\n                                }\n                            }\n                            else\n                            {\n                                try\n                                {\n                                    buffer.setIntLE(buffer.length(), CommonUtil.getInteger(records.getValue(i)));\n                                }\n                                catch (Exception exception)\n                                {\n                                    LOGGER.error(exception);\n\n                                    buffer.setIntLE(buffer.length(), DUMMY_NUMERIC_VALUE);\n                                }\n                            }\n                        }\n\n                        case INT64 ->\n                        {\n                            if (sparkline)\n                            {\n                                for (var row : records.getJsonObject(i).getMap().entrySet())\n                                {\n                                    try\n                                    {\n                                        buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));\n                                    }\n                                    catch (Exception exception)\n                                    {\n                                        LOGGER.error(exception);\n\n                                        buffer.setLongLE(buffer.length(), Long.MIN_VALUE);\n                                    }\n                                }\n\n                                for (var row : records.getJsonObject(i).getMap().entrySet())\n                                {\n                                    try\n                                    {\n                                        buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getValue()));\n                                    }\n                                    catch (Exception exception)\n                                    {\n                                        LOGGER.error(exception);\n\n                                        buffer.setLongLE(buffer.length(), Long.MIN_VALUE);\n                                    }\n                                }\n                            }\n                            else\n                            {\n                                try\n                                {\n                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(records.getValue(i)));\n                                }\n                                catch (Exception exception)\n                                {\n                                    LOGGER.error(exception);\n\n                                    buffer.setLongLE(buffer.length(), Long.MIN_VALUE);\n                                }\n                            }\n                        }\n\n\n                        case FLOAT32 ->\n                        {\n                            if (sparkline)\n                            {\n\n                                for (var row : records.getJsonObject(i).getMap().entrySet())\n                                {\n                                    try\n                                    {\n                                        buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));\n                                    }\n                                    catch (Exception exception)\n                                    {\n                                        LOGGER.error(exception);\n\n                                        buffer.setLongLE(buffer.length(), Long.MIN_VALUE);\n                                    }\n                                }\n\n                                for (var row : records.getJsonObject(i).getMap().entrySet())\n                                {\n                                    var bytes = new byte[]{};\n\n                                    try\n                                    {\n                                        bytes = ByteBuffer.allocate(8).putDouble(CommonUtil.getFloat(row.getValue())).array();\n                                    }\n                                    catch (Exception exception)\n                                    {\n                                        LOGGER.error(exception);\n\n                                        bytes = ByteBuffer.allocate(8).putDouble(-Float.MAX_VALUE).array();\n                                    }\n\n                                    for (var index = bytes.length - 1; index >= 0; index--)\n                                    {\n                                        buffer.setByte(buffer.length(), bytes[index]);\n                                    }\n                                }\n\n                            }\n                            else\n                            {\n                                var bytes = new byte[]{};\n\n                                try\n                                {\n                                    bytes = ByteBuffer.allocate(8).putDouble(CommonUtil.getFloat(records.getValue(i))).array();\n                                }\n                                catch (Exception exception)\n                                {\n                                    LOGGER.error(exception);\n\n                                    bytes = ByteBuffer.allocate(8).putDouble(-Float.MAX_VALUE).array();\n                                }\n\n                                for (var index = bytes.length - 1; index >= 0; index--)\n                                {\n                                    buffer.setByte(buffer.length(), bytes[index]);\n                                }\n                            }\n                        }\n\n                        case STRING ->\n                        {\n                            buffer.setIntLE(buffer.length(), CommonUtil.getInteger(String.valueOf(records.getValue(i)).getBytes(StandardCharsets.UTF_8).length));\n\n                            buffer.setString(buffer.length(), String.valueOf(records.getValue(i)));\n                        }\n                    }\n                }\n            }\n        });\n\n        return buffer;\n    }\n\n    public static Buffer packChartResult(JsonObject result, int visualizationType, long queryId, long subQueryId)\n    {\n        var buffer = Buffer.buffer();\n\n        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(result.containsKey(QUERY_PROGRESS) ? result.remove(QUERY_PROGRESS) : 100), CommonUtil.getShort(1));\n\n        buffer.setUnsignedByte(34, CommonUtil.getShort(visualizationType));\n\n        buffer.setUnsignedByte(35, CommonUtil.getShort(result.size()));\n\n        result.getMap().forEach((key, value) ->\n        {\n            var values = (JsonArray) value;\n\n            var dataType = new AtomicReference<>(STRING);\n\n            var category = DatastoreConstants.getDataCategory(true, key.split(CARET_SEPARATOR_WITH_ESCAPE)[0], CommonUtil.getString(values.getValue(0)));\n\n            if (category == DatastoreConstants.DataCategory.FLOAT.getName())\n            {\n                dataType.set(FLOAT32);\n            }\n            else\n            {\n                if (values.getValue(0) instanceof Double || values.getValue(0) instanceof Float)\n                {\n                    dataType.set(FLOAT32);\n                }\n\n                // in case of result received from custom script (GoLang), value converted from Long to BigInteger\n                else if (values.getValue(0) instanceof Long || values.getValue(0) instanceof BigInteger)\n                {\n                    dataType.set(INT64);\n                }\n\n                else if (values.getValue(0) instanceof Integer)\n                {\n                    dataType.set(INT32);\n\n                    // In the custom script report when reading response from script (python, golang, node js), getting mix array of integer and Long like [12,4,6,3,5,-9223372036854775808]. So, It is\n                    // throwing ClassCastException so, have to assign category by checking each value\n                    for (var index = 0; index < values.size(); index++)\n                    {\n                        if (values.getValue(index) instanceof Long || CommonUtil.getLong(values.getValue(index)) == Long.MIN_VALUE)\n                        {\n                            dataType.set(INT64);\n\n                            break;\n                        }\n                    }\n                }\n            }\n\n            buffer.setIntLE(36, values.size());\n\n            buffer.setIntLE(buffer.length(), 0);//extra bytes\n\n            buffer.setUnsignedByte(buffer.length(), STRING.equals(dataType.get()) ? CommonUtil.getShort(DatastoreConstants.AggregationType.COUNT.ordinal()) : CommonUtil.getShort(DatastoreConstants.AggregationType.LAST.ordinal()));\n\n            buffer.setUnsignedByte(buffer.length(), dataType.get().getSize());\n\n            buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));\n\n            buffer.setString(buffer.length(), key);\n\n            buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));\n\n            buffer.setString(buffer.length(), key);\n\n            values.forEach(entry ->\n            {\n                switch (dataType.get())\n                {\n                    case INT32 ->\n                    {\n                        try\n                        {\n                            buffer.setIntLE(buffer.length(), CommonUtil.getInteger(entry));\n                        }\n                        catch (Exception exception)\n                        {\n                            LOGGER.error(exception);\n\n                            buffer.setIntLE(buffer.length(), DUMMY_NUMERIC_VALUE);\n                        }\n                    }\n\n                    case INT64 ->\n                    {\n                        try\n                        {\n                            buffer.setLongLE(buffer.length(), CommonUtil.getLong(entry));\n                        }\n                        catch (Exception exception)\n                        {\n                            LOGGER.error(exception);\n\n                            buffer.setLongLE(buffer.length(), Long.MIN_VALUE);\n                        }\n                    }\n\n                    case FLOAT32 ->\n                    {\n\n                        var bytes = new byte[]{};\n\n                        try\n                        {\n                            bytes = ByteBuffer.allocate(8).putDouble(CommonUtil.getFloat(entry)).array();\n                        }\n                        catch (Exception exception)\n                        {\n                            LOGGER.error(exception);\n\n                            bytes = ByteBuffer.allocate(8).putDouble(-Float.MAX_VALUE).array();\n                        }\n\n                        for (var index = bytes.length - 1; index >= 0; index--)\n                        {\n                            buffer.setByte(buffer.length(), bytes[index]);\n                        }\n                    }\n\n                    case STRING ->\n                    {\n\n                        buffer.setIntLE(buffer.length(), CommonUtil.getInteger(CommonUtil.getString(entry).getBytes(StandardCharsets.UTF_8).length));\n\n                        buffer.setString(buffer.length(), CommonUtil.getString(entry));\n                    }\n                }\n            });\n        });\n\n        return buffer;\n    }\n\n    static void send(String category, String type, JsonObject result, String message, long queryId, long subQueryId, Logger logger, String event)\n    {\n        try\n        {\n            if (result != null && !result.isEmpty())\n            {\n                switch (VisualizationConstants.VisualizationCategory.valueOfName(category))\n                {\n                    case GAUGE ->\n                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packGaugeResult(result, queryId, subQueryId).getBytes()));\n\n                    case TOP_N ->\n                    {\n                        if (type.equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))\n                        {\n                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packGridResult(result, VisualizationCategoryOrdinal.VISUALIZATION_TOPN_GRID.ordinal(), queryId, subQueryId).getBytes()));\n                        }\n\n                        else\n                        {\n                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packChartResult(result, VisualizationCategoryOrdinal.VISUALIZATION_TOPN_CHART.ordinal(), queryId, subQueryId).getBytes()));\n                        }\n                    }\n                    case HISTOGRAM ->\n                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packChartResult(result, VisualizationCategoryOrdinal.VISUALIZATION_HISTOGRAM.ordinal(), queryId, subQueryId).getBytes()));\n\n\n                    default ->\n                            Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packGridResult(result, VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, subQueryId).getBytes()));\n                }\n            }\n\n            else\n            {\n                Bootstrap.vertx().eventBus().publish(event, CodecUtil.compress(packError(message, queryId, subQueryId).getBytes()));\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n    }\n\n    static void send(int category, Object value, Logger logger)\n    {\n        var buffer = Buffer.buffer();\n\n        if (CommonUtil.traceEnabled())\n        {\n            logger.trace(\"Event aggregations columns updated sending to DB:\" + JsonObject.mapFrom(value).encodePrettily());\n        }\n\n        buffer.appendByte(DatastoreConstants.OperationType.WIDGET_CREATE.getName()).appendBytes(new JsonObject().put(DatastoreConstants.DATASTORE_FORMAT, category)\n                .mergeIn(JsonObject.mapFrom(value)).encode().getBytes());\n\n        send(buffer);\n    }\n\n    // send this notification(s) to all DB , when create/delete widget happens\n    static void send(Buffer buffer)\n    {\n        for (var id : RemoteEventProcessorConfigStore.getStore().getIds())\n        {\n            var item = RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(id));\n\n            if (item != null && item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))\n            {\n                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject()\n                        .put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)\n                        .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))\n                        .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)\n                        .put(EVENT_CONTEXT, buffer.getBytes()));\n            }\n        }\n    }\n\n    // ------------------------------------- Decode Byte Buffer ----------------------------------------\n\n    static Buffer packGaugeResult(JsonObject result, long queryId, long subQueryId)\n    {\n        var buffer = Buffer.buffer();\n\n        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(100), CommonUtil.getShort(1));\n\n        buffer.setUnsignedByte(34, CommonUtil.getShort(VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal()));\n\n        buffer.setUnsignedByte(35, CommonUtil.getShort(result.size()));\n\n        buffer.setIntLE(36, 1);\n\n        result.getMap().forEach((key, value) ->\n        {\n            buffer.setIntLE(buffer.length(), 0);//extra bytes\n\n            buffer.setUnsignedByte(buffer.length(), CommonUtil.getShort(DatastoreConstants.AggregationType.COUNT.ordinal()));\n\n            buffer.setUnsignedByte(buffer.length(), CommonUtil.getShort(INT32.getName()));\n\n            buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));\n\n            buffer.setString(buffer.length(), key);\n\n            buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));\n\n            buffer.setString(buffer.length(), key);\n\n            buffer.setIntLE(buffer.length(), CommonUtil.getInteger(value));\n        });\n\n        return buffer;\n    }\n\n    // ------------------------------------- Result Packing ----------------------------------------\n\n    public static Buffer packError(String errorMessage, long queryId, long subQueryId)\n    {\n        var buffer = Buffer.buffer();\n\n        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(100), CommonUtil.getShort(0));\n\n        buffer.setIntLE(34, errorMessage.length());//length of error\n\n        buffer.setString(buffer.length(), errorMessage);//errors\n\n        return buffer;\n    }\n\n    static void packHeaders(Buffer buffer, long queryId, long subQueryId, short progress, short status)\n    {\n        buffer.setLongLE(0, queryId);//queryId\n\n        buffer.setLongLE(8, subQueryId);//subqueryId\n\n        buffer.setUnsignedByte(16, progress);//progress of query\n\n        buffer.setLongLE(17, 0);//records of query\n\n        buffer.setLongLE(25, 0);//time of query\n\n        buffer.setUnsignedByte(33, status);//status 0 means fail 1 success\n    }\n\n    public static Map<String, List<String>> filterGroups(String visualizationType, List<Object> filteredEntities)\n    {\n        var groups = new HashMap<String, List<String>>();\n\n        if (!filteredEntities.isEmpty())\n        {\n            var builder = new StringBuilder();\n\n            filteredEntities.forEach(group ->\n            {\n                var items = new JsonArray();\n\n                if (visualizationType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || visualizationType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))\n                {\n                    items = EventSourceConfigStore.getStore().flatItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, new JsonArray().add(CommonUtil.getLong(group)), EVENT_SOURCE);\n                }\n\n                else\n                {\n                    items = ObjectConfigStore.getStore().getObjectIdsByGroups(new JsonArray().add(CommonUtil.getLong(group)));\n                }\n\n                if (!items.isEmpty())\n                {\n                    builder.setLength(0);\n\n                    CommonUtil.normalizeGroupHierarchy(GroupConfigStore.getStore().getItem(CommonUtil.getLong(group)), builder, null);\n\n                    groups.put(builder.toString(), items.getList());\n                }\n            });\n        }\n\n        return groups;\n    }\n\n    public static void loadCategories(Map<String, Set<String>> eventCategories, Logger logger)\n    {\n        try\n        {\n            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + DatastoreConstants.EVENT_CATEGORIES);\n\n            if (file.exists())\n            {\n                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());\n\n                if (buffer != null && buffer.getBytes().length > 0)\n                {\n                    var categories = new JsonArray(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes())));\n\n                    for (var index = 0; index < categories.size(); index++)\n                    {\n                        var tokens = categories.getString(index).split(SEPARATOR_WITH_ESCAPE);\n\n                        eventCategories.computeIfAbsent(tokens[0], value -> new HashSet<>()).add(tokens[1]);\n                    }\n\n                    logger.info(\"loaded event categories\");\n                }\n            }\n            else\n            {\n                Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n    }\n\n    public static String prepareEventDataSource(JsonObject eventColumns, JsonObject context, JsonObject visualizationDataSource, Map<String, Object> entities, JsonObject user, JsonArray qualifiedSources, Set<Object> filteredEntities, Logger logger, Map<String, Set<String>> eventCategories)\n    {\n        var error = EMPTY_VALUE;\n\n        try\n        {\n\n            var aggregations = 0;\n\n            var plugins = new HashSet<>();\n\n            var columns = new HashSet<String>();\n\n            var visualizationDataPoints = new JsonArray();\n\n            var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);\n\n            var filterRequired = true;\n\n            var eventSourceFilter = false;\n\n            var eventHistory = false;\n\n            var eventCategory = visualizationDataSource.getString(VisualizationConstants.TYPE).toLowerCase();\n\n            var dataPointRequired = true;\n\n            if (!visualizationDataSource.containsKey(CATEGORY))\n            {\n                visualizationDataSource.put(CATEGORY, EMPTY_VALUE);\n            }\n\n            if (eventCategory.equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()) && visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.LOG.getName()))\n            {\n                JsonObject filters;\n\n                var key = DATA_FILTER;\n\n                //This case would fall when both filters not empty thus on that we would loop around drill down values and see if following condition wont fall so to choose between drilldown key or data filter.\n                if(visualizationDataSource.containsKey(FILTERS) && visualizationDataSource.getJsonObject(FILTERS).containsKey(DRILL_DOWN_FILTER) && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DRILL_DOWN_FILTER).isEmpty())\n                {\n                    var conditionGroups = visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS);\n\n                    for (var i = 0; i < conditionGroups.size(); i++)\n                    {\n                        var conditions = conditionGroups.getJsonObject(i).getJsonArray(CONDITIONS);\n\n                        for (var j = 0; j < conditions.size(); j++)\n                        {\n                            var condition = conditions.getJsonObject(j);\n\n                            if (!condition.getString(OPERAND).equalsIgnoreCase(EVENT_SOURCE) && !condition.getString(OPERAND).equalsIgnoreCase(MESSAGE) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_PATTERN_ID))\n                            {\n                                key = DRILL_DOWN_FILTER;\n\n                                break;\n                            }\n                        }\n                    }\n\n                    filters = visualizationDataSource.getJsonObject(FILTERS);\n                }\n                //priority will be given to data filter..\n                else if (visualizationDataSource.containsKey(FILTERS) && visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DRILL_DOWN_FILTER).isEmpty())\n                {\n                    filters = visualizationDataSource.getJsonObject(FILTERS);\n\n                    key = DRILL_DOWN_FILTER;\n                }\n                else\n                {\n                    filters = visualizationDataSource.containsKey(FILTERS) && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() ? visualizationDataSource.getJsonObject(FILTERS) : new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);\n                }\n\n\n                var conditionGroups = filters.getJsonObject(key).getJsonArray(CONDITION_GROUPS);\n\n                for (var i = 0; i < conditionGroups.size(); i++)\n                {\n                    var conditions = conditionGroups.getJsonObject(i).getJsonArray(CONDITIONS);\n\n                    for (var j = 0; j < conditions.size(); j++)\n                    {\n                        var condition = conditions.getJsonObject(j);\n\n                        if (!condition.getString(OPERAND).equalsIgnoreCase(EVENT_SOURCE) && !condition.getString(OPERAND).equalsIgnoreCase(MESSAGE) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE) && !condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_PATTERN_ID))\n                        {\n                            dataPointRequired = false;\n\n                            var operand = condition.getString(OPERAND);\n\n                            var ids = eventColumns.getJsonObject(operand).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);\n\n                            eventCategory = eventColumns.getJsonObject(operand).getString(DatastoreConstants.MAPPER_EVENT_CATEGORY, EMPTY_VALUE);\n\n                            removeDefaultPluginIds(ids);\n\n                            var dataPoint = dataPoints.getJsonObject(0);\n\n                            var pluginIds = new JsonArray();\n\n                            pluginIds.add(ids.getValue(0) + DASH_SEPARATOR + eventCategory.replace(\" \", \".\").toLowerCase());\n\n                            pluginIds.forEach(plugins::add);\n\n                            if (dataPoint.containsKey(ENTITY_TYPE) && (dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName())) && !dataPoint.getJsonArray(ENTITIES).isEmpty())\n                            {\n                                eventSourceFilter = true;\n                            }\n\n                            aggregations += prepareEventDataSource(true, visualizationDataPoints, pluginIds, plugins, dataPoint, visualizationDataSource, user, entities, qualifiedSources, filteredEntities, logger);\n\n                            break;\n                        }\n                    }\n                }\n            }\n\n            if (dataPointRequired)\n            {\n                for (var i = 0; i < dataPoints.size(); i++)\n                {\n                    var pluginIds = new JsonArray();\n\n                    var dataPoint = dataPoints.getJsonObject(i);\n\n                    var column = dataPoint.getString(VisualizationConstants.DATA_POINT);\n\n                    columns.add(column);\n\n                    if (eventColumns.containsKey(column) || column.equalsIgnoreCase(\"*\"))\n                    {\n                        if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.HEALTH_METRIC.getName()) || (visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.LOG.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName())))\n                        {\n                            var ids = eventColumns.getJsonObject(column).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);\n\n                            eventCategory = eventColumns.getJsonObject(column).getString(DatastoreConstants.MAPPER_EVENT_CATEGORY, EMPTY_VALUE);\n\n                            removeDefaultPluginIds(ids);\n\n                            if (column.equalsIgnoreCase(EVENT) || ids.getValue(0).equals(DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName()))\n                            {\n                                eventHistory = true;\n\n                                pluginIds.add(DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName());\n                            }\n\n                            else\n                            {\n\n                                pluginIds.add(ids.getValue(0) + DASH_SEPARATOR + eventCategory.replace(\" \", \".\").toLowerCase());\n                            }\n                        }\n\n                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_RESULT.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.POLICY_RESULT.getName() + DASH_SEPARATOR + eventCategory);\n                        }\n\n                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.CORRELATED_METRIC.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.CORRELATED_METRIC.getName() + DASH_SEPARATOR + eventCategory);\n                        }\n\n                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.RUNBOOK_WORKLOG.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName() + DASH_SEPARATOR + eventCategory);\n                        }\n\n                        else if ((visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.FLOW.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName())) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.FLOW.getName()))\n                        {\n                            var ids = eventColumns.getJsonObject(column).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);\n\n                            if (ids.getValue(0).equals(DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName()))\n                            {\n                                pluginIds.add(DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName() + DASH_SEPARATOR + VisualizationDataSource.FLOW.getName());\n                            }\n                            else\n                            {\n                                pluginIds.add(DatastoreConstants.PluginId.FLOW_EVENT.getName() + DASH_SEPARATOR + VisualizationDataSource.FLOW.getName());\n                            }\n                        }\n\n                        else if ((visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.AUDIT.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName())) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.AUDIT.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.AUDIT_EVENT.getName() + DASH_SEPARATOR + VisualizationDataSource.AUDIT.getName());\n\n                            filterRequired = false;\n                        }\n\n                        else if ((visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.USER_NOTIFICATION.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName())) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.USER_NOTIFICATION.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.NOTIFICATION_EVENT.getName() + DASH_SEPARATOR + VisualizationDataSource.USER_NOTIFICATION.getName());\n\n                            filterRequired = false;\n                        }\n\n                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP_FLAP.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP_ACKNOWLEDGEMENT.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.TRAP_EVENT.getName() + DASH_SEPARATOR + eventCategory);\n                        }\n\n                        else if ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName())) && visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.POLICY_FLOW.getName() + DASH_SEPARATOR + eventCategory);\n                        }\n\n                        else if ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName())) && visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.POLICY_EVENT.getName() + DASH_SEPARATOR + eventCategory);\n                        }\n\n                        else if ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName())) && visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.POLICY_TRAP.getName() + DASH_SEPARATOR + eventCategory);\n                        }\n\n                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.STATIC_METRIC.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.STATIC_METRIC.getName() + DASH_SEPARATOR + eventCategory);\n                        }\n                        else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.COMPLIANCE.getName()))\n                        {\n                            pluginIds.add(DatastoreConstants.PluginId.COMPLIANCE.getName() + DASH_SEPARATOR + eventCategory);\n\n                            filterRequired = false;\n                        }\n                    }\n\n                    if (context.containsKey(ENTITY_TYPE))//request from explorer dashboard with entity type to replace in filter\n                    {\n                        dataPoint.put(ENTITY_TYPE, context.getString(ENTITY_TYPE));\n\n                        dataPoint.put(ENTITIES, context.getJsonArray(ENTITIES));\n                    }\n\n                    if (!pluginIds.isEmpty())\n                    {\n                        pluginIds.forEach(plugins::add);\n\n                        if (dataPoint.containsKey(ENTITY_TYPE) && (dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) || dataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName())) && dataPoint.getJsonArray(ENTITIES) != null && !dataPoint.getJsonArray(ENTITIES).isEmpty())\n                        {\n                            eventSourceFilter = true;\n                        }\n\n                        aggregations += prepareEventDataSource(filterRequired, visualizationDataPoints, pluginIds, plugins, dataPoint, visualizationDataSource, user, entities, qualifiedSources, filteredEntities, logger);\n                    }\n                }\n            }\n\n            if (!visualizationDataPoints.isEmpty())\n            {\n                if (aggregations > VisualizationConstants.MAX_EVENT_AGGREGATION_FUNCTIONS)// combination of unique counters with selected aggregation should be less than 16\n                {\n                    error = ErrorMessageConstants.DATA_POINT_LIMIT_EXCEEDED;\n                }\n\n                else\n                {\n                    visualizationDataSource.put(ENTITIES, entities);\n\n                    visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));\n                }\n            }\n\n            else\n            {\n                error = ErrorMessageConstants.NO_ENTITY_QUALIFIED;\n            }\n\n            visualizationDataSource.put(LogEngineConstants.EVENT_CATEGORY, eventCategory);\n\n            if (filterRequired && !entities.isEmpty())\n            {\n                applyFilter(context, visualizationDataSource, entities.keySet(), eventSourceFilter, eventHistory, eventCategories);\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n\n        return error;\n    }\n\n    private static int prepareEventDataSource(boolean filterRequired, JsonArray visualizationDataPoints, JsonArray pluginIds, Set<Object> plugins, JsonObject visualizationDataPoint, JsonObject visualizationDataSource, JsonObject user, Map<String, Object> entities, JsonArray qualifiedSources, Set<Object> filteredEntities, Logger logger)\n    {\n        var aggregationFuncs = 0;\n\n        try\n        {\n            var qualifiedEntities = qualifyEventSources(visualizationDataSource, visualizationDataPoint, user.getJsonArray(User.USER_GROUPS), qualifiedSources, filteredEntities, pluginIds, logger);\n\n            if (!qualifiedEntities.isEmpty())\n            {\n                visualizationDataPoint.put(VisualizationConstants.PLUGINS, qualifiedEntities.remove(VisualizationConstants.PLUGINS));\n\n                if (visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))\n                {\n                    aggregationFuncs += 2;\n                }\n\n                else\n                {\n                    aggregationFuncs++;\n                }\n\n                entities.putAll(qualifiedEntities);\n\n                plugins.addAll(entities.values());\n\n                visualizationDataPoint.put(ENTITIES, qualifiedEntities);\n\n                visualizationDataPoints.add(visualizationDataPoint);\n            }\n\n            else if (!filterRequired)\n            {\n\n                visualizationDataPoint.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));\n\n                visualizationDataPoints.add(visualizationDataPoint);\n            }\n        }\n\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n\n        return aggregationFuncs;\n    }\n\n    /**\n     * Applies filters to the given visualization data source based on the provided category, context, and filter sets.\n     * <p>\n     * This method updates the visualizationDataSource with filter conditions for severities, policies, tags, and entities.\n     * It supports dynamic filter construction for different visualization types and categories, including metrics, events,\n     * and network route metrics/events. The method also handles special cases for cumulative object status flaps and\n     * ensures that the correct operands and operators are set for each filter condition.\n     *\n     * @param category                The visualization category (e.g., metric, event, etc.).\n     * @param context                 The context object containing additional filter flags and user roles.\n     * @param cache                   Indicates if the filter should use cached values.\n     * @param visualizationDataSource The data source object to which filters will be applied.\n     * @param severities              The set to collect severity filter values.\n     * @param policies                The set to collect policy filter values.\n     * @param entities                The set to collect entity filter values.\n     */\n    public static void applyFilter(String category, JsonObject context, boolean cache, JsonObject visualizationDataSource, Set<Object> severities, Set<Object> policies, Set<String> entities)\n    {\n        var filter = false;\n\n        var qualifiedEntities = new ArrayList<Integer>();\n\n        if (visualizationDataSource.containsKey(SEVERITY) && !visualizationDataSource.getJsonArray(SEVERITY).isEmpty())\n        {\n            filter = true;\n\n            severities.addAll(visualizationDataSource.getJsonArray(SEVERITY).getList());\n        }\n\n        if (visualizationDataSource.containsKey(POLICIES) && !visualizationDataSource.getJsonArray(POLICIES).isEmpty())\n        {\n            filter = true;\n\n            policies.addAll(visualizationDataSource.getJsonArray(POLICIES).getList());\n        }\n\n        if (visualizationDataSource.containsKey(TAGS) && !visualizationDataSource.getJsonArray(TAGS).isEmpty())\n        {\n            filter = true;\n\n            if (category.equalsIgnoreCase(METRIC))\n            {\n                policies.addAll(MetricPolicyConfigStore.getStore().flatItemsByMultiValueFieldAny(PolicyEngineConstants.POLICY_TAGS, visualizationDataSource.getJsonArray(TAGS), ID).getList());\n            }\n            else if (category.equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()) || category.equalsIgnoreCase(VisualizationDataSource.NETROUTE_EVENT.getName()))\n            {\n                policies.addAll(NetRoutePolicyConfigStore.getStore().flatItemsByMultiValueFieldAny(PolicyEngineConstants.POLICY_TAGS, visualizationDataSource.getJsonArray(TAGS), ID).getList());\n            }\n            else\n            {\n                policies.addAll(EventPolicyConfigStore.getStore().flatItemsByMultiValueFieldAny(PolicyEngineConstants.POLICY_TAGS, visualizationDataSource.getJsonArray(TAGS), ID).getList());\n            }\n        }\n\n        if (category.equalsIgnoreCase(METRIC) && (context.getBoolean(OBJECT_FILTER, false) || (!cache && !context.getBoolean(VisualizationConstants.ADMIN_ROLE, false))) && !entities.isEmpty() && !visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName()))\n        {\n            filter = true;\n\n            qualifiedEntities = (ArrayList<Integer>) entities.stream().map(CommonUtil::getInteger).collect(Collectors.toList());\n        }\n\n        if (!cache && filter)\n        {\n            var filters = visualizationDataSource.containsKey(FILTERS) && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() ? visualizationDataSource.getJsonObject(FILTERS) : new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);\n\n            var conditionGroup = filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0);\n\n            var conditions = !conditionGroup.getJsonArray(CONDITIONS).isEmpty() ? conditionGroup.getJsonArray(CONDITIONS) : new JsonArray();\n\n            if (visualizationDataSource.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()) && !conditions.isEmpty() && conditions.getJsonObject(0) != null && conditions.getJsonObject(0).containsKey(OPERAND) && conditions.getJsonObject(0).getString(OPERAND).equalsIgnoreCase(NMSConstants.INTERFACE))//TODO need proper condition\n            {\n                conditions.getJsonObject(0).put(OPERAND, INSTANCE);\n            }\n\n            if (severities != null && !severities.isEmpty())\n            {\n                conditions.add(new JsonObject().put(OPERAND, SEVERITY).put(VALUE, severities.size() > 1 ? new ArrayList<>(severities) : new ArrayList<>(severities).getFirst()).put(OPERATOR, severities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));\n            }\n\n            if (policies != null && !policies.isEmpty())\n            {\n                conditions.add(new JsonObject().put(OPERAND, PolicyEngineConstants.POLICY_ID).put(VALUE, policies.size() > 1 ? new ArrayList<>(policies) : new ArrayList<>(policies).getFirst()).put(OPERATOR, policies.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));\n            }\n\n            if (!qualifiedEntities.isEmpty())\n            {\n                conditions.add(new JsonObject().put(OPERAND, AIOpsObject.OBJECT_ID).put(VALUE, qualifiedEntities.size() > 1 ? qualifiedEntities : qualifiedEntities.getFirst()).put(OPERATOR, qualifiedEntities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));\n            }\n\n            conditionGroup.put(CONDITIONS, conditions);\n\n            visualizationDataSource.put(FILTERS, filters);\n        }\n    }\n\n    public static Map<String, Object> qualifyEntities(boolean correlated, JsonObject context, JsonArray qualifiedGroups, Set<Integer> qualifiedObjects, Set filteredGroupEntities, Set filteredTagEntities, Logger logger, boolean archived)\n    {\n        Map<String, Object> qualifiedEntities = null;\n\n        try\n        {\n\n            Map<String, String> entities = null;\n\n            if (!context.containsKey(ENTITY_TYPE))\n            {\n                context.put(ENTITY_TYPE, \"all\");\n            }\n\n            var items = filterEntities(context, qualifiedGroups, qualifiedObjects, filteredGroupEntities, filteredTagEntities, logger, archived);\n\n            var plugins = new ArrayList<String>();\n\n            if (correlated)\n            {\n                plugins.add(DatastoreConstants.PluginId.CORRELATED_METRIC.getName() + DASH_SEPARATOR + VisualizationDataSource.CORRELATED_METRIC.getName());\n            }\n\n            else\n            {\n                plugins.add(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName());\n            }\n\n            if (items != null && !items.isEmpty())\n            {\n                entities = new HashMap<>();\n\n                for (var item : items)\n                {\n                    entities.put(CommonUtil.getString(item), plugins.getFirst());\n                }\n            }\n\n            if (entities != null)\n            {\n                qualifiedEntities = new HashMap<>();\n\n                qualifiedEntities.put(ENTITIES, entities);\n\n                qualifiedEntities.put(VisualizationConstants.PLUGINS, plugins);\n            }\n        }\n\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n\n        return qualifiedEntities;\n    }\n\n    //public static final Set<String> RESERVED_METRICS = Set.of(\"~duration\");\n\n    private static void removeDefaultPluginIds(JsonArray pluginIds)\n    {\n        var iterator = pluginIds.stream().iterator();\n\n        while (iterator.hasNext())\n        {\n            var pluginId = CommonUtil.getInteger(iterator.next());\n\n            if (pluginId == DatastoreConstants.PluginId.TRAP_EVENT.getName() || pluginId == DatastoreConstants.PluginId.FLOW_EVENT.getName() || pluginId == DatastoreConstants.PluginId.AUDIT_EVENT.getName() || pluginId == DatastoreConstants.PluginId.NOTIFICATION_EVENT.getName() || pluginId == DatastoreConstants.PluginId.POLICY_EVENT.getName() || pluginId == DatastoreConstants.PluginId.POLICY_FLOW.getName() || pluginId == DatastoreConstants.PluginId.POLICY_TRAP.getName())\n            {\n                iterator.remove();\n            }\n        }\n    }\n\n    //    -----------------------------------For Event\n    private static Map<String, Object> qualifyEventSources(JsonObject visualizationDataSource, JsonObject context, JsonArray qualifiedGroups, JsonArray qualifiedSources, Set filteredEntities, JsonArray pluginIds, Logger logger)\n    {\n        List<String> items;\n\n        var qualifiedEntities = new HashMap<String, Object>();\n\n        try\n        {\n\n            var groupType = visualizationDataSource.getString(TYPE);\n\n            if (!context.containsKey(ENTITY_TYPE))\n            {\n                context.put(ENTITY_TYPE, \"all\");\n            }\n\n            if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                items = ObjectConfigStore.getStore().flatItemsByValues(ID, context.getJsonArray(ENTITIES), AIOpsObject.OBJECT_IP).stream().filter(qualifiedSources::contains).map(CommonUtil::getString).toList();\n            }\n\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                items = context.getJsonArray(ENTITIES).stream().filter(qualifiedSources::contains).map(CommonUtil::getString).toList();\n            }\n\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                filteredEntities.addAll(context.getJsonArray(ENTITIES).stream().toList());\n\n                if (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()))\n                {\n                    items = EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, new JsonArray(context.getJsonArray(ENTITIES).stream().filter(qualifiedGroups::contains).distinct().toList()), EVENT_SOURCE).stream().filter(qualifiedSources::contains).map(CommonUtil::getString).toList();\n                }\n\n                else\n                {\n                    items = EventSourceConfigStore.getStore().flatItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, new JsonArray(context.getJsonArray(ENTITIES).stream().filter(qualifiedGroups::contains).distinct().toList()), EVENT_SOURCE).stream().filter(qualifiedSources::contains).map(CommonUtil::getString).toList();\n                }\n            }\n\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                items = EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(PLUGIN_ID, new JsonArray(LogParserConfigStore.getStore().getItems().stream().map(JsonObject::mapFrom).filter(item -> context.getJsonArray(ENTITIES).contains(item.getString(LogParser.LOG_PARSER_SOURCE_TYPE))).map(item -> item.getInteger(PLUGIN_ID)).toList()), EVENT_SOURCE).getList();\n            }\n\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName()) && context.getJsonArray(ENTITIES) != null && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                items = EventSourceConfigStore.getStore().flatItemsByMultiValueFieldAny(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName(), context.getJsonArray(ENTITIES), EVENT_SOURCE).getList();\n            }\n\n            else\n            {\n\n                if ((visualizationDataSource.containsKey(CATEGORY) && visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(EVENT_LOG) && groupType.equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()) || (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || groupType.equalsIgnoreCase(VisualizationDataSource.HEALTH_METRIC.getName()))))\n                {\n                    var pluginId = CommonUtil.getInteger(pluginIds.getString(0).split(DASH_SEPARATOR)[0]);\n\n                    if (pluginId == DatastoreConstants.PluginId.HEALTH_METRIC.getName() || pluginId == DatastoreConstants.PluginId.CACHE_DATASTORE_EVENT.getName() || pluginId == DatastoreConstants.PluginId.QUERY_STATS_DATASTORE_EVENT.getName() || pluginId == DatastoreConstants.PluginId.PENDING_FILES_DATASTORE_EVENT.getName())\n                    {\n                        items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().getItemsByMultiValueFields(EVENT_TYPE, VisualizationDataSource.HEALTH_METRIC.getName(), EVENT_SOURCE, LogEngineConstants.EVENT_CATEGORY, pluginId)::contains).toList();\n                    }\n                    else if (pluginId != DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() && pluginId != DatastoreConstants.PluginId.LOG_EVENT_STAT.getName() && pluginId != DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName())\n                    {\n                        items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().getItemsByMultiValueFields(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.LOG.getName(), EVENT_SOURCE, LogEngineConstants.EVENT_CATEGORY, pluginId)::contains).toList();\n                    }\n                    else if (pluginId == DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName())\n                    {\n                        items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.FLOW.getName(), EVENT_SOURCE)::contains).toList();\n                    }\n                    else\n                    {\n                        items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().flatItemsByMultiValueFields(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.LOG.getName(), EVENT_SOURCE, LogEngineConstants.EVENT_CATEGORY)::contains).toList();\n                    }\n                }\n\n                else if ((visualizationDataSource.containsKey(CATEGORY) && visualizationDataSource.getString(CATEGORY).equals(EVENT_FLOW)) || groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))\n                {\n                    items = qualifiedSources.stream().map(CommonUtil::getString).filter(EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.FLOW.getName(), EVENT_SOURCE)::contains).toList();\n                }\n\n                else if ((visualizationDataSource.containsKey(CATEGORY) && visualizationDataSource.getString(CATEGORY).equals(EVENT_TRAP)) || groupType.contains(VisualizationConstants.VisualizationDataSource.TRAP.getName()) || groupType.contains(VisualizationDataSource.TRAP_FLAP.getName()) || groupType.contains(VisualizationDataSource.TRAP_ACKNOWLEDGEMENT.getName()))//as in trap will not be considering groups will be qualifying all hosts from which trap received\n                {\n                    items = EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, VisualizationConstants.VisualizationDataSource.TRAP.getName(), EVENT_SOURCE).getList();\n                }\n\n                else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_RESULT.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName()))//as in policy will not be considering groups will be qualifying all hosts from which log or flow is received\n                {\n                    items = EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()) ? VisualizationDataSource.FLOW.getName() : VisualizationDataSource.LOG.getName(), EVENT_SOURCE).getList();\n                }\n\n                else\n                {\n                    items = qualifiedSources.getList();\n                }\n            }\n\n            if (!items.isEmpty())\n            {\n                var plugins = new HashSet<>();\n\n                for (var item : items)\n                {\n                    for (var plugin : pluginIds)\n                    {\n                        qualifiedEntities.put(item, plugin);\n\n                        plugins.add(plugin);\n                    }\n                }\n\n                qualifiedEntities.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n\n        return qualifiedEntities;\n    }\n\n    public static void applyFilter(JsonObject context, JsonObject visualizationDataSource, Set<String> entities, boolean eventSourceFilter, boolean eventHistory, Map<String, Set<String>> eventCategories)\n    {\n        var filters = visualizationDataSource.containsKey(FILTERS) && !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty() ? visualizationDataSource.getJsonObject(FILTERS) : new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);\n\n        var conditionGroup = filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0);\n\n        var conditions = !conditionGroup.getJsonArray(CONDITIONS).isEmpty() ? conditionGroup.getJsonArray(CONDITIONS) : new JsonArray();\n\n        if (visualizationDataSource.containsKey(FILTERS) && visualizationDataSource.getJsonObject(FILTERS).containsKey(RESULT_FILTER) && !filters.containsKey(RESULT_FILTER))\n        {\n            filters.put(RESULT_FILTER, visualizationDataSource.getJsonObject(FILTERS).getJsonObject(RESULT_FILTER));\n        }\n\n        if (visualizationDataSource.containsKey(FILTERS) && visualizationDataSource.getJsonObject(FILTERS).containsKey(DRILL_DOWN_FILTER))\n        {\n            filters.put(DRILL_DOWN_FILTER, visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DRILL_DOWN_FILTER));\n        }\n\n        if ((visualizationDataSource.containsKey(FILTERS) && !filters.containsKey(DRILL_DOWN_FILTER)) || (visualizationDataSource.containsKey(FILTERS) && filters.containsKey(DRILL_DOWN_FILTER) && filters.getJsonObject(DRILL_DOWN_FILTER).isEmpty()))\n        {\n            filters.put(DRILL_DOWN_FILTER, new JsonObject(VisualizationConstants.VISUALIZATION_DRILL_DOWN_FILTER_JSON_TEMPLATE).getJsonObject(DRILL_DOWN_FILTER));\n        }\n\n        if (visualizationDataSource.containsKey(VisualizationConstants.FILTER_KEYS))//for flow purpose interface widget during interface drilldown will be taking filterkeys\n        {\n            filters = new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);\n\n            var operandValues = new ArrayList<>();\n\n            visualizationDataSource.getJsonArray(VisualizationConstants.FILTER_KEYS).stream().map(CommonUtil::getString).forEach(key ->\n            {\n                if (key.contains(\"-\"))\n                {\n                    operandValues.add(CommonUtil.getInteger(key.substring(key.lastIndexOf(\"-\") + 1).trim()));\n                }\n            });\n\n            if (!operandValues.isEmpty())\n            {\n                filters.getJsonObject(DATA_FILTER).put(CONDITION_GROUPS, new JsonArray().add(new JsonObject(VisualizationConstants.VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE).put(OPERATOR, DatastoreConstants.ConditionGroup.OR.getName()).put(CONDITIONS, new JsonArray().add(new JsonObject().put(OPERAND, FlowEngineConstants.SOURCE_INTERFACE_INDEX).put(VALUE, operandValues.size() > 1 ? operandValues : operandValues.getFirst()).put(OPERATOR, operandValues.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()))\n                        .add(new JsonObject().put(OPERAND, FlowEngineConstants.DESTINATION_INTERFACE_INDEX).put(VALUE, operandValues.size() > 1 ? operandValues : operandValues.getFirst()).put(OPERATOR, operandValues.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName())))));\n            }\n\n            if (!entities.isEmpty())\n            {\n                filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).add(new JsonObject(VisualizationConstants.VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE)\n                        .put(CONDITIONS, new JsonArray().add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, entities.size() > 1 ? new ArrayList<>(entities) : new ArrayList<>(entities).getFirst()).put(OPERATOR, entities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()))));\n            }\n        }\n        else\n        {\n            var drillDownConditionGroup = filters.getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0);\n\n            var drillDownConditions = !drillDownConditionGroup.getJsonArray(CONDITIONS).isEmpty() ? drillDownConditionGroup.getJsonArray(CONDITIONS) : new JsonArray();\n\n            if (!context.getBoolean(VisualizationConstants.ADMIN_ROLE, false) && !entities.isEmpty())\n            {\n                if (!drillDownConditions.isEmpty() && (!drillDownConditionGroup.getJsonArray(CONDITIONS).isEmpty() && !drillDownConditionGroup.getJsonArray(CONDITIONS).getJsonObject(0).getString(OPERAND).equalsIgnoreCase(EVENT_SOURCE)))\n                {\n\n                    filters.getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS).add(new JsonObject(VisualizationConstants.VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE).put(CONDITIONS,\n                            new JsonArray().add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, new ArrayList<>(entities)).put(OPERATOR, DatastoreConstants.ConditionGroup.IN.getName()))).getMap());\n\n                }\n                else if (drillDownConditions.isEmpty())\n                {\n\n                    drillDownConditions.add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, entities.size() > 1 ? new ArrayList<>(entities) : new ArrayList<>(entities).getFirst()).put(OPERATOR, entities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));\n\n                    drillDownConditionGroup.put(CONDITIONS, drillDownConditions);\n                }\n            }\n\n            else\n            {\n                if (eventSourceFilter)\n                {\n                    if (drillDownConditionGroup.size() > 1 && !drillDownConditions.isEmpty())\n                    {\n                        filters.getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS).add(new JsonObject(VisualizationConstants.VISUALIZATION_CONDITION_GROUP_JSON_TEMPLATE).put(CONDITIONS,\n                                new JsonArray().add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, entities.size() > 1 ? new ArrayList<>(entities) : new ArrayList<>(entities).getFirst()).put(OPERATOR, entities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()))).getMap());\n                    }\n\n                    else\n                    {\n                        drillDownConditions.add(new JsonObject().put(OPERAND, EVENT_SOURCE).put(VALUE, entities.size() > 1 ? new ArrayList<>(entities) : new ArrayList<>(entities).getFirst()).put(OPERATOR, entities.size() > 1 ? DatastoreConstants.ConditionGroup.IN.getName() : PolicyEngineConstants.Operator.EQUAL.getName()));\n\n                        drillDownConditionGroup.put(CONDITIONS, drillDownConditions);\n                    }\n                }\n\n                else if (drillDownConditions.isEmpty())\n                {\n                    filters.put(DRILL_DOWN_FILTER, new JsonObject());\n                }\n            }\n\n            if (conditions.isEmpty())\n            {\n                filters.put(DATA_FILTER, new JsonObject());\n            }\n\n            if (eventHistory && !filters.isEmpty() && ((filters.containsKey(DATA_FILTER) && !filters.getJsonObject(DATA_FILTER).isEmpty() && !filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).isEmpty()) || (filters.containsKey(DRILL_DOWN_FILTER) && !filters.getJsonObject(DRILL_DOWN_FILTER).isEmpty() && !filters.getJsonObject(DRILL_DOWN_FILTER).getJsonArray(CONDITION_GROUPS).isEmpty())))\n            {\n                var categories = new JsonObject();\n\n                var key = DATA_FILTER;\n                //priority will be given to data filter..\n                if ((!filters.containsKey(DATA_FILTER) || filters.getJsonObject(DATA_FILTER).isEmpty()) && (filters.containsKey(DRILL_DOWN_FILTER) && !filters.getJsonObject(DRILL_DOWN_FILTER).isEmpty()))\n                {\n                    key = DRILL_DOWN_FILTER;\n                }\n\n                var conditionGroups = filters.getJsonObject(key).getJsonArray(CONDITION_GROUPS);\n\n                /*\n                For Log Search Exclude function should not be included for categories.\n                 */\n                if (!conditionGroup.getString(FILTER).equalsIgnoreCase(\"exclude\"))\n                {\n                    for (var i = 0; i < conditionGroups.size(); i++)\n                    {\n                        conditions = conditionGroups.getJsonObject(i).getJsonArray(CONDITIONS);\n\n                        for (var j = 0; j < conditions.size(); j++)\n                        {\n                            var condition = conditions.getJsonObject(j);\n\n                            if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY) || condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE))\n                            {\n                                if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.EQUAL.getName()))\n                                {\n                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))\n                                    {\n                                        for (var values : eventCategories.values())\n                                        {\n                                            values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].equalsIgnoreCase(CommonUtil.getString(condition.getString(VALUE)))).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\").trim(), EMPTY_VALUE));\n                                        }\n                                    }\n\n                                    else\n                                    {\n                                        eventCategories.get(condition.getString(VALUE)).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\").trim(), EMPTY_VALUE));\n                                    }\n                                }\n\n                                else if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.IN.getName()))\n                                {\n                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))\n                                    {\n                                        condition.getJsonArray(VALUE).forEach(value ->\n                                        {\n                                            for (var values : eventCategories.values())\n                                            {\n                                                values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].equalsIgnoreCase(CommonUtil.getString(value))).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\"), EMPTY_VALUE));\n                                            }\n                                        });\n                                    }\n\n                                    else\n                                    {\n                                        condition.getJsonArray(VALUE).forEach(value -> eventCategories.get(CommonUtil.getString(value)).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\"), EMPTY_VALUE)));\n                                    }\n                                }\n\n                                else if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.CONTAINS.getName()))\n                                {\n                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))\n                                    {\n                                        for (var values : eventCategories.values())\n                                        {\n                                            values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].toLowerCase().contains(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\"), EMPTY_VALUE));\n                                        }\n                                    }\n\n                                    else\n                                    {\n                                        eventCategories.keySet().stream().filter(item -> item.toLowerCase().contains(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\"), EMPTY_VALUE));\n                                    }\n                                }\n\n                                else if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.START_WITH.getName()))\n                                {\n                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))\n                                    {\n                                        for (var values : eventCategories.values())\n                                        {\n                                            values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].toLowerCase().startsWith(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\"), EMPTY_VALUE));\n                                        }\n                                    }\n\n                                    else\n                                    {\n                                        eventCategories.keySet().stream().filter(item -> item.toLowerCase().startsWith(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\"), EMPTY_VALUE));\n                                    }\n                                }\n\n                                else if (condition.getString(OPERATOR).equalsIgnoreCase(PolicyEngineConstants.Operator.END_WITH.getName()))\n                                {\n                                    if (condition.getString(OPERAND).equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY))\n                                    {\n                                        for (var values : eventCategories.values())\n                                        {\n                                            values.stream().filter(item -> item.split(DASH_SEPARATOR)[1].toLowerCase().endsWith(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\"), EMPTY_VALUE));\n                                        }\n                                    }\n\n                                    else\n                                    {\n\n                                        eventCategories.keySet().stream().filter(item -> item.toLowerCase().endsWith(CommonUtil.getString(condition.getString(VALUE)).toLowerCase())).forEach(item -> categories.put(item.toLowerCase().trim().replace(\" \", \".\"), EMPTY_VALUE));\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n\n                if (!categories.isEmpty())\n                {\n                    visualizationDataSource.put(\"categories\", categories);\n                }\n            }\n        }\n\n        visualizationDataSource.put(FILTERS, filters);\n    }\n\n    public static Set<Integer> filterEntities(JsonObject context, JsonArray qualifiedGroups, Set<Integer> qualifiedObjects, Set filteredGroupEntities, Set filteredTagEntities, Logger logger, boolean archived)\n    {\n        Set<Integer> items = null;\n\n        try\n        {\n\n            if (archived && context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()) && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                items = ArchivedObjectConfigStore.getStore().getObjectIdsByIds(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).collect(Collectors.toSet());\n            }\n            else if (archived)\n            {\n                items = ArchivedObjectConfigStore.getStore().getArchiveObjectIds().stream().map(CommonUtil::getInteger).collect(Collectors.toSet());\n            }\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()) && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                items = ObjectConfigStore.getStore().getObjectIdsByIds(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());\n            }\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                filteredGroupEntities.addAll(context.getJsonArray(ENTITIES).stream().toList());\n\n                items = ObjectConfigStore.getStore().getObjectIdsByGroups(new JsonArray(context.getJsonArray(ENTITIES).stream().filter(qualifiedGroups::contains).distinct().toList())).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());\n            }\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(AIOpsObject.OBJECT_TYPE) && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                items = ObjectConfigStore.getStore().getObjectIdsByTypes(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());\n            }\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.CATEGORY.getName()) && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                items = ObjectConfigStore.getStore().getObjectIdsByCategories(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());\n            }\n            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.TAG.getName()) && !context.getJsonArray(ENTITIES).isEmpty())\n            {\n                filteredTagEntities.addAll(context.getJsonArray(ENTITIES).stream().toList());\n\n                if (context.getString(DATA_POINT).contains(INSTANCE_SEPARATOR) || (context.containsKey(INSTANCE) && context.getBoolean(INSTANCE)))\n                {\n                    items = TagCacheStore.getStore().getInstanceIdsByTags(context.getString(DATA_POINT).split(INSTANCE_SEPARATOR)[0], context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());\n                }\n                else\n                {\n                    items = ObjectConfigStore.getStore().getObjectIdsByTags(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).filter(qualifiedObjects::contains).collect(Collectors.toSet());\n                }\n            }\n            else\n            {\n                items = qualifiedObjects;\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n\n        return items;\n    }\n\n    public static void update(JsonObject columns, String[] tokens, boolean metric)\n    {\n        var column = tokens[DatastoreConstants.MetricWriterOrdinal.COLUMN.ordinal()];\n\n        if (!columns.containsKey(column))\n        {\n            columns.put(column, new JsonObject());\n        }\n\n        var mapper = columns.getJsonObject(column);\n\n        var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);\n\n        if (plugins == null)\n        {\n            plugins = new JsonArray(new ArrayList<>(1));\n        }\n\n        if (!plugins.contains(CommonUtil.getInteger(tokens[1])))\n        {\n            mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));\n        }\n\n        var dataTypes = mapper.getJsonArray(MAPPER_DATA_CATEGORIES);\n\n        if (dataTypes == null)\n        {\n            dataTypes = new JsonArray(new ArrayList<>(1));\n        }\n\n        if (!dataTypes.contains(CommonUtil.getInteger(tokens[0])))\n        {\n            mapper.put(MAPPER_DATA_CATEGORIES, dataTypes.add(CommonUtil.getInteger(tokens[0])));\n        }\n\n        if (plugins.contains(DatastoreConstants.PluginId.CORRELATED_METRIC.getName()))\n        {\n            mapper.put(DatastoreConstants.MAPPER_CORRELATED, tokens[3]);\n        }\n        else\n        {\n            mapper.put(DatastoreConstants.MAPPER_STATUS, tokens[3]);\n        }\n\n        if (!metric && tokens.length > 4)\n        {\n            mapper.put(DatastoreConstants.MAPPER_EVENT_CATEGORY, tokens[4]);\n        }\n\n        if (column.contains(GlobalConstants.INSTANCE_SEPARATOR))\n        {\n            mapper.put(DatastoreConstants.MAPPER_INSTANCE, column.split(GlobalConstants.INSTANCE_SEPARATOR)[0]);\n        }\n\n        if (mapper.containsKey(DatastoreConstants.MAPPER_CORRELATED) && mapper.getString(DatastoreConstants.MAPPER_CORRELATED).equalsIgnoreCase(GlobalConstants.YES))\n        {\n            mapper.put(DatastoreConstants.MAPPER_GROUP, tokens[DatastoreConstants.EventWriterOrdinal.GROUP.ordinal()]);\n        }\n    }\n\n    public static void publish(JsonObject context, Logger logger, Map<Long, JsonObject> queryResults, Map<Long, JsonObject> queryRawContexts, Map<Long, JsonObject> queryContexts, Long queryId, Buffer buffer)\n    {\n        if (context.getBoolean(\"publish\"))//if require event to be published to other events will be publishing it\n        {\n            if (queryResults.containsKey(queryId))\n            {\n                var result = VisualizationConstants.unpack(buffer, logger, false, queryContexts.get(queryId), false, true);\n\n                if (result.containsKey(RESULT) && !result.getJsonArray(RESULT).isEmpty())\n                {\n                    queryResults.get(queryId).getJsonArray(RESULT).addAll(result.getJsonArray(RESULT));\n                }\n\n                Bootstrap.vertx().eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, context.getInteger(QUERY_PROGRESS)).put(\"unpacked\", true).put(RESULT, queryResults.get(queryId)));\n            }\n            else\n            {\n                Bootstrap.vertx().eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, context.getInteger(QUERY_PROGRESS)).put(RESULT, buffer.getBytes()));\n            }\n        }\n        else\n        {\n\n            if (queryContexts.containsKey(queryId) && queryContexts.get(queryId).containsKey(VISUALIZATION_DRILL_DOWN))\n            {\n                var result = VisualizationConstants.unpack(buffer, logger, false, queryContexts.get(queryId), false, true);\n\n                if (result.containsKey(RESULT) && !result.getJsonArray(RESULT).isEmpty())\n                {\n                    if (queryResults.containsKey(queryId))\n                    {\n                        queryResults.get(queryId).getJsonArray(RESULT).addAll(result.getJsonArray(RESULT));\n                    }\n                    else\n                    {\n                        queryResults.put(queryId, result);\n                    }\n                }\n\n                Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION, queryRawContexts.get(queryId));\n            }\n        }\n    }\n\n    public static void composeQueryResponse(JsonObject columnMappers, int queryProgress, JsonObject result, String visualizationCategory, long queryId, long subQueryId, JsonObject context, Logger logger, boolean qualifyUniqueColumns)\n    {\n        var composedResult = new JsonObject();\n\n        try\n        {\n            if (CommonUtil.traceEnabled())\n            {\n                logger.trace(\"***** result: \" + result.encode());\n\n                logger.trace(\"***** context: \" + context.encode());\n            }\n\n            var columns = qualifyUniqueColumns ? getColumns(result, logger) : new HashSet<String>();\n\n            var type = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getString(CATEGORY, EMPTY_VALUE);\n\n            var sortedRows = new ArrayList<JsonObject>();\n\n            var sortBy = context.containsKey(SORT_BY) ? context.getString(SORT_BY) : EMPTY_VALUE;\n\n            if (context.containsKey(VisualizationConstants.JOIN_TYPE) && context.getString(VisualizationConstants.JOIN_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationJoinType.JOIN_TYPE_ANY.getName()))\n            {\n                var results = ((JsonObject) result.getJsonObject(CommonUtil.getString(subQueryId)).remove(RESULT));\n\n                if (results != null)\n                {\n                    for (var entry : results.getMap().entrySet())//main subquery result in which unique columns on whose result data is to be merged with other results\n                    {\n                        var rows = entry.getValue() instanceof ArrayList arrayList ? new JsonArray(arrayList) : (JsonArray) entry.getValue();\n\n                        for (var i = 0; i < rows.size(); i++)\n                        {\n                            var row = new JsonObject().mergeIn(rows.getJsonObject(i));\n\n                            var valid = false;\n\n                            for (var value : result.getMap().values())\n                            {\n                                var subQueryResults = ((JsonObject) value).getJsonObject(RESULT, null);\n\n                                if (subQueryResults != null && subQueryResults.containsKey(entry.getKey()))\n                                {\n                                    valid = true;\n\n                                    row.mergeIn(subQueryResults.getJsonArray(entry.getKey()).getJsonObject(0));\n\n                                    for (var column : columns)\n                                    {\n                                        if (!row.containsKey(column))\n                                        {\n                                            if (column.equalsIgnoreCase(sortBy))\n                                            {\n                                                row.put(column, 0);\n                                            }\n                                            else\n                                            {\n                                                row.put(column, EMPTY_VALUE);\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n\n                            if (valid)\n                            {\n                                if (!sortBy.isEmpty())\n                                {\n                                    if (row.containsKey(sortBy))//if sorting column is not present then ignore the result..\n                                    {\n                                        sortedRows.add(row);\n                                    }\n                                }\n\n                                else\n                                {\n                                    composedResult.put(VISUALIZATION_TYPE, type);\n\n                                    compose(row, composedResult);\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n            else if (context.containsKey(VisualizationConstants.JOIN_TYPE) && context.getString(VisualizationConstants.JOIN_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationJoinType.JOIN_TYPE_ALL.getName()))\n            {\n                var rows = result.getJsonObject(CommonUtil.getString(subQueryId)).getJsonArray(RESULT);\n\n                if (rows != null)\n                {\n                    for (var i = 0; i < rows.size(); i++)\n                    {\n                        var row = new JsonObject().mergeIn(rows.getJsonObject(i));\n\n                        for (var value : result.getMap().values())\n                        {\n                            var subQueryResults = ((JsonObject) value).getJsonArray(RESULT, null);\n\n                            if (subQueryResults != null)\n                            {\n                                for (var j = 0; j < subQueryResults.size(); j++)\n                                {\n                                    row.mergeIn(subQueryResults.getJsonObject(j));\n                                }\n                            }\n                        }\n\n                        if (!sortBy.isEmpty())\n                        {\n                            if (row.containsKey(sortBy))//if sorting column is not present then ignore the result..\n                            {\n                                sortedRows.add(row);\n                            }\n                        }\n                        else\n                        {\n                            compose(row, composedResult);\n                        }\n                    }\n                }\n            }\n            else if (!context.containsKey(VisualizationConstants.JOIN_TYPE) || (context.containsKey(VisualizationConstants.JOIN_TYPE) && context.getString(VisualizationConstants.JOIN_TYPE).equalsIgnoreCase(VisualizationJoinType.JOIN_TYPE_CUSTOM.getName())))\n            {\n                var rows = result.getJsonObject(CommonUtil.getString(subQueryId)).getJsonArray(RESULT);\n\n                if (rows != null)\n                {\n                    //for special column will be building result by adding special column value\n                    for (var i = 0; i < rows.size(); i++)\n                    {\n                        composedResult.put(VISUALIZATION_TYPE, type);\n\n                        compose(new JsonObject().mergeIn(rows.getJsonObject(i)), composedResult);\n                    }\n                }\n            }\n\n            if (!sortedRows.isEmpty())\n            {\n                sortedRows.sort(Comparator.comparing(item -> JsonObject.mapFrom(item).getLong(sortBy)).reversed());\n\n                for (var row : sortedRows)\n                {\n                    compose(row, composedResult);\n                }\n            }\n\n            if ((!composedResult.isEmpty() && context.containsKey(VisualizationConstants.JOIN_RESULT)) || (composedResult.isEmpty() && context.containsKey(VisualizationConstants.JOIN_RESULT) && context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"inventory\")))\n            {\n                join(context, composedResult, columnMappers, logger);\n            }\n\n            if (!composedResult.isEmpty())\n            {\n                if ((context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.TOP_N.getName()) || context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.HISTOGRAM.getName()) || context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.STREAM.getName())) && !context.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))\n                {\n                    var dataSource = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0);\n\n                    if ((context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HISTOGRAM.getName()) || context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.STREAM.getName())) && dataSource.containsKey(VISUALIZATION_RESULT_BY) && !dataSource.getJsonArray(VISUALIZATION_RESULT_BY).isEmpty())\n                    {\n                        var groupingColumns = dataSource.getJsonArray(VISUALIZATION_RESULT_BY);\n\n                        var category = dataSource.getString(CATEGORY, EMPTY_VALUE);\n\n                        for (var i = 0; i < groupingColumns.size(); i++)\n                        {\n                            if (REPLACEABLE_COLUMNS.containsKey(groupingColumns.getString(i)))\n                            {\n                                resolve(composedResult, groupingColumns.getString(i), i, category);\n                            }\n                        }\n                    }\n\n                    else if (context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.TOP_N.getName()) && context.containsKey(VISUALIZATION_RESULT_BY) && !context.getJsonArray(VISUALIZATION_RESULT_BY).isEmpty())\n                    {\n                        if (context.getJsonArray(VISUALIZATION_RESULT_BY).contains(PolicyEngineConstants.POLICY_ID))\n                        {\n                            //in topn alert group widget when there is policy id in grouping we need to showcase policy name so we do not require result of policy id as we resolve policy name so removing policy.id result\n                            composedResult.remove(PolicyEngineConstants.POLICY_ID);\n                        }\n                        else if (context.getJsonArray(VISUALIZATION_RESULT_BY).contains(NetRoute.NETROUTE_ID))\n                        {\n                            composedResult.remove(NetRoute.NETROUTE_ID);\n                        }\n                    }\n\n                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packChartResult(composedResult.put(QUERY_PROGRESS, queryProgress), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_TOPN_CHART.ordinal(), queryId, subQueryId).getBytes()));\n                }\n                else if (context.getString(VISUALIZATION_NAME) != null && context.getString(VISUALIZATION_NAME).equalsIgnoreCase(VISUALIZATION_TYPE_TODAY_AVAILABILITY))\n                {\n                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packChartResult(composedResult.put(QUERY_PROGRESS, queryProgress), VisualizationCategoryOrdinal.VISUALIZATION_CHART.ordinal(), queryId, subQueryId).getBytes()));\n                }\n                else\n                {\n                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(composedResult.put(QUERY_PROGRESS, queryProgress), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, subQueryId).getBytes()));\n                }\n            }\n            else\n            {\n                //send back empty result\n\n                VisualizationConstants.send(visualizationCategory, VisualizationConstants.VisualizationCategory.GRID.getName(), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, \"Preview Widget\", ErrorMessageConstants.INVALID_NO_DATA_FOUND), queryId, subQueryId, logger, EVENT_DATASTORE_QUERY_RESPONSE);\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.trace(\"***** result: \" + result.encode());\n\n            logger.trace(\"***** context: \" + context.encode());\n\n            logger.trace(\"***** visualizationCategory: \" + visualizationCategory);\n\n            logger.error(exception);\n        }\n    }\n\n    private static Set<String> getColumns(JsonObject result, Logger logger)\n    {\n        var columns = new HashSet<String>();\n\n        try\n        {\n            for (var key : result.getMap().keySet())\n            {\n                var results = result.getJsonObject(key).getJsonObject(RESULT, null);\n\n                if (results != null && !results.isEmpty())\n                {\n                    columns.addAll(((JsonArray) results.stream().findFirst().get().getValue()).getJsonObject(0).getMap().keySet());\n                }\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.trace(\"***** result: \" + result.encode());\n\n            logger.error(exception);\n        }\n\n        return columns;\n    }\n\n    public static void compose(JsonObject row, JsonObject composedResult)\n    {\n        var type = composedResult.containsKey(VISUALIZATION_TYPE) ? composedResult.remove(VISUALIZATION_TYPE) : EMPTY_VALUE;\n\n        row.forEach(entry ->\n        {\n            ((JsonArray) composedResult.getMap().computeIfAbsent(entry.getKey(), value -> new JsonArray())).add(entry.getValue());\n\n            if (VisualizationConstants.REPLACEABLE_COLUMNS.containsKey(entry.getKey()))\n            {\n                var result = transformColumn(entry.getKey(), CommonUtil.getString(entry.getValue()), CommonUtil.getString(type));\n\n                if (result != null)\n                {\n                    composedResult.getMap().computeIfAbsent(VisualizationConstants.REPLACEABLE_COLUMNS.get(entry.getKey()), value -> new JsonArray());\n\n                    composedResult.getJsonArray(VisualizationConstants.REPLACEABLE_COLUMNS.get(entry.getKey())).add(result);\n                }\n            }\n        });\n    }\n\n    private static void join(JsonObject context, JsonObject result, JsonObject columnMappers, Logger logger)\n    {\n        if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_STREAM.getName()))\n        {\n            var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, context.getString(User.USER_NAME));\n\n            var dateFormat = DateTimeUtil.getDateFormat(item.getJsonObject(User.USER_PREFERENCES).getString(User.USER_PREFERENCE_DATE_TIME_FORMAT), item);\n\n            if (context.containsKey(CATEGORY) && (context.getString(CATEGORY).equalsIgnoreCase(METRIC) || context.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName())))\n            {\n                result.getMap().computeIfAbsent(END_TIME, value -> new JsonArray());\n\n                result.getMap().computeIfAbsent(APIConstants.Entity.OBJECT.getName().toLowerCase(), value -> new JsonArray());\n\n                result.remove(EVENT_ID);\n\n                var values = (JsonArray) result.remove(TIMESTAMP);\n\n                var objects = (JsonArray) result.remove(AIOpsObject.OBJECT_ID);\n\n                result.getMap().computeIfAbsent(START_TIME, value -> new JsonArray());\n\n                var durations = result.getJsonArray(DURATION);\n\n                var firstTriggerTicks = result.getJsonArray(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK);\n\n                for (var index = 0; index < values.size(); index++)\n                {\n                    try\n                    {\n\n                        result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(objects.getValue(index));\n\n                        result.getJsonArray(START_TIME).add(dateFormat.format(new Date(values.getLong(index))));\n\n                        result.getJsonArray(END_TIME).add(dateFormat.format(new Date(values.getLong(index) + (durations.getLong(index) * 1000))));\n\n                        if (index >= firstTriggerTicks.size())\n                        {\n                            firstTriggerTicks.add(dateFormat.format(new Date()));\n                        }\n                        else\n                        {\n                            firstTriggerTicks.set(index, dateFormat.format(new Date(firstTriggerTicks.getLong(index))));\n                        }\n                    }\n\n                    catch (Exception ignored)\n                    {\n\n                    }\n\n                }\n            }\n            else\n            {\n                try\n                {\n                    result.put(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK, new JsonArray());\n\n                    result.remove(EVENT_ID);\n\n                    var policyIds = result.getJsonArray(PolicyEngineConstants.POLICY_ID);\n\n                    var ticks = result.getJsonArray(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK);\n\n                    if (context.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_EVENT.getName()) && result.containsKey(NetRoute.NETROUTE_ID))\n                    {\n                        for (var i = 0; i < result.getJsonArray(TIMESTAMP).size(); i++)\n                        {\n                            ticks.add(dateFormat.format(new Date(NetRoutePolicyFlapDurationCacheStore.getStore().getTriggerTicks(policyIds.getLong(i) + SEPARATOR + result.getJsonArray(NetRoute.NETROUTE_ID).getLong(i)).getLong(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK))));\n                        }\n                    }\n                    else\n                    {\n                        for (var i = 0; i < result.getJsonArray(TIMESTAMP).size(); i++)\n                        {\n                            ticks.add(dateFormat.format(new Date(EventPolicyCacheStore.getStore().getTriggerTicks(policyIds.getLong(i)).getLong(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK))));\n                        }\n                    }\n                }\n                catch (Exception ignored)\n                {\n\n                }\n            }\n        }\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()))\n        {\n            var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, context.getString(User.USER_NAME));\n\n            var dateFormat = DateTimeUtil.getDateFormat(item.getJsonObject(User.USER_PREFERENCES).getString(User.USER_PREFERENCE_DATE_TIME_FORMAT), item);\n\n            result.getMap().computeIfAbsent(END_TIME, value -> new JsonArray());\n\n            result.getMap().computeIfAbsent(APIConstants.Entity.OBJECT.getName().toLowerCase(), value -> new JsonArray());\n\n            result.remove(EVENT_ID);\n\n            var values = (JsonArray) result.remove(TIMESTAMP);\n\n            var objects = (JsonArray) result.remove(AIOpsObject.OBJECT_ID);\n\n            result.getMap().computeIfAbsent(START_TIME, value -> new JsonArray());\n\n            var dataSources = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0);\n\n            JsonArray durations;\n\n            if (dataSources.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName()) && dataSources.containsKey(INSTANCE_TYPE) && !dataSources.getString(INSTANCE_TYPE).isEmpty())\n            {\n                durations = result.getJsonArray(dataSources.getString(INSTANCE_TYPE) + INSTANCE_SEPARATOR + DURATION);\n            }\n            else\n            {\n                durations = result.getJsonArray(DURATION);\n            }\n\n            for (var index = 0; index < values.size(); index++)\n            {\n                try\n                {\n\n                    result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(objects.getValue(index));\n\n                    result.getJsonArray(END_TIME).add(dateFormat.format(new Date(values.getLong(index) + (durations.getLong(index) * 1000))));\n\n                    result.getJsonArray(START_TIME).add(dateFormat.format(new Date(values.getLong(index))));\n                }\n\n                catch (Exception ignored)\n                {\n\n                }\n            }\n        }\n\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"integration.ticket\"))\n        {\n            var values = result.getJsonArray(TIMESTAMP);\n\n            var instance = !result.getJsonArray(INSTANCE + CARET_SEPARATOR + VALUE).getString(0).isEmpty();\n\n            result.getMap().computeIfAbsent(IntegrationEngine.ACK_ID, value -> new JsonArray());\n\n            for (var index = 0; index < values.size(); index++)\n            {\n                String policyKey;\n\n                if (instance)\n                {\n                    policyKey = IntegrationCacheStore.getStore().getIntegrationType(result.getJsonArray(PolicyEngineConstants.POLICY_ID + CARET_SEPARATOR + VALUE).getString(index)) + SEPARATOR + result.getJsonArray(PolicyEngineConstants.POLICY_ID + CARET_SEPARATOR + VALUE).getValue(index)\n                            + SEPARATOR + ObjectConfigStore.getStore().getIdByObjectId(result.getJsonArray(AIOpsObject.OBJECT_ID + CARET_SEPARATOR + VALUE).getInteger(index)) + SEPARATOR +\n                            result.getJsonArray(PolicyEngineConstants.POLICY_TYPE + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR + result.getJsonArray(METRIC + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR +\n                            result.getJsonArray(INSTANCE + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR +\n                            result.getJsonArray(SEVERITY + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR + CommonUtil.getString(values.getLong(index) / 1000L);\n                }\n                else\n                {\n                    policyKey = IntegrationCacheStore.getStore().getIntegrationType(result.getJsonArray(PolicyEngineConstants.POLICY_ID + CARET_SEPARATOR + VALUE).getString(index)) + SEPARATOR + result.getJsonArray(PolicyEngineConstants.POLICY_ID + CARET_SEPARATOR + VALUE).getValue(index)\n                            + SEPARATOR + ObjectConfigStore.getStore().getIdByObjectId(result.getJsonArray(AIOpsObject.OBJECT_ID + CARET_SEPARATOR + VALUE).getInteger(index)) + SEPARATOR +\n                            result.getJsonArray(PolicyEngineConstants.POLICY_TYPE + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR + result.getJsonArray(METRIC + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR +\n                            result.getJsonArray(SEVERITY + CARET_SEPARATOR + VALUE).getValue(index) + SEPARATOR + CommonUtil.getString(values.getLong(index) / 1000L);\n                }\n\n                result.getJsonArray(IntegrationEngine.ACK_ID).add(IntegrationCacheStore.getStore().getTicketId(policyKey));\n            }\n        }\n\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"inventory\"))\n        {\n            var queryApps = false; // Boolean to know if the inventory category needs Application data.\n\n            var queryObjectSeverity = true; // it means it is an object level and false is instance level.\n\n            if (CommonUtil.traceEnabled())\n            {\n                logger.trace(\"inventory result received::\" + result);\n            }\n\n            var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, context.getString(User.USER_NAME, DEFAULT_USER));\n\n            var userGroups = user.getJsonArray(User.USER_GROUPS);\n\n            var containerTypeReport = context.containsKey(VisualizationConstants.CONTAINER_TYPE) && context.getString(VisualizationConstants.CONTAINER_TYPE) != null && context.getString(VisualizationConstants.CONTAINER_TYPE).equalsIgnoreCase(VisualizationConstants.CONTAINER_TYPE_REPORT);\n\n            var inventory = VISUALIZATION_INVENTORY_WIDGETS.getOrDefault(context.getString(VISUALIZATION_NAME), VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\"));\n\n            var category = inventory.getString(CATEGORY);\n\n            var queryCustomMonitoringFields = inventory.getBoolean(\"queryCustomMonitoringFields\"); // Boolean to know if the inventory category needs Custom Monitoring data.\n\n            var queryActiveAlerts = inventory.getBoolean(\"queryActiveAlerts\"); // Boolean to know if the inventory category needs Active Alert data.\n\n            if (context.getString(VISUALIZATION_NAME).equalsIgnoreCase(\"Service Check Inventory Summary\"))//for extra fields according to visualization\n            {\n                result.put(PORT, new JsonArray());\n            }\n            else if (context.getString(VISUALIZATION_NAME).equalsIgnoreCase(\"Server Inventory Summary\"))\n            {\n                result.put(NMSConstants.APPS, new JsonArray());\n\n                queryApps = true;\n            }\n\n            JsonArray objectsByCategory;\n\n            if (context.containsKey(VISUALIZATION_INVENTORY_TYPES) && !context.getJsonArray(VISUALIZATION_INVENTORY_TYPES).isEmpty())\n            {\n                objectsByCategory = ObjectConfigStore.getStore().getObjectIdsByTypes(context.getJsonArray(VISUALIZATION_INVENTORY_TYPES));\n            }\n            else\n            {\n                objectsByCategory = ObjectConfigStore.getStore().getObjectIdsByCategories(new JsonArray().add(category));\n            }\n\n            var entityQualified = true;\n\n            if (!result.containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()))//in case of no result will get data from config objects\n            {\n                result.getMap().computeIfAbsent(APIConstants.Entity.OBJECT.getName().toLowerCase(), value -> new JsonArray());\n\n                entityQualified = false;\n            }\n\n            var items = new HashSet<Integer>();\n\n            if (!entityQualified && !inventory.getString(INSTANCE).isEmpty())//in case of interface process or service inventory if no result entity result received so need to send back empty result to UI\n            {\n                VisualizationConstants.send(context.getString(VISUALIZATION_CATEGORY, VisualizationCategory.GRID.getName()), VisualizationConstants.VisualizationCategory.GRID.getName(), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, \"Inventory\", ErrorMessageConstants.INVALID_NO_DATA_FOUND), context.getLong(QUERY_ID), context.getLong(VisualizationConstants.SUB_QUERY_ID), logger, EVENT_DATASTORE_QUERY_RESPONSE);\n            }\n            else\n            {\n                for (var index = 0; index < objectsByCategory.size(); index++)\n                {\n                    if (!user.getLong(ID).equals(DEFAULT_ID))\n                    {\n                        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getIdByObjectId(objectsByCategory.getInteger(index)), false);\n\n                        if (userGroups.stream().anyMatch(item.getJsonArray(AIOpsObject.OBJECT_GROUPS)::contains))\n                        {\n                            items.add(objectsByCategory.getInteger(index));\n\n                            if (!entityQualified)\n                            {\n                                result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(objectsByCategory.getInteger(index));\n                            }\n                        }\n                    }\n                    else\n                    {\n                        items.add(objectsByCategory.getInteger(index));\n\n                        if (!entityQualified)\n                        {\n                            result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(objectsByCategory.getInteger(index));\n                        }\n                    }\n                }\n\n                var objects = result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase());\n\n                if (queryActiveAlerts)\n                {\n                    result.put(Severity.CRITICAL.name().toLowerCase(), new JsonArray()).put(Severity.DOWN.name().toLowerCase(), new JsonArray()).put(Severity.MAJOR.name().toLowerCase(), new JsonArray()).put(Severity.UNREACHABLE.name().toLowerCase(), new JsonArray()).put(Severity.WARNING.name().toLowerCase(), new JsonArray()).put(Severity.CLEAR.name().toLowerCase(), new JsonArray());\n                }\n\n                result.put(SEVERITY, new JsonArray());\n\n                //Adding all common columns for all objects\n                result.put(AIOpsObject.OBJECT_CREATION_TIME, new JsonArray()).put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()).put(AIOpsObject.OBJECT_DISCOVERY_METHOD, new JsonArray()).put(AIOpsObject.OBJECT_HOST, new JsonArray()).put(AIOpsObject.OBJECT_CATEGORY, new JsonArray()).put(AIOpsObject.OBJECT_GROUPS, new JsonArray()).put(AIOpsObject.OBJECT_TAGS, new JsonArray())\n                        .put(AIOpsObject.OBJECT_TARGET, new JsonArray()).put(AIOpsObject.OBJECT_NAME, new JsonArray()).put(AIOpsObject.OBJECT_VENDOR, new JsonArray()).put(AIOpsObject.OBJECT_TYPE, new JsonArray()).put(AIOpsObject.OBJECT_IP, new JsonArray()).put(STATUS, new JsonArray());\n\n                var customMonitoringFields = new HashMap<String, String>();\n\n                if (queryCustomMonitoringFields)\n                {\n                    //set Custom fields in result as it has dynamic keys.\n                    for (var customMonitoringId : CustomMonitoringFieldConfigStore.getStore().getFieldIds())\n                    {\n                        customMonitoringFields.put(customMonitoringId, CustomMonitoringFieldConfigStore.getStore().getItem(CommonUtil.getLong(customMonitoringId), false).getString(CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME));\n\n                        result.put(CustomMonitoringFieldConfigStore.getStore().getItem(CommonUtil.getLong(customMonitoringId)).getString(CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME), new JsonArray());\n                    }\n                }\n\n                JsonArray instances = null;\n\n                var instanceType = inventory.getString(INSTANCE, EMPTY_VALUE);\n\n                if (!inventory.getString(INSTANCE).isEmpty())\n                {\n                    instances = result.getJsonArray(inventory.getString(INSTANCE));\n\n                    result.put(INSTANCE_TAGS, new JsonArray());\n\n                    queryObjectSeverity = false; //instance level data\n                }\n\n                for (var i = 0; i < objects.size(); i++)\n                {\n                    var object = CommonUtil.getInteger(objects.getValue(i));\n\n                    var instance = EMPTY_VALUE;\n\n                    if (instances != null)\n                    {\n                        instance = CommonUtil.getString(instances.getValue(i));\n\n                        setTagInstances(result, object, instanceType.equalsIgnoreCase(NMSConstants.INTERFACE) ? result.getJsonArray(\"interface~index^last\").getString(i) : instance, instanceType, containerTypeReport);\n                    }\n\n                    items.remove(object);\n\n                    composeResult(result, customMonitoringFields, category, object, queryActiveAlerts, queryCustomMonitoringFields, queryApps, instance, queryObjectSeverity, containerTypeReport);\n                }\n\n                /*\n                    If an object exists in the Object config store, but the corresponding data is not available in the database, we will map that object in columnMappers and then will call composeResult.\n                 */\n                if (!items.isEmpty() && queryObjectSeverity)\n                {\n                    var dataPoints = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(DATA_POINTS);\n\n                    for (var item : items)\n                    {\n                        result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(item);\n\n                        for (var i = 0; i < dataPoints.size(); i++)\n                        {\n                            var dataPoint = dataPoints.getJsonObject(i);\n\n                            var mapper = columnMappers.getJsonObject(dataPoint.getString(DATA_POINT));\n\n                            if (mapper.getJsonArray(MAPPER_DATA_CATEGORIES).contains(CommonUtil.getInteger(DatastoreConstants.DataCategory.FLOAT.getName())))\n                            {\n                                result.getJsonArray(dataPoint.getString(DATA_POINT) + CARET_SEPARATOR + dataPoint.getString(AGGREGATOR)).add(\"-\" + Float.MAX_VALUE);\n                            }\n                            else if (mapper.getJsonArray(MAPPER_DATA_CATEGORIES).contains(CommonUtil.getInteger(DatastoreConstants.DataCategory.NUMERIC.getName())))\n                            {\n                                result.getJsonArray(dataPoint.getString(DATA_POINT) + CARET_SEPARATOR + dataPoint.getString(AGGREGATOR)).add(Long.MIN_VALUE);\n                            }\n                            else\n                            {\n                                result.getJsonArray(dataPoint.getString(DATA_POINT) + CARET_SEPARATOR + dataPoint.getString(AGGREGATOR)).add(EMPTY_VALUE);\n                            }\n                        }\n\n                        composeResult(result, customMonitoringFields, category, item, queryActiveAlerts, queryCustomMonitoringFields, queryApps, EMPTY_VALUE, true, containerTypeReport);\n                    }\n\n                }\n\n                if (context.containsKey(VISUALIZATION_TAGS) && !context.getJsonArray(VISUALIZATION_TAGS).isEmpty())\n                {\n                    joinTags(result, context);\n                }\n\n                if (CommonUtil.traceEnabled())\n                {\n                    logger.trace(\"composed inventory result::\" + result);\n                }\n            }\n        }\n\n        //To ensure accurate results and proper functionality in column settings, it is important to have \"monitor\" as \"Result By\" field. This will prevent users from adding \"Tags\" as a column if \"Tags\" or \"Group\" or else are already present in the \"Result By\" field.\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(VisualizationGrouping.TAG.getName()) && (context.containsKey(VISUALIZATION_TAGS) && !context.getJsonArray(VISUALIZATION_TAGS).isEmpty()))\n        {\n            joinTags(result, context);\n        }\n\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"log.event\"))\n        {\n            try\n            {\n                result.remove(EVENT_ID + CARET_SEPARATOR + VALUE);\n\n                var columns = context.containsKey(VISUALIZATION_EXTRA_COLUMNS) ? context.getJsonArray(VISUALIZATION_EXTRA_COLUMNS) : null;\n\n                var row = new JsonObject();\n\n                if (columns != null && !columns.isEmpty() && result.containsKey(EVENT + CARET_SEPARATOR + VALUE))\n                {\n                    for (var i = 0; i < result.getJsonArray(TIMESTAMP).size(); i++)\n                    {\n                        VisualizationConstants.extractEvent(Buffer.buffer(result.getJsonArray(EVENT + CARET_SEPARATOR + VALUE).getBinary(i)), row, null, null, logger);\n\n                        for (var j = 0; j < columns.size(); j++)\n                        {\n                            result.getMap().computeIfAbsent(columns.getString(j), val -> new JsonArray());\n\n                            result.getJsonArray(columns.getString(j)).add(CommonUtil.getString(row.getValue(columns.getString(j), EMPTY_VALUE)));\n                        }\n\n                        row.clear();\n                    }\n\n                    result.remove(EVENT + CARET_SEPARATOR + VALUE);\n                }\n            }\n            catch (Exception exception)\n            {\n                logger.error(exception);\n            }\n        }\n\n        //config.event -> This event will extract event^value data into simpler rows.\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"config.event\"))\n        {\n            var composedResult = composeEventResponse(result.getJsonArray((ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE)), logger);\n\n            for (var i = 0; i < composedResult.getJsonArray(ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE).size(); i++)\n            {\n                var row = composedResult.getJsonArray(ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE).getJsonObject(i);\n\n                for (var key : ConfigConstants.CONFIG_ACTION_FIELDS) // We will be adding dummy value for the columns which are not present in the old data in DB\n                {\n                    if (row.containsKey(key))\n                    {\n                        if (result.containsKey(key))\n                        {\n                            result.getJsonArray(key).add(row.getValue(key));\n                        }\n                        else\n                        {\n                            result.getMap().computeIfAbsent(key, value -> new JsonArray().add(row.getValue(key)));\n                        }\n                    }\n                    else\n                    {\n                        if (result.containsKey(key))\n                        {\n                            result.getJsonArray(key).add(EMPTY_VALUE);\n                        }\n                        else\n                        {\n                            result.getMap().computeIfAbsent(key, value -> new JsonArray().add(EMPTY_VALUE));\n                        }\n                    }\n                }\n            }\n\n            //removing event^value from the map after fetching data from it.\n            result.remove(ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE);\n        }\n\n        //object.columns -> This join result will be used to merge the config related info of object with widget query response.\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"object.columns\"))\n        {\n            joinColumns(result, context);\n        }\n\n        //  instance.ip -> This join will be used to get the instance ip of the instances.\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"instance.ip\"))\n        {\n            if (!context.getJsonArray(VISUALIZATION_RESULT_BY).isEmpty() && NMSConstants.INSTANCE_IP_METRICS.containsKey(context.getJsonArray(VISUALIZATION_RESULT_BY).getString(0)))\n            {\n                result.put(INSTANCE_IP, new JsonArray());\n\n                for (var i = 0; i < result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).size(); i++)\n                {\n                    result.getJsonArray(INSTANCE_IP).add(\n                            ObjectCacheStore.getStore().getInstanceIP(\n                                    ObjectConfigStore.getStore().getIdByObjectId(Integer.parseInt(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getString(i))),\n                                    result.getJsonArray(context.getJsonArray(VISUALIZATION_RESULT_BY).getString(0)).getString(i)));\n                }\n            }\n\n            // In case of tag as column and instance.ip both are required but we can not use two use join types, we need to check for the tag as a column.\n            if (context.containsKey(VISUALIZATION_TAGS) && !context.getJsonArray(VISUALIZATION_TAGS).isEmpty())\n            {\n                joinTags(result, context);\n            }\n        }\n\n        /*\n            Deleted instances are still shown in string-level counters because garbage data is not removed from the database.\n            Similarly, in the switch port view widget, deleted interfaces appear as we query only string counters.\n            However, when an interface is deleted, its instance is removed from the cache store.\n            To ensure accuracy, the port view widget considers only instances available in the cache store.\n         */\n\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"port.view\"))\n        {\n            var values = result.getJsonArray(INTERFACE);\n\n            var items = ObjectStatusCacheStore.getStore().getInstanceItems(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(0))), false);\n\n            var indices = new HashSet<Integer>();\n\n            var plugin = ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName());\n\n            for (var i = 0; i < values.size(); i++)\n            {\n                if (items.containsKey(NMSConstants.INTERFACE + INSTANCE_SEPARATOR + values.getString(i) + INSTANCE_SEPARATOR + plugin))\n                {\n                    indices.add(i);\n                }\n            }\n\n            if (!indices.isEmpty())\n            {\n                var record = new JsonObject();\n\n                for (var entry : result.getMap().entrySet())\n                {\n                    var entities = ((JsonArray) entry.getValue());\n\n                    record.getMap().computeIfAbsent(entry.getKey(), value -> new JsonArray());\n\n                    for (var index : indices)\n                    {\n                        record.getJsonArray(entry.getKey()).add(entities.getValue(CommonUtil.getInteger(index)));\n                    }\n                }\n\n                result.mergeIn(record);\n            }\n        }\n\n        // Check if the join result type is \"raw\" and remove the \"OBJECT_ID\" key from the result if it exists.\n        // This ensures that raw data does not include object.id-specific identifiers.\n        else if (context.getString(VisualizationConstants.JOIN_RESULT).equalsIgnoreCase(\"raw\"))\n        {\n            try\n            {\n                //For Instance Counter, we need to remove the instance as well as object.id\n                for (var entry : result.getMap().keySet())\n                {\n                    if (entry.contains(INSTANCE_SEPARATOR))\n                    {\n                        var instance = entry.split(INSTANCE_SEPARATOR)[0];\n\n                        if (result.containsKey(instance))\n                        {\n                            result.remove(instance);\n\n                            break;\n                        }\n                    }\n                }\n\n                //For Scaler Counter, need to only remove the object.id\n                if (result.containsKey(AIOpsObject.OBJECT_ID))\n                {\n                    result.remove(AIOpsObject.OBJECT_ID);\n                }\n\n            }\n            catch (Exception exception)\n            {\n                logger.error(exception);\n            }\n        }\n    }\n\n    private static void setTagInstances(JsonObject result, int object, String instance, String instanceType, boolean containerTypeReport)\n    {\n        try\n        {\n            var builder = new StringBuilder();\n\n            var tags = TagCacheStore.getStore().getTags(object + KEY_SEPARATOR + instanceType + KEY_SEPARATOR + instance);\n\n            if (tags != null && !tags.isEmpty())\n            {\n                for (var tag : tags)\n                {\n                    if (!builder.isEmpty())\n                    {\n                        builder.append(containerTypeReport ? COMMA_SEPARATOR : VALUE_SEPARATOR); // Append Value separator if not the first string\n                    }\n\n                    builder.append(TagConfigStore.getStore().getTag(CommonUtil.getLong(tag)));\n                }\n            }\n            else\n            {\n                builder.append(EMPTY_VALUE);\n            }\n\n            result.getJsonArray(INSTANCE_TAGS).add(builder);\n\n        }\n        catch (Exception ignored)\n        {\n\n        }\n    }\n\n    private static JsonObject composeEventResponse(JsonArray rows, Logger logger)\n    {\n        var result = new JsonObject();\n\n        for (var i = 0; i < rows.size(); i++)\n        {\n            var event = new JsonObject();\n\n            extractEvent(Buffer.buffer(rows.getBinary(i)), event, null, null, logger);\n\n            event.remove(LogEngineConstants.EVENT_PATTERN_ID);\n\n            VisualizationConstants.compose(new JsonObject().put(ConfigConstants.CONFIG_EVENT + CARET_SEPARATOR + VALUE, event), result);\n        }\n\n        return result;\n    }\n\n    private static void joinTags(JsonObject result, JsonObject context)\n    {\n        //HashSet is used for unique tags, if tags are same but have lowercase/uppercase, it will create packing error thus will only add only one of them.\n        var visualizationTags = new HashSet<String>();\n\n        for (var i = 0; i < context.getJsonArray(VISUALIZATION_TAGS).size(); i++)\n        {\n            //if tag is already a key in result, it would ignore the tag...\n            if (!result.containsKey(context.getJsonArray(VISUALIZATION_TAGS).getString(i)))\n            {\n                visualizationTags.add(context.getJsonArray(VISUALIZATION_TAGS).getString(i).toLowerCase());\n            }\n        }\n\n        if (result.containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()) && !result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).isEmpty())\n        {\n            var instanceType = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getValue(INSTANCE_TYPE);\n\n            var groupBy = context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getString(GROUP_BY);\n\n            var instanceTypes = new HashSet<String>();\n\n            // we need to check if the instance is a string or an array of strings\n            if (instanceType instanceof JsonArray instances)\n            {\n                for (var i = 0; i < instances.size(); i++)\n                {\n                    var type = instances.getString(i);\n\n                    // if the instance empty and if group by is in PASSOVER_INSTANCES than we need to consider it as a object rather than instance\n                    if (type != null && !type.isEmpty() && !PASSOVER_INSTANCES.contains(groupBy))\n                    {\n                        instanceTypes.add(type);\n                    }\n                }\n            }\n            else if ((instanceType instanceof String) && (!CommonUtil.getString(instanceType).isEmpty()))\n            {\n                instanceTypes.add(CommonUtil.getString(instanceType));\n            }\n\n            if (!instanceTypes.isEmpty())\n            {\n                var builder = new StringBuilder();\n\n                var instance = EMPTY_VALUE;\n\n                for (var i = 0; i < result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).size(); i++)\n                {\n                    for (var visualizationTag : visualizationTags)\n                    {\n                        result.getMap().computeIfAbsent(visualizationTag, value -> new JsonArray());\n\n                        JsonArray tags = null;\n\n                        for (String type : instanceTypes)\n                        {\n                            /*\n                             * In case of Inventory report, we will have VMs a column which includes all the instances of the object, and type of instance will be retrieved from the \"group.by\"\n                             * In case of widget or availability Report, we will have instance.type as a column (i.e. \"esxi.vm\" as a column) and instance type will be retrieved from \"visualization.result.by\"\n                             * */\n                            if (context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).containsKey(VISUALIZATION_RESULT_BY) && context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VISUALIZATION_RESULT_BY).contains(type) && result.containsKey(type))\n                            {\n                                instance = CommonUtil.getString(result.getJsonArray(type).getValue(i));\n                            }\n                            else if (result.containsKey(groupBy))\n                            {\n                                instance = CommonUtil.getString(result.getJsonArray(groupBy).getValue(i));\n                            }\n\n                            if (context.containsKey(VisualizationConstants.CONTAINER_TYPE) && context.getString(VisualizationConstants.CONTAINER_TYPE) != null && context.getString(VisualizationConstants.CONTAINER_TYPE).equalsIgnoreCase(VisualizationConstants.CONTAINER_TYPE_REPORT))\n                            {\n                                /*\n                                 * first we need to check for the combination of instance and object id to verify instance exist for the object id\n                                 * i.e.  object.id is 22 and instance.types are [\"esxi.vm\", \"citrixxen.vm\", \"hyperv.vm\"], in such case we need to check for the which instance belongs to respected object.id\n                                 *       here esxi.vm is instance of object.id 22, if we found tags for the instance (22 + esxi.vm) then we can break the loop\n                                 * */\n                                if (TagCacheStore.getStore().existTag(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), type))\n                                {\n                                    tags = TagCacheStore.getStore().getInstanceTagKeys(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), visualizationTag, instance, type);\n\n                                    break;  // if we found the instance tag for the object id then we can break the loop\n                                }\n                            }\n                            else\n                            {\n                                if (context.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VISUALIZATION_RESULT_BY).contains(type) && result.containsKey(type))\n                                {\n                                    tags = TagCacheStore.getStore().getInstanceTagKeys(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), visualizationTag, instance, type);\n                                }\n                                else\n                                {\n                                    //Result By - Monitor for Instance Counter will send all the instance tags.\n                                    tags = TagCacheStore.getStore().getTagsByTagKey(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), type, visualizationTag);\n                                }\n                                break;\n                            }\n\n                        }\n\n                        if (tags != null)\n                        {\n                            for (var tag : tags)\n                            {\n                                if (!builder.isEmpty())\n                                {\n                                    builder.append(COMMA_SEPARATOR); // Append Value separator if not the first string\n                                }\n\n                                builder.append(tag);\n                            }\n\n                            result.getJsonArray(visualizationTag.toLowerCase()).add(builder.toString());\n\n                            builder.setLength(0);\n\n                        }\n                        else\n                        {\n                            result.getJsonArray(visualizationTag.toLowerCase()).add(EMPTY_VALUE);\n                        }\n                    }\n                }\n            }\n            else\n            {\n                for (var i = 0; i < result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).size(); i++)\n                {\n                    for (var visualizationTag : visualizationTags)\n                    {\n                        result.getMap().computeIfAbsent(visualizationTag, value -> new JsonArray());\n\n                        var tags = TagCacheStore.getStore().getObjectTagsByTagKey(CommonUtil.getInteger(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getValue(i)), visualizationTag);\n\n                        if (tags != null)\n                        {\n                            result.getJsonArray(visualizationTag.toLowerCase()).add(StringUtils.join(tags, COMMA_SEPARATOR));\n                        }\n                        else\n                        {\n                            result.getJsonArray(visualizationTag.toLowerCase()).add(EMPTY_VALUE);\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    private static void joinColumns(JsonObject result, JsonObject context)\n    {\n        var containerTypeReport = context.containsKey(VisualizationConstants.CONTAINER_TYPE) && context.getString(VisualizationConstants.CONTAINER_TYPE) != null && context.getString(VisualizationConstants.CONTAINER_TYPE).equalsIgnoreCase(VisualizationConstants.CONTAINER_TYPE_REPORT);\n\n        //Adding all common columns for all objects\n        result.put(AIOpsObject.OBJECT_CREATION_TIME, new JsonArray()).put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()).put(AIOpsObject.OBJECT_DISCOVERY_METHOD, new JsonArray()).put(AIOpsObject.OBJECT_HOST, new JsonArray()).put(AIOpsObject.OBJECT_CATEGORY, new JsonArray()).put(AIOpsObject.OBJECT_GROUPS, new JsonArray()).put(AIOpsObject.OBJECT_TAGS, new JsonArray()).put(AIOpsObject.OBJECT_TARGET, new JsonArray()).put(AIOpsObject.OBJECT_NAME, new JsonArray()).put(AIOpsObject.OBJECT_VENDOR, new JsonArray()).put(AIOpsObject.OBJECT_TYPE, new JsonArray()).put(AIOpsObject.OBJECT_IP, new JsonArray()).put(STATUS, new JsonArray());\n\n        for (var i = 0; i < result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).size(); i++)\n        {\n            setObjectColumns(result, ObjectConfigStore.getStore().getItemByObjectId(Integer.parseInt(result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).getString(i))), containerTypeReport);\n        }\n    }\n\n    /*\n     This function would compose the result as per the objects and set custom monitoring fields, object columns, active alerts.\n     */\n    private static void composeResult(JsonObject result, Map<String, String> customMonitoringFields, String category, Integer object, boolean activeAlerts, boolean customMonitoring, boolean application, String instance, boolean queryObjectSeverity, boolean containerTypeReport)\n    {\n        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getIdByObjectId(object), false);\n\n        if (item == null)\n        {\n            item = ArchivedObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_ID, object);\n        }\n\n        if ((queryObjectSeverity || (instance != null && !instance.equalsIgnoreCase(EMPTY_VALUE))))\n        {\n            setSeverityColumns(result, item.getLong(ID), instance);\n        }\n\n        setObjectColumns(result, item, containerTypeReport);\n\n        if (activeAlerts)\n        {\n            setPolicyStats(result, item.getLong(ID));\n        }\n\n        if (customMonitoring)\n        {\n            setCustomMonitoringFields(result, item, customMonitoringFields);\n        }\n\n        if (category.equalsIgnoreCase(NMSConstants.Category.SERVER.getName()) && application)\n        {\n            setApps(result, item.getLong(ID));\n        }\n        else if (category.equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))\n        {\n            if (item.containsKey(AIOpsObject.OBJECT_CONTEXT) && item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).containsKey(PORT))\n            {\n                result.getJsonArray(PORT).add(item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT));\n            }\n            else\n            {\n                result.getJsonArray(PORT).add(NOT_AVAILABLE);\n            }\n        }\n    }\n\n    private static void setSeverityColumns(JsonObject result, Long id, String instance)\n    {\n        if (instance != null && !instance.isEmpty())\n        {\n            result.getJsonArray(SEVERITY).add(MetricPolicyCacheStore.getStore().getInstanceSeverity(id + SEPARATOR + instance) != null ? MetricPolicyCacheStore.getStore().getInstanceSeverity(id + SEPARATOR + instance) : Severity.UNKNOWN.name());\n        }\n        else\n        {\n            result.getJsonArray(SEVERITY).add(MetricPolicyCacheStore.getStore().getSeverity(id) != null ? MetricPolicyCacheStore.getStore().getSeverity(id) : Severity.UNKNOWN.name());\n        }\n    }\n\n    private static void setObjectColumns(JsonObject result, JsonObject object, boolean containerTypeReport)\n    {\n        result.getJsonArray(AIOpsObject.OBJECT_CREATION_TIME).add(object.getValue(AIOpsObject.OBJECT_CREATION_TIME_SECONDS, 0));\n\n        if (object.containsKey(AIOpsObject.OBJECT_EVENT_PROCESSORS))\n        {\n            var objectEventProcessors = object.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS);\n\n            var builder = new StringBuilder();\n\n            if (!objectEventProcessors.isEmpty())\n            {\n                for (var objectEventProcessor : objectEventProcessors)\n                {\n                    if (!builder.isEmpty())\n                    {\n                        builder.append(COMMA_SEPARATOR); // Append comma separator if not the first string\n                    }\n\n                    builder.append(RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(objectEventProcessor)).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP));\n                }\n            }\n            else\n            {\n                builder.append(EMPTY_VALUE);\n            }\n\n            result.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).add(builder);\n        }\n        else\n        {\n            result.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).add(EMPTY_VALUE);\n        }\n\n        result.getJsonArray(AIOpsObject.OBJECT_TARGET).add(object.getString(AIOpsObject.OBJECT_TARGET, EMPTY_VALUE));\n\n        result.getJsonArray(AIOpsObject.OBJECT_DISCOVERY_METHOD).add(object.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD, EMPTY_VALUE));\n\n        result.getJsonArray(AIOpsObject.OBJECT_CATEGORY).add(object.getString(AIOpsObject.OBJECT_CATEGORY, EMPTY_VALUE));\n\n        result.getJsonArray(AIOpsObject.OBJECT_HOST).add(object.getString(AIOpsObject.OBJECT_HOST, EMPTY_VALUE));\n\n        result.getJsonArray(AIOpsObject.OBJECT_NAME).add(object.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE));\n\n        result.getJsonArray(AIOpsObject.OBJECT_TYPE).add(object.getString(AIOpsObject.OBJECT_TYPE, EMPTY_VALUE));\n\n        result.getJsonArray(AIOpsObject.OBJECT_VENDOR).add(object.getString(AIOpsObject.OBJECT_VENDOR, EMPTY_VALUE));\n\n        result.getJsonArray(AIOpsObject.OBJECT_IP).add(object.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE));\n\n        result.getJsonArray(STATUS).add(ObjectStatusCacheStore.getStore().existItem(object.getLong(ID)) ? ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)) : STATUS_UNKNOWN);\n\n        if (object.containsKey(AIOpsObject.OBJECT_TAGS))\n        {\n            var tags = object.getJsonArray(AIOpsObject.OBJECT_TAGS);\n\n            var builder = new StringBuilder();\n\n            if (tags != null && !tags.isEmpty())\n            {\n                for (var tag : tags)\n                {\n                    if (!builder.isEmpty())\n                    {\n                        builder.append(containerTypeReport ? COMMA_SEPARATOR : VALUE_SEPARATOR); // Append Value separator if not the first string\n                    }\n\n                    builder.append(TagConfigStore.getStore().getTag(CommonUtil.getLong(tag)));\n                }\n            }\n            else\n            {\n                builder.append(EMPTY_VALUE);\n            }\n\n            result.getJsonArray(AIOpsObject.OBJECT_TAGS).add(builder);\n        }\n        else\n        {\n            result.getJsonArray(AIOpsObject.OBJECT_TAGS).add(EMPTY_VALUE);\n        }\n\n        if (object.containsKey(AIOpsObject.OBJECT_GROUPS))\n        {\n            var builder = new StringBuilder();\n\n            var groups = object.getJsonArray(AIOpsObject.OBJECT_GROUPS);\n\n            if (!groups.isEmpty())\n            {\n                for (var group : groups)\n                {\n                    if (!builder.isEmpty())\n                    {\n                        builder.append(COMMA_SEPARATOR); // Append comma separator if not the first string\n                    }\n\n                    // In Report, we require to have group name instead of group ID\n                    builder.append(containerTypeReport ? GroupConfigStore.getStore().getItem(CommonUtil.getLong(group), false).getString(Group.FIELD_GROUP_NAME) : group);\n                }\n            }\n            else\n            {\n                builder.append(EMPTY_VALUE);\n            }\n\n            result.getJsonArray(AIOpsObject.OBJECT_GROUPS).add(builder);\n        }\n    }\n\n    private static void setApps(JsonObject result, long id)\n    {\n        try\n        {\n            var builder = new StringBuilder();\n\n            var apps = MetricConfigStore.getStore().getAppsByObjectId(id);\n\n            if (apps != null && !apps.isEmpty())\n            {\n                for (var app : apps.getMap().entrySet())\n                {\n                    if (!builder.isEmpty())\n                    {\n                        builder.append(COMMA_SEPARATOR); // Append comma separator if not the first string\n                    }\n\n                    builder.append(CommonUtil.getString(app.getValue()));\n                }\n            }\n            else\n            {\n                builder.append(EMPTY_VALUE);\n            }\n\n            result.getJsonArray(NMSConstants.APPS).add(builder);\n\n        }\n        catch (Exception ignored)\n        {\n\n        }\n    }\n\n    private static void setCustomMonitoringFields(JsonObject result, JsonObject object, Map<String, String> customMonitoringFields)\n    {\n        if (object.containsKey(AIOpsObject.OBJECT_CUSTOM_FIELDS))\n        {\n            var fields = object.getJsonObject(AIOpsObject.OBJECT_CUSTOM_FIELDS);\n\n            for (var customMonitoringField : customMonitoringFields.entrySet())\n            {\n                if (fields.containsKey(customMonitoringField.getKey()))\n                {\n                    result.getJsonArray(customMonitoringField.getValue()).add(fields.getString(customMonitoringField.getKey()));\n                }\n                else\n                {\n                    result.getJsonArray(customMonitoringField.getValue()).add(EMPTY_VALUE);\n                }\n            }\n        }\n        else\n        {\n            for (var customMonitoringField : customMonitoringFields.entrySet())\n            {\n                result.getJsonArray(customMonitoringField.getValue()).add(EMPTY_VALUE);\n            }\n        }\n    }\n\n    private static void setPolicyStats(JsonObject result, long id)\n    {\n        var items = MetricPolicyCacheStore.getStore().getSeveritiesByObject(id);\n\n        result.getJsonArray(Severity.CRITICAL.name().toLowerCase()).add(items.getValue(Severity.CRITICAL.name().toLowerCase()));\n\n        result.getJsonArray(Severity.CLEAR.name().toLowerCase()).add(items.getValue(Severity.CLEAR.name().toLowerCase()));\n\n        result.getJsonArray(Severity.MAJOR.name().toLowerCase()).add(items.getValue(Severity.MAJOR.name().toLowerCase()));\n\n        result.getJsonArray(Severity.DOWN.name().toLowerCase()).add(items.getValue(Severity.DOWN.name().toLowerCase()));\n\n        result.getJsonArray(Severity.UNREACHABLE.name().toLowerCase()).add(items.getValue(Severity.UNREACHABLE.name().toLowerCase()));\n\n        result.getJsonArray(Severity.WARNING.name().toLowerCase()).add(items.getValue(Severity.WARNING.name().toLowerCase()));\n    }\n\n    public static String transformColumn(String column, String value, String type)\n    {\n        String result = null;\n\n        if (column.equalsIgnoreCase(SNMPTrapProcessor.SNMP_TRAP_OID))\n        {\n            var profile = SNMPTrapProfileConfigStore.getStore().getTrapProfiles(value);\n\n            if (profile != null)\n            {\n                result = profile.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_NAME);\n            }\n            else\n            {\n                result = value;\n            }\n        }\n        else if (column.equalsIgnoreCase(PolicyEngineConstants.POLICY_ID))\n        {\n            if (type.equalsIgnoreCase(APIConstants.Entity.NETROUTE.getName()) || type.equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()) || type.equalsIgnoreCase(VisualizationDataSource.NETROUTE_EVENT.getName()))\n            {\n                if (NetRoutePolicyConfigStore.getStore().existItem(CommonUtil.getLong(value)))\n                {\n                    result = NetRoutePolicyConfigStore.getStore().getItem(CommonUtil.getLong(value), false).getString(PolicyEngineConstants.POLICY_NAME);\n                }\n                else\n                {\n                    result = value;\n                }\n            }\n            else\n            {\n                var policy = MetricPolicyConfigStore.getStore().getItem(CommonUtil.getLong(value));\n\n                if (policy != null)\n                {\n                    result = policy.getString(PolicyEngineConstants.POLICY_NAME);\n                }\n                else\n                {\n                    policy = EventPolicyConfigStore.getStore().getItem(CommonUtil.getLong(value), false);\n\n                    if (policy != null)\n                    {\n                        result = policy.getString(PolicyEngineConstants.POLICY_NAME);\n                    }\n                    else\n                    {\n                        result = value;\n                    }\n                }\n            }\n        }\n\n        else if (column.equalsIgnoreCase(NetRoute.NETROUTE_ID))\n        {\n            var item = NetRouteConfigStore.getStore().getItem(CommonUtil.getLong(value));\n\n            if (item != null)\n            {\n                result = item.getString(NetRoute.NETROUTE_NAME);\n            }\n            else\n            {\n                result = value;\n            }\n        }\n\n        return result;\n    }\n\n    private static void resolve(JsonObject result, String column, int index, String category)\n    {\n        var iterator = result.iterator();\n\n        var partialResult = new JsonObject();\n\n        while (iterator.hasNext())\n        {\n            var value = iterator.next();\n\n            var tokens = value.getKey().split(CARET_SEPARATOR_WITH_ESCAPE);\n\n            if (tokens.length > 1)\n            {\n                var transformedValue = transformColumn(column, tokens[index], category);\n\n                if (transformedValue != null)\n                {\n                    partialResult.put(value.getKey().replace(tokens[index], transformedValue), value.getValue());\n\n                    iterator.remove();\n                }\n            }\n        }\n\n        if (!partialResult.isEmpty())\n        {\n            result.mergeIn(partialResult);\n        }\n    }\n\n    public static void send(Logger logger, JsonArray ids)\n    {\n        var context = new HashMap<Integer, Object>();\n\n        for (var entry : DataRetentionPolicyConfigStore.getStore().getItem().getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT))\n        {\n            if (!entry.getKey().equalsIgnoreCase(\"CONFIG\"))//as Config is internal component so need to send its data to DB\n            {\n                context.put(DatastoreConstants.DatastoreType.valueOf(entry.getKey()).ordinal(), entry.getValue());\n            }\n        }\n\n        var buffer = Buffer.buffer();\n\n        if (CommonUtil.traceEnabled())\n        {\n            logger.trace(\"Data Retention Policy columns updated sending to DB:\" + JsonObject.mapFrom(context).encodePrettily());\n        }\n\n        buffer.appendByte(DatastoreConstants.OperationType.STORE_RETENTION.getName()).appendBytes((JsonObject.mapFrom(context)).encode().getBytes());\n\n        for (var id : ids)\n        {\n            var item = RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(id));\n\n            if (item != null && item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))\n            {\n                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject()\n                        .put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)\n                        .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)\n                        .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))\n                        .put(EVENT_CONTEXT, buffer.getBytes()));\n            }\n        }\n    }\n\n    public static void sendCycleStartNotification(String sloId, long cycleId, long startTime, Logger logger)\n    {\n        try\n        {\n            var context = new HashMap<String, JsonArray>();\n\n            // in case of motadata or db restarts\n            if (sloId.equalsIgnoreCase(EMPTY_VALUE))\n            {\n                var items = SLOCycleConfigStore.getStore().getItems();\n\n                for (var index = 0; index < items.size(); index++)\n                {\n                    var item = items.getJsonObject(index);\n\n                    sloId = item.getString(SLOCycle.SLO_PROFILE_ID);\n\n                    cycleId = item.getLong(ID);\n\n                    startTime = item.getLong(SLOCycle.SLO_CYCLE_START_TIME);\n\n                    context.computeIfAbsent(sloId, value -> new JsonArray()).add(new JsonObject()\n                            .put(SLOCycle.SLO_CYCLE_START_TIME, startTime)\n                            .put(SLOProfile.SLO_CYCLE_ID, cycleId));\n                }\n            }\n            else\n            {\n                context.put(sloId, new JsonArray().add(new JsonObject()\n                        .put(SLOCycle.SLO_CYCLE_START_TIME, startTime)\n                        .put(SLOProfile.SLO_CYCLE_ID, cycleId)));\n            }\n\n            var buffer = Buffer.buffer();\n\n            buffer.appendByte(DatastoreConstants.OperationType.START_SLO_CYCLE.getName()).appendBytes((JsonObject.mapFrom(context)).encode().getBytes());\n\n            var items = RemoteEventProcessorConfigStore.getStore().getItems();\n\n            for (var index = 0; index < items.size(); index++)\n            {\n                var item = items.getJsonObject(index);\n\n                if (item != null && item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))\n                {\n                    Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject()\n                            .put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)\n                            .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)\n                            .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))\n                            .put(EVENT_CONTEXT, buffer.getBytes()));\n                }\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n    }\n\n    public static void setQueryParameters(JsonObject visualizationContext, JsonObject subQueryContext, Set<String> aggregations, JsonObject queryContext)\n    {\n        var priority = QueryPriority.P0.getName();\n\n        if (visualizationContext.containsKey(PolicyEngineConstants.POLICY_ID))\n        {\n            priority = QueryPriority.P3.getName();\n        }\n        else if (visualizationContext.containsKey(ReportConstants.REPORT_ID))\n        {\n            priority = QueryPriority.P4.getName();\n        }\n        else if (visualizationContext.containsKey(VisualizationConstants.VISUALIZATION_STREAMING))\n        {\n            priority = QueryPriority.P2.getName();\n        }\n\n        subQueryContext.put(QUERY_PRIORITY, priority);\n\n        subQueryContext.put(QUERY_CREATION_TIME, DateTimeUtil.currentSeconds());\n\n        var visualizationDataSources = subQueryContext.getJsonObject(VisualizationConstants.VISUALIZATION_DATA_SOURCES);\n\n        if (visualizationDataSources.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY.getName()))\n        {\n            if (visualizationDataSources.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(METRIC))\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.METRIC_POLICY.ordinal());\n            }\n\n            else\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.EVENT_POLICY.ordinal());\n            }\n            //MOTADATA-2396\n            if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.TRAP.getName()) && (visualizationDataSources.containsKey(VisualizationConstants.VISUALIZATION_DRILL_DOWN) && visualizationDataSources.getString(VISUALIZATION_DRILL_DOWN).equalsIgnoreCase(YES)))\n            {\n                subQueryContext.put(VISUALIZATION_DRILL_DOWN, YES);\n            }\n        }\n\n        else if (visualizationDataSources.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_ACKNOWLEDGEMENT.getName()))\n        {\n            if (visualizationDataSources.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(METRIC))\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.METRIC_POLICY_ACKNOWLEDGEMENT.ordinal());\n            }\n\n            else\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.EVENT_POLICY_ACKNOWLEDGEMENT.ordinal());\n            }\n        }\n\n        else if (visualizationDataSources.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()))\n        {\n            if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.USER_NOTIFICATION.getName()))\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.NOTIFICATION.ordinal());\n            }\n\n            else if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.AUDIT.getName()))\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.AUDIT.ordinal());\n            }\n\n            else if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.FLOW.getName()))\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.FLOW.ordinal());\n            }\n\n            else if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.PERFORMANCE_METRIC.getName()))\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.METRIC_POLICY.ordinal());\n            }\n\n            else if (visualizationDataSources.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.LOG.getName()))\n            {\n                visualizationDataSources.put(VisualizationConstants.TYPE, DatastoreConstants.DatastoreType.LOG.ordinal());\n            }\n\n            subQueryContext.put(VISUALIZATION_DRILL_DOWN, YES);\n        }\n\n        else\n        {\n            visualizationDataSources.put(VisualizationConstants.TYPE, DATASTORE_TYPES.get(visualizationDataSources.getString(TYPE)));\n        }\n\n        if (aggregations != null && aggregations.size() == 1 && !visualizationDataSources.getString(TYPE).equalsIgnoreCase(CommonUtil.getString(DatastoreConstants.DatastoreType.OBJECT_STATUS_FLAP_METRIC.ordinal())))\n        {\n            for (var aggregation : aggregations)\n            {\n                if (aggregation.equalsIgnoreCase(EMPTY_VALUE))\n                {\n                    if (!visualizationDataSources.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()))\n                    {\n                        subQueryContext.put(QUERY_ABORT_REQUIRED, YES);\n                    }\n\n                    subQueryContext.put(VISUALIZATION_DRILL_DOWN, YES);\n\n                    if (queryContext != null)\n                    {\n                        queryContext.put(VISUALIZATION_DRILL_DOWN, YES);\n                    }\n                }\n            }\n        }\n    }\n\n    protected static void validateFilters(JsonObject filters)\n    {\n        if (filters != null && !filters.isEmpty())\n        {\n            var filter = filters.getJsonObject(DATA_FILTER);\n\n            if (filter != null && !filter.isEmpty() && filter.containsKey(CONDITION_GROUPS))\n            {\n                var groups = filter.getJsonArray(CONDITION_GROUPS);\n\n                if (groups.size() > 3)\n                {\n                    for (var i = 3; i < groups.size(); i++)\n                    {\n                        groups.remove(i);\n                    }\n                }\n            }\n        }\n    }\n\n    // this method converts Long object.id into the small object.id in filters\n    protected static void enrichFilters(JsonObject filters)\n    {\n        if (filters != null && !filters.isEmpty())\n        {\n            var filter = filters.getJsonObject(DATA_FILTER);\n\n            if (filter != null && !filter.isEmpty() && filter.containsKey(CONDITION_GROUPS))\n            {\n                var groups = filter.getJsonArray(CONDITION_GROUPS);\n\n                for (var index = 0; index < groups.size(); index++)\n                {\n                    var group = groups.getJsonObject(index);\n\n                    if (group.containsKey(CONDITIONS))\n                    {\n                        var conditions = group.getJsonArray(CONDITIONS);\n\n                        for (var j = 0; j < conditions.size(); j++)\n                        {\n                            var condition = conditions.getJsonObject(j);\n\n                            if (condition.containsKey(OPERAND) && condition.getString(OPERAND).equalsIgnoreCase(AIOpsObject.OBJECT_ID))\n                            {\n                                condition.put(VALUE, ObjectConfigStore.getStore().getObjectId(condition.getLong(VALUE)));\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    /*\n      This function will set filteredEntities from TagCacheStore, as when the data of result By and InstanceType is same, thus filtering out and put it in entity.keys.\n     */\n    public static void setTagInstanceEntities(JsonArray pluginIds, String column, Map<String, String> entities, Map<String, String> entityKeys, JsonObject filteredEntities, Map<String, Set<Integer>> pluginEntities, Set<Object> plugins)\n    {\n        for (var i = 0; i < filteredEntities.getJsonArray(ID).size(); i++)\n        {\n            entityKeys.put(filteredEntities.getJsonArray(ID).getString(i) + CARET_SEPARATOR + filteredEntities.getJsonArray(INSTANCE).getString(i) + CARET_SEPARATOR + column, pluginIds.getLong(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginIds.getInteger(0)));\n\n            entities.put(filteredEntities.getJsonArray(ID).getString(i), pluginIds.getLong(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginIds.getInteger(0)));\n\n            plugins.add(pluginIds.getLong(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginIds.getInteger(0)));\n\n            pluginEntities.get(pluginIds.getLong(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginIds.getInteger(0))).add(CommonUtil.getInteger(filteredEntities.getJsonArray(ID).getString(i)));\n\n        }\n    }\n\n    /*\n     Set entities as per MetricConfigStore for InstanceKeys, if only single monitor is qualified then will be sending entity keys also.\n     */\n    public static void setEntities(JsonArray pluginIds, String column, Map<String, String> entities, Map<String, String> entityKeys, Map<String, Set<Integer>> pluginEntities, Set<Object> plugins, JsonArray instanceKeys, Set<Integer> items)\n    {\n        for (var item : MetricConfigStore.getStore().getInstanceObjectsByPlugins(items, pluginIds))\n        {\n            if (item.getJsonArray(NMSConstants.OBJECTS) != null)\n            {\n                for (var instance : item.getJsonArray(NMSConstants.OBJECTS))\n                {\n                    if (!instanceKeys.isEmpty())\n                    {\n                        if (instanceKeys.contains(instance))\n                        {\n                            entityKeys.put(item.getInteger(AIOpsObject.OBJECT_ID) + CARET_SEPARATOR + instance + CARET_SEPARATOR + column, item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID)));\n                        }\n                    }\n                    else\n                    {\n                        entityKeys.put(item.getInteger(AIOpsObject.OBJECT_ID) + CARET_SEPARATOR + instance + CARET_SEPARATOR + column, item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID)));\n                    }\n                }\n\n                entities.put(CommonUtil.getString(item.getValue(AIOpsObject.OBJECT_ID)), item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID)));\n\n                plugins.add(item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID)));\n\n                pluginEntities.get(item.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(item.getInteger(PLUGIN_ID))).add(item.getInteger(AIOpsObject.OBJECT_ID));\n            }\n        }\n    }\n\n    /*\n        Set Entities as per Archived if not then we will be taking data from ObjectConfigStore and then set entities.\n     */\n    public static void setEntities(JsonArray pluginIds, boolean archived, Set<Integer> items, Map<String, String> entities, Set<Object> plugins, Map<String, Set<Integer>> pluginEntities)\n    {\n        for (var pluginId : pluginIds)\n        {\n            var plugin = CommonUtil.getInteger(pluginId);\n\n            for (var item : archived ? ArchivedObjectConfigStore.getStore().getItemsByPlugin(items, plugin) : ObjectConfigStore.getStore().getItemsByPlugin(items, plugin))\n            {\n                entities.put(CommonUtil.getString(item), plugin + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(plugin));\n\n                //entityKeys.put(item + CARET_SEPARATOR + column, plugin + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(plugin));\n\n                plugins.add(plugin + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(plugin));\n\n                pluginEntities.get(plugin + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(plugin)).add(CommonUtil.getInteger(item));\n            }\n        }\n    }\n\n    public static void extractEvent(Buffer buffer, JsonObject event, Map<String, TopKUtil> topKObjects, Map<String, Integer> counts, Logger logger)\n    {\n        var position = 0;\n\n        for (var j = 0; j < buffer.length(); j = position)\n        {\n            try\n            {\n                var ordinal = buffer.getIntLE(position);\n\n                if (EventOrdinalCacheStore.getStore().getValue(ordinal) != null)\n                {\n                    position += 4;\n                }\n\n                else\n                {\n                    position += 2;\n\n                    ordinal = buffer.getIntLE(position);\n\n                    position += 4;\n                }\n\n\n                var key = EventOrdinalCacheStore.getStore().getValue(ordinal).split(SEPARATOR_WITH_ESCAPE)[1];\n\n                var length = buffer.getShortLE(position);\n\n                position += 2;\n\n                var value = buffer.getString(position, position + length);\n\n                position += length;\n\n                if (topKObjects != null)\n                {\n                    topKObjects.computeIfAbsent(key, val -> new TopKUtil(10));\n\n                    topKObjects.get(key).add(value);\n\n                    counts.put(key, counts.getOrDefault(key, 0) + 1);\n                }\n\n                event.put(key, value);\n            }\n\n            catch (Exception exception)\n            {\n                logger.error(exception);\n            }\n        }\n    }\n\n    /**\n     * Defines how data from different sources is joined in visualizations.\n     * <p>\n     * This enum specifies the join strategy used when combining data from multiple sources:\n     * <ul>\n     *   <li>JOIN_TYPE_ANY - Includes results that match any of the specified conditions</li>\n     *   <li>JOIN_TYPE_CUSTOM - Uses a custom join strategy defined in the visualization context</li>\n     *   <li>JOIN_TYPE_ALL - Includes only results that match all specified conditions</li>\n     * </ul>\n     */\n    public enum VisualizationJoinType\n    {\n        /**\n         * Join type that includes results matching any condition\n         */\n        JOIN_TYPE_ANY(\"any\"),\n\n        /**\n         * Join type that uses a custom join strategy\n         */\n        JOIN_TYPE_CUSTOM(\"custom\"),\n\n        /**\n         * Join type that includes only results matching all conditions\n         */\n        JOIN_TYPE_ALL(\"all\");\n\n        private static final Map<String, VisualizationJoinType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(VisualizationJoinType::getName, e -> e)));\n        private final String name;\n\n        VisualizationJoinType(String name)\n        {\n            this.name = name;\n        }\n\n        public static VisualizationJoinType valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n\n    /**\n     * Defines grouping options for visualization data.\n     * <p>\n     * This enum specifies how data should be grouped in visualizations:\n     * <ul>\n     *   <li>MONITOR - Group by monitoring entity</li>\n     *   <li>GROUP - Group by defined groups</li>\n     *   <li>TAG - Group by tags</li>\n     *   <li>EVENT_SOURCE - Group by event source</li>\n     *   <li>EVENT_CATEGORY - Group by event category</li>\n     *   <li>EVENT_SOURCE_TYPE - Group by event source type</li>\n     *   <li>CATEGORY - Group by general category</li>\n     * </ul>\n     */\n    public enum VisualizationGrouping\n    {\n        /**\n         * Group by monitoring entity\n         */\n        MONITOR(\"monitor\"),\n\n        /**\n         * Group by defined groups\n         */\n        GROUP(\"group\"),\n\n        /**\n         * Group by tags\n         */\n        TAG(\"tag\"),\n\n        /**\n         * Group by event source\n         */\n        EVENT_SOURCE(\"event.source\"),\n\n        /**\n         * Group by event category\n         */\n        EVENT_CATEGORY(\"event.category\"),\n\n        /**\n         * Group by event source type\n         */\n        EVENT_SOURCE_TYPE(\"event.source.type\"),\n\n        /**\n         * Group by general category\n         */\n        CATEGORY(\"category\");\n\n        private static final Map<String, VisualizationGrouping> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(VisualizationGrouping::getName, e -> e)));\n        private final String name;\n\n        VisualizationGrouping(String name)\n        {\n            this.name = name;\n        }\n\n        public static VisualizationGrouping valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n\n    /**\n     * Defines available data sources for visualizations.\n     * <p>\n     * This enum specifies the different types of data that can be used as sources for visualizations.\n     * Each data source provides specific types of information that can be visualized in different ways.\n     * <p>\n     * Data sources include metrics, events, logs, availability information, and various other types\n     * of monitoring and operational data collected by the system.\n     */\n    public enum VisualizationDataSource\n    {\n        /**\n         * Object availability status data\n         */\n        OBJECT_AVAILABILITY(\"availability\"),\n\n        /**\n         * Performance metric data\n         */\n        PERFORMANCE_METRIC(\"metric\"),\n\n        /**\n         * Log data\n         */\n        LOG(\"log\"),\n\n        /**\n         * Flow data\n         */\n        FLOW(\"flow\"),\n\n        /**\n         * SNMP trap data\n         */\n        TRAP(\"trap\"),\n\n        /**\n         * Policy data\n         */\n        POLICY(\"policy\"),\n\n        /**\n         * Audit log data\n         */\n        AUDIT(\"audit\"),\n\n        /**\n         * User notification data\n         */\n        USER_NOTIFICATION(\"notification\"),\n\n        /**\n         * Event history data\n         */\n        EVENT_HISTORY(\"event.history\"),\n\n        /**\n         * Cumulative object status flap data\n         */\n        CUMULATIVE_OBJECT_STATUS_FLAP(\"status.flap\"),\n\n        /**\n         * Policy flap data\n         */\n        POLICY_FLAP(\"policy.flap\"),\n\n        /**\n         * Trap flap data\n         */\n        TRAP_FLAP(\"trap.flap\"),\n\n        /**\n         * Policy stream data\n         */\n        POLICY_STREAM(\"policy.stream\"),\n\n        /**\n         * Policy trigger tick data\n         */\n        POLICY_TRIGGER_TICK(\"policy.trigger.tick\"),\n\n        /**\n         * Trap acknowledgement data\n         */\n        TRAP_ACKNOWLEDGEMENT(\"trap.acknowledgement\"),\n\n        /**\n         * Policy acknowledgement data\n         */\n        POLICY_ACKNOWLEDGEMENT(\"policy.acknowledgement\"),\n\n        /**\n         * Correlated metric data\n         */\n        CORRELATED_METRIC(\"correlated.metric\"),\n\n        /**\n         * Runbook worklog data\n         */\n        RUNBOOK_WORKLOG(\"runbook.worklog\"),\n\n        /**\n         * Policy result data\n         */\n        POLICY_RESULT(\"policy.result\"),\n\n        /**\n         * Correlation worklog data\n         */\n        CORRELATION_WORKLOG(\"correlation.worklog\"),\n\n        /**\n         * Application performance monitoring data\n         */\n        APM(\"apm\"),\n\n        /**\n         * Static configuration metric data (cannot be renamed due to migration issues)\n         */\n        STATIC_METRIC(\"config\"),\n\n        /**\n         * Health metric data\n         */\n        HEALTH_METRIC(\"health.metric\"),\n\n        SLO_INSTANCE(\"slo.instance\"),\n\n        SLO(\"slo\"),\n\n        SLO_INSTANCE_FLAP(\"slo.instance.flap\"),\n\n        SLO_FLAP(\"slo.flap\"),\n\n        CONFIG_METRIC(\"config.metric\"),//for ncm cache level data as in ncm explorer will be using this metric as discussed with @Viram\n\n        COMPLIANCE(\"compliance\"),\n\n        COMPLIANCE_TRAIL(\"compliance.trail\"),\n\n        COMPLIANCE_STATS_ENTITY(\"compliance.stats.entity\"),\n\n        COMPLIANCE_STATS_POLICY(\"compliance.stats.policy\"),\n\n        NETROUTE_EVENT(\"netroute.event\"),\n\n        NETROUTE_METRIC(\"netroute.metric\"),\n\n        NETROUTE_AVAILABILITY(\"netroute.availability\"),\n\n        HOURLY_OBJECT_STATUS_FLAP(\"hourly.status.flap\");\n\n        private static final Map<String, VisualizationDataSource> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(VisualizationDataSource::getName, e -> e)));\n\n        private final String name;\n\n        VisualizationDataSource(String name)\n        {\n            this.name = name;\n        }\n\n        public static VisualizationDataSource valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n\n    public enum Template\n    {\n        WINDOWS(\"Windows\"),\n        LINUX(\"Linux\"),\n        WINDOWS_AGENT(\"Windows Agent\"),\n        LINUX_AGENT(\"Linux Agent\"),\n        IBM_AIX(\"IBM AIX\"),\n        IBM_AIX_AGENT(\"IBM AIX Agent\"),\n        HP_UX(\"HP-UX\"),\n        SOLARIS(\"Solaris\"),\n        WINDOWS_CLUSTER(\"Windows Cluster\"),\n        ACTIVE_DIRECTORY(\"Active Directory\"),\n        APACHE_HTTP(\"Apache HTTP\"),\n        BIND9(\"Bind9\"),\n        MICROSOFT_IIS(\"Microsoft IIS\"),\n        NGINX(\"Nginx\"),\n        DOTNET(\"Dotnet\"),\n        IBM_DB2(\"IBM Db2\"),\n        ORACLE_DATABASE(\"Oracle Database\"),\n        SQL_SERVER(\"SQL Server\"),\n        MYSQL(\"MySQL\"),\n        MARIADB(\"MariaDB\"),\n        POSTGRESQL(\"PostgreSQL\"),\n        WINDOWS_DNS(\"Windows DNS\"),\n        WINDOWS_DHCP(\"Windows DHCP\"),\n        EXCHANGE_MAILBOX(\"Exchange Mailbox\"),\n        EXCHANGE_CLIENT_ACCESS_ROLE(\"Exchange Client Access Role\"),\n        EXCHANGE_EDGE_TRANSPORT_ROLE(\"Exchange Edge Transport Role\"),\n        EXCHANGE_MAILBOX_ROLE(\"Exchange Mailbox Role\"),\n        HA_PROXY(\"HAProxy\"),\n        IBM_MQ(\"IBM MQ\"),\n        MSMQ(\"MSMQ\"),\n        RABBITMQ(\"RabbitMQ\"),\n        APACHE_MQ(\"Apache MQ\"),\n        ORACLE_WEBLOGIC(\"Oracle WebLogic\"),\n        LIGHTTPD(\"Light Httpd\"),\n        LINUX_DHCP(\"Linux DHCP\"),\n        APACHE_TOMCAT(\"Apache Tomcat\"),\n        IBM_WEBSPHERE(\"IBM WebSphere\"),\n        WILDFLY(\"WildFly\"),\n        WINDOWS_RDP(\"Windows RDP\"),\n        SYBASE(\"Sybase\"),\n        SAP_HANA(\"SAP HANA\"),\n        SAP_MAXDB(\"SAP MaxDB\"),\n        CERTIFICATE(\"SSL Certificate\"),\n        CISCO_UCS(\"Cisco UCS\"),\n        CISCO_WIRELESS(\"Cisco Wireless\"),\n        DNS(\"DNS\"),\n        DOMAIN(\"Domain\"),\n        EMAIL(\"Email\"),\n        ZIMBRA(\"Zimbra\"),\n        VMWARE_ESXI(\"VMware ESXi\"),\n        VCENTER(\"vCenter\"),\n        HYPER_V(\"Hyper-V\"),\n        HYPER_V_CLUSTER(\"Hyper-V Cluster\"),\n        CITRIX_XEN(\"Citrix Xen\"),\n        CITRIX_XEN_CLUSTER(\"Citrix Xen Cluster\"),\n        VMWARE_ESXI_VM(\"VMware ESXi Vm\"),\n        HYPER_V_VM(\"Hyper-V Vm\"),\n        CITRIX_XEN_VM(\"Citrix Xen Vm\"),\n        NTP(\"NTP\"),\n        FTP(\"FTP\"),\n        OFFICE_365(\"Office 365\"),\n        ONEDRIVE(\"OneDrive\"),\n        PING(\"Ping\"),\n        PORT(\"Port\"),\n        RADIUS(\"RADIUS\"),\n        URL(\"URL\"),\n        RUCKUS_WIRELESS(\"Ruckus Wireless\"),\n        RUCKUS_WIRELESS_SMARTZONE(\"Ruckus Wireless SmartZone\"),\n        ARUBA_WIRELESS(\"Aruba Wireless\"),\n        EXCHANGE_ONLINE(\"Exchange Online\"),\n        SHAREPOINT_ONLINE(\"SharePoint Online\"),\n        MICROSOFT_TEAMS(\"Microsoft Teams\"),\n        MICROSOFT_DYNAMICS_CRM(\"Microsoft Dynamics CRM\"),\n        AWS_CLOUD(\"AWS Cloud\"),\n        AMAZON_DYNAMO_DB(\"Amazon DynamoDB\"),\n        AMAZON_EBS(\"Amazon EBS\"),\n        AMAZON_EC2(\"Amazon EC2\"),\n        AWS_ELB(\"AWS ELB\"),\n        AWS_ELB_NETWORK(\"AWS ELB Network\"),\n        AWS_ELB_APPLICATION(\"AWS ELB Application\"),\n        AWS_ELB_CLASSIC(\"AWS ELB Classic\"),\n        AMAZON_RDS(\"Amazon RDS\"),\n        AMAZON_S3(\"Amazon S3\"),\n        AMAZON_SNS(\"Amazon SNS\"),\n        AMAZON_CLOUD_FRONT(\"Amazon CloudFront\"),\n        AWS_AUTO_SCALING(\"AWS Auto Scaling\"),\n        AWS_LAMBDA(\"AWS Lambda\"),\n        AMAZON_SQS(\"Amazon SQS\"),\n        AWS_ELASTIC_BEANSTALK(\"AWS Elastic Beanstalk\"),\n        AMAZON_DOCUMENTDB(\"Amazon DocumentDB\"),\n        AZURE_CLOUD(\"Azure Cloud\"),\n        AZURE_COSMOS_DB(\"Azure Cosmos DB\"),\n        AZURE_SQL_DATABASE(\"Azure SQL Database\"),\n        AZURE_STORAGE(\"Azure Storage\"),\n        AZURE_VM(\"Azure VM\"),\n        AZURE_WEB_APP(\"Azure WebApp\"),\n        AZURE_SERVICE_BUS(\"Azure Service Bus\"),\n        AZURE_APPLICATION_GATEWAY(\"Azure Application Gateway\"),\n        AZURE_FUNCTION(\"Azure Function\"),\n        AZURE_LOAD_BALANCER(\"Azure Load Balancer\"),\n        AZURE_VM_SCALE_SET(\"Azure VM Scale Set\"),\n        AZURE_CDN(\"Azure CDN\"),\n        AZURE_MYSQL(\"Azure MySQL Server\"),\n        SYMANTEC_MESSAGING_GATEWAY(\"Symantec Corporation Email Gateway\"),\n\n        IBM_TAPE_LIBRARY(\"IBM Tape Library\"),\n        IRONPORT_MESSAGING_GATEWAY(\"IronPort Systems Email Gateway\"),\n        AZURE_POSTGRESQL(\"Azure PostgreSQL Server\"),\n        CISCO_SYSTEMS_SWITCH(\"Cisco Systems Switch\"),\n        JUNIPER_NETWORKS_SWITCH(\"Juniper Networks Switch\"),\n        CISCO_SYSTEMS_ROUTER(\"Cisco Systems Router\"),\n\n        CISCO_SYSTEMS_ROUTER_L3(\"Cisco Systems Router L3\"),\n        CISCO_SYSTEMS_FIREWALL(\"Cisco Systems Firewall\"),\n        CISCO_SYSTEMS_SWITCH_STACK(\"Cisco Systems Switch Stack\"),\n\n        CISCO_SYSTEMS_SWITCH_STACK_L3(\"Cisco Systems Switch Stack L3\"),\n        CISCO_WIRELESS_ACCESS_POINT(\"Cisco Wireless Access Point\"),\n        ARUBA_WIRELESS_ACCESS_POINT(\"Aruba Wireless Access Point\"),\n        RUCKUS_WIRELESS_SMARTZONE_ACCESS_POINT(\"Ruckus Wireless SmartZone Access Point\"),\n        RUCKUS_WIRELESS_ACCESS_POINT(\"Ruckus Wireless Access Point\"),\n        PALO_ALTO_NETWORKS_FIREWALL(\"Palo Alto Networks Firewall\"),\n        CHECK_POINT_FIREWALL(\"Check Point Firewall\"),\n        CHECK_POINT_FIREWALL_L3(\"Check Point Firewall L3\"),\n        FORTINET_FIREWALL(\"Fortinet Firewall\"),\n        HEWLETT_PACKARD_ENTERPRISE_SWITCH(\"Hewlett Packard Enterprise Switch\"),\n\n        HEWLETT_PACKARD_ENTERPRISE_SWITCH_L3(\"Hewlett Packard Enterprise Switch L3\"),\n\n        JUNIPER_NETWORKS_ROUTER(\"Juniper Networks Router\"),\n        GENERIC_SNMP_DEVICE(\"Generic Snmp Device\"),\n        ARISTA_NETWORKS_SWITCH(\"Arista Networks Switch\"),\n\n        ARISTA_NETWORKS_SWITCH_L3(\"Arista Networks Switch L3\"),\n        ARUBA_NETWORKS_SWITCH(\"Aruba Networks Switch\"),\n\n        ARUBA_NETWORKS_SWITCH_L3(\"Aruba Networks Switch L3\"),\n        H3C_SWITCH(\"H3C Switch\"),\n\n        H3C_SWITCH_L3(\"H3C Switch L3\"),\n        HUAWEI_SWITCH(\"Huawei Switch\"),\n\n        HUAWEI_SWITCH_L3(\"Huawei Switch L3\"),\n        INTEL_CORPORATION_SWITCH(\"Intel Corporation Switch\"),\n\n        INTEL_CORPORATION_SWITCH_L3(\"Intel Corporation Switch L3\"),\n        NETGEAR_SWITCH(\"Netgear Switch\"),\n\n        NETGEAR_SWITCH_L3(\"Netgear Switch L3\"),\n        ALTEON_NETWORKS(\"Alteon Networks Load Balancer\"),\n        RADWARE(\"Radware Load Balancer\"),\n        SONICWALL(\"SonicWall Firewall\"),\n        INOCINC(\"INOC Inc. Firewall\"),\n\n        CISCO_SYSTEMS_SWITCH_L3(\"Cisco Systems Switch L3\"),\n\n        JUNIPER_NETWORKS_SWITCH_L3(\"Juniper Networks Switch L3\"),\n\n        JUNIPER_NETWORKS_ROUTER_L3(\"Juniper Networks Router L3\"),\n\n        EXTREME_NETWORKS_SWITCH(\"Extreme Networks Switch\"),\n\n        EXTREME_NETWORKS_SWITCH_L3(\"Extreme Networks Switch L3\"),\n\n        EXTREME_NETWORKS_ROUTER(\"Extreme Networks Router\"),\n\n        EXTREME_NETWORKS_ROUTER_L3(\"Extreme Networks Router L3\"),\n\n        NETSCALER(\"Netscaler Load Balancer\"),\n\n        F5_LOAD_BALANCER(\"F5 Networks Load Balancer\"),\n\n        DLINK_SWITCH(\"D-Link Switch\"),\n\n        DLINK_SWITCH_L3(\"D-Link Switch L3\"),\n\n        DELL_HARDWARE_SENSOR(\"Dell Hardware Sensor\"),\n\n        HEWLETT_PACKARD_ENTERPRISE_HARDWARE_SENSOR(\"Hewlett Packard Enterprise Hardware Sensor\"),\n\n        PRISM(\"Prism\"),\n\n        NUTANIX(\"Nutanix\"),\n\n        NUTANIX_VM(\"Nutanix VM\"),\n\n        CISCO_VMANAGE(\"Cisco vManage\"),\n\n        CISCO_VSMART(\"Cisco vSmart\"),\n\n        CISCO_VEDGE(\"Cisco vEdge\"),\n\n        CISCO_VBOND(\"Cisco vBond\"),\n\n        CISCO_MERAKI(\"Cisco Meraki\"),\n\n        CISCO_MERAKI_SECURITY(\"Cisco Meraki Security\"),\n\n        CISCO_MERAKI_SWITCH(\"Cisco Meraki Switch\"),\n\n        CISCO_MERAKI_RADIO(\"Cisco Meraki Radio\"),\n        CISCO_SYSTEMS_FIREWALL_CISCO_ISE(\"Cisco Systems Firewall Cisco ISE\"),\n        CISCO_SYSTEMS_FIREWALL_CISCO_FIREPOWER(\"Cisco Systems Firewall Firepower\"),\n        EXTREME_NETWORKS_SWITCH_STACK(\"Extreme Networks Switch Stack\"),\n        LOG_ANALYTICS(\"Log Analytics\"),\n        TRAP_ANALYTICS(\"Trap Analytics\"),\n        FLOW_ANALYTICS(\"Flow Analytics\"),\n        NETAPP_ONTAP_CLUSTER(\"NetApp ONTAP Cluster\"),\n        HPE_STORE_ONCE(\"HPE StoreOnce\"),\n        CISCO_ACI(\"Cisco ACI\"),\n        DOCKER(\"Docker\"),\n        HPE_PRIMERA(\"HPE Primera\"),\n        HPE_3PAR(\"HPE 3PAR\"),\n        DELL_EMC_UNITY(\"Dell EMC Unity\"),\n        WINDOWS_SNMP(\"Windows Windows (SNMP)\"),\n        LINUX_SNMP(\"Linux Linux (SNMP)\"),\n        EMAIL_GATEWAY(\"Barracuda Networks Email Gateway\"),\n        MOXA_SWITCH(\"Moxa Switch\"),\n        TANZU_KUBERNETES(\"Tanzu Kubernetes\"),\n        KUBERNETES(\"Kubernetes\"),\n        HEWLETT_PACKARD_ENTERPRISE_HARDWARE_SENSOR_ONBOARD_ADMINISTRATOR(\"Hewlett Packard Enterprise Hardware Sensor Onboard Administrator\"),\n        IBM_AS_400(\"IBM AS/400\"),\n        MONGODB(\"MongoDB\"),\n        NSXT(\"VMware NSX-T\");\n\n\n        public static final Map<String, Long> templates = Map.<String, Long>ofEntries(\n\n                entry(Template.ACTIVE_DIRECTORY.getName(), 10000000000001L),\n                entry(Template.APACHE_TOMCAT.getName(), 10000000000002L),\n                entry(Template.AWS_AUTO_SCALING.getName(), 10000000000003L),\n                entry(Template.AMAZON_CLOUD_FRONT.getName(), 10000000000004L),\n                entry(Template.AMAZON_DOCUMENTDB.getName(), 10000000000005L),\n                entry(Template.AMAZON_DYNAMO_DB.getName(), 10000000000006L),\n                entry(Template.AMAZON_EBS.getName(), 10000000000007L),\n                entry(Template.AMAZON_EC2.getName(), 10000000000008L),\n                entry(Template.AWS_ELASTIC_BEANSTALK.getName(), 10000000000009L),\n                entry(Template.AWS_LAMBDA.getName(), 10000000000010L),\n                entry(Template.AMAZON_RDS.getName(), 10000000000011L),\n                entry(Template.AMAZON_S3.getName(), 10000000000012L),\n                entry(Template.AMAZON_SNS.getName(), 10000000000013L),\n                entry(Template.AMAZON_SQS.getName(), 10000000000014L),\n                entry(Template.AZURE_APPLICATION_GATEWAY.getName(), 10000000000015L),\n                entry(Template.AZURE_CDN.getName(), 10000000000016L),\n                entry(Template.AZURE_COSMOS_DB.getName(), 10000000000017L),\n                entry(Template.AZURE_FUNCTION.getName(), 10000000000018L),\n                entry(Template.AZURE_LOAD_BALANCER.getName(), 10000000000019L),\n                entry(Template.AZURE_SQL_DATABASE.getName(), 10000000000020L),\n                entry(Template.AZURE_VM_SCALE_SET.getName(), 10000000000021L),\n                entry(Template.AZURE_SERVICE_BUS.getName(), 10000000000022L),\n                entry(Template.AZURE_VM.getName(), 10000000000023L),\n                entry(Template.AZURE_WEB_APP.getName(), 10000000000024L),\n                entry(Template.BIND9.getName(), 10000000000025L),\n                entry(Template.HA_PROXY.getName(), 10000000000026L),\n                entry(Template.IBM_DB2.getName(), 10000000000029L),\n                entry(Template.IBM_MQ.getName(), 10000000000034L),\n                entry(Template.IBM_WEBSPHERE.getName(), 10000000000035L),\n                entry(Template.LIGHTTPD.getName(), 10000000000036L),\n                entry(Template.LINUX_DHCP.getName(), 10000000000037L),\n                entry(Template.MICROSOFT_IIS.getName(), 10000000000038L),\n                entry(Template.MSMQ.getName(), 10000000000039L),\n                entry(Template.SQL_SERVER.getName(), 10000000000040L),\n                entry(Template.MYSQL.getName(), 10000000000047L),\n                entry(Template.NGINX.getName(), 10000000000050L),\n                entry(Template.OFFICE_365.getName(), 10000000000051L),\n                entry(Template.EXCHANGE_ONLINE.getName(), 10000000000052L),\n                entry(Template.ONEDRIVE.getName(), 10000000000053L),\n                entry(Template.SHAREPOINT_ONLINE.getName(), 10000000000054L),\n                entry(Template.MICROSOFT_TEAMS.getName(), 10000000000055L),\n                entry(Template.ORACLE_DATABASE.getName(), 10000000000056L),\n                entry(Template.POSTGRESQL.getName(), 10000000000065L),\n                entry(Template.RABBITMQ.getName(), 10000000000069L),\n                entry(Template.SAP_HANA.getName(), 10000000000070L),\n                entry(Template.SYBASE.getName(), 10000000000074L),\n                entry(Template.VCENTER.getName(), 10000000000075L),\n                entry(Template.VMWARE_ESXI.getName(), 10000000000076L),\n                entry(Template.HYPER_V.getName(), 10000000000077L),\n                entry(Template.HYPER_V_CLUSTER.getName(), 10000000000078L),\n                entry(Template.CITRIX_XEN.getName(), 10000000000079L),\n                entry(Template.CITRIX_XEN_CLUSTER.getName(), 10000000000080L),\n                entry(Template.CITRIX_XEN_VM.getName(), 10000000000081L),\n                entry(Template.VMWARE_ESXI_VM.getName(), 10000000000082L),\n                entry(Template.HYPER_V_VM.getName(), 10000000000083L),\n                entry(Template.WILDFLY.getName(), 10000000000084L),\n                entry(Template.WINDOWS_CLUSTER.getName(), 10000000000085L),\n                entry(Template.WINDOWS_DHCP.getName(), 10000000000086L),\n                entry(Template.WINDOWS_DNS.getName(), 10000000000087L),\n                entry(Template.WINDOWS_RDP.getName(), 10000000000088L),\n                entry(Template.CISCO_WIRELESS.getName(), 10000000000089L),\n                entry(Template.RUCKUS_WIRELESS.getName(), 10000000000092L),\n                entry(Template.RUCKUS_WIRELESS_SMARTZONE.getName(), 10000000000422L),\n                entry(Template.ARUBA_WIRELESS.getName(), 10000000000095L),\n                entry(Template.APACHE_HTTP.getName(), 10000000000098L),\n                entry(Template.WINDOWS.getName(), 10000000000099L),\n                entry(Template.LINUX.getName(), 10000000000100L),\n                entry(Template.HP_UX.getName(), 10000000000101L),\n                entry(Template.IBM_AIX.getName(), 10000000000102L),\n                entry(Template.SOLARIS.getName(), 10000000000103L),\n                entry(Template.URL.getName(), 10000000000104L),\n                entry(Template.DOMAIN.getName(), 10000000000105L),\n                entry(Template.PORT.getName(), 10000000000106L),\n                entry(Template.PING.getName(), 10000000000107L),\n                entry(Template.FTP.getName(), 10000000000108L),\n                entry(Template.DNS.getName(), 10000000000109L),\n                entry(Template.RADIUS.getName(), 10000000000110L),\n                entry(Template.NTP.getName(), 10000000000111L),\n                entry(Template.EMAIL.getName(), 10000000000112L),\n                entry(Template.CERTIFICATE.getName(), 10000000000113L),\n                entry(Template.WINDOWS_AGENT.getName(), 10000000000114L),\n                entry(Template.LINUX_AGENT.getName(), 10000000000115L),\n                entry(Template.CISCO_SYSTEMS_SWITCH.getName(), 10000000000116L),\n                entry(Template.JUNIPER_NETWORKS_SWITCH.getName(), 10000000000117L),\n                entry(Template.CISCO_SYSTEMS_ROUTER.getName(), 10000000000120L),\n                entry(Template.JUNIPER_NETWORKS_ROUTER.getName(), 10000000000221L),\n                entry(Template.CISCO_SYSTEMS_FIREWALL.getName(), 10000000000121L),\n                entry(Template.PALO_ALTO_NETWORKS_FIREWALL.getName(), 10000000000122L),\n                entry(Template.CHECK_POINT_FIREWALL.getName(), 10000000000123L),\n                entry(Template.CHECK_POINT_FIREWALL_L3.getName(), 10000000000123L),\n                entry(Template.FORTINET_FIREWALL.getName(), 10000000000124L),\n                entry(Template.GENERIC_SNMP_DEVICE.getName(), 10000000000155L),\n                entry(Template.AWS_ELB_NETWORK.getName(), 10000000000156L),\n                entry(Template.AWS_ELB_APPLICATION.getName(), 10000000000157L),\n                entry(Template.AWS_ELB_CLASSIC.getName(), 10000000000158L),\n                entry(Template.AZURE_STORAGE.getName(), 10000000000159L),\n                entry(Template.SYMANTEC_MESSAGING_GATEWAY.getName(), 10000000000164L),\n                entry(Template.EXCHANGE_EDGE_TRANSPORT_ROLE.getName(), 10000000000166L),\n                entry(Template.EXCHANGE_MAILBOX.getName(), 10000000000167L),\n                entry(Template.EXCHANGE_CLIENT_ACCESS_ROLE.getName(), 10000000000202L),\n                entry(Template.EXCHANGE_MAILBOX_ROLE.getName(), 10000000000167L),\n                entry(Template.SAP_MAXDB.getName(), 10000000000169L),\n                entry(Template.MARIADB.getName(), 10000000000170L),\n                entry(Template.APACHE_MQ.getName(), 10000000000174L),\n                entry(Template.DOTNET.getName(), 10000000000175L),\n                entry(Template.ZIMBRA.getName(), 10000000000176L),\n                entry(Template.CISCO_UCS.getName(), 10000000000177L),\n                entry(Template.ARISTA_NETWORKS_SWITCH.getName(), 10000000000179L),\n                entry(Template.ARUBA_NETWORKS_SWITCH.getName(), 10000000000181L),\n                entry(Template.H3C_SWITCH.getName(), 10000000000183L),\n                entry(Template.HEWLETT_PACKARD_ENTERPRISE_SWITCH.getName(), 10000000000185L),\n                entry(Template.HUAWEI_SWITCH.getName(), 10000000000188L),\n                entry(Template.INTEL_CORPORATION_SWITCH.getName(), 10000000000190L),\n                entry(Template.NETGEAR_SWITCH.getName(), 10000000000192L),\n                entry(Template.AZURE_POSTGRESQL.getName(), 10000000000194L),\n                entry(Template.AZURE_MYSQL.getName(), 10000000000195L),\n                entry(Template.CISCO_SYSTEMS_SWITCH_STACK.getName(), 10000000000196L),\n                entry(Template.CISCO_WIRELESS_ACCESS_POINT.getName(), 10000000000205L),\n                entry(Template.ARUBA_WIRELESS_ACCESS_POINT.getName(), 10000000000206L),\n                entry(Template.RUCKUS_WIRELESS_ACCESS_POINT.getName(), 10000000000207L),\n                entry(Template.RUCKUS_WIRELESS_SMARTZONE_ACCESS_POINT.getName(), 10000000000424L),\n                entry(Template.ALTEON_NETWORKS.getName(), 10000000000203L),\n                entry(Template.RADWARE.getName(), 10000000000203L),\n                entry(Template.SONICWALL.getName(), 10000000000208L),\n                entry(Template.INOCINC.getName(), 10000000000208L),\n                entry(Template.CISCO_SYSTEMS_SWITCH_L3.getName(), 10000000000119L),\n                entry(Template.JUNIPER_NETWORKS_SWITCH_L3.getName(), 10000000000118L),\n                entry(Template.EXTREME_NETWORKS_SWITCH.getName(), 10000000000211L),\n                entry(Template.EXTREME_NETWORKS_SWITCH_L3.getName(), 10000000000214L),\n                entry(Template.EXTREME_NETWORKS_ROUTER.getName(), 10000000000218L),\n                entry(Template.EXTREME_NETWORKS_ROUTER_L3.getName(), 10000000000233L),\n                entry(Template.JUNIPER_NETWORKS_ROUTER_L3.getName(), 10000000000230L),\n                entry(Template.CISCO_SYSTEMS_ROUTER_L3.getName(), 10000000000236L),\n                entry(Template.CISCO_SYSTEMS_SWITCH_STACK_L3.getName(), 10000000000224L),\n                entry(Template.ARISTA_NETWORKS_SWITCH_L3.getName(), 10000000000239L),\n                entry(Template.ARUBA_NETWORKS_SWITCH_L3.getName(), 10000000000245L),\n                entry(Template.H3C_SWITCH_L3.getName(), 10000000000242L),\n                entry(Template.HEWLETT_PACKARD_ENTERPRISE_SWITCH_L3.getName(), 10000000000248L),\n                entry(Template.HUAWEI_SWITCH_L3.getName(), 10000000000252L),\n                entry(Template.INTEL_CORPORATION_SWITCH_L3.getName(), 10000000000255L),\n                entry(Template.NETGEAR_SWITCH_L3.getName(), 10000000000258L),\n                entry(Template.NETSCALER.getName(), 10000000000261L),\n                entry(Template.F5_LOAD_BALANCER.getName(), 10000000000262L),\n                entry(Template.DLINK_SWITCH.getName(), 10000000000263L),\n                entry(Template.DLINK_SWITCH_L3.getName(), 10000000000265L),\n                entry(Template.DELL_HARDWARE_SENSOR.getName(), 10000000000290L),\n                entry(Template.HEWLETT_PACKARD_ENTERPRISE_HARDWARE_SENSOR.getName(), 10000000000289L),\n                entry(Template.IBM_TAPE_LIBRARY.getName(), 10000000000286L),\n                entry(Template.IRONPORT_MESSAGING_GATEWAY.getName(), 10000000000287L),\n                entry(Template.PRISM.getName(), 10000000000295L),\n                entry(Template.NUTANIX.getName(), 10000000000298L),\n                entry(Template.NUTANIX_VM.getName(), 10000000000301L),\n                entry(Template.CISCO_VMANAGE.getName(), 10000000000302L),\n                entry(Template.CISCO_VSMART.getName(), 10000000000305L),\n                entry(Template.CISCO_VBOND.getName(), 10000000000306L),\n                entry(Template.CISCO_VEDGE.getName(), 10000000000307L),\n                entry(Template.IBM_AIX_AGENT.getName(), 10000000000311L),\n                entry(Template.CISCO_MERAKI.getName(), 10000000000320L),\n                entry(Template.CISCO_MERAKI_SECURITY.getName(), 10000000000315L),\n                entry(Template.CISCO_MERAKI_SWITCH.getName(), 10000000000316L),\n                entry(Template.CISCO_MERAKI_RADIO.getName(), 10000000000319L),\n                entry(Template.CISCO_SYSTEMS_FIREWALL_CISCO_ISE.getName(), 10000000000322L),\n                entry(Template.CISCO_SYSTEMS_FIREWALL_CISCO_FIREPOWER.getName(), 10000000000326L),\n                entry(Template.EXTREME_NETWORKS_SWITCH_STACK.getName(), 10000000000329L),\n                entry(Template.LOG_ANALYTICS.getName(), 10000000000334L),\n                entry(Template.FLOW_ANALYTICS.getName(), 10000000000335L),\n                entry(Template.TRAP_ANALYTICS.getName(), 10000000000336L),\n                entry(Template.NETAPP_ONTAP_CLUSTER.getName(), 10000000000337L),\n                entry(Template.HPE_STORE_ONCE.getName(), 10000000000342L),\n                entry(Template.CISCO_ACI.getName(), 10000000000345L),\n                entry(Template.DOCKER.getName(), 10000000000360L),\n                entry(Template.HPE_PRIMERA.getName(), 10000000000361L),\n                entry(Template.HPE_3PAR.getName(), 10000000000369L),\n                entry(Template.DELL_EMC_UNITY.getName(), 10000000000380L),\n                entry(Template.WINDOWS_SNMP.getName(), 10000000000387L),\n                entry(Template.LINUX_SNMP.getName(), 10000000000395L),\n                entry(Template.MOXA_SWITCH.getName(), 10000000000367L),\n                entry(Template.HEWLETT_PACKARD_ENTERPRISE_HARDWARE_SENSOR_ONBOARD_ADMINISTRATOR.getName(), 10000000000375L),\n                entry(Template.EMAIL_GATEWAY.getName(), 10000000000400L),\n                entry(Template.TANZU_KUBERNETES.getName(), 10000000000404L),\n                entry(Template.KUBERNETES.getName(), 10000000000418L),\n                entry(Template.IBM_AS_400.getName(), 10000000000390L),\n                entry(Template.MONGODB.getName(), 10000000000412L),\n                entry(Template.NSXT.getName(), 10000000000408L),\n                entry(Template.ORACLE_WEBLOGIC.getName(), 10000000000417L));\n\n        //Last template id 10000000000421\n\n        private static final Map<String, Template> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(Template::getName, e -> e)));\n        private final String name;\n\n        Template(String name)\n        {\n            this.name = name;\n        }\n\n        public static Template valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public static long getTemplate(String name)\n        {\n            return templates.getOrDefault(name, DEFAULT_NETWORK_TEMPLATE);\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n\n\n    /**\n     * Defines ordinal values for visualization categories at the database level.\n     * <p>\n     * This enum is used for database-level constants representing different types of visualizations.\n     * Each constant represents a specific type of visualization that can be stored and retrieved\n     * from the database.\n     */\n    public enum VisualizationCategoryOrdinal\n    {\n        /**\n         * Gauge visualization type\n         */\n        VISUALIZATION_GAUGE,\n\n        /**\n         * Top-N chart visualization type\n         */\n        VISUALIZATION_TOPN_CHART,\n\n        /**\n         * Top-N grid visualization type\n         */\n        VISUALIZATION_TOPN_GRID,\n\n        /**\n         * Histogram visualization type\n         */\n        VISUALIZATION_HISTOGRAM,\n\n        /**\n         * Grid visualization type\n         */\n        VISUALIZATION_GRID,\n\n        /**\n         * Chart visualization type\n         */\n        VISUALIZATION_CHART,\n\n        /**\n         * Metric history visualization type\n         */\n        VISUALIZATION_METRIC_HISTORY,\n\n        /**\n         * Status flap history visualization type\n         */\n        VISUALIZATION_STATUS_FLAP_HISTORY,\n\n        /**\n         * Trap history visualization type\n         */\n        VISUALIZATION_TRAP_HISTORY,\n\n        /**\n         * Policy flap history visualization type\n         */\n        VISUALIZATION_POLICY_FLAP_HISTORY,\n\n        /**\n         * Audit history visualization type\n         */\n        VISUALIZATION_AUDIT_HISTORY,\n\n        /**\n         * User notification history visualization type\n         */\n        VISUALIZATION_USER_NOTIFICATION_HISTORY,\n\n        /**\n         * Event history visualization type\n         */\n        VISUALIZATION_EVENT_HISTORY\n    }\n\n    /**\n     * Defines categories of visualizations available in the system.\n     * <p>\n     * This enum specifies the different types of visualizations that can be created and displayed.\n     * Each category represents a specific visualization format with its own rendering and data\n     * processing requirements.\n     */\n    public enum VisualizationCategory\n    {\n        /**\n         * Histogram chart visualization\n         */\n        HISTOGRAM(\"Chart\"),\n\n        /**\n         * Grid/table visualization\n         */\n        GRID(\"Grid\"),\n\n        /**\n         * Top-N ranking visualization\n         */\n        TOP_N(\"TopN\"),\n\n        /**\n         * Gauge/meter visualization\n         */\n        GAUGE(\"Gauge\"),\n\n        /**\n         * Sankey diagram visualization\n         */\n        SANKEY(\"Sankey\"),\n\n        /**\n         * Heat map visualization\n         */\n        HEAT_MAP(\"HeatMap\"),\n\n        /**\n         * Custom chart visualization\n         */\n        CHART(\"Custom\"),\n\n        /**\n         * Data stream visualization\n         */\n        STREAM(\"Stream\"),\n\n        /**\n         * Active alerts visualization\n         */\n        ACTIVE_ALERT(\"Active Alerts\"),\n\n        /**\n         * Geographic map visualization\n         */\n        MAP(\"Map\"),\n\n        /**\n         * Forecast/prediction visualization\n         */\n        FORECAST(\"Forecast\"),\n\n        /**\n         * Baseline comparison visualization\n         */\n        BASELINE(\"Baseline\"),\n\n        /**\n         * Anomaly detection visualization\n         */\n        ANOMALY(\"Anomaly\");\n\n        private static final Map<String, VisualizationCategory> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(VisualizationCategory::getName, e -> e)));\n        private final String name;\n\n        VisualizationCategory(String name)\n        {\n            this.name = name;\n        }\n\n        public static VisualizationCategory valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n\n    /**\n     * Defines types of visualization results that can be returned.\n     * <p>\n     * This enum specifies the different formats in which visualization results can be returned:\n     * <ul>\n     *   <li>COMPACTED - Results are compressed to minimize size</li>\n     *   <li>NORMALIZED - Results are standardized for consistent processing</li>\n     *   <li>EXTENDED - Results include additional details and metadata</li>\n     * </ul>\n     */\n    public enum VisualizationResultType\n    {\n        /**\n         * Compacted result format with minimal size\n         */\n        COMPACTED(0),\n\n        /**\n         * Normalized result format with standardized structure\n         */\n        NORMALIZED(1),\n\n        /**\n         * Extended result format with additional details\n         */\n        EXTENDED(2);\n\n        private static final Map<Integer, VisualizationResultType> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(VisualizationResultType::getName, e -> e)));\n\n        private final int name;\n\n        VisualizationResultType(int name)\n        {\n            this.name = name;\n        }\n\n        public static VisualizationResultType valueOfName(int name)\n        {\n            return VALUES.get(name);\n        }\n\n        public int getName()\n        {\n            return name;\n        }\n    }\n\n    /**\n     * Defines time ranges for visualizations.\n     * <p>\n     * This enum specifies the different time ranges that can be used for visualizations.\n     * Each time range has a name and a duration in minutes. A duration of -1 indicates\n     * a special time range that requires custom calculation (like \"today\" or \"this month\").\n     * <p>\n     * The time ranges include fixed durations (like \"last 5 minutes\") and relative periods\n     * (like \"today\" or \"this month\").\n     */\n    public enum VisualizationTimeline\n    {\n        /**\n         * Last 5 minutes (5 minutes)\n         */\n        LAST_5_MINUTES(\"-5m\", 5),\n\n        /**\n         * Last 15 minutes (15 minutes)\n         */\n        LAST_15_MINUTES(\"-15m\", 15),\n\n        /**\n         * Last 30 minutes (30 minutes)\n         */\n        LAST_30_MINUTES(\"-30m\", 30),\n\n        /**\n         * Last 1 hour (60 minutes)\n         */\n        LAST_1_HOUR(\"-1h\", 60),\n\n        /**\n         * Last 6 hours (360 minutes)\n         */\n        LAST_6_HOURS(\"-6h\", 360),\n\n        /**\n         * Last 12 hours (720 minutes)\n         */\n        LAST_12_HOURS(\"-12h\", 720),\n\n        /**\n         * Last 24 hours (1440 minutes)\n         */\n        LAST_24_HOURS(\"-24h\", 1440),\n\n        /**\n         * Last 48 hours (2880 minutes)\n         */\n        LAST_48_HOURS(\"-48h\", 2880),\n\n        /**\n         * Today (from midnight to now)\n         */\n        TODAY(\"today\", -1),\n\n        /**\n         * Yesterday (previous day)\n         */\n        YESTERDAY(\"yesterday\", -1),\n\n        /**\n         * Last week (previous calendar week)\n         */\n        LAST_WEEK(\"last.week\", -1),\n\n        /**\n         * Last month (previous calendar month)\n         */\n        LAST_MONTH(\"last.month\", -1),\n\n        /**\n         * Last quarter (previous calendar quarter)\n         */\n        LAST_QUARTER(\"last.quarter\", -1),\n\n        /**\n         * Last year (previous calendar year)\n         */\n        LAST_YEAR(\"last.year\", -1),\n\n        /**\n         * This week (current calendar week)\n         */\n        THIS_WEEK(\"this.week\", -1),\n\n        /**\n         * This month (current calendar month)\n         */\n        THIS_MONTH(\"this.month\", -1),\n\n        /**\n         * This quarter (current calendar quarter)\n         */\n        THIS_QUARTER(\"this.quarter\", -1),\n\n        /**\n         * This year (current calendar year)\n         */\n        THIS_YEAR(\"this.year\", -1),\n\n        /**\n         * Last day (1440 minutes)\n         */\n        LAST_DAY(\"-1d\", 1440),\n\n        /**\n         * Last 2 days (2880 minutes)\n         */\n        LAST_2_DAYS(\"-2d\", 2880),\n\n        /**\n         * Last 7 days (10080 minutes)\n         */\n        LAST_7_DAYS(\"-7d\", 10080),      //  7 * 24 * 60 = 10080\n\n        /**\n         * Last 14 days (20160 minutes)\n         */\n        LAST_14_DAYS(\"-14d\", 20160),    // 14 * 24 * 60 = 20160\n\n        /**\n         * Last 15 days (21600 minutes)\n         */\n        LAST_15_DAYS(\"-15d\", 21600),    // 15 * 24 * 60 = 21600\n\n        /**\n         * Last 30 days (43200 minutes)\n         */\n        LAST_30_DAYS(\"-30d\", 43200),    // 30 * 24 * 60 = 43200\n\n        /**\n         * Last 60 days (86400 minutes)\n         */\n        LAST_60_DAYS(\"-60d\", 86400),    // 60 * 24 * 60 = 86400\n\n        /**\n         * Last 90 days (129600 minutes)\n         */\n        LAST_90_DAYS(\"-90d\", 129600),   // 90 * 24 * 60 = 129600\n\n        /**\n         * Custom time range (requires explicit start and end times)\n         */\n        CUSTOM(\"custom\", -1);\n\n        private static final Map<String, VisualizationTimeline> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(VisualizationTimeline::getName, e -> e)));\n\n        private final String name;\n\n        private final int duration;\n\n        VisualizationTimeline(String name, int duration)\n        {\n            this.name = name;\n\n            this.duration = duration;\n        }\n\n        public static VisualizationTimeline valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public Integer getDuration()\n        {\n            return duration;\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n\n    /**\n     * Defines status types for availability calculations and visualizations.\n     * <p>\n     * This enum specifies the different status types that can be used to categorize\n     * time periods in availability calculations and visualizations.\n     */\n    public enum StatusType\n    {\n        /**\n         * Time period when the monitored entity was up and functioning normally\n         */\n        UPTIME(\"uptime\"),\n\n        /**\n         * Time period when the monitored entity was down or not functioning\n         */\n        DOWNTIME(\"downtime\"),\n\n        /**\n         * Time period when the monitored entity was suspended\n         */\n        SUSPENDTIME(\"suspendtime\"),\n\n        /**\n         * Time period when the status of the monitored entity was unknown\n         */\n        UNKNOWNTIME(\"unknowntime\"),\n\n        /**\n         * Time period when the monitored entity was in maintenance mode\n         */\n        MAINTENANCETIME(\"maintenancetime\"),\n\n        /**\n         * Time period when the monitored entity was disabled\n         */\n        DISABLETIME(\"disabletime\"),\n\n        /**\n         * Time period when the monitored entity was unreachable\n         */\n        UNREACHABLETIME(\"unreachabletime\");\n\n        private static final Map<String, StatusType> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(StatusType::getName, e -> e)));\n\n        private final String name;\n\n        StatusType(String name)\n        {\n            this.name = name;\n        }\n\n        public static StatusType valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n\n    /**\n     * Defines priority levels for visualization queries.\n     * <p>\n     * This enum specifies the different priority levels that can be assigned to visualization queries.\n     * Lower numbers indicate higher priority (P0 is highest priority, P4 is lowest).\n     * <p>\n     * Query priorities are used to determine the order in which queries are processed when\n     * multiple queries are pending.\n     */\n    public enum QueryPriority\n    {\n        /**\n         * Highest priority (0)\n         */\n        P0(0),\n\n        /**\n         * High priority (1)\n         */\n        P1(1),\n\n        /**\n         * Medium priority (2)\n         */\n        P2(2),\n\n        /**\n         * Low priority (3)\n         */\n        P3(3),\n\n        /**\n         * Lowest priority (4)\n         */\n        P4(4);\n\n        private final int priority;\n\n        QueryPriority(int name)\n        {\n            this.priority = name;\n        }\n\n        public int getName()\n        {\n            return priority;\n        }\n    }\n}\n", "baseTimestamp": 1754466622975, "deltas": [{"timestamp": 1754466785493, "changes": [{"type": "MODIFY", "lineNumber": 3240, "content": "            var inventory = VISUALIZATION_INVENTORY_WIDGETS.getOrDefault(VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\"));", "oldContent": "            var inventory = VISUALIZATION_INVENTORY_WIDGETS.getOrDefault(context.getString(VISUALIZATION_NAME), VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\"));"}]}, {"timestamp": 1754466790800, "changes": [{"type": "MODIFY", "lineNumber": 3240, "content": "            var inventory = VISUALIZATION_INVENTORY_WIDGETS.get(VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\"));", "oldContent": "            var inventory = VISUALIZATION_INVENTORY_WIDGETS.getOrDefault(VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\"));"}]}, {"timestamp": 1754466848905, "changes": [{"type": "MODIFY", "lineNumber": 3240, "content": "            var inventory = VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\");", "oldContent": "            var inventory = VISUALIZATION_INVENTORY_WIDGETS.get(VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\"));"}]}, {"timestamp": 1754466891066, "changes": [{"type": "MODIFY", "lineNumber": 3240, "content": "            var inventory = VISUALIZATION_INVENTORY_WIDGETS.getOrDefault(context.getString(VISUALIZATION_NAME), VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\"));", "oldContent": "            var inventory = VISUALIZATION_INVENTORY_WIDGETS.get(\"Server Inventory Summary\");"}]}, {"timestamp": 1754466896284, "changes": [{"type": "INSERT", "lineNumber": 3240, "content": "            c"}]}, {"timestamp": 1754466906366, "changes": [{"type": "MODIFY", "lineNumber": 3240, "content": "            context.put()", "oldContent": "            c"}]}, {"timestamp": 1754466911417, "changes": [{"type": "MODIFY", "lineNumber": 3240, "content": "            context.put(VISUALIZATION_NAME, )", "oldContent": "            context.put()"}]}, {"timestamp": 1754466918923, "changes": [{"type": "MODIFY", "lineNumber": 3240, "content": "            context.put(VISUALIZATION_NAME, \"Server Inventory Summary\");", "oldContent": "            context.put(VISUALIZATION_NAME, )"}, {"type": "INSERT", "lineNumber": 3241, "content": ""}]}, {"timestamp": 1754467197239, "changes": [{"type": "DELETE", "lineNumber": 3240, "oldContent": "            context.put(VISUALIZATION_NAME, \"Server Inventory Summary\");"}, {"type": "DELETE", "lineNumber": 3241, "oldContent": ""}]}]}, "/fragment.java": {"filePath": "/fragment.java", "baseContent": "context.getString(VISUALIZATION_NAME)", "baseTimestamp": 1754466736800, "deltas": [{"timestamp": 1754467257656, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "context.getString(VisualizationConstants.JOIN_RESULT)", "oldContent": "context.getString(VISUALIZATION_NAME)"}]}]}, "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/logs/visualization/06-August-2025 13-Visualization Metric Response Processor.log": {"filePath": "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/logs/visualization/06-August-2025 13-Visualization Metric Response Processor.log", "baseContent": "06-August-2025 13 01:16:54.718 PM ERROR [com.mindarray.visualization.VisualizationMetricResponseProcessor]: java.lang.NullPointerException: Cannot invoke \"Object.hashCode()\" because \"pk\" is null\n\tat java.base/java.util.ImmutableCollections$MapN.probe(ImmutableCollections.java:1328)\n\tat java.base/java.util.ImmutableCollections$MapN.get(ImmutableCollections.java:1242)\n\tat java.base/java.util.ImmutableCollections$AbstractImmutableMap.getOrDefault(ImmutableCollections.java:1097)\n\tat com.mindarray.visualization.VisualizationConstants.join(VisualizationConstants.java:3241)\n\tat com.mindarray.visualization.VisualizationConstants.composeQueryResponse(VisualizationConstants.java:2940)\n\tat com.mindarray.visualization.VisualizationMetricResponseProcessor.lambda$start$0(VisualizationMetricResponseProcessor.java:72)\n\tat io.vertx.core.impl.ContextImpl.emit(ContextImpl.java:342)\n\tat io.vertx.core.impl.DuplicatedContext.emit(DuplicatedContext.java:163)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.dispatch(MessageConsumerImpl.java:174)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration$InboundDeliveryContext.execute(HandlerRegistration.java:137)\n\tat io.vertx.core.eventbus.impl.DeliveryContextBase.next(DeliveryContextBase.java:80)\n\tat io.vertx.core.eventbus.impl.DeliveryContextBase.dispatch(DeliveryContextBase.java:43)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration.dispatch(HandlerRegistration.java:98)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.deliver(MessageConsumerImpl.java:180)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration.lambda$receive$0(HandlerRegistration.java:49)\n\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)\n\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)\n\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)\n\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\n\n06-August-2025 13 01:18:11.152 PM ERROR [com.mindarray.visualization.VisualizationMetricResponseProcessor]: java.lang.NullPointerException: Cannot invoke \"Object.hashCode()\" because \"pk\" is null\n\tat java.base/java.util.ImmutableCollections$MapN.probe(ImmutableCollections.java:1328)\n\tat java.base/java.util.ImmutableCollections$MapN.get(ImmutableCollections.java:1242)\n\tat java.base/java.util.ImmutableCollections$AbstractImmutableMap.getOrDefault(ImmutableCollections.java:1097)\n\tat com.mindarray.visualization.VisualizationConstants.join(VisualizationConstants.java:3241)\n\tat com.mindarray.visualization.VisualizationConstants.composeQueryResponse(VisualizationConstants.java:2940)\n\tat com.mindarray.visualization.VisualizationMetricResponseProcessor.lambda$start$0(VisualizationMetricResponseProcessor.java:72)\n\tat io.vertx.core.impl.ContextImpl.emit(ContextImpl.java:342)\n\tat io.vertx.core.impl.DuplicatedContext.emit(DuplicatedContext.java:163)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.dispatch(MessageConsumerImpl.java:174)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration$InboundDeliveryContext.execute(HandlerRegistration.java:137)\n\tat io.vertx.core.eventbus.impl.DeliveryContextBase.next(DeliveryContextBase.java:80)\n\tat io.vertx.core.eventbus.impl.DeliveryContextBase.dispatch(DeliveryContextBase.java:43)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration.dispatch(HandlerRegistration.java:98)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.deliver(MessageConsumerImpl.java:180)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration.lambda$receive$0(HandlerRegistration.java:49)\n\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)\n\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)\n\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)\n\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\n\n06-August-2025 13 01:23:15.410 PM ERROR [com.mindarray.visualization.VisualizationMetricResponseProcessor]: java.lang.NullPointerException: Cannot invoke \"Object.hashCode()\" because \"pk\" is null\n\tat java.base/java.util.ImmutableCollections$MapN.probe(ImmutableCollections.java:1328)\n\tat java.base/java.util.ImmutableCollections$MapN.get(ImmutableCollections.java:1242)\n\tat java.base/java.util.ImmutableCollections$AbstractImmutableMap.getOrDefault(ImmutableCollections.java:1097)\n\tat com.mindarray.visualization.VisualizationConstants.join(VisualizationConstants.java:3241)\n\tat com.mindarray.visualization.VisualizationConstants.composeQueryResponse(VisualizationConstants.java:2940)\n\tat com.mindarray.visualization.VisualizationMetricResponseProcessor.lambda$start$0(VisualizationMetricResponseProcessor.java:72)\n\tat io.vertx.core.impl.ContextImpl.emit(ContextImpl.java:342)\n\tat io.vertx.core.impl.DuplicatedContext.emit(DuplicatedContext.java:163)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.dispatch(MessageConsumerImpl.java:174)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration$InboundDeliveryContext.execute(HandlerRegistration.java:137)\n\tat io.vertx.core.eventbus.impl.DeliveryContextBase.next(DeliveryContextBase.java:80)\n\tat io.vertx.core.eventbus.impl.DeliveryContextBase.dispatch(DeliveryContextBase.java:43)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration.dispatch(HandlerRegistration.java:98)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.deliver(MessageConsumerImpl.java:180)\n\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)\n\tat io.vertx.core.eventbus.impl.HandlerRegistration.lambda$receive$0(HandlerRegistration.java:49)\n\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)\n\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)\n\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)\n\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\n\n", "baseTimestamp": 1754466816194, "deltas": [{"timestamp": 1754466840718, "changes": [{"type": "INSERT", "lineNumber": 81, "content": "06-August-2025 13 01:23:55.564 PM ERROR [com.mindarray.visualization.VisualizationMetricResponseProcessor]: java.lang.NullPointerException: Cannot invoke \"io.vertx.core.json.JsonObject.getString(String)\" because \"inventory\" is null"}, {"type": "INSERT", "lineNumber": 82, "content": "\tat com.mindarray.visualization.VisualizationConstants.join(VisualizationConstants.java:3243)"}, {"type": "INSERT", "lineNumber": 83, "content": "\tat com.mindarray.visualization.VisualizationConstants.composeQueryResponse(VisualizationConstants.java:2940)"}, {"type": "INSERT", "lineNumber": 84, "content": "\tat com.mindarray.visualization.VisualizationMetricResponseProcessor.lambda$start$0(VisualizationMetricResponseProcessor.java:72)"}, {"type": "INSERT", "lineNumber": 85, "content": "\tat io.vertx.core.impl.ContextImpl.emit(ContextImpl.java:342)"}, {"type": "INSERT", "lineNumber": 86, "content": "\tat io.vertx.core.impl.DuplicatedContext.emit(DuplicatedContext.java:163)"}, {"type": "INSERT", "lineNumber": 87, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.dispatch(MessageConsumerImpl.java:174)"}, {"type": "INSERT", "lineNumber": 88, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration$InboundDeliveryContext.execute(HandlerRegistration.java:137)"}, {"type": "INSERT", "lineNumber": 89, "content": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.next(DeliveryContextBase.java:80)"}, {"type": "INSERT", "lineNumber": 90, "content": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.dispatch(DeliveryContextBase.java:43)"}, {"type": "INSERT", "lineNumber": 91, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.dispatch(HandlerRegistration.java:98)"}, {"type": "INSERT", "lineNumber": 92, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.deliver(MessageConsumerImpl.java:180)"}, {"type": "INSERT", "lineNumber": 93, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)"}, {"type": "INSERT", "lineNumber": 94, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.lambda$receive$0(HandlerRegistration.java:49)"}, {"type": "INSERT", "lineNumber": 95, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)"}, {"type": "INSERT", "lineNumber": 96, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)"}, {"type": "INSERT", "lineNumber": 97, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)"}, {"type": "INSERT", "lineNumber": 98, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)"}, {"type": "INSERT", "lineNumber": 99, "content": "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)"}, {"type": "INSERT", "lineNumber": 100, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)"}, {"type": "INSERT", "lineNumber": 101, "content": "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)"}, {"type": "INSERT", "lineNumber": 102, "content": "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)"}, {"type": "INSERT", "lineNumber": 103, "content": "\tat java.base/java.lang.Thread.run(Thread.java:1583)"}, {"type": "INSERT", "lineNumber": 105, "content": ""}]}, {"timestamp": 1754466876682, "changes": [{"type": "DELETE", "lineNumber": 82, "oldContent": ""}, {"type": "DELETE", "lineNumber": 84, "oldContent": ""}, {"type": "DELETE", "lineNumber": 86, "oldContent": "\tat java.base/java.lang.Thread.run(Thread.java:1583)"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)"}, {"type": "INSERT", "lineNumber": 93, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)"}, {"type": "INSERT", "lineNumber": 95, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)"}, {"type": "INSERT", "lineNumber": 96, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)"}, {"type": "INSERT", "lineNumber": 97, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)"}, {"type": "INSERT", "lineNumber": 98, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)"}, {"type": "INSERT", "lineNumber": 99, "content": "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)"}, {"type": "INSERT", "lineNumber": 100, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)"}, {"type": "INSERT", "lineNumber": 101, "content": "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)"}, {"type": "INSERT", "lineNumber": 102, "content": "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)"}, {"type": "INSERT", "lineNumber": 103, "content": "\tat java.base/java.lang.Thread.run(Thread.java:1583)"}, {"type": "INSERT", "lineNumber": 104, "content": ""}, {"type": "INSERT", "lineNumber": 105, "content": "06-August-2025 13 01:24:16.415 PM ERROR [com.mindarray.visualization.VisualizationMetricResponseProcessor]: java.lang.NullPointerException: Cannot invoke \"io.vertx.core.json.JsonObject.getString(String)\" because \"inventory\" is null"}, {"type": "INSERT", "lineNumber": 106, "content": "\tat com.mindarray.visualization.VisualizationConstants.join(VisualizationConstants.java:3243)"}, {"type": "INSERT", "lineNumber": 107, "content": "\tat com.mindarray.visualization.VisualizationConstants.composeQueryResponse(VisualizationConstants.java:2940)"}, {"type": "INSERT", "lineNumber": 108, "content": "\tat com.mindarray.visualization.VisualizationMetricResponseProcessor.lambda$start$0(VisualizationMetricResponseProcessor.java:72)"}, {"type": "INSERT", "lineNumber": 109, "content": "\tat io.vertx.core.impl.ContextImpl.emit(ContextImpl.java:342)"}, {"type": "INSERT", "lineNumber": 110, "content": "\tat io.vertx.core.impl.DuplicatedContext.emit(DuplicatedContext.java:163)"}, {"type": "INSERT", "lineNumber": 111, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.dispatch(MessageConsumerImpl.java:174)"}, {"type": "INSERT", "lineNumber": 112, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration$InboundDeliveryContext.execute(HandlerRegistration.java:137)"}, {"type": "INSERT", "lineNumber": 113, "content": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.next(DeliveryContextBase.java:80)"}, {"type": "INSERT", "lineNumber": 114, "content": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.dispatch(DeliveryContextBase.java:43)"}, {"type": "INSERT", "lineNumber": 115, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.dispatch(HandlerRegistration.java:98)"}, {"type": "INSERT", "lineNumber": 116, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.deliver(MessageConsumerImpl.java:180)"}, {"type": "INSERT", "lineNumber": 118, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.lambda$receive$0(HandlerRegistration.java:49)"}, {"type": "INSERT", "lineNumber": 119, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)"}, {"type": "INSERT", "lineNumber": 120, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)"}, {"type": "INSERT", "lineNumber": 121, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)"}, {"type": "INSERT", "lineNumber": 122, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)"}, {"type": "INSERT", "lineNumber": 123, "content": "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)"}, {"type": "INSERT", "lineNumber": 124, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)"}, {"type": "INSERT", "lineNumber": 125, "content": "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)"}, {"type": "INSERT", "lineNumber": 126, "content": "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)"}, {"type": "INSERT", "lineNumber": 127, "content": "\tat java.base/java.lang.Thread.run(Thread.java:1583)"}, {"type": "INSERT", "lineNumber": 128, "content": ""}, {"type": "INSERT", "lineNumber": 129, "content": ""}]}, {"timestamp": 1754466934125, "changes": [{"type": "DELETE", "lineNumber": 87, "oldContent": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)"}, {"type": "MODIFY", "lineNumber": 89, "content": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.next(DeliveryContextBase.java:80)", "oldContent": "\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)"}, {"type": "INSERT", "lineNumber": 90, "content": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.dispatch(DeliveryContextBase.java:43)"}, {"type": "INSERT", "lineNumber": 91, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.dispatch(HandlerRegistration.java:98)"}, {"type": "INSERT", "lineNumber": 92, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.deliver(MessageConsumerImpl.java:180)"}, {"type": "INSERT", "lineNumber": 93, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)"}, {"type": "INSERT", "lineNumber": 94, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.lambda$receive$0(HandlerRegistration.java:49)"}, {"type": "INSERT", "lineNumber": 95, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.next(DeliveryContextBase.java:80)"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.dispatch(DeliveryContextBase.java:43)"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.dispatch(HandlerRegistration.java:98)"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.deliver(MessageConsumerImpl.java:180)"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.lambda$receive$0(HandlerRegistration.java:49)"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)"}, {"type": "DELETE", "lineNumber": 108, "oldContent": ""}, {"type": "DELETE", "lineNumber": 110, "oldContent": ""}, {"type": "DELETE", "lineNumber": 112, "oldContent": "\tat java.base/java.lang.Thread.run(Thread.java:1583)"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)"}, {"type": "DELETE", "lineNumber": 122, "oldContent": "\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)"}, {"type": "DELETE", "lineNumber": 124, "oldContent": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)"}, {"type": "INSERT", "lineNumber": 117, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)"}, {"type": "INSERT", "lineNumber": 118, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.lambda$receive$0(HandlerRegistration.java:49)"}, {"type": "INSERT", "lineNumber": 120, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)"}, {"type": "INSERT", "lineNumber": 121, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)"}, {"type": "INSERT", "lineNumber": 122, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)"}, {"type": "INSERT", "lineNumber": 123, "content": "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)"}, {"type": "INSERT", "lineNumber": 124, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)"}, {"type": "INSERT", "lineNumber": 125, "content": "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)"}, {"type": "INSERT", "lineNumber": 126, "content": "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)"}, {"type": "INSERT", "lineNumber": 127, "content": "\tat java.base/java.lang.Thread.run(Thread.java:1583)"}, {"type": "INSERT", "lineNumber": 128, "content": ""}, {"type": "INSERT", "lineNumber": 129, "content": "06-August-2025 13 01:24:41.887 PM ERROR [com.mindarray.visualization.VisualizationMetricResponseProcessor]: java.lang.NullPointerException: Cannot invoke \"String.equalsIgnoreCase(String)\" because the return value of \"io.vertx.core.json.JsonObject.getString(String)\" is null"}, {"type": "INSERT", "lineNumber": 130, "content": "\tat com.mindarray.visualization.VisualizationConstants.join(VisualizationConstants.java:3249)"}, {"type": "INSERT", "lineNumber": 131, "content": "\tat com.mindarray.visualization.VisualizationConstants.composeQueryResponse(VisualizationConstants.java:2940)"}, {"type": "INSERT", "lineNumber": 132, "content": "\tat com.mindarray.visualization.VisualizationMetricResponseProcessor.lambda$start$0(VisualizationMetricResponseProcessor.java:72)"}, {"type": "INSERT", "lineNumber": 133, "content": "\tat io.vertx.core.impl.ContextImpl.emit(ContextImpl.java:342)"}, {"type": "INSERT", "lineNumber": 134, "content": "\tat io.vertx.core.impl.DuplicatedContext.emit(DuplicatedContext.java:163)"}, {"type": "INSERT", "lineNumber": 135, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.dispatch(MessageConsumerImpl.java:174)"}, {"type": "INSERT", "lineNumber": 136, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration$InboundDeliveryContext.execute(HandlerRegistration.java:137)"}, {"type": "INSERT", "lineNumber": 137, "content": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.next(DeliveryContextBase.java:80)"}, {"type": "INSERT", "lineNumber": 138, "content": "\tat io.vertx.core.eventbus.impl.DeliveryContextBase.dispatch(DeliveryContextBase.java:43)"}, {"type": "INSERT", "lineNumber": 139, "content": "\tat io.vertx.core.eventbus.impl.HandlerRegistration.dispatch(HandlerRegistration.java:98)"}, {"type": "INSERT", "lineNumber": 140, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.deliver(MessageConsumerImpl.java:180)"}, {"type": "INSERT", "lineNumber": 141, "content": "\tat io.vertx.core.eventbus.impl.MessageConsumerImpl.doReceive(MessageConsumerImpl.java:165)"}, {"type": "INSERT", "lineNumber": 143, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)"}, {"type": "INSERT", "lineNumber": 144, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)"}, {"type": "INSERT", "lineNumber": 145, "content": "\tat io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)"}, {"type": "INSERT", "lineNumber": 146, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)"}, {"type": "INSERT", "lineNumber": 147, "content": "\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)"}, {"type": "INSERT", "lineNumber": 148, "content": "\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)"}, {"type": "INSERT", "lineNumber": 149, "content": "\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)"}, {"type": "INSERT", "lineNumber": 150, "content": "\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)"}, {"type": "INSERT", "lineNumber": 151, "content": "\tat java.base/java.lang.Thread.run(Thread.java:1583)"}, {"type": "INSERT", "lineNumber": 152, "content": ""}, {"type": "INSERT", "lineNumber": 153, "content": ""}]}]}, "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/test/java/com/mindarray/suite/SLOSuite.java": {"filePath": "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/test/java/com/mindarray/suite/SLOSuite.java", "baseContent": "package com.mindarray.suite;\n\npublic class SLOSuite \n{\n}\n", "baseTimestamp": 1754475159381, "deltas": [{"timestamp": 1754475168343, "changes": [{"type": "INSERT", "lineNumber": 2, "content": "import org.junit.platform.suite.api.Suite;"}, {"type": "INSERT", "lineNumber": 3, "content": ""}, {"type": "INSERT", "lineNumber": 4, "content": "@Suite"}, {"type": "INSERT", "lineNumber": 5, "content": ""}]}, {"timestamp": 1754475178879, "changes": [{"type": "INSERT", "lineNumber": 2, "content": "import com.mindarray.TestInit;"}, {"type": "INSERT", "lineNumber": 3, "content": "import org.junit.platform.suite.api.SelectClasses;"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "public class SLOSuite "}, {"type": "DELETE", "lineNumber": 5, "oldContent": "{"}, {"type": "INSERT", "lineNumber": 7, "content": "@SelectClasses({TestInit.class})"}, {"type": "INSERT", "lineNumber": 8, "content": "public class SLOSuite "}, {"type": "INSERT", "lineNumber": 9, "content": "{"}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}]}, {"timestamp": 1754475214158, "changes": [{"type": "MODIFY", "lineNumber": 4, "content": "import org.junit.platform.suite.api.Suite;", "oldContent": "import org.junit.platform.suite.api.Suite;"}, {"type": "INSERT", "lineNumber": 8, "content": "public class SLOSuite"}, {"type": "INSERT", "lineNumber": 9, "content": "{"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "public class SLOSuite "}, {"type": "DELETE", "lineNumber": 11, "oldContent": "{"}]}, {"timestamp": 1754481547571, "changes": [{"type": "INSERT", "lineNumber": 3, "content": "import com.mindarray.slo.TestSLOProcessor;"}, {"type": "INSERT", "lineNumber": 4, "content": "import org.junit.platform.suite.api.SelectClasses;"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "import org.junit.platform.suite.api.Suite;"}, {"type": "MODIFY", "lineNumber": 8, "content": "@SelectClasses({TestInit.class, TestSLOProcessor.class})", "oldContent": "@SelectClasses({TestInit.class})"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 11, "content": "}"}]}]}, "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/test/java/com/mindarray/slo/TestSLOProcessor.java": {"filePath": "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/test/java/com/mindarray/slo/TestSLOProcessor.java", "baseContent": "package com.mindarray.slo;\n\npublic class TestSLOProcessor \n{\n}\n", "baseTimestamp": 1754475214729, "deltas": [{"timestamp": 1754481201930, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "    "}, {"type": "INSERT", "lineNumber": 24, "content": "    "}]}, {"timestamp": 1754481224231, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "", "oldContent": "package com.mindarray.slo;"}, {"type": "MODIFY", "lineNumber": 24, "content": "    ", "oldContent": "    "}, {"type": "MODIFY", "lineNumber": 30, "content": "            var MonitorAvailabilitySLO = ", "oldContent": "            "}, {"type": "DELETE", "lineNumber": 39, "oldContent": "        JsonObject monitorPerformanceProfile = SLOProfileConfigStore.getStore()"}, {"type": "DELETE", "lineNumber": 40, "oldContent": ""}, {"type": "DELETE", "lineNumber": 42, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                .getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorAvailabilitySLO\");"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "    }"}]}, {"timestamp": 1754481233299, "changes": [{"type": "DELETE", "lineNumber": 5, "oldContent": ""}, {"type": "DELETE", "lineNumber": 6, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 5, "content": "import io.vertx.junit5.Timeout;"}, {"type": "INSERT", "lineNumber": 6, "content": "import io.vertx.junit5.VertxExtension;"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "    "}, {"type": "INSERT", "lineNumber": 9, "content": "import org.junit.jupiter.api.MethodOrderer;"}, {"type": "INSERT", "lineNumber": 10, "content": "import org.junit.jupiter.api.TestMethodOrder;"}, {"type": "INSERT", "lineNumber": 11, "content": "import org.junit.jupiter.api.condition.EnabledIfSystemProperty;"}, {"type": "INSERT", "lineNumber": 12, "content": "import org.junit.jupiter.api.extension.ExtendWith;"}, {"type": "INSERT", "lineNumber": 13, "content": "import org.junit.jupiter.api.parallel.Execution;"}, {"type": "INSERT", "lineNumber": 14, "content": "import org.junit.jupiter.api.parallel.ExecutionMode;"}, {"type": "INSERT", "lineNumber": 15, "content": ""}, {"type": "INSERT", "lineNumber": 16, "content": "@ExtendWith(VertxExtension.class)"}, {"type": "INSERT", "lineNumber": 17, "content": "@Execution(ExecutionMode.SAME_THREAD)"}, {"type": "INSERT", "lineNumber": 18, "content": "@TestMethodOrder(MethodOrderer.OrderAnnotation.class)"}, {"type": "INSERT", "lineNumber": 19, "content": "@Timeout(60 * 1000)"}, {"type": "INSERT", "lineNumber": 20, "content": "@EnabledIfSystemProperty(named = \"test.mode\", matches = \"^APP|^PRIMARY$\")"}, {"type": "INSERT", "lineNumber": 21, "content": "public class TestSLOProcessor"}, {"type": "INSERT", "lineNumber": 23, "content": "    private static final Logger LOGGER = new Logger(TestServiceOpsIntegration.class, GlobalConstants.MOTADATA_SLO, \"Test ServiceOps Integration\");"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "    static void beforeALl"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "@ExtendWith(VertxExtension.class)"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "            "}, {"type": "DELETE", "lineNumber": 19, "oldContent": "        {"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "        try"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "    {"}, {"type": "INSERT", "lineNumber": 25, "content": "    @BeforeAll"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "    "}, {"type": "DELETE", "lineNumber": 24, "oldContent": "    "}, {"type": "DELETE", "lineNumber": 25, "oldContent": ""}, {"type": "DELETE", "lineNumber": 26, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 28, "oldContent": ""}, {"type": "INSERT", "lineNumber": 27, "content": "    {"}, {"type": "INSERT", "lineNumber": 28, "content": "        try"}, {"type": "MODIFY", "lineNumber": 30, "content": "            var monitorAvailabilitySLO = SLOPr ", "oldContent": "            var MonitorAvailabilitySLO = "}, {"type": "DELETE", "lineNumber": 35, "oldContent": "        {"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "        catch (Exception exception)"}, {"type": "INSERT", "lineNumber": 35, "content": ""}, {"type": "INSERT", "lineNumber": 36, "content": "            testContext.failNow(exception.getMessage());"}, {"type": "INSERT", "lineNumber": 37, "content": "        }"}, {"type": "INSERT", "lineNumber": 38, "content": "    }"}, {"type": "INSERT", "lineNumber": 39, "content": "}"}, {"type": "INSERT", "lineNumber": 40, "content": ""}]}, {"timestamp": 1754481248108, "changes": [{"type": "INSERT", "lineNumber": 3, "content": "import com.mindarray.api.SLOProfile;"}, {"type": "INSERT", "lineNumber": 5, "content": "import com.mindarray.store.SLOProfileConfigStore;"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "    "}, {"type": "DELETE", "lineNumber": 14, "oldContent": "{"}, {"type": "MODIFY", "lineNumber": 24, "content": "{", "oldContent": "    {"}, {"type": "INSERT", "lineNumber": 26, "content": "    "}, {"type": "INSERT", "lineNumber": 28, "content": "    static void beforeAll(VertxTestContext testContext)"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "            var monitorAvailabilitySLO = SLOPr "}, {"type": "INSERT", "lineNumber": 32, "content": "            var monitorAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorAvailabilitySLO\");"}, {"type": "INSERT", "lineNumber": 33, "content": "            var "}, {"type": "DELETE", "lineNumber": 38, "oldContent": ""}, {"type": "INSERT", "lineNumber": 43, "content": ""}]}, {"timestamp": 1754481256811, "changes": [{"type": "MODIFY", "lineNumber": 6, "content": "import com.mindarray.util.Logger;", "oldContent": "import com.mindarray.util.Logger;"}, {"type": "MODIFY", "lineNumber": 29, "content": "    {", "oldContent": "    {"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "        }"}, {"type": "INSERT", "lineNumber": 33, "content": "            var MonitorPerformanceSLO"}, {"type": "INSERT", "lineNumber": 34, "content": "        }"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "            var "}, {"type": "DELETE", "lineNumber": 42, "oldContent": ""}]}, {"timestamp": 1754481263457, "changes": [{"type": "INSERT", "lineNumber": 5, "content": "import com.mindarray.store.SLOProfileConfigStore;"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "import com.mindarray.util.Logger;"}, {"type": "INSERT", "lineNumber": 28, "content": "    static void beforeAll(VertxTestContext testContext)"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "    {"}, {"type": "MODIFY", "lineNumber": 33, "content": "            var monitorPerformanceSLO = ", "oldContent": "            var MonitorPerformanceSLO"}]}, {"timestamp": 1754481266853, "changes": [{"type": "DELETE", "lineNumber": 32, "oldContent": "            var MonitorPerformanceSLO"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "            var monitorPerformanceSLO = "}, {"type": "INSERT", "lineNumber": 32, "content": "            var monitorAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorAvailabilitySLO\");"}, {"type": "INSERT", "lineNumber": 33, "content": "            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorAvailabilitySLO\");"}, {"type": "INSERT", "lineNumber": 42, "content": "}"}]}, {"timestamp": 1754481270640, "changes": [{"type": "MODIFY", "lineNumber": 33, "content": "            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorPerformanceSLO\");", "oldContent": "            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorAvailabilitySLO\");"}]}, {"timestamp": 1754481282740, "changes": [{"type": "INSERT", "lineNumber": 34, "content": "            var InstanceAvailabilitySLO = "}]}, {"timestamp": 1754481332387, "changes": [{"type": "MODIFY", "lineNumber": 26, "content": "", "oldContent": "    "}, {"type": "MODIFY", "lineNumber": 34, "content": "            var InstanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorAvailabilitySLO\");", "oldContent": "            var InstanceAvailabilitySLO = "}]}, {"timestamp": 1754481337045, "changes": [{"type": "MODIFY", "lineNumber": 10, "content": "import org.junit.jupiter.api.BeforeAll;", "oldContent": "}"}, {"type": "MODIFY", "lineNumber": 34, "content": "            var InstanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"InstanceAvailabilitySLO\");", "oldContent": "            var InstanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorAvailabilitySLO\");"}]}, {"timestamp": 1754481353419, "changes": [{"type": "MODIFY", "lineNumber": 34, "content": "            var InstanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"InstanceAvailabilitySLO\");  ", "oldContent": "            var InstanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"InstanceAvailabilitySLO\");"}, {"type": "INSERT", "lineNumber": 44, "content": ""}]}, {"timestamp": 1754481412207, "changes": [{"type": "MODIFY", "lineNumber": 34, "content": "            var InstanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"InstanceAvailabilitySLO\");", "oldContent": "            var InstanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"InstanceAvailabilitySLO\");  "}]}, {"timestamp": 1754481418665, "changes": [{"type": "MODIFY", "lineNumber": 9, "content": "import io.vertx.junit5.VertxTestContext;", "oldContent": "}"}, {"type": "MODIFY", "lineNumber": 31, "content": "        {", "oldContent": "        catch (Exception exception)"}, {"type": "INSERT", "lineNumber": 35, "content": "            var "}, {"type": "DELETE", "lineNumber": 36, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "        }"}, {"type": "INSERT", "lineNumber": 37, "content": "        catch (Exception exception)"}, {"type": "INSERT", "lineNumber": 38, "content": "        {"}, {"type": "INSERT", "lineNumber": 39, "content": "            LOGGER.error(exception);"}, {"type": "DELETE", "lineNumber": 40, "oldContent": ""}, {"type": "INSERT", "lineNumber": 41, "content": "            testContext.failNow(exception.getMessage());"}, {"type": "INSERT", "lineNumber": 42, "content": "        }"}, {"type": "INSERT", "lineNumber": 43, "content": "    }"}]}, {"timestamp": 1754481452423, "changes": [{"type": "MODIFY", "lineNumber": 35, "content": "            var InstancePerformanceSLO", "oldContent": "            var "}, {"type": "INSERT", "lineNumber": 40, "content": ""}, {"type": "INSERT", "lineNumber": 41, "content": "            testContext.failNow(exception.getMessage());"}, {"type": "INSERT", "lineNumber": 43, "content": "    }"}, {"type": "INSERT", "lineNumber": 44, "content": "}"}, {"type": "INSERT", "lineNumber": 45, "content": ""}]}, {"timestamp": 1754481455107, "changes": [{"type": "MODIFY", "lineNumber": 34, "content": "            var instanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"InstanceAvailabilitySLO\");", "oldContent": "            var InstanceAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"InstanceAvailabilitySLO\");"}, {"type": "MODIFY", "lineNumber": 36, "content": "        }", "oldContent": "}"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "            testContext.failNow(exception.getMessage());"}, {"type": "DELETE", "lineNumber": 43, "oldContent": ""}, {"type": "DELETE", "lineNumber": 44, "oldContent": "}"}, {"type": "INSERT", "lineNumber": 42, "content": "        }"}, {"type": "INSERT", "lineNumber": 44, "content": "}"}, {"type": "INSERT", "lineNumber": 45, "content": ""}]}, {"timestamp": 1754481461607, "changes": [{"type": "MODIFY", "lineNumber": 4, "content": "import com.mindarray.integration.TestServiceOpsIntegration;", "oldContent": "public class TestSLOProcessor "}, {"type": "DELETE", "lineNumber": 29, "oldContent": ""}, {"type": "DELETE", "lineNumber": 30, "oldContent": "            var "}, {"type": "INSERT", "lineNumber": 29, "content": "    {"}, {"type": "INSERT", "lineNumber": 30, "content": "        try"}, {"type": "MODIFY", "lineNumber": 35, "content": "            var instancePerformanceSLO = S", "oldContent": "            var InstancePerformanceSLO"}, {"type": "MODIFY", "lineNumber": 41, "content": "            testContext.failNow(exception.getMessage());", "oldContent": ""}, {"type": "INSERT", "lineNumber": 43, "content": "    }"}]}, {"timestamp": 1754481473328, "changes": [{"type": "MODIFY", "lineNumber": 25, "content": "    private static final Logger LOGGER = new Logger(TestServiceOpsIntegration.class, GlobalConstants.MOTADATA_SLO, \"Test ServiceOps Integration\");", "oldContent": "            var "}, {"type": "MODIFY", "lineNumber": 27, "content": "    @BeforeAll", "oldContent": "            var monitorAvailabilitySLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorAvailabilitySLO\");"}, {"type": "MODIFY", "lineNumber": 33, "content": "            var monitorPerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"MonitorPerformanceSLO\");", "oldContent": "            var InstanceAvailabilitySLO = "}, {"type": "MODIFY", "lineNumber": 35, "content": "            var instancePerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO)", "oldContent": "            var instancePerformanceSLO = S"}, {"type": "INSERT", "lineNumber": 39, "content": "            LOGGER.error(exception);"}, {"type": "DELETE", "lineNumber": 40, "oldContent": ""}, {"type": "INSERT", "lineNumber": 45, "content": ""}]}, {"timestamp": 1754481482753, "changes": [{"type": "MODIFY", "lineNumber": 2, "content": "import com.mindarray.GlobalConstants;", "oldContent": "public class TestSLOProcessor "}, {"type": "MODIFY", "lineNumber": 23, "content": "public class TestSLOProcessor", "oldContent": ""}, {"type": "MODIFY", "lineNumber": 35, "content": "            var instancePerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO_PROFILE_NAME, \"InstancePerformanceSLO\");", "oldContent": "            var instancePerformanceSLO = SLOProfileConfigStore.getStore().getItemByValue(SLOProfile.SLO)"}]}, {"timestamp": 1754481636431, "changes": [{"type": "MODIFY", "lineNumber": 22, "content": "@EnabledIfSystemProperty(named = \"test.mode\", matches = \"^APP|^PRIMARY$\")", "oldContent": ""}]}]}, "/home/<USER>/.codeium/brain/d947e7c5-2abf-4c7e-a3bf-1655a1e06c3c/plan.md": {"filePath": "/home/<USER>/.codeium/brain/d947e7c5-2abf-4c7e-a3bf-1655a1e06c3c/plan.md", "baseContent": "# SLOProcessor.java Analysis Plan\n\n## Notes\n- User requested a thorough analysis of SLOProcessor.java.\n- No code changes are to be made at this stage.\n- Objective is to collect data on possible test cases and coverage strategies.\n- Comprehensive analysis of SLOProcessor.java completed.\n- Core functionality: SLO cycle management, real-time status processing, cache management, event-driven Vert.x architecture, datastore integration, support for both availability and performance SLOs.\n- Key methods: start(), qualifyCycles(), startCycle(), process(), calculate(), updateSLO(), and utilities.\n- Test case categories: Initialization & Configuration, SLO Cycle Management, Real-time Processing, Calculation & Metrics, Cache & Persistence, Event Handling, Error Handling & Edge Cases, Integration.\n\n## Task List\n- [x] Analyze SLOProcessor.java code structure and logic\n- [ ] Identify possible test cases based on code paths\n- [ ] Suggest strategies to cover all cases\n- [ ] Document findings for user review\n\n## Current Goal\nDocument findings and propose test coverage strategies", "baseTimestamp": 1754475563466, "deltas": [{"timestamp": 1754475629576, "changes": [{"type": "DELETE", "lineNumber": 6, "oldContent": "- Comprehensive analysis of SLOProcessor.java completed."}, {"type": "DELETE", "lineNumber": 7, "oldContent": "- Core functionality: SLO cycle management, real-time status processing, cache management, event-driven Vert.x architecture, datastore integration, support for both availability and performance SLOs."}, {"type": "DELETE", "lineNumber": 8, "oldContent": "- Key methods: start(), qualifyCycles(), startCycle(), process(), calculate(), updateSLO(), and utilities."}, {"type": "DELETE", "lineNumber": 9, "oldContent": "- Test case categories: Initialization & Configuration, SLO Cycle Management, Real-time Processing, Calculation & Metrics, Cache & Persistence, Event Handling, Error Handling & Edge Cases, Integration."}, {"type": "DELETE", "lineNumber": 12, "oldContent": "- [x] Analyze SLOProcessor.java code structure and logic"}, {"type": "INSERT", "lineNumber": 8, "content": "- [ ] Analyze SLOProcessor.java code structure and logic"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "Document findings and propose test coverage strategies"}, {"type": "INSERT", "lineNumber": 14, "content": "Analyze SLOProcessor.java for test case possibilities"}]}, {"timestamp": 1754475643490, "changes": [{"type": "MODIFY", "lineNumber": 6, "content": "- Comprehensive analysis of SLOProcessor.java completed.", "oldContent": "- [ ] Analyze SLOProcessor.java code structure and logic"}, {"type": "INSERT", "lineNumber": 7, "content": "- Core functionality: SLO cycle management, real-time status processing, cache management, event-driven Vert.x architecture, datastore integration, support for both availability and performance SLOs."}, {"type": "INSERT", "lineNumber": 8, "content": "- Key methods: start(), qualifyCycles(), startCycle(), process(), calculate(), updateSLO(), and utilities."}, {"type": "INSERT", "lineNumber": 9, "content": "- Test case categories: Initialization & Configuration, SLO Cycle Management, Real-time Processing, Calculation & Metrics, Cache & Persistence, Event Handling, Error Handling & Edge Cases, Integration."}, {"type": "INSERT", "lineNumber": 10, "content": "- Next: Analyze existing test case architecture, including TestServiceOpsIntegration.java, TestServiceNowIntegration.java, and related classes."}, {"type": "DELETE", "lineNumber": 9, "oldContent": "- [ ] Identify possible test cases based on code paths"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "Analyze SLOProcessor.java for test case possibilities"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "- [ ] Suggest strategies to cover all cases"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "- [ ] Document findings for user review"}, {"type": "INSERT", "lineNumber": 13, "content": "- [x] Analyze SLOProcessor.java code structure and logic"}, {"type": "INSERT", "lineNumber": 14, "content": "- [x] Identify possible test cases based on code paths"}, {"type": "INSERT", "lineNumber": 15, "content": "- [x] Suggest strategies to cover all cases"}, {"type": "INSERT", "lineNumber": 16, "content": "- [x] Document findings for user review"}, {"type": "INSERT", "lineNumber": 17, "content": "- [ ] Analyze existing test case architecture and patterns"}, {"type": "INSERT", "lineNumber": 20, "content": "Analyze test case architecture and patterns"}]}]}, "/Dummy.txt": {"filePath": "/Dummy.txt", "baseContent": "SLO instance profile related changes", "baseTimestamp": 1754480802687, "deltas": [{"timestamp": 1754483566455, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "Refa<PERSON>", "oldContent": "SLO instance profile related changes"}]}]}, "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/test/java/com/mindarray/suite/TestSuite.java": {"filePath": "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/test/java/com/mindarray/suite/TestSuite.java", "baseContent": "/*\n *   Copyright (c) Motadata 2025. All rights reserved.\n *\n *   This source code is the property of Motadata and constitutes\n *   proprietary and confidential information. Unauthorized copying, distribution,\n *   modification, or use of this file, via any medium, is strictly prohibited\n *   unless prior written permission is obtained from Motadata.\n *\n *   Unauthorized access or use of this software may result in legal action\n *   and/or prosecution to the fullest extent of the law.\n *\n *   This software is provided \"AS IS,\" without warranties of any kind, express\n *   or implied, including but not limited to implied warranties of\n *   merchantability or fitness for a particular purpose. In no event shall\n *   Motadata be held liable for any damages arising from the use\n *   of this software.\n *\n *   For inquiries, contact: <EMAIL>\n *\n */\n\npackage com.mindarray.suite;\n\nimport org.junit.jupiter.api.ClassOrderer;\nimport org.junit.jupiter.api.TestClassOrder;\nimport org.junit.platform.suite.api.SelectClasses;\nimport org.junit.platform.suite.api.Suite;\n\n@Suite\n@SelectClasses({BootstrapStandaloneSuite.class, MotadataAppManagerSuite.class, LDAPServerSuite.class, HASuite.class, TrapProcessorSuite.class, MailServerSuite.class, NotificationSuite.class, UserProfileSuite.class, CredentialProfilesSuite.class, DiscoveryProfilesSuite.class, AgentSuite.class, DiscoveryRunSuite.class, PostDiscoverySuite.class, LicenseSuite.class, ProvisionObjectSuite.class, MetricSchedulerSuite.class, RediscoverySuite.class, PollerScheduleSuite.class, ConfigSuite.class, ComplianceSuite.class, MetricPollerSuite.class, DatastoreSuite.class, HealthSuite.class, DependencySuite.class, DependencyMapperSuite.class, CorrelationEngineSuite.class, RunbookSuite.class, TagAPISuite.class, FlowAPISuite.class, ServerAPISuite.class, SLOSuite.class ,VisualizationSuite.class, PolicySuite.class, FlowProcessorSuite.class, LogProcessorSuite.class, EventTrackerSuite.class,EventEngineSuite.class, ReportSuite.class, IntegrationSuite.class, StreamingEngineSuite.class, PatchSuite.class, MiscellaneousSuite.class})\n@TestClassOrder(ClassOrderer.class)\npublic class TestSuite\n{\n\n}", "baseTimestamp": 1754481529199, "deltas": [{"timestamp": 1754481531235, "changes": [{"type": "MODIFY", "lineNumber": 29, "content": "@SelectClasses({BootstrapStandaloneSuite.class, MotadataAppManagerSuite.class, LDAPServerSuite.class, HASuite.class, TrapProcessorSuite.class, MailServerSuite.class, NotificationSuite.class, UserProfileSuite.class, CredentialProfilesSuite.class, DiscoveryProfilesSuite.class, AgentSuite.class, DiscoveryRunSuite.class, PostDiscoverySuite.class, LicenseSuite.class, ProvisionObjectSuite.class, MetricSchedulerSuite.class, RediscoverySuite.class, PollerScheduleSuite.class, ConfigSuite.class, ComplianceSuite.class, MetricPollerSuite.class, DatastoreSuite.class, HealthSuite.class, DependencySuite.class, DependencyMapperSuite.class, CorrelationEngineSuite.class, RunbookSuite.class, TagAPISuite.class, FlowAPISuite.class, ServerAPISuite.class, SLOSuite.class, VisualizationSuite.class, PolicySuite.class, FlowProcessorSuite.class, LogProcessorSuite.class, EventTrackerSuite.class, EventEngineSuite.class, ReportSuite.class, IntegrationSuite.class, StreamingEngineSuite.class, PatchSuite.class, MiscellaneousSuite.class})", "oldContent": "@SelectClasses({BootstrapStandaloneSuite.class, MotadataAppManagerSuite.class, LDAPServerSuite.class, HASuite.class, TrapProcessorSuite.class, MailServerSuite.class, NotificationSuite.class, UserProfileSuite.class, CredentialProfilesSuite.class, DiscoveryProfilesSuite.class, AgentSuite.class, DiscoveryRunSuite.class, PostDiscoverySuite.class, LicenseSuite.class, ProvisionObjectSuite.class, MetricSchedulerSuite.class, RediscoverySuite.class, PollerScheduleSuite.class, ConfigSuite.class, ComplianceSuite.class, MetricPollerSuite.class, DatastoreSuite.class, HealthSuite.class, DependencySuite.class, DependencyMapperSuite.class, CorrelationEngineSuite.class, RunbookSuite.class, TagAPISuite.class, FlowAPISuite.class, ServerAPISuite.class, SLOSuite.class ,VisualizationSuite.class, PolicySuite.class, FlowProcessorSuite.class, LogProcessorSuite.class, EventTrackerSuite.class,EventEngineSuite.class, ReportSuite.class, IntegrationSuite.class, StreamingEngineSuite.class, PatchSuite.class, MiscellaneousSuite.class})"}]}]}, "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/main/java/com/mindarray/datastore/DatastoreConstants.java": {"filePath": "/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/src/main/java/com/mindarray/datastore/DatastoreConstants.java", "baseContent": "/*\n *   Copyright (c) Motadata 2025. All rights reserved.\n *\n *   This source code is the property of Motadata and constitutes\n *   proprietary and confidential information. Unauthorized copying, distribution,\n *   modification, or use of this file, via any medium, is strictly prohibited\n *   unless prior written permission is obtained from Motadata.\n *\n *   Unauthorized access or use of this software may result in legal action\n *   and/or prosecution to the fullest extent of the law.\n *\n *   This software is provided \"AS IS,\" without warranties of any kind, express\n *   or implied, including but not limited to implied warranties of\n *   merchantability or fitness for a particular purpose. In no event shall\n *   Motadata be held liable for any damages arising from the use\n *   of this software.\n *\n *   For inquiries, contact: <EMAIL>\n *\n */\n\n/*\nChange Logs\n *   Date          Author              Notes\n *  2025-03-18     <PERSON><PERSON>        Added .latency as data category for Float.\n *  2025-04-01     <PERSON><PERSON>        Added latency as data category for Float.\n *  24-Feb-2025\t   Pruthviraj Jadeja   NetRoute related new plugin id added\n *  05-Jun-2025\t   Vismit              Motadata-6451 Added replace method to replace reserved separators with empty value\n *  20-Jun-2025\t   Sankalp\t\t       MOTADATA-5513 : Removed started.time.seconds from default data type string\n\n */\n\npackage com.mindarray.datastore;\n\nimport com.mindarray.Bootstrap;\nimport com.mindarray.GlobalConstants;\nimport com.mindarray.api.RemoteEventProcessor;\nimport com.mindarray.eventbus.EventBusConstants;\nimport com.mindarray.log.LogEngineConstants;\nimport com.mindarray.nms.NMSConstants;\nimport com.mindarray.store.RemoteEventProcessorConfigStore;\nimport com.mindarray.util.CodecUtil;\nimport com.mindarray.util.CommonUtil;\nimport com.mindarray.util.Logger;\nimport com.mindarray.util.MotadataConfigUtil;\nimport com.mindarray.visualization.VisualizationConstants;\nimport io.vertx.core.buffer.Buffer;\nimport io.vertx.core.json.JsonArray;\nimport io.vertx.core.json.JsonObject;\nimport org.apache.commons.lang3.math.NumberUtils;\n\nimport java.io.File;\nimport java.nio.charset.StandardCharsets;\nimport java.nio.file.Files;\nimport java.util.*;\nimport java.util.stream.Collectors;\n\nimport static com.mindarray.GlobalConstants.*;\nimport static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;\nimport static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;\nimport static com.mindarray.eventbus.EventBusConstants.*;\nimport static com.mindarray.visualization.VisualizationConstants.VisualizationGrouping.EVENT_SOURCE;\n\npublic class DatastoreConstants\n{\n    //datastore const ..\n\n    public static final Set<String> floatingColumns = new HashSet<>();\n    public static final String METRIC_COLUMNS = \"metric-columns\";\n    public static final String EVENT_COLUMNS = \"event-columns\";\n    public static final String INDEXABLE_COLUMNS = \"indexable-columns\";\n    public static final String EVENT_CATEGORIES = \"event-categories\";\n    public static final String FLOATING_COLUMNS = \"floating-columns\";\n    public static final String MAPPER = \"mapper\";\n    public static final String MAPPER_PLUGIN_IDS = \"mapper.plugin.ids\";\n    public static final String MAPPER_DATA_CATEGORIES = \"mapper.data.categories\";\n    public static final String MAPPER_INSTANCE = \"mapper.instance\";\n    public static final String MAPPER_CORRELATED = \"mapper.correlated\";\n    public static final String MAPPER_SHADOW_COUNTER = \"mapper.shadow.counter\";\n    public static final String MAPPER_EVENT_CATEGORY = \"mapper.event.category\";\n    public static final String MAPPER_DURATION = \"mapper.duration\";\n    public static final String MAPPER_STATUS = \"mapper.status\";\n    public static final String MAPPER_GROUP = \"mapper.group\";\n    public static final String DATASTORE_CATEGORY = \"datastore.category\";\n    public static final String DATASTORE_FORMAT = \"datastore.format\";\n\n    public static final String DATASTORE_TYPE = \"datastore.type\";\n    public static final String OPERATION_TYPE = \"operation.type\";\n    public static final String BATCHES = \"batches\";\n    public static final String BATCH_FILE = \"batch.file\";\n    public static final String BACKUP_SIZE_BYTES = \"backup.size.bytes\";\n    public static final String DATASTORE_WRITER_MAX_BUFFER_BYTES = \"datastore.writer.max.buffer.bytes\";\n    public static final Set<String> EVENT_BATCH_PASSOVER_FIELDS = Set.of(GlobalConstants.PLUGIN_ID, EventBusConstants.EVENT_TIMESTAMP, DATASTORE_TYPE, EventBusConstants.EVENT_SOURCE, EventBusConstants.EVENT_VOLUME_BYTES, EVENT_COPY_REQUIRED);\n    public static final String DATASTORE_UUID = \"datastore.uuid\";\n    public static final String DATASTORE_PASSOVER_STEP_1_QUERY = \"passover.step1.query\";\n    public static final int DATASTORE_REQUEST_BATCH_SIZE = MotadataConfigUtil.getDatastoreRequestBatchSize(); // Used to send number of monitors in a single histogram request\n    public static final String DATASTORE_TYPES = \"datastore.types\";\n\n    private DatastoreConstants()\n    {\n    }\n\n    public static void runRetention(String eventType, long seconds, Logger logger)\n    {\n        try\n        {\n            var path = CURRENT_DIR + PATH_SEPARATOR + EventBusConstants.EVENT_DIR + PATH_SEPARATOR + EventBusConstants.replace(eventType);\n\n            var eventFiles = new File(path + GlobalConstants.PATH_SEPARATOR + \"bookmarks\");\n\n            if (eventFiles.exists())\n            {\n                var content = Files.readString(eventFiles.toPath(), StandardCharsets.UTF_8);\n\n                if (!content.isEmpty())\n                {\n                    var events = new JsonObject(content);\n\n                    logger.info(String.format(\"%s file found in path : %s \", events.size(), eventFiles.getPath()));\n\n                    if (!events.isEmpty())\n                    {\n                        for (var entry : events.getMap().entrySet())\n                        {\n                            var timestamp = CommonUtil.getLong(entry.getKey());\n\n                            if (new File(path + GlobalConstants.PATH_SEPARATOR + timestamp + \".dat\").exists() && timestamp < seconds)\n                            {\n                                Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_RETENTION_TRIGGER + DOT_SEPARATOR + eventType, timestamp);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        catch (Exception exception)\n        {\n            logger.error(exception);\n        }\n    }\n\n    public static byte getDataCategory(boolean metric, String key, String value)\n    {\n        var category = DataCategory.STRING.getName();\n\n        if (metric && (key.endsWith(\".id\") || key.endsWith(\".uptime\") || key.endsWith(\".version\") || key.endsWith(\".state\") ||\n                key.endsWith(\".status\") || key.endsWith(\".creation.time.seconds\") || key.endsWith(\".uptime.sec\") ||\n                key.endsWith(\".uptime.seconds\") || key.endsWith(\".started.time\")))\n        {\n\n            return category;\n        }\n        else if (!key.endsWith(NMSConstants.INSTANCE_NAME) && value != null && !value.startsWith(\"0x\") && NumberUtils.isCreatable(value))\n        {\n            if (key.endsWith(\".percent\") || key.endsWith(\".cost\") ||\n                    key.endsWith(\".amount\") ||\n                    key.endsWith(\".system.load.avg\") ||\n                    key.contains(\".avg.\") || key.contains(\"latency\") ||\n                    floatingColumns.contains(key))\n            {\n                category = DataCategory.FLOAT.getName();\n            }\n            else if (CommonUtil.isNumeric(value))\n            {\n                category = DataCategory.NUMERIC.getName();\n            }\n        }\n\n        return category;\n    }\n\n    // write datastore event\n    public static void write(JsonObject event, String eventCategory, Set<String> mappers, StringBuilder builder)\n    {\n        try\n        {\n            builder.setLength(0);\n\n            var source = event.getString(EventBusConstants.EVENT_SOURCE);\n\n            var correlatedBatch = event.getString(NMSConstants.CORRELATION_METRICS, NO);\n\n            event.remove(NMSConstants.CORRELATION_METRICS);\n\n            if (!event.containsKey(LogEngineConstants.EVENT_CATEGORY))\n            {\n                event.put(LogEngineConstants.EVENT_CATEGORY, eventCategory);\n            }\n\n            var iterator = event.iterator();\n\n            var buffer = Buffer.buffer(\"UTF-8\");\n\n            appendBytes(buffer, event.getLong(EventBusConstants.EVENT_TIMESTAMP), event.getInteger(PLUGIN_ID), event.getInteger(DATASTORE_TYPE), source != null ? source : EMPTY_VALUE, eventCategory, mappers, builder);\n\n            while (iterator.hasNext())\n            {\n                var entry = iterator.next();\n\n                if (!EVENT_BATCH_PASSOVER_FIELDS.contains(entry.getKey()) && entry.getValue() != null)\n                {\n                    updateEventFieldBatch(entry.getKey(), replace(CommonUtil.getString(entry.getValue())), event.getInteger(GlobalConstants.PLUGIN_ID), buffer, mappers, builder, eventCategory, correlatedBatch);\n                }\n            }\n\n            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_WRITE + \".\" + DatastoreCategory.EVENT.getName(), buffer.getBytes());\n\n        }\n        catch (Exception exception)\n        {\n            exception.getStackTrace();\n        }\n    }\n\n    private static void appendBytes(Buffer bytes, long timestamp, int pluginId, int dataStoreType, String source, String eventCategory, Set<String> mappers, StringBuilder builder)\n    {\n        bytes.setLongLE(0, timestamp);\n\n        var plugin = pluginId + DASH_SEPARATOR + (eventCategory.trim().replace(\" \", \".\").toLowerCase());\n\n        var valueBytes = plugin.getBytes(StandardCharsets.UTF_8);\n\n        bytes.appendIntLE(valueBytes.length);\n\n        bytes.appendBytes(valueBytes);\n\n        bytes.appendByte(DatastoreFormat.HORIZONTAL.getName().byteValue());\n\n        bytes.appendByte(CommonUtil.getByteValue(dataStoreType));\n\n        valueBytes = source.getBytes(StandardCharsets.UTF_8);\n\n        bytes.appendShortLE(CommonUtil.getShort(valueBytes.length));\n\n        bytes.appendBytes(valueBytes);\n\n        builder.append(DataCategory.STRING.getName()).append(COLUMN_SEPARATOR).append(PluginId.GENERIC_LOG_EVENT.getName()).append(COLUMN_SEPARATOR).append(EVENT_SOURCE.getName()).append(COLUMN_SEPARATOR).append(NO).append(COLUMN_SEPARATOR).append(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName());\n\n        if (!mappers.contains(builder.toString()))\n        {\n            mappers.add(builder.toString());\n\n            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,\n                    new JsonObject()\n                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name())\n                            .put(MAPPER, builder.toString()));\n        }\n    }\n\n    private static void updateEventFieldBatch(String column, String value, int pluginId, Buffer buffer, Set<String> mappers, StringBuilder builder, String eventCategory, String correlatedMetric)\n    {\n        builder.setLength(0);\n\n        byte category;\n\n        if (column.equalsIgnoreCase(GlobalConstants.MESSAGE) || column.equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE.getName()) || column.equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName())\n                || column.equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()) || column.equalsIgnoreCase(LogEngineConstants.EVENT_SEVERITY))\n        {\n            pluginId = PluginId.GENERIC_LOG_EVENT.getName();\n\n            category = DataCategory.STRING.getName();\n\n            eventCategory = \"event.history\";\n        }\n        else\n        {\n            category = getDataCategory(false, column, value);\n        }\n\n        if (category == DataCategory.FLOAT.getName())\n        {\n            buffer.appendByte(DataCategory.NUMERIC.getName());\n\n            category = DataCategory.NUMERIC.getName();\n        }\n        else\n        {\n            buffer.appendByte(category);\n        }\n\n        var bytes = column.getBytes(StandardCharsets.UTF_8);\n\n        buffer.appendIntLE(bytes.length);\n\n        buffer.appendBytes(bytes);\n\n        if (category == DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer\n        {\n            buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));\n        }\n        else\n        {\n            var valueBytes = value.getBytes(StandardCharsets.UTF_8);\n\n            buffer.appendIntLE(valueBytes.length);\n\n            buffer.appendBytes(valueBytes);\n        }\n\n\n        builder.append(category).append(COLUMN_SEPARATOR).append(pluginId).append(COLUMN_SEPARATOR).append(column).append(COLUMN_SEPARATOR).append(correlatedMetric).append(COLUMN_SEPARATOR).append(eventCategory);\n\n        if (!mappers.contains(builder.toString()))\n        {\n            mappers.add(builder.toString());\n\n            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,\n                    new JsonObject()\n                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name())\n                            .put(MAPPER, builder.toString()));\n        }\n    }\n\n    //add default floating columns\n    public static void loadConfigs()\n    {\n        var file = new File(CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + FLOATING_COLUMNS);\n\n        if (file.exists())\n        {\n            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());\n\n            if (buffer != null && buffer.getBytes().length > 0)\n            {\n                floatingColumns.addAll(new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).fieldNames());\n            }\n        }\n    }\n\n    public static void notify(JsonObject context, int operationType, JsonArray ids)\n    {\n        var buffer = Buffer.buffer();\n\n        // introduce switch case if multiple operation types are there in future\n        if (operationType == OperationType.MODIFY_BACKUP_PROFILE.ordinal())\n        {\n            buffer.appendByte(OperationType.MODIFY_BACKUP_PROFILE.getName()).appendBytes(context.encode().getBytes());\n\n            for (var id : ids)\n            {\n                var item = RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(id));\n\n                if (item != null && item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))\n                {\n                    Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)\n                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)\n                            .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)\n                            .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))\n                            .put(EVENT_CONTEXT, buffer.getBytes()));\n                }\n            }\n        }\n    }\n\n    /**\n     * Replaces all reserved separators in the given value with a space character.\n     * This method is used to sanitize event data before inserting into the database\n     * to prevent conflicts with reserved separator characters.\n     *\n     * @param value The string value to sanitize\n     * @return The sanitized string with all reserved separators replaced by spaces\n     */\n    public static String replace(String value)\n    {\n        if (value == null) return null;\n\n        return value.replace(\"@#$\", EMPTY_VALUE).replace(\"###\", EMPTY_VALUE).replace(\"§\", EMPTY_VALUE).replace(\"$$\", EMPTY_VALUE).replace(\"$\", EMPTY_VALUE).replace(\"#@#\", EMPTY_VALUE);\n    }\n\n    public enum MetricWriterOrdinal\n    {\n        DATA_CATEGORY,\n\n        OBJECT_ID,\n\n        COLUMN,\n\n        INSTANCE,\n\n        VALUE,\n\n        STATUS\n    }\n\n    public enum EventWriterOrdinal\n    {\n        DATA_CATEGORY,\n\n        SOURCE,\n\n        COLUMN,\n\n        VALUE,\n\n        GROUP\n    }\n\n    public enum PluginId\n    {\n        CORRELATED_METRIC(490000),\n\n        FLOW_EVENT(500000),\n\n        TRAP_EVENT(500001),\n\n        AUDIT_EVENT(500002),\n\n        POLICY_EVENT(500004),\n\n        POLICY_FLOW(500005),\n\n        POLICY_RESULT(500007),\n\n        NOTIFICATION_EVENT(500008),\n\n        UNKNOWN_LOG_EVENT(500009),\n\n        POLICY_TRAP(500010),\n\n        LOG_EVENT_STAT(500011),\n\n        GENERIC_LOG_EVENT(499999),\n\n        LOG_EVENT(600000),\n\n        METRIC_POLICY_DURATION_METRIC(499998),\n\n        OBJECT_STATUS_DURATION_METRIC(0),\n\n        HEALTH_METRIC(500014),\n\n        CACHE_DATASTORE_EVENT(500015),\n\n        PENDING_FILES_DATASTORE_EVENT(500016),\n\n        QUERY_STATS_DATASTORE_EVENT(500017),\n\n        FLOW_EVENT_STAT(500018),\n\n        STATIC_METRIC(500019),\n\n        JVM_HEALTH_METRIC(500020),\n\n        JVM_GC_HEALTH_METRIC(500021),\n\n        RUNBOOK_WORKLOG(500022),\n\n        COMPLIANCE(500023),\n\n        NETROUTE_STATUS_DURATION_METRIC(500024),\n\n        NETROUTE_METRIC(500025),\n\n        NETROUTE_EVENT(500026),\n\n        POLICY_NETROUTE(500027),\n\n        NETROUTE_POLICY_DURATION_METRIC(500028),\n\n        SLO(500029),\n\n        SLO_INSTANCE(500030),\n\n        SLO_FLAP(500031),\n\n        SLO_INSTANCE_FLAP(500032);\n\n        private static final Map<Integer, PluginId> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(PluginId::getName, e -> e)));\n        private final int name;\n\n        PluginId(int name)\n        {\n            this.name = name;\n        }\n\n        public static PluginId valueOfName(int name)\n        {\n            return VALUES.get(name);\n        }\n\n        public int getName()\n        {\n            return name;\n        }\n    }\n\n    public enum DatastoreFormat\n    {\n        VERTICAL(0),\n        HORIZONTAL(1);\n\n        private static final Map<Integer, DatastoreFormat> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(DatastoreFormat::getName, e -> e)));\n        private final int name;\n\n        DatastoreFormat(Integer name)\n        {\n            this.name = name;\n        }\n\n        public static DatastoreFormat valueOfName(Integer name)\n        {\n            return VALUES.get(name);\n        }\n\n        public Integer getName()\n        {\n            return name;\n        }\n    }\n\n    public enum DatastoreCategory\n    {\n        METRIC(0),\n        EVENT(1),\n        AVAILABILITY(2);\n\n        private static final Map<Integer, DatastoreCategory> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(DatastoreCategory::getName, e -> e)));\n        private final int name;\n\n        DatastoreCategory(Integer name)\n        {\n            this.name = name;\n        }\n\n        public static DatastoreCategory valueOfName(Integer name)\n        {\n            return VALUES.get(name);\n        }\n\n        public Integer getName()\n        {\n            return name;\n        }\n    }\n\n    public enum DatastoreType\n    {\n        INDEX,\n\n        PERFORMANCE_METRIC,\n\n        OBJECT_STATUS_METRIC,\n\n        TRAP_ACKNOWLEDGEMENT,\n\n        METRIC_POLICY_ACKNOWLEDGEMENT,\n\n        EVENT_POLICY_ACKNOWLEDGEMENT,\n\n        AUDIT,\n\n        NOTIFICATION,\n\n        METRIC_POLICY_FLAP_HISTORY,\n\n        OBJECT_STATUS_FLAP_HISTORY,\n\n        TRAP_FLAP_HISTORY,\n\n        LOG,\n\n        FLOW,\n\n        TRAP,\n\n        METRIC_POLICY,\n\n        EVENT_POLICY,\n\n        CORRELATED_METRIC,\n\n        RUNBOOK_WORKLOG,\n\n        POLICY_RESULT,\n\n        CORRELATION_WORKLOG,\n\n        HEALTH_METRIC,\n\n        STATIC_METRIC,\n\n        COMPLIANCE,\n\n        TRACE_METRIC,\n\n        TRACE_EVENT,\n\n        NETROUTE_METRIC,\n\n        OBJECT_STATUS_FLAP_METRIC,\n\n        NETROUTE_STATUS_METRIC,\n\n        SLO_METRIC,\n\n        SLO_FLAP_METRIC\n    }\n\n    public enum OperationType\n    {\n        DATA_WRITE((byte) 0),\n\n        DATA_UPDATE((byte) 1),\n\n        DATA_READ((byte) 2),\n\n        QUERY_ABORT((byte) 3),\n\n        INDEX_UPDATE((byte) 5),\n\n        DATASTORE_INIT((byte) 6),\n\n        DATA_FLUSH((byte) 9),\n\n        WIDGET_CREATE((byte) 10),\n\n        TRAP_ACKNOWLEDGE((byte) 11),\n\n        METRIC_POLICY_ACKNOWLEDGE((byte) 12),\n\n        EVENT_POLICY_ACKNOWLEDGE((byte) 13),\n\n        STORE_RETENTION((byte) 14),\n\n        STORE_BACKUP((byte) 15),\n\n        WIDGET_DELETE((byte) 17),\n\n        CONNECTION_KEEP_ALIVE((byte) 18),\n\n        HEALTH_STATS((byte) 19),\n\n        MODIFY_LOG_LEVEL((byte) 20),\n\n        PROFILER((byte) 21),\n\n        ACKNOWLEDGE((byte) 22),\n\n        MODIFY_BACKUP_PROFILE((byte) 23),\n\n        START_SLO_CYCLE((byte) 25);\n\n        private static final Map<Byte, OperationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(OperationType::getName, e -> e)));\n        private final Byte name;\n\n        OperationType(Byte name)\n        {\n            this.name = name;\n        }\n\n        public static OperationType valueOfName(Byte name)\n        {\n            return VALUES.get(name);\n        }\n\n        public Byte getName()\n        {\n            return name;\n        }\n\n    }\n\n    public enum DataCategory\n    {\n        STRING((byte) 0),\n\n        NUMERIC((byte) 1),\n\n        FLOAT((byte) 2);\n\n        private static final Map<Byte, DataCategory> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(DataCategory::getName, e -> e)));\n        private final byte name;\n\n        DataCategory(byte name)\n        {\n            this.name = name;\n        }\n\n        public static DataCategory valueOfName(byte name)\n        {\n            return VALUES.get(name);\n        }\n\n        public byte getName()\n        {\n            return name;\n        }\n    }\n\n    public enum ConditionGroup\n    {\n        AND(\"and\"),\n\n        OR(\"or\"),\n\n        IN(\"in\"),\n\n        EQUAL(\"=\"),\n\n        WHERE(\"where\");\n\n        private static final Map<String, ConditionGroup> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(ConditionGroup::getName, e -> e)));\n        private final String name;\n\n        ConditionGroup(String name)\n        {\n            this.name = name;\n        }\n\n        public static ConditionGroup valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n\n    public enum DataType\n    {\n        INT32((byte) 48),\n\n        INT64((byte) 112),\n\n        FLOAT32((byte) 160),\n\n        STRING((byte) 176);\n\n        private static final Map<Byte, DataType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(DataType::getName, e -> e)));\n        private final byte name;\n\n        DataType(byte name)\n        {\n            this.name = name;\n        }\n\n        public static DataType valueOfName(byte name)\n        {\n            return VALUES.get(name);\n        }\n\n        public byte getName()\n        {\n            return name;\n        }\n\n        public short getSize()\n        {\n            return CommonUtil.getShort(getName());\n        }\n    }\n\n    public enum AggregationType\n    {\n        NO_AGGREGATION(\"none\"),\n\n        AVG(\"avg\"),\n\n        MIN(\"min\"),\n\n        MAX(\"max\"),\n\n        SUM(\"sum\"),\n\n        COUNT(\"count\"),\n\n        SPARKLINE(\"sparkline\"),\n\n        LAST(\"last\");\n\n        private static final Map<String, AggregationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())\n                .collect(Collectors.toMap(AggregationType::getName, e -> e)));\n        private final String name;\n\n        AggregationType(String name)\n        {\n            this.name = name;\n        }\n\n        public static AggregationType valueOfName(String name)\n        {\n            return VALUES.get(name);\n        }\n\n        public String getName()\n        {\n            return name;\n        }\n    }\n}\n", "baseTimestamp": 1754483546096}}}