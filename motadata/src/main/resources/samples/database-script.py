#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode

from motadatasdk.constants import Constant
from motadatasdk.databaseclient import DatabaseClient
from motadatasdk.logger import Logger
from motadatasdk.util import Util


class OracleSessionByCPUMetricPlugin:

    def __init__(self):

        super().__init__()

        self.errors = []

    def collect(self, context):

        result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

        database_client = None

        try:

            instance_name = context.get('database') if context.get('database') is not None and \
                                                               len(context.get('database')) > 0 else None

            database_client = DatabaseClient().get_connection(context, context.get(Constant.PORT), instance_name,
                                                              DatabaseClient.ORACLE_SERVER, None)

            if database_client.init():

                rows = database_client.execute_query(
                    "SELECT stat.sid,sess.sql_id, sess.LOGON_TIME,sess.status,sess.command, sess.machine, sess.username, "
                    "sess.program ,(SYSDATE-sess.logon_time)*86400 elapsed_time, stat.cpu, stat.memsorts, stat.tablescans, "
                    "stat.phyreads, stat.logreads, stat.disksorts, stat.blks_changed, stat.chained_rows, stat.commits, stat.cursors, "
                    "round((1-(stat.phyreads/DECODE(stat.logreads,0,NULL,stat.logreads)))*100) buffer_cache_hitrate "
                    "from (SELECT st.sid, SUM(DECODE(name, 'CPU used by this session', value, 0)) cpu, SUM(DECODE(name, 'sorts (disk)', value, 0)) disksorts,"
                    "SUM(DECODE(name, 'sorts (memory)', value, 0)) memsorts, SUM(DECODE(SUBSTR(name,0,11), 'table scans', value, 0)) tablescans,"
                    "SUM(DECODE(name, 'physical reads', value, 0)) phyreads, SUM(DECODE(name, 'session logical reads', value, 0)) logreads,"
                    "SUM(DECODE(name,'db block changes', value, 0)) blks_changed, SUM(DECODE(name, 'table fetch continued row', value, 0)) "
                    "chained_rows, SUM(DECODE(name, 'user commits', value, 0)) commits, SUM(DECODE(name, 'opened cursors current', value, 0)) cursors "
                    "FROM V$SESSTAT st, V$STATNAME sn WHERE st.statistic# = sn.statistic# GROUP BY st.sid) stat, v$session sess where stat.sid = "
                    "sess.sid and ROWNUM <= $$$count$$$ ORDER BY stat.cpu DESC")

                self._set_session_metrics(database_client, rows, result)

        except Exception as exception:

            self.errors.append({Constant.MESSAGE: str(exception), Constant.ERROR: Logger.get_stack_trace(),
                           Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        finally:

            if database_client is not None:
                database_client.destroy()

            if len(database_client.get_errors()) > 0:
                self.errors = self.errors + database_client.get_errors()

            if len(self.errors) > 0:
                result[Constant.ERRORS] = self.errors

        return result

    def _set_session_metrics(self, database_client, rows, result):

        try:

            if rows is not None and len(rows) > 0:

                commands = self._get_commands(database_client)

                queries = self._get_queries(database_client)

                sessions = []

                for row in rows:

                    if row is not None:

                        session = {}

                        if row.get("SID") is not None:
                            session["oracle.session.id"] = Util.convert_to_int(row.get("SID"))

                        if row.get("COMMAND") is not None and commands is not None and \
                                len(Util.convert_to_string(row.get("COMMAND"))) > 0 and row.get("COMMAND") in commands:
                            session['oracle.session.command'] = Util.convert_to_int(row.get("COMMAND"))

                        if row.get("STATUS") is not None:
                            session['oracle.session.status'] = row.get("STATUS")

                        if row.get("MACHINE") is not None:
                            session['oracle.session.remote.client'] = row.get("MACHINE")

                        if row.get("USERNAME") is not None:
                            session['oracle.session.user'] = row.get("USERNAME")

                        if row.get("SQL_ID") is not None and queries is not None and \
                                Util.convert_to_string(row.get("SQL_ID")) in queries:
                            session['oracle.session.query'] = queries.get(Util.convert_to_string(row.get("SQL_ID")))

                        if row.get("PROGRAM") is not None:
                            session['oracle.session.application'] = row.get("PROGRAM")

                        if row.get("ELAPSED_TIME") is not None and \
                                Util.convert_to_int(row.get("ELAPSED_TIME")) >= 0:
                            session['oracle.session.duration.sec'] = Util.convert_to_int(row.get("ELAPSED_TIME"))

                            session['oracle.session.duration'] = Util.convert_seconds_to_uptime(
                                Util.convert_to_int(row.get("ELAPSED_TIME")))

                        if row.get("LOGON_TIME") is not None:
                            session['oracle.session.logon.time'] = Util.convert_to_string(row.get("LOGON_TIME"))

                        if row.get("CPU") is not None:
                            session['oracle.session.cpu.time.ms'] = Util.convert_to_int(row.get("CPU")) * 10

                        if row.get("MEMSORTS") is not None:
                            session['oracle.session.memory.sorts'] = Util.convert_to_int(row.get("MEMSORTS"))

                        if row.get("DISKSORTS") is not None:
                            session['oracle.session.disk.sorts'] = Util.convert_to_int(row.get("DISKSORTS"))

                        if row.get("TABLESCANS") is not None:
                            session['oracle.session.table.sorts'] = Util.convert_to_int(row.get("TABLESCANS"))

                        if row.get("PHYREADS") is not None:
                            session['oracle.session.physical.reads'] = Util.convert_to_int(row.get("PHYREADS"))

                        if row.get("LOGREADS") is not None:
                            session['oracle.session.logical.reads'] = Util.convert_to_int(row.get("LOGREADS"))

                        if row.get("COMMITS") is not None:
                            session['oracle.session.commits'] = Util.convert_to_int(row.get("COMMITS"))

                        if row.get("CURSORS") is not None:
                            session['oracle.session.cursors'] = Util.convert_to_int(row.get("CURSORS"))

                        sessions.append(session)

                    if len(sessions) > 0:
                        result[Constant.RESULT]["oracle.session"] = sessions

        except Exception as exception:

            self.errors.append({Constant.ERROR: str(Logger.get_stack_trace()), Constant.MESSAGE: str(exception),
                           Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

    def _get_commands(self, database_client):

        commands = {}

        try:

            command_type = 0

            rows = database_client.execute_query("SELECT * FROM v$sqlcommand where command_type >0")

            if rows is not None:

                for row in rows:

                    if row.get("COMMAND_TYPE") is not None:
                        command_type = Util.convert_to_int(row.get("COMMAND_TYPE"))

                    if row.get("COMMAND_NAME") is not None:
                        commands[command_type] = Util.convert_to_string(row.get("COMMAND_NAME"))

        except Exception as exception:

            self.errors.append({Constant.ERROR: str(Logger.get_stack_trace()), Constant.MESSAGE: str(exception),
                           Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        return commands

    def _get_queries(self, database_client):

        queries = {}

        try:

            rows = database_client.execute_query(
                "select a.sql_text,sess.sql_id,a.sql_id from v$session sess,v$sqlarea a"
                " where a.sql_id=sess.sql_id")

            sql_address = Constant.BLANK_STRING

            if rows is not None:

                for row in rows:

                    if row.get("SQL_ID") is not None:
                        sql_address = Util.convert_to_string(row.get("SQL_ID"))

                    if row.get("SQL_TEXT") is not None:
                        queries[sql_address] = Util.convert_to_string(row.get("SQL_TEXT"))

        except Exception as exception:

            self.errors.append({Constant.ERROR: str(Logger.get_stack_trace()), Constant.MESSAGE: str(exception),
                           Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        return queries


if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    print(b64encode(json.dumps(OracleSessionByCPUMetricPlugin().collect(Util.load_plugin_context(str(args.context))).decode(), end=Constant.BLANK_STRING)
