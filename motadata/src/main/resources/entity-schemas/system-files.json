{"entity": "System File", "table": "tbl_config_system_file", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "system.file", "title": "File/Directory", "type": "string", "rules": ["required"]}, {"name": "system.file.type", "title": "Type", "type": "string", "rules": ["required"], "values": ["File", "Directory"]}, {"name": "system.file.os", "title": "OS", "type": "string", "rules": ["required"], "values": ["Windows", "Linux", "IBM AIX", "HP-UX", "Solar<PERSON>"]}], "entries": [{"type": "inline", "records": [{"id": 10000000000002, "system.file": "/var/log", "system.file.type": "Directory", "system.file.os": "Linux"}, {"id": 1000000000003, "system.file": "/var/log/syslog", "system.file.type": "File", "system.file.os": "Linux"}], "version": "1.0"}]}