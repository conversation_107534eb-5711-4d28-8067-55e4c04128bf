{"************": {"discovery.device.timeout": 20, "errors": [{"error": "unknown error Post \"http://************:5985/wsman\": dial tcp ************:5985: connect: network is unreachable", "error.code": "MD002", "message": "Invalid port 5985, Please verify that port 5985 is up and Mo<PERSON><PERSON> is able to connect"}], "object.ip": "************", "objects": [{"object.name": "C:\\Users\\<USER>\\Documents\\dhvani kakadiya"}], "password": "Mind@123", "port": 5985, "status": "fail", "username": "dhvani"}}