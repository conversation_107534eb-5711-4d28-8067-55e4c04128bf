{"172.16.8.86": {"result": {"started.time.sec": 9838438.0, "started.time": "113.0 days, 20.0 hours, 53.0 minutes, 58.0 seconds", "exchange.client.access.role.rpc.client.connections": 0.0, "exchange.client.access.role.ping.pending.commands": 0.0, "exchange.client.access.role.sync.pending.commands": 0.0, "exchange.client.access.role.sync.active.requests": 0.0, "exchange.client.access.role.availability.requests": 0.0, "exchange.client.access.role.sync.requests.per.sec": 0.0, "exchange.client.access.role.nspi.rpc.browse.request.latency.ms": 0.0, "exchange.client.access.role.nspi.rpc.request.latency.ms": 0.0, "exchange.client.access.role.referral.rpc.request.latency.ms": 0.0, "exchange.client.access.role.control.panel.outbound.proxy.request.latency.ms": 0.0, "exchange.client.access.role.control.panel.request.latency.ms": 0.0, "exchange.client.access.role.owa.unique.users": 0.0, "exchange.client.access.role.owa.requests.per.sec": 0.0, "exchange.client.access.role.web.connection.attempts.per.sec": 0.0, "exchange.client.access.role.auto.discovery.requests.per.sec": 0.0, "exchange.client.access.role.webservice.requests.per.sec": 0.0, "exchange.client.access.role.web.connections": 0.0, "exchange.client.access.role.availability.free.or.busy.request.time.ms": 0.0, "exchange.client.access.role.owa.search.time.ms": 0.0, "exchange.client.access.role.rpc.operations.per.sec": 0.0, "exchange.client.access.role.rpc.users": 0.0, "exchange.client.access.role.rpc.active.users": 0.0, "exchange.client.access.role.rpc.requests": 0.0, "exchange.client.access.role.rpc.latency.ms": 0.0}, "errors": [{"message": "failed to execute command [powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);(get-counter '\\System\\System Up Time','\\MSExchange RpcClientAccess\\Connection Count','\\ASP.NET Applications(__total__)\\Requests In Application Queue','\\MSExchange ActiveSync\\Ping Commands Pending', '\\MSExchange ActiveSync\\Sync Commands Pending', '\\MSExchange ActiveSync\\Current Requests', '\\MSExchange Availability Service\\Availability Requests (sec)', '\\MSExchange ActiveSync\\Requests/sec', '\\MSExchangeAB\\NSPI RPC Browse Requests Average Latency','\\MSExchangeAB\\NSPI RPC Requests Average Latency', '\\MSExchangeAB\\Referral RPC Requests Average Latency','\\MSExchange Control Panel\\Outbound Proxy Requests - Average Response Time', '\\MSExchange Control Panel\\Requests - Average Response Time', '\\MSExchange OWA\\Current Unique Users', '\\MSExchange OWA\\Requests/sec', '\\Web Service(_Total)\\Connection Attempts/sec', '\\MSExchangeAutodiscover\\Requests/sec', '\\MSExchangeWS\\Requests/sec', '\\Web Service(_Total)\\Current Connections', '\\ASP.NET\\Request Wait Time', '\\MSExchange Availability Service\\Average Time to Process a Free Busy Request', '\\ASP.NET\\Application Restarts', '\\ASP.NET\\Worker Process Restarts', '\\MSExchange OWA\\Average Search Time', '\\MSExchange RpcClientAccess\\RPC Operations/sec','\\MSExchange RpcClientAccess\\User Count',  '\\MSExchange RpcClientAccess\\Active User Count','\\MSExchange RpcClientAccess\\RPC Requests','\\MSExchange RpcClientAccess\\RPC Averaged Latency' -ErrorAction SilentlyContinue).countersamples | Format-List -Property Path,Cookedvalue;$password = convertto-securestring -AsPlainText -Force -String 'Mind#@123';$credential = new-object -typename System.Management.Automation.PSCredential -argumentlist 'Administrator',$password;$session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://WIN-RC9AF0C6565.afleck.local/PowerShell/ -Authentication Kerberos -credential $credential ;Import-PSSession $Session | Out-Null;Add-PSSnapin Microsoft.Exchange.Management.PowerShell.SnapIn;Add-PSSnapin Microsoft.Exchange.Management.PowerShell.E2010;Get-ExchangeServer | Select AdminDisplayVersion | fl ;Remove-PSSession -Session $session | Format-List\"] on target http://WIN-RC9AF0C6565.afleck.local:5985/wsman", "error.code": "MD059", "error": "New-PSSession : [win-rc9af0c6565.afleck.local] Connecting to remote server win-rc9af0c6565.afleck.local failed with the following error message : The WS-Management service cannot process the request. The load quota for the system has been exceeded. Send future requests at a slower rate or raise the system quota. For more information, see the about_Remote_Troubleshooting Help topic.\r\nAt line:1 char:1750\r\n+ $Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50); ...\r\n+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n    + CategoryInfo          : OpenError: (System.Manageme....RemoteRunspace:RemoteRunspace) [New-PSSession], PSRemotingTransportException\r\n    + FullyQualifiedErrorId : -2144108120,PSSessionOpenFailed\r\nImport-PSSession : Cannot validate argument on parameter 'Session'. The argument is null. Provide a valid value for the argument, and then try running the command again.\r\nAt line:1 char:1932\r\n+ ... port-PSSession $Session | Out-Null;Add-PSSnapin Microsoft.Exchange.Management.Po ...\r\n+                    ~~~~~~~~\r\n    + CategoryInfo          : InvalidData: (:) [Import-PSSession], ParameterBindingValidationException\r\n    + FullyQualifiedErrorId : ParameterArgumentValidationError,Microsoft.PowerShell.Commands.ImportPSSessionCommand\r\n \r\nAdd-PSSnapin : The Windows PowerShell snap-in 'Microsoft.Exchange.Management.PowerShell.SnapIn' is not installed on this computer.\r\nAt line:1 char:1952\r\n+ ... ion | Out-Null;Add-PSSnapin Microsoft.Exchange.Management.PowerShell.SnapIn;Add- ...\r\n+                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n    + CategoryInfo          : InvalidArgument: (Microsoft.Excha...werShell.SnapIn:String) [Add-PSSnapin], PSArgumentException\r\n    + FullyQualifiedErrorId : AddPSSnapInRead,Microsoft.PowerShell.Commands.AddPSSnapinCommand\r\n \r\nGet-ExchangeServer : Value cannot be null.\r\nParameter name: serverSettings\r\nAt line:1 char:2073\r\n+ ... werShell.E2010;Get-ExchangeServer | Select AdminDisplayVersion | fl ;Remove-PSSe ...\r\n+                    ~~~~~~~~~~~~~~~~~~\r\n    + CategoryInfo          : NotSpecified: (:) [Get-ExchangeServer], ArgumentNullException\r\n    + FullyQualifiedErrorId : System.ArgumentNullException,Microsoft.Exchange.Management.SystemConfigurationTasks.GetExchangeServer\r\n \r\nRemove-PSSession : Cannot bind argument to parameter 'Session' because it is null.\r\nAt line:1 char:2153\r\n+ ... ssion -Session $session | Format-List\r\n+                    ~~~~~~~~\r\n    + CategoryInfo          : InvalidData: (:) [Remove-PSSession], ParameterBindingValidationException\r\n    + FullyQualifiedErrorId : ParameterArgumentValidationErrorNullNotAllowed,Microsoft.PowerShell.Commands.RemovePSSessionCommand\r\n \r\n"}]}, "WIN-RC9AF0C6565.web8.local": {"result": {"started.time.sec": 9844382.0, "started.time": "113.0 days, 22.0 hours, 33.0 minutes, 2.0 seconds", "exchange.client.access.role.rpc.client.connections": 0.0, "exchange.client.access.role.queued.requests": 0.0, "exchange.client.access.role.ping.pending.commands": 0.0, "exchange.client.access.role.sync.pending.commands": 0.0, "exchange.client.access.role.sync.active.requests": 0.0, "exchange.client.access.role.availability.requests": 0.0, "exchange.client.access.role.sync.requests.per.sec": 0.0, "exchange.client.access.role.nspi.rpc.browse.request.latency.ms": 0.0, "exchange.client.access.role.nspi.rpc.request.latency.ms": 0.0, "exchange.client.access.role.referral.rpc.request.latency.ms": 0.0, "exchange.client.access.role.control.panel.outbound.proxy.request.latency.ms": 0.0, "exchange.client.access.role.control.panel.request.latency.ms": 0.0, "exchange.client.access.role.owa.unique.users": 0.0, "exchange.client.access.role.owa.requests.per.sec": 0.0, "exchange.client.access.role.web.connection.attempts.per.sec": 0.0, "exchange.client.access.role.auto.discovery.requests.per.sec": 0.0, "exchange.client.access.role.webservice.requests.per.sec": 0.0, "exchange.client.access.role.web.connections": 0.0, "exchange.client.access.role.request.wait.time.ms": 0.0, "exchange.client.access.role.availability.free.or.busy.request.time.ms": 0.0, "exchange.client.access.role.application.restarts": 0.0, "exchange.client.access.role.worker.process.restarts": 0.0, "exchange.client.access.role.owa.search.time.ms": 0.0, "exchange.client.access.role.rpc.operations.per.sec": 0.0, "exchange.client.access.role.rpc.users": 0.0, "exchange.client.access.role.rpc.active.users": 0.0, "exchange.client.access.role.rpc.requests": 0.0, "exchange.client.access.role.rpc.latency.ms": 0.0}, "errors": [{"message": "failed to execute command [powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);(get-counter '\\System\\System Up Time','\\MSExchange RpcClientAccess\\Connection Count','\\ASP.NET Applications(__total__)\\Requests In Application Queue','\\MSExchange ActiveSync\\Ping Commands Pending', '\\MSExchange ActiveSync\\Sync Commands Pending', '\\MSExchange ActiveSync\\Current Requests', '\\MSExchange Availability Service\\Availability Requests (sec)', '\\MSExchange ActiveSync\\Requests/sec', '\\MSExchangeAB\\NSPI RPC Browse Requests Average Latency','\\MSExchangeAB\\NSPI RPC Requests Average Latency', '\\MSExchangeAB\\Referral RPC Requests Average Latency','\\MSExchange Control Panel\\Outbound Proxy Requests - Average Response Time', '\\MSExchange Control Panel\\Requests - Average Response Time', '\\MSExchange OWA\\Current Unique Users', '\\MSExchange OWA\\Requests/sec', '\\Web Service(_Total)\\Connection Attempts/sec', '\\MSExchangeAutodiscover\\Requests/sec', '\\MSExchangeWS\\Requests/sec', '\\Web Service(_Total)\\Current Connections', '\\ASP.NET\\Request Wait Time', '\\MSExchange Availability Service\\Average Time to Process a Free Busy Request', '\\ASP.NET\\Application Restarts', '\\ASP.NET\\Worker Process Restarts', '\\MSExchange OWA\\Average Search Time', '\\MSExchange RpcClientAccess\\RPC Operations/sec','\\MSExchange RpcClientAccess\\User Count',  '\\MSExchange RpcClientAccess\\Active User Count','\\MSExchange RpcClientAccess\\RPC Requests','\\MSExchange RpcClientAccess\\RPC Averaged Latency' -ErrorAction SilentlyContinue).countersamples | Format-List -Property Path,Cookedvalue;$password = convertto-securestring -AsPlainText -Force -String 'Mind#@123';$credential = new-object -typename System.Management.Automation.PSCredential -argumentlist 'Administrator',$password;$session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://WIN-RC9AF0C6565.web8.local/PowerShell/ -Authentication Kerberos -credential $credential ;Import-PSSession $Session | Out-Null;Add-PSSnapin Microsoft.Exchange.Management.PowerShell.SnapIn;Add-PSSnapin Microsoft.Exchange.Management.PowerShell.E2010;Get-ExchangeServer | Select AdminDisplayVersion | fl ;Remove-PSSession -Session $session | Format-List\"] on target http://WIN-RC9AF0C6565.web8.local:5985/wsman", "error.code": "MD059", "error": "New-PSSession : [win-rc9af0c6565.web8.local] Connecting to remote server win-rc9af0c6565.web8.local failed with the following error message : The WS-Management service cannot process the request. The load quota for the system has been exceeded. Send future requests at a slower rate or raise the system quota. For more information, see the about_Remote_Troubleshooting Help topic.\r\nAt line:1 char:1750\r\n+ $Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50); ...\r\n+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n    + CategoryInfo          : OpenError: (System.Manageme....RemoteRunspace:RemoteRunspace) [New-PSSession], PSRemotingTransportException\r\n    + FullyQualifiedErrorId : -2144108120,PSSessionOpenFailed\r\nImport-PSSession : Cannot validate argument on parameter 'Session'. The argument is null. Provide a valid value for the argument, and then try running the command again.\r\nAt line:1 char:1930\r\n+ ... port-PSSession $Session | Out-Null;Add-PSSnapin Microsoft.Exchange.Management.Po ...\r\n+                    ~~~~~~~~\r\n    + CategoryInfo          : InvalidData: (:) [Import-PSSession], ParameterBindingValidationException\r\n    + FullyQualifiedErrorId : ParameterArgumentValidationError,Microsoft.PowerShell.Commands.ImportPSSessionCommand\r\n \r\nActive Directory operation failed on . The supplied credential for 'WEB8\\Administrator' is invalid.\r\nAt line:1 char:2071\r\n+ ... werShell.E2010;Get-ExchangeServer | Select AdminDisplayVersion | fl ;Remove-PSSe ...\r\n+                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n    + CategoryInfo          : NotSpecified: (:) [], ADInvalidCredentialException\r\n    + FullyQualifiedErrorId : [Server=WIN-RC9AF0C6565,RequestId=56ab8da1-e8c0-4a88-8400-c3a398f443a0,TimeStamp=6/8/2022 11:18:52 AM] [FailureCategory=Cmdlet-ADInvalidCredentialException] ********\r\n \r\nRemove-PSSession : Cannot bind argument to parameter 'Session' because it is null.\r\nAt line:1 char:2151\r\n+ ... ssion -Session $session | Format-List\r\n+                    ~~~~~~~~\r\n    + CategoryInfo          : InvalidData: (:) [Remove-PSSession], ParameterBindingValidationException\r\n    + FullyQualifiedErrorId : ParameterArgumentValidationErrorNullNotAllowed,Microsoft.PowerShell.Commands.RemovePSSessionCommand\r\n \r\n"}]}}