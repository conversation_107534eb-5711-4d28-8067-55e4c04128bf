/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.util.PortUtil;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

import static com.mindarray.GlobalConstants.HOST;
import static com.mindarray.GlobalConstants.PORT;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TYPE;
import static com.mindarray.eventbus.EventBusConstants.SOCKET_MODE;

/**
 * A decorator for TransportClient that provides automatic reconnection capabilities.
 *
 * <p>This class implements the TransportClient interface and wraps another TransportClient
 * implementation, adding automatic reconnection functionality. It periodically checks
 * the connection status and attempts to reconnect if the connection has been idle for
 * longer than the configured timeout period.</p>
 *
 * <p>Key features:</p>
 * <ul>
 *   <li>Transparent delegation to the underlying transport client</li>
 *   <li>Periodic connection health checks</li>
 *   <li>Automatic reconnection when connection is lost</li>
 *   <li>Connection timeout based on message activity</li>
 *   <li>Port availability verification before reconnection attempts</li>
 * </ul>
 *
 * <p>The reconnection logic works by:</p>
 * <ol>
 *   <li>Tracking the timestamp of the last received message</li>
 *   <li>Periodically checking if the time since the last message exceeds the timeout</li>
 *   <li>Verifying that the remote endpoint is reachable</li>
 *   <li>Closing the existing connection and establishing a new one</li>
 * </ol>
 *
 * <p>Example usage:</p>
 * <pre>{@code
 * // Create a base transport client
 * TransportClient baseClient = new ZMQClient(config);
 *
 * // Wrap it with auto-reconnect functionality
 * TransportClient client = new AutoReconnectClient(baseClient, config);
 *
 * // Use the client normally - reconnection is handled automatically
 * client.connect();
 * client.registerMessageHandler(message -> {
 *     // Process message
 * });
 * client.receive();
 * }</pre>
 *
 * <p>Alternatively, use the TransportProvider factory method:</p>
 * <pre>{@code
 * // Create a client with auto-reconnect enabled
 * JsonObject config = new JsonObject()
 *     .put(TRANSPORT_PROTOCOL, TransportProtocol.ZMQ.getValue())
 *     .put(AUTO_RECONNECT, true)
 *     .put("host", "localhost")
 *     .put("port", 5555);
 * TransportClient client = TransportProvider.getClient(config);
 * }</pre>
 *
 * <AUTHOR> Engineering Team
 * @version 1.0
 * @since 1.0
 * @see TransportClient
 * @see TransportProvider
 * @see ZMQClient
 */
public class TransportReconnectClient implements TransportClient
{
    /**
     * The underlying transport client that this class decorates.
     * All transport operations are delegated to this client.
     */
    private final TransportClient client;

    /**
     * Configuration object containing connection parameters such as host, port, and event type.
     * This configuration is used for reconnection attempts.
     */
    private final JsonObject config;

    /**
     * Atomic timestamp tracking the last time a message was received.
     * Used to determine if the connection has been idle for too long.
     */
    private final AtomicLong timestamp = new AtomicLong(DateTimeUtil.currentSeconds());

    /**
     * The timeout period in seconds after which a reconnection attempt will be made
     * if no messages have been received.
     */
    private static final long RECONNECT_TIMEOUT_SECONDS = TimeUnit.MINUTES.toSeconds(MotadataConfigUtil.getRemoteSessionTimeoutMinutes());

    /**
     * The interval in milliseconds at which the connection health is checked.
     */
    private static final long REMOTE_SESSION_PROBE_MILLIS = TimeUnit.MINUTES.toMillis(MotadataConfigUtil.getRemoteSessionProbeMinutes());

    /**
     * Timer ID for the periodic connection check.
     * Stored to allow cancellation when the client is closed.
     */
    private long timer = -1;

    /**
     * Logger instance for this class.
     */
    private static final Logger LOGGER = new Logger(TransportReconnectClient.class, GlobalConstants.MOTADATA_EVENT_BUS, "TransportReconnect Client");

    /**
     * The event type from the configuration, used for routing received messages.
     */
    private final String eventType;

    private final String EVENT_RECONNECT_TRIGGER = "event.reconnect.trigger";

    /**
     * Constructs a new AutoReconnectClient that wraps the specified transport client.
     *
     * <p>This constructor creates an AutoReconnectClient that delegates all transport
     * operations to the provided client while adding automatic reconnection capabilities.
     * The configuration object is used to extract connection parameters for reconnection
     * attempts and message routing.</p>
     *
     * <p>Required configuration parameters:</p>
     * <ul>
     *   <li>{@link GlobalConstants#HOST} - The remote host address</li>
     *   <li>{@link GlobalConstants#PORT} - The remote port number</li>
     *   <li>{@link EventBusConstants#EVENT_TYPE} - The event type for message routing</li>
     * </ul>
     *
     * <p>The reconnection behavior is controlled by system-wide configuration:</p>
     * <ul>
     *   <li>{@link MotadataConfigUtil#getRemoteSessionTimeoutMinutes()} - Timeout period before reconnection</li>
     *   <li>{@link MotadataConfigUtil#getRemoteSessionProbeMinutes()} - Interval for connection checks</li>
     * </ul>
     *
     * @param client the underlying transport client to delegate operations to
     * @param config the configuration object containing connection parameters
     * @throws NullPointerException if client or config is null
     * @throws IllegalArgumentException if required configuration parameters are missing
     */
    public TransportReconnectClient(TransportClient client, JsonObject config)
    {
        this.client = client;

        this.config = config;

        eventType = config.getString(EVENT_TYPE);
    }

    /**
     * Connects to the remote endpoint and starts the connection health check timer.
     *
     * <p>This method delegates the connection operation to the underlying transport client
     * and, upon successful connection, starts a periodic timer to check the connection
     * health. If the connection has been idle for longer than the configured timeout,
     * a reconnection attempt will be made.</p>
     *
     * <p>The connection process:</p>
     * <ol>
     *   <li>Registers the message handler to track message activity</li>
     *   <li>Delegates the connection operation to the underlying client</li>
     *   <li>Upon successful connection, starts the connection health check timer</li>
     * </ol>
     *
     * <p>The connection health check:</p>
     * <ul>
     *   <li>Runs at the interval specified by {@link #REMOTE_SESSION_PROBE_MILLIS}</li>
     *   <li>Checks if the time since the last message exceeds {@link #RECONNECT_TIMEOUT_SECONDS}</li>
     *   <li>If the timeout is exceeded, verifies that the remote endpoint is reachable</li>
     *   <li>If reachable, initiates a reconnection attempt</li>
     * </ul>
     *
     * @return a Future that completes when the connection is established,
     *         or fails with an exception if the connection cannot be established
     * @see TransportClient#connect()
     * @see #checkConnection()
     * @see #reconnect()
     */
    @Override
    public Future<Void> connect()
    {
        registerMessageHandler(this::processMessage);

        return client.connect().onComplete(handler ->
        {
            if (handler.succeeded())
            {
                checkConnection();
            }
            else
            {
                LOGGER.error(handler.cause());
            }
        });
    }

    /**
     * Handles received messages and updates the activity timestamp.
     *
     * <p>This method is called for each message received from the underlying transport client.
     * It updates the activity timestamp to indicate that the connection is active, and forwards
     * the message to the appropriate event bus address based on the configured event type.</p>
     *
     * <p>The method performs two main functions:</p>
     * <ol>
     *   <li>Updates the activity timestamp to prevent unnecessary reconnection attempts</li>
     *   <li>Forwards the received message to the Vert.x event bus for processing</li>
     * </ol>
     *
     * <p>This method is registered as the message handler for the underlying client
     * in the {@link #connect()} method.</p>
     *
     * @param message the received message as a byte array
     * @see #connect()
     * @see #registerMessageHandler(Consumer)
     */
    private void processMessage(byte[] message)
    {
        timestamp.set(DateTimeUtil.currentSeconds());

        Bootstrap.vertx().eventBus().send(eventType, message);
    }

    /**
     * Sets up a periodic timer to check the connection health.
     *
     * <p>This method cancels any existing timer and sets up a new periodic timer
     * to check the connection health. The timer runs at the interval specified by
     * {@link #REMOTE_SESSION_PROBE_MILLIS} and checks if the time since the last
     * received message exceeds the timeout period.</p>
     *
     * <p>If the timeout is exceeded, the method:</p>
     * <ol>
     *   <li>Logs that the threshold has been breached</li>
     *   <li>Verifies that the remote endpoint is reachable using {@link PortUtil#isConnected}</li>
     *   <li>If reachable, initiates a reconnection attempt</li>
     *   <li>If not reachable, logs a warning and continues monitoring</li>
     * </ol>
     *
     * <p>This method is called automatically after a successful connection in the
     * {@link #connect()} method.</p>
     *
     * @see #connect()
     * @see #reconnect()
     * @see PortUtil#isConnected(String, int)
     */
    private void checkConnection()
    {
        if (EventBusConstants.SocketMode.valueOf(config.getString(SOCKET_MODE)) != EventBusConstants.SocketMode.PUSH)
        {
            Bootstrap.vertx().cancelTimer(timer);

            LOGGER.info(String.format("%s checking connection to address : %s : %s",Bootstrap.bootstrapType(),config.getString(HOST),config.getString(PORT)));

            timer = Bootstrap.vertx().setPeriodic(REMOTE_SESSION_PROBE_MILLIS, id ->
            {
                if (DateTimeUtil.currentSeconds() - timestamp.get() > RECONNECT_TIMEOUT_SECONDS)
                {
                    LOGGER.info(String.format("%s threshold breach for address : %s : %s: . time difference : %s ",Bootstrap.bootstrapType(),config.getString(HOST),config.getString(PORT),DateTimeUtil.currentSeconds() - timestamp.get()));

                    if (PortUtil.isConnected(config.getString(HOST), config.getInteger(PORT)))
                    {
                        LOGGER.info(String.format("from %s , APP server is up for port : %s , hence reconnecting ....", Bootstrap.bootstrapType(),config.getInteger(PORT)));

                        Bootstrap.vertx().eventBus().send(EVENT_RECONNECT_TRIGGER,true);

                        reconnect();
                    }
                    else
                    {
                        LOGGER.warn(String.format(String.format(" server : %s is not reachable ",config.getString(HOST) + ":" +config.getString(PORT))));
                    }
                }
            });
        }
        else
        {
            Bootstrap.vertx().eventBus().localConsumer(EVENT_RECONNECT_TRIGGER, message -> reconnect());
        }
    }

    /**
     * Closes the existing connection and establishes a new one.
     *
     * <p>This method performs a reconnection by:</p>
     * <ol>
     *   <li>Closing the existing connection</li>
     *   <li>Establishing a new connection</li>
     * </ol>
     *
     * <p>The reconnection is performed by calling the {@link #close()} method
     * to release resources associated with the existing connection, and then
     * calling the {@link #connect()} method to establish a new connection.</p>
     *
     * <p>This method is called automatically when the connection health check
     * determines that a reconnection is necessary.</p>
     *
     * @see #checkConnection()
     * @see #close()
     * @see #connect()
     */
    private void reconnect()
    {
        LOGGER.info(String.format("%s port : %s is reconnecting ... ",Bootstrap.bootstrapType(),config.getInteger(PORT)));

        client.close().onComplete(handler ->
        {
            if (handler.succeeded())
            {
                client.connect().onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("%s  type : %s port : %s reconnect successfully ...", Bootstrap.bootstrapType(),config.getString(SOCKET_MODE),config.getInteger(PORT)));

                        client.receive();
                    }
                    else
                    {
                        LOGGER.error(result.cause());
                    }
                });
            }
            else
            {
                LOGGER.error(handler.cause());
            }
        });
    }

    /**
     * Sends a message through the underlying transport client.
     *
     * <p>This method delegates the send operation to the underlying transport client.
     * It is a direct pass-through with no additional processing.</p>
     *
     * @param data the message to send as a byte array
     * @see TransportClient#send(byte[])
     */
    @Override
    public void send(byte[] data)
    {
        client.send(data);
    }

    /**
     * Starts receiving messages from the underlying transport client.
     *
     * <p>This method delegates the receive operation to the underlying transport client.
     * It is a direct pass-through with no additional processing.</p>
     *
     * @return a Future that completes when message reception is started,
     *         or fails with an exception if reception cannot be started
     * @see TransportClient#receive()
     */
    @Override
    public Future<Void> receive()
    {
        return client.receive();
    }

    /**
     * Registers a message handler with the underlying transport client.
     *
     * <p>This method delegates the handler registration to the underlying transport client.
     * The registered handler will be called for each message received from the transport.</p>
     *
     * <p>Note that the {@link #connect()} method automatically registers an internal
     * handler that updates the activity timestamp and forwards messages to the event bus.
     * If you register your own handler, it will replace this internal handler.</p>
     *
     * @param consumer the message handler to register
     * @see TransportClient#registerMessageHandler(Consumer)
     * @see #processMessage(byte[])
     */
    @Override
    public void registerMessageHandler(Consumer<byte[]> consumer)
    {
        client.registerMessageHandler(consumer);
    }

    /**
     * Closes the connection and releases all resources.
     *
     * <p>This method performs a clean shutdown by:</p>
     * <ol>
     *   <li>Canceling the connection health check timer</li>
     *   <li>Closing the underlying transport client</li>
     * </ol>
     *
     * <p>After calling this method, the client cannot be used until {@link #connect()}
     * is called again to establish a new connection.</p>
     *
     * @see TransportClient#close()
     * @see #connect()
     */
    @Override
    public Future<Void> close()
    {
        Bootstrap.vertx().cancelTimer(timer);

        return client.close();
    }
}