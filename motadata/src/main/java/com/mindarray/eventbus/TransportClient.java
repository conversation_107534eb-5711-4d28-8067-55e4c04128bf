/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import io.vertx.core.Future;

import java.util.function.Consumer;

/**
 * Transport client interface for the Motadata event bus system.
 *
 * <p>This interface defines the contract for all transport client implementations
 * in the Motadata event bus architecture. It provides a standardized abstraction
 * layer that allows different transport protocols (ZMQ, TCP, UDP) to be used
 * interchangeably without affecting the application logic.</p>
 *
 * <p>The interface supports both synchronous and asynchronous operations:</p>
 * <ul>
 *   <li><b>Connection Management</b>: Asynchronous connection establishment and cleanup</li>
 *   <li><b>Message Sending</b>: Synchronous message transmission</li>
 *   <li><b>Message Receiving</b>: Asynchronous message reception with custom handlers</li>
 *   <li><b>Resource Management</b>: Proper cleanup and resource release</li>
 * </ul>
 *
 * <p>Typical usage pattern:</p>
 * <pre>{@code
 * // Create and configure transport client
 * TransportClient client = TransportProvider.getClient(config);
 *
 * // Register message handler
 * client.registerMessageHandler(message -> {
 *     // Process received message
 *     processMessage(message);
 * });
 *
 * // Connect to remote endpoint
 * client.connect().onSuccess(v -> {
 *     // Start receiving messages
 *     client.receive();
 *
 *     // Send messages
 *     client.send("Hello World".getBytes());
 * }).onFailure(error -> {
 *     // Handle connection failure
 *     handleError(error);
 * });
 *
 * // Cleanup when done
 * client.close();
 * }</pre>
 *
 * <p><b>Implementation Requirements:</b></p>
 * <ul>
 *   <li>Implementations must be thread-safe for concurrent access</li>
 *   <li>Connection state must be properly managed</li>
 *   <li>Resources must be properly released in the close() method</li>
 *   <li>Error conditions should be handled gracefully</li>
 *   <li>Message handlers should be called from appropriate threads</li>
 * </ul>
 *
 * <p><b>Supported Transport Protocols:</b></p>
 * <ul>
 *   <li>{@link ZMQClient} - ZeroMQ transport implementation</li>
 * </ul>
 *
 * <AUTHOR> Engineering Team
 * @version 1.0
 * @since 1.0
 * @see TransportServer
 * @see TransportProvider
 * @see ZMQClient
 */
public interface TransportClient
{
    /**
     * Registers a message handler for processing received messages.
     *
     * <p>This method allows clients to specify custom logic for handling messages
     * received from the transport layer. The handler will be invoked for each
     * message received through the {@link #receive()} method.</p>
     *
     * <p>The message handler receives raw message bytes and is responsible for:</p>
     * <ul>
     *   <li>Deserializing or parsing the message content</li>
     *   <li>Processing the message according to application logic</li>
     *   <li>Forwarding the message to appropriate components (e.g., event bus)</li>
     *   <li>Handling any errors that occur during message processing</li>
     * </ul>
     *
     * <p><b>Threading Considerations:</b></p>
     * <ul>
     *   <li>The handler may be called from transport-specific threads</li>
     *   <li>Implementations should be thread-safe if concurrent access is possible</li>
     *   <li>Blocking operations should be avoided or delegated to separate threads</li>
     *   <li>For Vert.x integration, use {@code vertx.executeBlocking()} for heavy processing</li>
     * </ul>
     *
     * <p><b>Error Handling:</b></p>
     * <ul>
     *   <li>Exceptions thrown by the handler should not crash the transport client</li>
     *   <li>Implementations should log errors and continue processing subsequent messages</li>
     *   <li>Critical errors may require connection recovery or client restart</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * client.registerMessageHandler(messageBytes -> {
     *     try {
     *         // Parse message
     *         JsonObject message = new JsonObject(new String(messageBytes));
     *
     *         // Process message
     *         processMessage(message);
     *
     *         // Forward to event bus
     *         vertx.eventBus().send("message.topic", message);
     *     } catch (Exception e) {
     *         logger.error("Error processing message", e);
     *     }
     * });
     * }</pre>
     *
     * @param handler the message handler consumer that will process received messages.
     *                Must not be null. The consumer receives raw message bytes.
     * @throws NullPointerException if handler is null
     * @throws IllegalStateException if called after connection is established (implementation-dependent)
     * @see #receive()
     * @see #connect()
     */
    void registerMessageHandler(Consumer<byte[]> handler);

    /**
     * Establishes a connection to the remote transport endpoint.
     *
     * <p>This method initiates an asynchronous connection to the configured remote
     * endpoint. The connection process varies depending on the transport protocol:</p>
     * <ul>
     *   <li><b>ZMQ</b>: Creates socket, configures parameters, and connects to remote address</li>
     *   <li><b>TCP</b>: Establishes TCP socket connection with specified host and port</li>
     *   <li><b>UDP</b>: Prepares UDP socket for datagram communication</li>
     * </ul>
     *
     * <p>The connection process typically includes:</p>
     * <ol>
     *   <li>Socket creation and configuration</li>
     *   <li>Network connection establishment</li>
     *   <li>Protocol-specific handshaking (if required)</li>
     *   <li>Connection state validation</li>
     * </ol>
     *
     * <p><b>Connection State:</b></p>
     * <ul>
     *   <li>After successful connection, the client is ready to send and receive messages</li>
     *   <li>Connection failures should be handled gracefully with appropriate error reporting</li>
     *   <li>Some implementations may support automatic reconnection on connection loss</li>
     * </ul>
     *
     * <p><b>Configuration Requirements:</b></p>
     * <ul>
     *   <li>Remote host address and port must be specified in client configuration</li>
     *   <li>Transport-specific parameters (timeouts, buffer sizes) should be configured</li>
     *   <li>Security settings (if applicable) must be properly configured</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * client.connect()
     *     .onSuccess(v -> {
     *         logger.info("Connected successfully");
     *         // Start receiving messages
     *         client.receive();
     *     })
     *     .onFailure(error -> {
     *         logger.error("Connection failed", error);
     *         // Handle connection failure
     *         handleConnectionError(error);
     *     });
     * }</pre>
     *
     * @return a Future that completes successfully when the connection is established,
     *         or fails with an exception if the connection cannot be established
     * @throws IllegalStateException if the client is already connected
     * @throws IllegalArgumentException if the client configuration is invalid
     * @see #send(byte[])
     * @see #receive()
     * @see #close()
     */
    Future<Void> connect();

    /**
     * Sends a message through the transport connection.
     *
     * <p>This method transmits the provided byte array as a message through the
     * established transport connection. The sending behavior depends on the
     * transport protocol:</p>
     * <ul>
     *   <li><b>ZMQ</b>: Sends message through ZMQ socket (PUSH, PUB patterns)</li>
     *   <li><b>TCP</b>: Sends data through TCP socket stream</li>
     *   <li><b>UDP</b>: Sends datagram packet to configured destination</li>
     * </ul>
     *
     * <p><b>Synchronous Operation:</b></p>
     * <ul>
     *   <li>This method is synchronous and may block until the message is queued for sending</li>
     *   <li>Actual network transmission may be asynchronous depending on the transport</li>
     *   <li>For high-throughput scenarios, consider the transport's buffering behavior</li>
     * </ul>
     *
     * <p><b>Message Delivery:</b></p>
     * <ul>
     *   <li>Message delivery guarantees depend on the underlying transport protocol</li>
     *   <li>ZMQ provides different delivery patterns (load balancing, pub-sub, etc.)</li>
     *   <li>TCP provides reliable, ordered delivery</li>
     *   <li>UDP provides best-effort delivery without guarantees</li>
     * </ul>
     *
     * <p><b>Error Handling:</b></p>
     * <ul>
     *   <li>Connection errors may cause exceptions to be thrown</li>
     *   <li>Buffer overflow conditions should be handled gracefully</li>
     *   <li>Network errors may require connection recovery</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * try {
     *     String message = "Hello, World!";
     *     client.send(message.getBytes(StandardCharsets.UTF_8));
     *     logger.debug("Message sent successfully");
     * } catch (Exception e) {
     *     logger.error("Failed to send message", e);
     *     // Handle send failure
     * }
     * }</pre>
     *
     * @param data the message data to send as a byte array. Must not be null.
     *             Empty arrays are allowed but behavior is transport-dependent.
     * @throws IllegalStateException if the client is not connected
     * @throws NullPointerException if data is null
     * @throws RuntimeException if there's an error sending the message
     * @see #connect()
     * @see #receive()
     */
    void send(byte[] data);

    /**
     * Starts receiving messages from the transport connection.
     *
     * <p>This method initiates asynchronous message reception from the established
     * transport connection. The receiving behavior depends on the transport protocol:</p>
     * <ul>
     *   <li><b>ZMQ</b>: Starts receiving loop for SUB or PULL socket patterns</li>
     *   <li><b>TCP</b>: Begins reading from TCP socket stream</li>
     *   <li><b>UDP</b>: Starts listening for UDP datagram packets</li>
     * </ul>
     *
     * <p><b>Asynchronous Operation:</b></p>
     * <ul>
     *   <li>This method returns immediately after starting the receive process</li>
     *   <li>Actual message reception happens in background threads</li>
     *   <li>Received messages are processed by the registered message handler</li>
     *   <li>The receiving process continues until the client is closed</li>
     * </ul>
     *
     * <p><b>Message Processing:</b></p>
     * <ul>
     *   <li>Each received message is passed to the registered message handler</li>
     *   <li>Message handlers are called from transport-specific threads</li>
     *   <li>Handler exceptions should not stop the receiving process</li>
     *   <li>Message ordering depends on the transport protocol</li>
     * </ul>
     *
     * <p><b>Threading Model:</b></p>
     * <ul>
     *   <li>Most implementations create dedicated threads for message reception</li>
     *   <li>Thread names typically include transport type and connection details</li>
     *   <li>Threads are automatically cleaned up when the client is closed</li>
     * </ul>
     *
     * <p><b>Error Handling:</b></p>
     * <ul>
     *   <li>Connection errors are logged and may trigger reconnection attempts</li>
     *   <li>Transient errors should not stop the receiving process</li>
     *   <li>Critical errors may require manual intervention or client restart</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * // Register message handler first
     * client.registerMessageHandler(message -> {
     *     processReceivedMessage(message);
     * });
     *
     * // Start receiving messages
     * client.receive()
     *     .onSuccess(v -> {
     *         logger.info("Started receiving messages");
     *     })
     *     .onFailure(error -> {
     *         logger.error("Failed to start receiving", error);
     *     });
     * }</pre>
     *
     * @return a Future that completes successfully when message reception is started,
     *         or fails with an exception if reception cannot be initiated
     * @throws IllegalStateException if the client is not connected or no message handler is registered
     * @see #registerMessageHandler(Consumer)
     * @see #connect()
     * @see #close()
     */
    Future<Void> receive();

    /**
     * Closes the transport client and releases all associated resources.
     *
     * <p>This method performs a graceful shutdown of the transport client by:</p>
     * <ol>
     *   <li>Stopping any active message receiving processes</li>
     *   <li>Closing the underlying transport connection</li>
     *   <li>Terminating background threads</li>
     *   <li>Releasing system resources (sockets, buffers, etc.)</li>
     * </ol>
     *
     * <p><b>Graceful Shutdown:</b></p>
     * <ul>
     *   <li>Ongoing send operations are allowed to complete</li>
     *   <li>Receiving threads are signaled to stop gracefully</li>
     *   <li>Pending messages may be processed or discarded depending on implementation</li>
     *   <li>Connection cleanup is performed according to transport protocol requirements</li>
     * </ul>
     *
     * <p><b>Resource Management:</b></p>
     * <ul>
     *   <li>All network connections are properly closed</li>
     *   <li>Thread resources are cleaned up and terminated</li>
     *   <li>Memory buffers and caches are released</li>
     *   <li>File descriptors and system handles are closed</li>
     * </ul>
     *
     * <p><b>Post-Close State:</b></p>
     * <ul>
     *   <li>After closing, the client cannot be reused for new connections</li>
     *   <li>Attempts to send or receive messages will fail</li>
     *   <li>A new client instance must be created for subsequent operations</li>
     *   <li>Multiple calls to close() are safe and have no additional effect</li>
     * </ul>
     *
     * <p><b>Thread Safety:</b></p>
     * <ul>
     *   <li>This method is thread-safe and can be called from any thread</li>
     *   <li>Concurrent calls to close() are handled safely</li>
     *   <li>The method may block briefly while cleanup operations complete</li>
     * </ul>
     *
     * <p><b>Best Practices:</b></p>
     * <ul>
     *   <li>Always call close() when the client is no longer needed</li>
     *   <li>Use try-with-resources or finally blocks to ensure cleanup</li>
     *   <li>Consider implementing AutoCloseable for automatic resource management</li>
     *   <li>Handle any exceptions that may occur during cleanup</li>
     * </ul>
     *
     * <p>Example usage:</p>
     * <pre>{@code
     * TransportClient client = null;
     * try {
     *     client = TransportProvider.getClient(config);
     *     client.connect().get();
     *     // Use client for messaging
     * } finally {
     *     if (client != null) {
     *         client.close();
     *     }
     * }
     *
     * // Or with try-with-resources (if AutoCloseable is implemented)
     * try (TransportClient client = TransportProvider.getClient(config)) {
     *     client.connect().get();
     *     // Use client for messaging
     * } // Automatic cleanup
     * }</pre>
     *
     * @see #connect()
     * @see #receive()
     */
    Future<Void> close();
}
