/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 *  June-5-2025     <PERSON><PERSON>            Added Support for widget/Alert for Netroute.
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.ACKNOWLEDGED;
import static com.mindarray.GlobalConstants.NO;
import static com.mindarray.nms.NMSConstants.PREVIOUS_FLAP_TIMESTAMP;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_ACKNOWLEDGE;

public class NetRoutePolicyFlapDurationCacheStore extends AbstractCacheStore
{
    private static final NetRoutePolicyFlapDurationCacheStore STORE = new NetRoutePolicyFlapDurationCacheStore();

    private static final Logger LOGGER = new Logger(NetRoutePolicyFlapDurationCacheStore.class, GlobalConstants.MOTADATA_STORE, "NetRoute Policy Flap Duration Cache Store");

    private final Map<String, Long> flaps = new ConcurrentHashMap<>();  // policy with its previous.flap.timestamp

    private final Map<String, String> acks = new ConcurrentHashMap<>();  // policy with its acknowledgment

    private final Map<String, JsonObject> ticks = new ConcurrentHashMap<>();  // policy with its acknowledgment

    private NetRoutePolicyFlapDurationCacheStore()
    {
    }

    public static NetRoutePolicyFlapDurationCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NETROUTE_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
            {
                try
                {
                    reply.result().body().getMap().forEach((key, value) ->
                    {
                        var item = (JsonObject) value;

                        flaps.put(key, item.getLong(PREVIOUS_FLAP_TIMESTAMP));

                        var ack = item.containsKey(POLICY_ACKNOWLEDGE) ? new JsonObject(item.getString(POLICY_ACKNOWLEDGE)) : null;

                        acks.put(key, ack != null ? ack.containsKey(ACKNOWLEDGED) ? ack.getString(ACKNOWLEDGED) : NO : NO);
                    });

                    var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.NETROUTE_POLICY_TRIGGER_TICKS);

                    if (file.exists())
                    {
                        var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

                        if (buffer != null && buffer.getBytes().length > 0)
                        {
                            var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes())));

                            new JsonObject(context.getString(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName())).getMap().forEach((key, value) ->
                                    ticks.put(key, JsonObject.mapFrom(value)));

                            LOGGER.info(String.format("%s loaded from the backup file...", PolicyEngineConstants.NETROUTE_POLICY_TRIGGER_TICKS));
                        }
                    }
                    else
                    {
                        Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
                    }

                    LOGGER.info("NetRoute Policy Flap Duration Cache Store initialized successfully");

                    promise.complete();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }

            });
        }
        catch (Exception exception)
        {
            LOGGER.info(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    public void update(String key, JsonObject objectPolicyStatus, boolean updateTimestamp)
    {
        var ack = objectPolicyStatus.containsKey(POLICY_ACKNOWLEDGE) ? new JsonObject(objectPolicyStatus.getString(POLICY_ACKNOWLEDGE)) : null;

        acks.put(key, ack != null ? ack.containsKey(ACKNOWLEDGED) ? ack.getString(ACKNOWLEDGED) : NO : NO);

        if (updateTimestamp)
        {
            flaps.put(key, objectPolicyStatus.getLong(PREVIOUS_FLAP_TIMESTAMP));
        }
    }

    public Long getFlapTick(String key)
    {
        return flaps.getOrDefault(key, 0L);
    }

    public String getAcknowledgment(String key)
    {
        return acks.get(key);
    }

    public void updateTriggerTicks(String key, long tick)
    {
        ticks.computeIfAbsent(key, value -> new JsonObject());

        var context = ticks.get(key);

        context.getMap().computeIfAbsent(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK, value -> tick * 1000);

        context.put(PolicyEngineConstants.POLICY_LAST_TRIGGER_TICK, tick * 1000);

        context.remove(PolicyEngineConstants.POLICY_ACKNOWLEDGE);
    }

    public void remove(String key)
    {
        ticks.remove(key);
    }

    public Map<String, JsonObject> getTriggerTicks()
    {
        return new HashMap<>(ticks);
    }

    public JsonObject getTriggerTicks(String key)
    {
        return ticks.getOrDefault(key, new JsonObject());
    }
}