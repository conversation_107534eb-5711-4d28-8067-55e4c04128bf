/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  25-Feb-2025		Chandresh		MOTADATA-3680: Added INSTANCE field to worklog
 *  03-Mar-2025     Chopra Deven    MOTADATA-4973: Dumping small object.id instead of long object.id into DB in case of runbook worklog
 * 	27-Jul-2025		Darshan Parmar	MOTADATA-6849: macros constants
 */

package com.mindarray.integration;

import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_SOURCE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * This class contains constants, enums, and utility methods used throughout the integration package.
 * It defines various integration types, states, actions, and categories that are used to configure
 * and manage integrations with external systems like ServiceNow, Jira, ServiceOps, and Microsoft Teams.
 * <p>
 * The constants and enums in this class serve as a central reference point for all integration-related
 * configurations and operations, ensuring consistency across the application.
 */
public class IntegrationConstants
{
    // constants
    public static final String INCIDENT_SUBJECT = "incident.subject";
    public static final String INCIDENT_DESCRIPTION = "incident.description";

    public static final String SUMMARY_TEMPLATE = "$$$policy.name$$$-$$$object.name$$$($$$object.ip$$$)-$$$counter$$$";
    public static final String INSTANCE_SUMMARY_TEMPLATE = "$$$policy.name$$$-$$$object.name$$$($$$object.ip$$$)-$$$counter$$$-$$$instance$$$";
    public static final String DESCRIPTION_TEMPLATE = "Object Name: $$$object.name$$$\nIP / Host: $$$object.ip$$$\nObject Type: $$$object.type$$$\nMetric: $$$counter$$$\nMetric Value: $$$value$$$\nSeverity: $$$severity$$$\nPolicy Name: $$$policy.name$$$\nPolicy Type: $$$policy.type$$$\nMessage: $$$policy.message$$$";
    public static final String INSTANCE_DESCRIPTION_TEMPLATE = "Object Name: $$$object.name$$$\nIP / Host: $$$object.ip$$$\nObject Type: $$$object.type$$$\nMetric: $$$counter$$$\nInstance: $$$instance$$$\nMetric Value: $$$value$$$\nSeverity: $$$severity$$$\nPolicy Name: $$$policy.name$$$\nPolicy Type: $$$policy.type$$$\nMessage: $$$policy.message$$$";

    /**
     * Set of integration types that are used for sending notifications.
     * Currently, only Microsoft Teams is supported for notifications.
     */
    public static final Set<String> NOTIFICATION_INTEGRATIONS = Set.of("Microsoft Teams");

    /**
     * Dumps event information to the datastore for logging and tracking purposes.
     * This method processes the event data, truncates it if necessary to prevent excessive storage usage,
     * and writes it to the datastore as a runbook worklog entry.
     *
     * @param event   The event object containing information to be dumped
     * @param mappers Set of mapper identifiers used for processing the event
     * @param builder StringBuilder used for building the result string
     * @param logger  Logger instance for logging any errors during the dump process
     */
    public static void dump(JsonObject event, Set<String> mappers, StringBuilder builder, Logger logger)
    {
        try
        {
            builder.setLength(0);

            if (event.containsKey(RESULT) && event.getValue(RESULT) != null)
            {
                if (event.getValue(RESULT) instanceof JsonArray)
                {
                    builder.append(event.getJsonArray(RESULT).encodePrettily());
                }
                else if (event.getValue(RESULT) instanceof JsonObject)
                {
                    builder.append(event.getJsonObject(RESULT).encodePrettily());
                }
                else
                {
                    builder.append(event.getString(RESULT));
                }
            }

            if (builder.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES)
            {
                builder.setLength(RUNBOOK_WORKLOG_RESULT_MAX_BYTES);
            }

            var error = event.getString(ERROR, EMPTY_VALUE);

            if (error.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES)
            {
                error = error.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES);
            }

            DatastoreConstants.write(new JsonObject().put(RUNBOOK_WORKLOG_STATUS, event.getValue(STATUS, STATUS_FAIL))
                            .put(RUNBOOK_WORKLOG_RESULT, builder.toString())
                            .put(RUNBOOK_WORKLOG_ID, event.containsKey(ID) ? event.getLong(ID) : 0)
                            .put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.INTEGRATION.ordinal())
                            .put(POLICY_ID, event.containsKey(POLICY_ID) ? event.getLong(POLICY_ID) : 0)
                            .put(AIOpsObject.OBJECT_ID, event.containsKey(AIOpsConstants.ENTITY_ID) ? ObjectConfigStore.getStore().getObjectId(event.getLong(AIOpsConstants.ENTITY_ID)) : 0L)
                            .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName())
                            .put(EVENT_TIMESTAMP, event.containsKey(EVENT_TIMESTAMP) ? event.getLong(EVENT_TIMESTAMP) : DateTimeUtil.currentSeconds())
                            .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal())
                            .put(USER_NAME, DEFAULT_USER)
                            .put(RUNBOOK_WORKLOG_ERROR, error)
                            .put(EVENT_SOURCE, event.containsKey(AIOpsObject.OBJECT_IP) ? event.getString(AIOpsObject.OBJECT_IP) : SYSTEM_REMOTE_ADDRESS)
                            .put(INSTANCE, event.getString(INSTANCE, EMPTY_VALUE))
                    , VisualizationConstants.VisualizationDataSource.RUNBOOK_WORKLOG.getName(), mappers, builder);
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    /**
     * Enum representing the unique identifiers for different integration types.
     * Each integration type has a unique long value that serves as its identifier.
     * These IDs are used throughout the application to reference specific integration types.
     */
    public enum IntegrationId
    {
        SERVICEOPS(10000000000001L),

        SERVICENOW(10000000000002L),

        MICROSOFT_TEAMS(10000000000003L),

        ATLASSIAN_JIRA(10000000000004L);

        private static final Map<Long, IntegrationConstants.IntegrationId> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(IntegrationConstants.IntegrationId::getName, e -> e)));

        private final Long name;

        IntegrationId(long name)
        {
            this.name = name;
        }

        public static IntegrationConstants.IntegrationId valueOfName(long name)
        {
            return VALUES.get(name);
        }

        public Long getName()
        {
            return name;
        }
    }

    /**
     * Enum representing the possible states of a ticket in an external ticketing system.
     * These states are used to determine the current status of a ticket and to make decisions
     * about how to handle ticket updates.
     */
    public enum TicketState
    {
        CLOSED("closed"),

        RESOLVED("resolved");

        private static final Map<String, IntegrationConstants.TicketState> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(IntegrationConstants.TicketState::getName, e -> e)));
        private final String name;

        TicketState(String name)
        {
            this.name = name;
        }

        public static IntegrationConstants.TicketState valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Enum representing the possible actions to take when an alert reoccurs.
     * When an alert that was previously reported to an external system occurs again,
     * the system can either create a new ticket or reopen an existing one.
     */
    public enum AlertReoccurrenceAction
    {
        CREATE("create"),

        REOPEN("reopen");

        private static final Map<String, AlertReoccurrenceAction> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(AlertReoccurrenceAction::getName, e -> e)));
        private final String name;

        AlertReoccurrenceAction(String name)
        {
            this.name = name;
        }

        public static AlertReoccurrenceAction valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Enum representing the types of requests that can be created in external systems.
     * Different integration systems may support different request types, such as events or incidents.
     * This enum is used to specify which type of request should be created.
     */
    public enum RequestType
    {
        EVENT("event"),

        INCIDENT("incident");

        private static final Map<String, IntegrationConstants.RequestType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(IntegrationConstants.RequestType::getName, e -> e)));
        private final String name;

        RequestType(String name)
        {
            this.name = name;
        }

        public static IntegrationConstants.RequestType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Enum representing the different types of integrations supported by the system.
     * Each integration type corresponds to a specific external system that can be integrated with,
     * such as ServiceNow, Jira, ServiceOps, or Microsoft Teams.
     */
    public enum IntegrationType
    {
        SERVICEOPS("ServiceOps"),

        SERVICENOW("ServiceNow"),

        MICROSOFT_TEAMS("Microsoft Teams"),

        ATLASSIAN_JIRA("Atlassian Jira");

        private static final Map<String, IntegrationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(IntegrationType::getName, e -> e)));
        private final String name;

        IntegrationType(String name)
        {
            this.name = name;
        }

        public static IntegrationType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    /**
     * Enum representing the categories of integrations.
     * Integrations can be categorized based on their primary purpose, such as
     * sending notifications or managing incidents.
     */
    public enum IntegrationCategory
    {
        NOTIFICATION("Notification"),

        INCIDENT("Incident");

        private static final Map<String, IntegrationCategory> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(IntegrationCategory::getName, e -> e)));
        private final String name;

        IntegrationCategory(String name)
        {
            this.name = name;
        }

        public static IntegrationCategory valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
