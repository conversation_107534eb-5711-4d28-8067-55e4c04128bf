/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  21-Apr-2025		Bharat		    MOTADATA-5798: Enhance compliance in the plugin engine by implementing batching for CLI sessions.
 */

package com.mindarray.compliance;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Tag;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ComplianceWeightedCalculationConfigStore;
import com.mindarray.store.ConfigurationConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;

public class ComplianceConstants
{
    public static final String COMPLIANCE_RULE_ID = "compliance.rule.id";
    public static final String COMPLIANCE_POLICY_ID = "compliance.policy.id";
    public static final String COMPLIANCE_BENCHMARK_ID = "compliance.benchmark.id";
    public static final String RULE_CONDITION = "condition";
    public static final String RULE_OCCURRENCE = "occurrence";
    public static final String RESULT_PATTERN = "result.pattern";
    public static final String CONTENT = "content";
    public static final String LAST_SCAN_TIMESTAMP = "last.scan.timestamp";
    public static final String LAST_SCAN_STATUS = "last.scan.status";
    public static final String CURRENT_SCAN_TIMESTAMP = "current.scan.timestamp";
    public static final String CURRENT_SCAN_STATUS = "current.scan.status";
    public static final String COMPLIANCE_POLICY_EMAIL_NOTIFICATION_RECIPIENT = "compliance.policy.email.notification.recipients";
    public static final String COMPLIANCE_POLICY_USER_NOTIFICATION_RECIPIENT = "compliance.policy.user.notification.recipients";
    public static final String COMPLIANCE_POLICY_NOTIFICATION_CONTEXT = "compliance.policy.notification.context";
    public static final String SCANNED_RULE = "scanned.rule";
    public static final String COMPLIANCE_PERCENTAGE = "compliance.percentage";
    public static final String BLOCK_CRITERIA_REGEX_PATTERN = "%s.*?%s";
    public static final String BLOCK_CRITERIA_REGEX_PATTERN_WITH_START = "%s.*?.*";
    // Weightage calculation constants... as of now creating constants here... once API are available, we will move them.
    public static final String RULE_WEIGHTAGE = "rule.weightage";
    public static final String OBJECT_STATE_WEIGHTAGE = "object.state.weightage";
    public static final String START_RANGE = "start.range";
    public static final String END_RANGE = "end.range";
    public static final String COMPLIANCE_CLI_IDENTIFIER = "compliance.cli.identifier";
    private static final Logger LOGGER = new Logger(ComplianceConstants.class, GlobalConstants.MOTADATA_COMPLIANCE, "Compliance Constants");

    /*
     This Method is used to qualify objects as per Category for Compliance.
     */
    public static JsonArray qualifyObjects(JsonObject context, NMSConstants.Category category)
    {
        var objects = new JsonArray();

        if (category == NMSConstants.Category.NETWORK)
        {
            if (context.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
            {
                objects = ObjectConfigStore.getStore().getItems(context.getJsonArray(ENTITIES));
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
            {
                // As Group , only NCM monitors are required thus filtering out only items which are configured for it.
                var items = ConfigurationConfigStore.getStore().getItemsByDiscoveryStatus(ObjectConfigStore.getStore().getUniqueObjectIdsByGroups(context.getJsonArray(ENTITIES)));

                if (items != null)
                {
                    objects = items;
                }
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
            {
                var counts = new HashSet<Long>();

                var items = ObjectConfigStore.getStore().getItemsByMultiValueFieldAny(AIOpsObject.OBJECT_TAGS,
                        new JsonArray(TagConfigStore.getStore().getIdsByItems(context.getJsonArray(ENTITIES))
                                .stream().map(CommonUtil::getInteger).toList()));

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    if (counts.add(item.getLong(ID)))
                    {
                        objects.add(item);
                    }
                }
            }
        }
        else
        {
            LOGGER.warn(String.format("Unsupported category %s", category.getName()));
        }

        return objects;
    }

    public static String getState(long score)
    {
        for (var entry : ComplianceWeightedCalculationConfigStore.getStore().getItem(GlobalConstants.DEFAULT_ID).getJsonObject(ComplianceConstants.OBJECT_STATE_WEIGHTAGE))
        {
            if (score >= ((JsonObject) entry.getValue()).getInteger(ComplianceConstants.START_RANGE) && score <= ((JsonObject) entry.getValue()).getInteger(ComplianceConstants.END_RANGE))
            {
                return entry.getKey();
            }
        }

        return ComplianceState.SECURE.name();
    }

    public static int getScoreByRuleWeightage(ComplianceConstants.RuleSeverity severity)
    {
        return ComplianceWeightedCalculationConfigStore.getStore().getItem(GlobalConstants.DEFAULT_ID).getJsonObject(ComplianceConstants.RULE_WEIGHTAGE).getInteger(severity.name());
    }

    public enum ComplianceState
    {
        VULNERABLE,
        MODERATE,
        POOR,
        SECURE
    }

    public enum RuleCheckIn
    {
        CONFIG_FILE("Config File"),
        CLI("CLI");

        private static final Map<String, RuleCheckIn> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(ComplianceConstants.RuleCheckIn::getName, entity -> entity)));
        private final String name;

        RuleCheckIn(String name)
        {
            this.name = name;
        }

        public static ComplianceConstants.RuleCheckIn valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }


    public enum RuleCheckConfiguration
    {
        BASIC,
        ADVANCED
    }

    public enum RuleSeverity
    {
        CRITICAL,
        HIGH,
        MEDIUM,
        LOW,
        INFO
    }

    public enum RuleStatus
    {
        UNKNOWN(0),

        SUCCEEDED(1),

        FAILED(2);

        private static final Map<Integer, RuleStatus> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(RuleStatus::getType, e -> e)));

        private final Integer type;

        RuleStatus(Integer type)
        {
            this.type = type;
        }

        public static RuleStatus valueOfName(Integer type)
        {
            return VALUES.get(type);
        }

        public Integer getType()
        {
            return type;
        }
    }
}
