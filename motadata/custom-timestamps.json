{"(YYYY\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\d{3}Z)": "YYYY-MM-dd'T'HH:mm:ss.SSSSSS'Z'", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\d{3}[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ", "(E\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\d{3}\\s\\d{4})": "E MMM dd HH:mm:ss.SSSSSS yyyy", "(\\d{1,2}\\-[JFMAMJSONDjfmamjsond]\\w+\\-\\d{4}\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\s[Aa|Pp][Mm])": "dd-MMM-yyyy HH hh:mm:ss.SSS a", "(\\b\\w+\\b\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\s\\d{4})": "EEE MMM dd HH:mm:ss.SSS yyyy", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}Z)": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", "([JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\d{3}\\s\\d{4})": "MMM dd HH:mm:ss.SSSSSS yyyy", "(\\d{4}\\/\\d{1,2}\\/\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\d{3}S)": "yyyy/MM/dd HH:mm:ss.SSSSSSS", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\d{3}[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd HH:mm:ss.SSSSSSZ", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd'T'HH:mm:ss.SSSZ", "(\\b\\w+\\b\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\d{4}\\s[-+]\\d{2}:?\\d{2})": "EEE MMM dd HH:mm:ss yyyy Z", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3}\\s[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd'T'HH:mm:ss.SSS Z", "(\\b\\w+\\b\\s[JFMAMJSONDjfmamjsond]\\w+\\s{2}\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\d{4}\\s[-+]\\d{2}:?\\d{2})": "EEE MMM  dd HH:mm:ss yyyy Z", "(\\b\\w+\\b\\,\\s\\d{1,2}\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\w{3})": "EEE, dd MMM yyyy HH:mm:ss z", "(\\d{1,2}\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "dd MMM yyyy HH:mm:ss", "(\\b\\w+\\b\\,\\s\\d{1,2}\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[-+]\\d{2}:?\\d{2})": "EEE, dd MMM yyyy HH:mm:ss Z", "(\\b\\w+\\b\\,\\s\\d{1,2}\\-[JFMAMJSONDjfmamjsond]\\w+\\-\\d{2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\w{3})": "EEEE, dd-MMM-yy HH:mm:ss z", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\([-+]\\d{2}:?\\d{2}\\))": "yyyy-MM-dd'T'HH:mm:ss (Z)", "(\\b\\w+\\b\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\w{3}\\s\\d{4})": "EEE MMM dd HH:mm:ss z yyyy", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\,\\d{1,2}m\\s[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd HH:mm:ss,mmm Z", "(\\b\\w+\\b\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[-+]\\d{2}:?\\d{2}\\s\\d{4})": "EEE MMM dd HH:mm:ss Z yyyy", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\,\\d{3})": "yyyy-MM-dd HH:mm:ss,SSS", "(\\d{1,2}\\-[JFMAMJSONDjfmamjsond]\\w+\\-\\d{4}:\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{1,2}m)": "dd-MMM-yyyy:HH:mm:ss.mmm", "(\\b\\w+\\b\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\d{4})": "EEE MMM dd HH:mm:ss yyyy", "([JFMAMJSONDjfmamjsond]\\w+\\s{2}\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\d{4})": "MMM  dd HH:mm:ss yyyy", "([JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\d{4})": "MMM dd HH:mm:ss yyyy", "(\\d{1,2}\\/[JFMAMJSONDjfmamjsond]\\w+\\/\\d{4}:\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[-+]\\d{2}:?\\d{2})": "dd/MMM/yyyy:HH:mm:ss Z", "([JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\,\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[Aa|Pp][Mm])": "MMM dd, yyyy HH:mm:ss a", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\,\\d{1,2}m)": "yyyy-MM-dd HH:mm:ss,mmm", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}\\-[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd'T'HH:mm:ss-Z", "(\\d{2}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\,\\d{1,2}m\\s[-+]\\d{2}:?\\d{2})": "yy-MM-dd HH:mm:ss,mmm Z", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2})": "yyyy-MM-dd'T'HH:mm:ss", "(\\d{4}\\s\\d{1,2}\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{2}\\s\\d{1,2}:\\d{1,2}\\s[-+]\\d{2}:?\\d{2})": "yyyy dd MMM yy HH:mm Z", "(\\d{4}\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[-+]\\d{2}:?\\d{2})": "yyyy MMM dd HH:mm:ss Z", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}T\\d{1,2}:\\d{1,2}:\\d{1,2}[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd'T'HH:mm:ssZ", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s\\w{3})": "yyyy-MM-dd HH:mm:ss z", "(\\d{4}\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "yyyy MMM dd HH:mm:ss", "([JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "MMM dd yyyy HH:mm:ss", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd HH:mm:ss Z", "(\\d{2}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\,\\d{3})": "yy-MM-dd HH:mm:ss,SSS", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "yyyy-MM-dd HH:mm:ss", "(\\d{4}\\/\\d{1,2}\\/\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "yyyy/MM/dd HH:mm:ss", "(\\d{4}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}[-+]\\d{2}:?\\d{2})": "yyyy-MM-dd HH:mm:ssZ", "([JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}.\\d{3})": "MMM dd HH:mm:ss.SSS", "(\\d{1,2}\\/M\\/\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[Aa|Pp][Mm])": "dd/M/yyyy HH:mm:ss a", "(\\d{1,2}\\/\\d{1,2}\\/\\d{2}\\,\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "MM/dd/yy, HH:mm:ss", "(\\d{1,2}\\s[JFMAMJSONDjfmamjsond]\\w+\\s\\d{2}\\s\\d{1,2}:\\d{1,2}\\s\\w{3})": "dd MMM yy HH:mm z", "([JFMAMJSONDjfmamjsond]\\w+\\s{2}\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "MMM  dd HH:mm:ss", "(\\d{2}\\-\\d{1,2}\\-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "yy-MM-dd HH:mm:ss", "(\\d{2}\\/\\d{1,2}\\/\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "yy/MM/dd HH:mm:ss", "(\\d{2}\\d{1,2}\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "yyMMdd HH:mm:ss", "([JFMAMJSONDjfmamjsond]\\w+\\s\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2})": "MMM dd HH:mm:ss", "(\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\s\\d{1,2}:\\d{1,2})": "dd/MM/yyyy HH:mm", "(\\d{1,2}\\/\\d{1,2}\\/\\d{4})": "MM/dd/yyyy"}