{"entity": "System Service", "table": "tbl_config_system_service", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "system.service", "title": "Service", "type": "string", "rules": ["required"]}, {"name": "system.service.app.type", "title": "Application Type", "type": "string"}, {"name": "system.service.os", "title": "OS", "type": "string", "rules": ["required"], "values": ["Windows"]}], "entries": [{"type": "inline", "records": [{"system.service": "IISADMIN", "system.service.app.type": "Microsoft IIS", "system.service.os": "Windows", "id": 10000000000001}, {"system.service": "DHCPServer", "system.service.app.type": "Windows DHCP", "system.service.os": "Windows", "id": 10000000000002}, {"system.service": "ADWS", "system.service.app.type": "Active Directory", "system.service.os": "Windows", "id": 10000000000003}, {"system.service": "DNS", "system.service.app.type": "Windows DNS", "system.service.os": "Windows", "id": 10000000000004}, {"system.service": "RabbitMQ", "system.service.app.type": "RabbitMQ", "system.service.os": "Windows", "id": 10000000000005}, {"system.service": "TermService", "system.service.app.type": "Windows RDP", "system.service.os": "Windows", "id": 10000000000006}, {"system.service": "MSMQ", "system.service.app.type": "MSMQ", "system.service.os": "Windows", "id": 10000000000007}, {"system.service": "MSExchangeIS", "system.service.app.type": "Exchange Mailbox Role", "system.service.os": "Windows", "id": 10000000000008}, {"system.service": "MSExchangeTransport", "system.service.app.type": "Exchange Edge Transport Role", "system.service.os": "Windows", "id": 10000000000009}, {"system.service": "MSExchangeRPC", "system.service.app.type": "Exchange Client Access Role", "system.service.os": "Windows", "id": 10000000000010}, {"system.service": "MQ_Installation1", "system.service.app.type": "IBM MQ", "system.service.os": "Windows", "id": 10000000000011}, {"system.service": "IBMWAS", "system.service.app.type": "IBM WebSphere", "system.service.os": "Windows", "id": 10000000000012}, {"system.service": "OracleService", "system.service.app.type": "Oracle Database", "system.service.os": "Windows", "id": 10000000000013}, {"system.service": "Wildfly", "system.service.app.type": "<PERSON><PERSON><PERSON>", "system.service.os": "Windows", "id": 10000000000014}, {"system.service": "SAP DBTech", "system.service.app.type": "SAP MaxDB", "system.service.os": "Windows", "id": 10000000000015}, {"system.service": "nginx", "system.service.app.type": "<PERSON><PERSON><PERSON>", "system.service.os": "Windows", "id": 10000000000016}, {"system.service": "Tomcat", "system.service.app.type": "Apache Tomcat", "system.service.os": "Windows", "id": 10000000000017}, {"system.service": "MSSQL", "system.service.app.type": "SQL Server", "system.service.os": "Windows", "id": 10000000000018}, {"system.service": "MySQL", "system.service.app.type": "MySQL", "system.service.os": "Windows", "id": 10000000000019}, {"system.service": "DB2", "system.service.app.type": "IBM Db2", "system.service.os": "Windows", "id": 10000000000020}, {"system.service": "postgresql", "system.service.app.type": "PostgreSQL", "system.service.os": "Windows", "id": 10000000000021}, {"system.service": "SYBSQL", "system.service.app.type": "Sybase", "system.service.os": "Windows", "id": 10000000000022}, {"system.service": "NortonSecurity", "system.service.os": "Windows", "id": 10000000000023}, {"system.service": "AVP", "system.service.os": "Windows", "id": 10000000000024}, {"system.service": "McAfee", "system.service.os": "Windows", "id": 10000000000025}, {"system.service": "Amsp", "system.service.os": "Windows", "id": 10000000000026}], "version": "1.0"}]}