/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  25-Mar-2025     Smit Prajapati           MOTADATA-5435: Flow back-pressure mechanism.
 */
package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;

import static com.mindarray.api.FlowSettings.*;

public class FlowSettingsConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(FlowSettingsConfigStore.class, GlobalConstants.MOTADATA_STORE, "Flow Settings Config Store");

    private static final FlowSettingsConfigStore STORE = new FlowSettingsConfigStore();

    private FlowSettingsConfigStore()
    {
        super(DBConstants.TBL_FLOW_SETTINGS, LOGGER, true);
    }

    public static FlowSettingsConfigStore getStore()
    {
        return STORE;
    }

    public int getsFlowPort()
    {
        return this.getItem().getInteger(FLOW_SETTINGS_SFLOW_PORT, 6343);
    }

    public int getNetFlowPort()
    {
        return this.getItem().getInteger(FLOW_SETTINGS_NETFLOW_PORT, 2055);
    }

    public int getFlowEngineAggregationTimerMinutes()
    {
        return this.getItem().getInteger(FLOW_SETTINGS_AGGREGATION_INTERVAL_MINUTES, 3);
    }

    public int getBGPsFlowPort()
    {
        return this.getItem().getInteger(FLOW_SETTINGS_BGP_SFLOW_PORT, 6344);
    }

    public int getBGPNetFlowPort()
    {
        return this.getItem().getInteger(FLOW_SETTINGS_BGP_NETFLOW_PORT, 2056);
    }

    public boolean getBGPFlowEnabled()
    {
        return GlobalConstants.YES.equalsIgnoreCase(this.getItem().getString(FLOW_SETTINGS_BGP_FLOW_ENABLED, GlobalConstants.NO));
    }
}
