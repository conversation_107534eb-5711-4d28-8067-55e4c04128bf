package com.mindarray.slo;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

public class SLOConstants
{
    // ------------------------ Constants ------------------------ //

    // file name to dump the cache
    public static final String SLO_CACHE_FILE = "slo-details";

    public static final String LAST_TIMESTAMP = "last.timestamp";

    public static final String LAST_BREACHED_TIMESTAMP = "last.breached.timestamp";

    public static final String LAST_SLO_STATUS = "last.slo.status";

    public static final String LAST_VIOLATED_SECONDS = "last.violated.seconds";

    public static final String DURATION_LEFT = "duration.left";

    public static final String DOWN_INCIDENT_COUNT = "down.incident.count";

    public static final String UP_INCIDENT_COUNT = "up.incident.count";

    // ------------------------ Scaler counters for slo instances ------------------------ //

    public static final String SLO_OBJECT_STATUS_FLAP = "slo.object.status.flap";

    public static final String SLO_OBJECT_STATUS = "slo.object.status";

    public static final String SLO_OBJECT_DURATION = "slo.object.duration";

    public static final String SLO_OBJECT_ACHIEVED_PERCENT = "slo.object.achieved.percent";

    public static final String SLO_OBJECT_VIOLATED_PERCENT = "slo.object.violated.percent";

    public static final String SLO_OBJECT_VIOLATED_SECONDS = "slo.object.violated.seconds";

    public static final String SLO_OBJECT_ERROR_BUDGET_LEFT_PERCENT = "slo.object.error.budget.left.percent";

    public static final String SLO_OBJECT_ERROR_BUDGET_LEFT_SECONDS = "slo.object.error.budget.left.seconds";

    public static final String SLO_OBJECT_BURN_RATE_PERCENT = "slo.object.burn.rate.percent";

    public static final String SLO_OBJECT_BURN_RATE_SECONDS = "slo.object.burn.rate.seconds";

    public static final String SLO_OBJECT_MTTR_SECONDS = "slo.object.mttr.seconds";

    public static final String SLO_OBJECT_MTBF_SECONDS = "slo.object.mtbf.seconds";

    // ------------------------ Instance counters for slo instances ------------------------ //

    public static final String SLO_INSTANCE_DURATION = "slo.instance~duration";

    public static final String SLO_INSTANCE_NAME = "slo.instance~instance.name";

    public static final String SLO_INSTANCE_ACHIEVED_PERCENT = "slo.instance~achieved.percent";

    public static final String SLO_INSTANCE_VIOLATED_PERCENT = "slo.instance~violated.percent";

    public static final String SLO_INSTANCE_VIOLATED_SECONDS = "slo.instance~violated.seconds";

    public static final String SLO_INSTANCE_STATUS_FLAP = "slo.instance~status.flap";

    public static final String SLO_INSTANCE_STATUS = "slo.instance~status";

    public static final String SLO_INSTANCE_ERROR_BUDGET_LEFT_PERCENT = "slo.instance~error.budget.left.percent";

    public static final String SLO_INSTANCE_ERROR_BUDGET_LEFT_SECONDS = "slo.instance~error.budget.left.seconds";

    public static final String SLO_INSTANCE_BURN_RATE_PERCENT = "slo.instance~burn.rate.percent";

    public static final String SLO_INSTANCE_BURN_RATE_SECONDS = "slo.instance~burn.rate.seconds";

    public static final String SLO_INSTANCE_MTTR_SECONDS = "slo.instance~mttr.seconds";

    public static final String SLO_INSTANCE_MTBF_SECONDS = "slo.instance~mtbf.seconds";

    // ------------------------ Instance counters for global SLO ------------------------ //

    public static final String SLO_DURATION = "slo~duration";

    public static final String SLO_NAME = "slo~instance.name";

    public static final String SLO_ACHIEVED_PERCENT = "slo~achieved.percent";

    public static final String SLO_VIOLATED_PERCENT = "slo~violated.percent";

    public static final String SLO_VIOLATED_SECONDS = "slo~violated.seconds";

    public static final String SLO_STATUS_FLAP = "slo~status.flap";

    public static final String SLO_STATUS = "slo~status";

    public static final String SLO_ERROR_BUDGET_LEFT_PERCENT = "slo~error.budget.left.percent";

    public static final String SLO_ERROR_BUDGET_LEFT_SECONDS = "slo~error.budget.left.seconds";

    public static final String SLO_BURN_RATE_SECONDS = "slo~burn.rate.seconds";

    public static final String SLO_BURN_RATE_PERCENT = "slo~burn.rate.percent";

    public static final String SLO_MTTR_SECONDS = "slo~mttr.seconds";

    public static final String SLO_MTBF_SECONDS = "slo~mtbf.seconds";

    // ------------------------ Enums ------------------------ //

    public enum SLOFrequency
    {
        DAILY("Daily"),
        WEEKLY("Weekly"),
        MONTHLY("Monthly"),
        QUARTERLY("Quarterly");

        private final String name;

        SLOFrequency(String name)
        {
            this.name = name;
        }

        public String getName()
        {
            return name;
        }

        private static final Map<String, SLOFrequency> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(SLOFrequency::getName, e -> e)));

        public static SLOFrequency valueOfName(String name)
        {
            return VALUES.get(name);
        }
    }

    public enum SLOType
    {
        AVAILABILITY("Availability"),
        PERFORMANCE("Performance");

        private final String name;

        SLOType(String name)
        {
            this.name = name;
        }

        public String getName()
        {
            return name;
        }

        private static final Map<String, SLOType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(SLOType::getName, e -> e)));

        public static SLOType valueOfName(String name)
        {
            return VALUES.get(name);
        }
    }

    public enum SLOStatus
    {
        OK("Ok"),
        BREACHED("Breached"),
        WARNING("Warning");

        private final String name;

        SLOStatus(String name)
        {
            this.name = name;
        }

        public String getName()
        {
            return name;
        }

        private static final Map<String, SLOStatus> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(SLOStatus::getName, e -> e)));

        public static SLOStatus valueOfName(String name)
        {
            return VALUES.get(name);
        }
    }

    public enum SLOFlapStatus
    {
        HEALTHY("Healthy"),
        DEGRADED("Degraded"),
        NOT_CALCULATED("Not Calculated"),
        CORRECTED("Corrected");

        private final String name;

        SLOFlapStatus(String name)
        {
            this.name = name;
        }

        public String getName()
        {
            return name;
        }

        private static final Map<String, SLOFlapStatus> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(SLOFlapStatus::getName, e -> e)));

        public static SLOFlapStatus valueOfName(String name)
        {
            return VALUES.get(name);
        }
    }
}
