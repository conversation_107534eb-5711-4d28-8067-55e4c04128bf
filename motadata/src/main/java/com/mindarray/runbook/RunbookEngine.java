/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author			Notes
 *  11-Feb-2025		Chandresh		MOTADATA-4905: In case of ping check disabled added monitor id
 *  21-Feb-2025		Chandresh		MOTADATA-3680: Added OBJECT_ID and INSTANCE fields to worklog for instance runbook support
 *  24-Feb-2025		Chandresh		MOTADATA-3680: Refactored check for ENTITY_ID in complete method to ensure runbook completion logic works correctly
 *  26-Feb-2025     Chandresh       MOTADATA-5216: Runbook | Proper status and error message should be shown after testing any runbook
 *  28-Feb-2025		Darshan Parmar	MOTADATA-5215: SonarQube Suggestions Resolution
 *  03-Mar-2025     Chopra Deven    MOTADATA-4973: Dumping small object.id instead of long object.id into DB in case of runbook worklog
 *  03-Mar-2025     Chopra Deven    MOTADATA-4973: Dumping small object.id in DB and long object.id in cache in case of runbook worklog
 *  11-APR-2025     Sankalp         MOTADATA-5180: Added Ansible category in Runbook
 *  18-Apr-2025     Sankalp         MOTADATA-5859 : System user will be dumped for runbooks triggered by policy.
 *  18-Apr-2025     Vismit          MOTADATA-5613: Added decrementing total probe count logic for config.management.status off devices
 *  24-Jul-2025     Umang           MOTADATA-6754 : Add Type and severity for runbook.query.event
 */

package com.mindarray.runbook;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.config.ConfigConstants.TOTAL_PROBES;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * Core engine for executing and managing runbooks.
 * <p>
 * The RunbookEngine is responsible for:
 * <ul>
 *   <li>Processing runbook execution requests</li>
 *   <li>Managing runbook execution context and variables</li>
 *   <li>Routing runbook execution to appropriate handlers</li>
 *   <li>Storing and retrieving runbook execution results (worklogs)</li>
 *   <li>Handling notifications for runbook execution status</li>
 *   <li>Supporting high availability through synchronization</li>
 * </ul>
 * <p>
 * This engine handles different types of runbooks (SSH, PowerShell, HTTP, etc.)
 * and supports execution against various entity types (objects, groups, event sources).
 * It maintains a cache of execution results and periodically persists them to disk.
 */
public class RunbookEngine extends AbstractVerticle
{
    public static final String RUNBOOK_WORKLOG_DIR = "runbook-worklogs";
    private static final Logger LOGGER = new Logger(RunbookEngine.class, GlobalConstants.MOTADATA_RUNBOOK, "Runbook Engine");
    private static final String EVENT_SOURCE = "event.source";
    private static final String MANUAL_RUN = "manual.run";
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions();
    private final StringBuilder builder = new StringBuilder(0);
    private final Map<Long, Map<String, JsonObject>> worklogs = new HashMap<>();
    private Set<String> mappers;
    private EventEngine eventEngine;
    private boolean updated = false;
    private boolean dirty = false;
    private int cacheFlushTimer = MotadataConfigUtil.getRunbookWorklogCacheFlushTimerSeconds();

    /**
     * Initializes and starts the RunbookEngine.
     * <p>
     * This method:
     * <ul>
     *   <li>Loads cached runbook execution results</li>
     *   <li>Sets up periodic tasks to persist cache to disk</li>
     *   <li>Registers event bus handlers for runbook operations</li>
     *   <li>Sets up high availability synchronization</li>
     *   <li>Configures the event engine for processing runbook execution requests</li>
     * </ul>
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        var replyTopic = EventBusConstants.EVENT_RUNBOOK + EventBusConstants.EVENT_REPLY;

        loadCache();

        // dump the cached result
        vertx.setPeriodic(60 * 1000L, timer ->
        {
            try
            {
                if (updated)
                {
                    updated = false;

                    Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + RUNBOOK_WORKLOG_DIR,
                            Buffer.buffer(CodecUtil.compress(new JsonObject().put("items", worklogs).encode().getBytes())));

                    dirty = true;
                }

                cacheFlushTimer -= 60;

                if (cacheFlushTimer <= 0)
                {
                    Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + RUNBOOK_WORKLOG_DIR,
                            Buffer.buffer(CodecUtil.compress(new JsonObject().put("items", worklogs).encode().getBytes())));

                    dirty = true;

                    cacheFlushTimer = MotadataConfigUtil.getRunbookWorklogCacheFlushTimerSeconds();
                }
            }
            catch (Exception exception)
            {
                LOGGER.warn(exception);
            }
        });

        /*
        returns the cached result by runbook id and event source.
        if event contains runbook id and event source (monitor IP) then return last result of only that monitor
        else return last results of all monitors for that particular runbook
         */
        vertx.eventBus().<JsonObject>localConsumer(EVENT_RUNBOOK_WORKLOG_QUERY, message ->
        {
            try
            {
                var event = message.body();

                if (event != null && !event.isEmpty() && event.containsKey(ID))
                {
                    if (event.containsKey(EVENT_SOURCE))
                    {
                        var entityId = event.getLong(ID);

                        var eventSource = event.getString(EVENT_SOURCE);

                        var item = new JsonObject();

                        if (entityId != null && worklogs.containsKey(entityId))
                        {
                            var entity = worklogs.get(entityId);

                            if (entity != null && eventSource != null && entity.containsKey(eventSource))
                            {
                                item.mergeIn(entity.get(eventSource));
                            }
                        }

                        publish(event.getString(SESSION_ID), EVENT_RUNBOOK_WORKLOG_QUERY,
                                new JsonObject().put(EVENT_TYPE, EVENT_RUNBOOK_WORKLOG_QUERY)
                                        .put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID))
                                        .put(RESULT, item));
                    }
                    else
                    {
                        var entityId = event.getLong(ID);

                        var items = new JsonArray();

                        if (entityId != null && worklogs.containsKey(entityId))
                        {
                            for (var entry : worklogs.get(entityId).entrySet())
                            {
                                var item  = new JsonObject().mergeIn(entry.getValue()).put(EVENT_SOURCE, entry.getKey());

                                item.put(AIOpsObject.OBJECT_TYPE, ObjectConfigStore.getStore().getItem(item.getLong(AIOpsObject.OBJECT_ID)).getString(AIOpsObject.OBJECT_TYPE));

                                var severity = MetricPolicyCacheStore.getStore().getSeverity(item.getLong(AIOpsObject.OBJECT_ID));

                                item.put(SEVERITY, severity != null ? severity : Severity.UNKNOWN.name());

                                items.add(item);
                            }
                        }

                        publish(event.getString(SESSION_ID), EVENT_RUNBOOK_WORKLOG_QUERY,
                                new JsonObject().put(EVENT_TYPE, EVENT_RUNBOOK_WORKLOG_QUERY)
                                        .put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID))
                                        .put(RESULT, items));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.warn(exception);
            }
        });

        // for HA sync
        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            try
            {
                if (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_CACHE && dirty)
                {
                    dirty = false;

                    HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, RUNBOOK_WORKLOG_DIR).put(RESULT, vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + RUNBOOK_WORKLOG_DIR)));
                }
            }
            catch (Exception exception)
            {
                LOGGER.warn(exception);
            }
        }).exceptionHandler(LOGGER::error);

        eventEngine = new EventEngine().setEventType(EventBusConstants.EVENT_RUNBOOK).setPersistEventOffset(true)
                .setBlockingEvent(true).setLogger(LOGGER).setEventHandler(event ->
                {
                    try
                    {
                        var eventId = event.getLong(EventBusConstants.EVENT_ID);

                        // if runbook is executed directly from runbook settings screen, the ui will pass manual.run=yes
                        var manual = event.containsKey(MANUAL_RUN) && event.getString(MANUAL_RUN).equalsIgnoreCase(YES);

                        // if event is for testing the runbook then create a dummy runbook context else get the context from runbook config store
                        var runbook = event.containsKey(EventBusConstants.EVENT) && event.getString(EventBusConstants.EVENT).equalsIgnoreCase(EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_TEST)
                                ? new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, event.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME))
                                .put(ID, CommonUtil.newId()).put(RunbookPlugin.RUNBOOK_PLUGIN_TYPE, event.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE))
                                : RunbookPluginConfigStore.getStore().getItem(event.getLong(ID));

                        if (runbook != null)
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("request received to run runbook %s", runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME)));
                            }

                            runbook.mergeIn(event);

                            JsonObject runbookContext = null;

                            if (runbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT))
                            {
                                runbook.mergeIn(runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT));

                                runbookContext = runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);

                                runbook.remove(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);
                            }

                            var id = runbook.getLong(ID);

                            JsonArray objects = null;

                            JsonArray eventSources = null;

                            // MOTADATA-3429 :- merge in the proxy configuration in case of runbook contains a variable of proxy.enabled = yes
                            if (runbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES) && runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES).containsKey("proxy.enabled") && runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES).getString("proxy.enabled").equalsIgnoreCase(YES))
                            {
                                runbook.mergeIn(new JsonObject().put("proxy", ProxyServerConfigStore.getStore().getItem()));
                            }

                            var runbookPluginType = Runbook.RunbookPluginType.valueOfName(runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE));

                            EventBusConstants.startEvent(eventId, Thread.currentThread().getName());

                            if (runbookPluginType != Runbook.RunbookPluginType.ANSIBLE_PLAYBOOK)
                            {
                                var reply = event.containsKey(EventBusConstants.EVENT_REPLY) && event.getString(EventBusConstants.EVENT_REPLY).equalsIgnoreCase(YES);

                                // build the object context using the runbook.plugin.entities (in case of event source we'll not merge any object context as the monitor with the ip address might be not provisioned)
                                if (runbook.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES) != null && !runbook.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES).isEmpty())
                                {
                                    if (runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                                    {
                                        objects = ObjectConfigStore.getStore().getItems(runbook.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES));
                                    }

                                    else if (runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                                    {
                                        var items = ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getItemsByGroups(runbook.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES)));

                                        if (items != null)
                                        {
                                            var types = Runbook.getObjectTypesByRunbookPluginType(runbookPluginType);

                                            objects = new JsonArray();

                                            for (var index = 0; index < items.size(); index++)
                                            {
                                                // for custom script types is null and for powershell, ssh, jdbc and snmp scripts will qualify only same objects types
                                                if (types == null || types.contains(items.getJsonObject(index).getString(AIOpsObject.OBJECT_TYPE)))
                                                {
                                                    objects.add(items.getJsonObject(index));
                                                }
                                            }
                                        }
                                    }

                                    else if (runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(EVENT_SOURCE))
                                    {
                                        eventSources = new JsonArray();

                                        for (var entity : runbook.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES))
                                        {
                                            eventSources.add(new JsonObject().put(AIOpsObject.OBJECT_TARGET, entity).put(AIOpsObject.OBJECT_IP, entity));
                                        }
                                    }
                                }

                                // used while sending the mail (required specially for runbook.plugin.entity.type=GROUP)
                                if (runbook.containsKey(EventBusConstants.EVENT_SCHEDULER))
                                {
                                    runbook.put(RunbookPlugin.RUNBOOK_PLUGIN_OBJECTS, objects != null && !objects.isEmpty() ? objects.size() : eventSources.size());
                                }

                                var futures = new ArrayList<Future<JsonObject>>();

                                // ignore if runbook is already running
                                if (!runbook.containsKey(EventBusConstants.EVENT_SCHEDULER) || RunbookCacheStore.getStore().getItem(id) == NOT_AVAILABLE) // ignore if already running or execute outside from runbook (RCA, Topology, Policy)
                                {
                                    if (objects != null && !objects.isEmpty())
                                    {
                                        RunbookCacheStore.getStore().updateItem(id, objects.size());

                                        // if manually run from runbook setting screen then clear all past results
                                        if (manual)
                                        {
                                            updated = true;

                                            worklogs.remove(id);
                                        }

                                        if (runbook.containsKey(EventBusConstants.EVENT_SCHEDULER) || runbook.containsKey(EventBusConstants.UI_EVENT_UUID)) // for scheduler runbook and manually run runbook only send state change event to ui side
                                        {
                                            EventBusConstants.publish(EventBusConstants.EVENT_RUNBOOK_STATE_CHANGE, new JsonObject().put(ID, id).put(NMSConstants.STATE, NMSConstants.STATE_RUNNING));
                                        }

                                        for (var index = 0; index < objects.size(); index++)
                                        {
                                            var future = Promise.<JsonObject>promise();

                                            futures.add(future.future());

                                            var object = objects.getJsonObject(index);

                                            if (object.containsKey(AIOpsObject.OBJECT_CONTEXT))
                                            {
                                                object.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                                                // it will override the existing port and timeout context if explicitly provided in the runbook
                                                if (runbookContext != null)
                                                {
                                                    object.mergeIn(runbookContext);
                                                }

                                                object.remove(AIOpsObject.OBJECT_CONTEXT);
                                            }

                                            if (runbookPluginType == Runbook.RunbookPluginType.PING)
                                            {
                                                var pingProbe = true;

                                                var availabilityProbe = new JsonObject().mergeIn(runbook).mergeIn(object).put(USER_NAME, runbook.getString(USER_NAME, DEFAULT_USER));

                                                availabilityProbe.put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, CommonUtil.isNotNullOrEmpty(event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST)) ? event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST)
                                                                : PluginEngineConstants.PluginEngineRequest.PING.getName())
                                                        .put(Metric.METRIC_OBJECT, object.getLong(ID))
                                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE)
                                                        .put(EventBusConstants.EVENT_REPLY, YES)
                                                        .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId())
                                                        .put(AIOpsObject.OBJECT_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.REMOTE.name()) // for runbook ping of agent monitor -> we will ping it from remote device instead sending event to agent hence need this condition
                                                        .put(AIOpsConstants.ENTITY_ID, id); //runbook id

                                                if (object.containsKey(AIOpsObject.OBJECT_AGENT))
                                                {
                                                    var item = AgentConfigStore.getStore().getItem(object.getLong(AIOpsObject.OBJECT_AGENT));

                                                    pingProbe = false;

                                                    if (item != null)
                                                    {
                                                        route(availabilityProbe, reply, manual).onComplete(result -> future.complete(result.result()));
                                                    }
                                                }

                                                if (pingProbe)
                                                {
                                                    if ((object.containsKey(AIOpsObject.OBJECT_TYPE) && object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.PING.getName()))
                                                            || (object.containsKey(NMSConstants.PING_CHECK_STATUS) && object.getString(NMSConstants.PING_CHECK_STATUS).equalsIgnoreCase(YES)))
                                                    {
                                                        route(availabilityProbe, reply, manual).onComplete(result -> future.complete(result.result()));
                                                    }
                                                    else
                                                    {
                                                        // TODO : check the scenario
                                                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                .put(MESSAGE, String.format("failed to run runbook %s for monitor %s, reason: %s", runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME), object.getString(AIOpsObject.OBJECT_IP), "ping check is disabled"));

                                                        future.complete(availabilityProbe.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                .put(MESSAGE, String.format("failed to run runbook %s for monitor %s, reason: %s", runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME), object.getString(AIOpsObject.OBJECT_IP), "ping check is disabled")));

                                                        complete(availabilityProbe.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                .put(MESSAGE, String.format("failed to run runbook %s for monitor %s, reason: %s", runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME), object.getString(AIOpsObject.OBJECT_IP), "ping check is disabled")), manual);

                                                        LOGGER.warn(event.getString(MESSAGE));
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                if (runbook.containsKey(PORT))
                                                {
                                                    object.put(PORT, runbook.getInteger(PORT));
                                                }

                                                var context = new JsonObject().mergeIn(runbook).mergeIn(object).put(USER_NAME, runbook.getString(USER_NAME, DEFAULT_USER));

                                                enrich(context, runbook);
                                                // for HTTP Script/, Database Script, Custom Script we have metric.type so based on metric type build metric context else build based on object type

                                                context.put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, PluginEngineConstants.PluginEngineRequest.RUNBOOK.getName())
                                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE)
                                                        .put(EventBusConstants.EVENT_REPLY, YES)
                                                        .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId())
                                                        .put(AIOpsConstants.ENTITY_ID, id); //runbook id

                                                // verifying runbook eligibility based on specified conditions for a particular category
                                                if (qualify(context, runbook, future))
                                                {
                                                    // if object is disabled or down then don't execute runbook (the check is removed due to MOTADATA-3428)
                                                    route(context, reply, manual).onComplete(result -> future.complete(result.result()));
                                                }
                                            }
                                        }
                                    }

                                    else if (eventSources != null && !eventSources.isEmpty())
                                    {
                                        RunbookCacheStore.getStore().updateItem(id, eventSources.size());

                                        if (runbook.containsKey(EventBusConstants.EVENT_SCHEDULER) || runbook.containsKey(EventBusConstants.UI_EVENT_UUID)) // for scheduler runbook and manually run runbook only send state change event to ui side
                                        {
                                            EventBusConstants.publish(EventBusConstants.EVENT_RUNBOOK_STATE_CHANGE, new JsonObject().put(ID, id).put(NMSConstants.STATE, NMSConstants.STATE_RUNNING));
                                        }

                                        for (var index = 0; index < eventSources.size(); index++)
                                        {
                                            var source = eventSources.getJsonObject(index);

                                            if (runbook.containsKey(PORT))
                                            {
                                                source.put(PORT, runbook.getInteger(PORT));
                                            }

                                            var context = new JsonObject().mergeIn(runbook).mergeIn(source).put(USER_NAME, runbook.getString(USER_NAME, DEFAULT_USER));

                                            var credential = runbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CREDENTIAL_PROFILE) ?
                                                    CredentialProfileConfigStore.getStore().getItem(runbook.getLong(RunbookPlugin.RUNBOOK_PLUGIN_CREDENTIAL_PROFILE)) : null;

                                            if (credential != null)
                                            {
                                                if (credential.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                                {
                                                    credential.mergeIn(credential.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                                    credential.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                                }

                                                credential.remove(ID);

                                                context.mergeIn(credential);
                                            }

                                            context.put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, PluginEngineConstants.PluginEngineRequest.RUNBOOK.getName())
                                                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE)
                                                    .put(EventBusConstants.EVENT_REPLY, YES)
                                                    .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId())
                                                    .put(AIOpsObject.OBJECT_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.REMOTE.name())
                                                    .put(AIOpsConstants.ENTITY_ID, id); //runbook id

                                            var future = Promise.<JsonObject>promise();

                                            futures.add(future.future());

                                            route(context, reply, manual).onComplete(result -> future.complete(result.result()));
                                        }
                                    }
                                    else
                                    {
                                        event.put(STATUS, STATUS_FAIL)
                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                .put(MESSAGE, String.format("failed to run runbook %s, reason: object(s) not qualified", runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME)));

                                        LOGGER.warn(event.getString(MESSAGE));
                                    }
                                }
                                else
                                {
                                    event.put(STATUS, STATUS_FAIL)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(MESSAGE, String.format("failed to run runbook %s, reason: already running", runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME)));

                                    LOGGER.warn(event.getString(MESSAGE));
                                }

                                Future.join(futures).onComplete(result ->
                                {
                                    if (reply)
                                    {
                                        event.put(EventBusConstants.EVENT_REPLY_CONTEXTS, new JsonArray(result.result().list()));
                                    }

                                    vertx.eventBus().send(replyTopic, event);
                                });
                            }
                        }
                        else
                        {
                            LOGGER.warn(String.format("failed to run runbook %s, reason: %s", event.getLong(ID), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.RUNBOOK_PLUGIN.getName())));

                            vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_ABORT).put(MESSAGE, String.format(ErrorMessageConstants.RUNBOOK_EXECUTION_FAILED, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.RUNBOOK_PLUGIN.getName())))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        vertx.eventBus().send(replyTopic, event);
                    }
                }).start(vertx, promise);
    }

    /**
     * Enriches the runbook execution context with additional information.
     * <p>
     * This method enhances the runbook execution context by adding:
     * <ul>
     *   <li>Metric configuration data based on runbook category</li>
     *   <li>Configuration data for CONFIG category runbooks</li>
     *   <li>Credential profile information</li>
     * </ul>
     * <p>
     * The enrichment process varies based on the runbook category and ensures
     * that all necessary information is available for runbook execution.
     *
     * @param context The execution context to be enriched
     * @param runbook The runbook configuration containing category and other details
     */
    private void enrich(JsonObject context, JsonObject runbook)
    {
        var runbookCategory = Runbook.RunbookCategory.valueOfName(runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY));

        JsonObject item = null;

        JsonObject credentialProfile = null;

        switch (runbookCategory)
        {
            case DIAGNOSTICS, TROUBLESHOOTING, INTEGRATION, LOG_COLLECTION, SYSTEM_MANAGEMENT, PERFORMANCE_MONITORING,
                 STORAGE_PROFILE, ANSIBLE_PLAYBOOK, OTHER ->
            {
                item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(context.getLong(ID),
                        NMSConstants.getMetricPlugin(NMSConstants.Type.valueOfName(context.containsKey(Metric.METRIC_TYPE) ? context.getString(Metric.METRIC_TYPE)
                                : context.getString(AIOpsObject.OBJECT_TYPE)))));

                if (item != null && item.containsKey(Metric.METRIC_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(Metric.METRIC_CONTEXT));

                    item.remove(Metric.METRIC_CONTEXT);
                }

                if (runbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CREDENTIAL_PROFILE))
                {
                    credentialProfile = CredentialProfileConfigStore.getStore().getItem(runbook.getLong(RunbookPlugin.RUNBOOK_PLUGIN_CREDENTIAL_PROFILE));
                }
                else
                {
                    credentialProfile = item != null ? CredentialProfileConfigStore.getStore().getItem(item.getLong(Metric.METRIC_CREDENTIAL_PROFILE)) : null;
                }
            }

            case CONFIG ->
            {
                // retrieving the config object using the object ID to obtain the config ID for further processing in ConfigResponseProcessor
                item = ConfigurationConfigStore.getStore().getItem(ConfigurationConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getObjectId(context.getLong(ID))));

                item.mergeIn(item.getJsonObject(Configuration.CONFIG_CONTEXT));

                item.remove(Configuration.CONFIG_CONTEXT);

                // send the timestamp to the remote event router, so we can use in plugin engine response process
                item.put(EVENT_TIMESTAMP, item.containsKey(EVENT_TIMESTAMP) ? item.getLong(EVENT_TIMESTAMP) : DateTimeUtil.currentSeconds());

                credentialProfile = CredentialProfileConfigStore.getStore().getItem(item.getLong(Configuration.CONFIG_CREDENTIAL_PROFILE));

                EventBusConstants.publish(EventBusConstants.EVENT_CONFIG_REQUEST_STATE_CHANGE, new JsonObject().put(ConfigConstants.CONFIG_OPERATION, ConfigConstants.ConfigOperation.RUNBOOK.getName()).put(ID, item.getLong(ID)).put(NMSConstants.STATE, NMSConstants.STATE_RUNNING));
            }
            default ->
            {
                // do nothing
            }
        }


        if (item != null)
        {
            // need to remove id here as it's overriding the object's id
            item.remove(ID);

            if (runbook.containsKey(PORT))
            {
                item.remove(PORT); // we will use runbook port for runbook event so need to remove monitor metric port from here
            }

            item.remove(TIMEOUT); // we will use runbook timeout for runbook event so need to remove monitor metric timeout from here

            context.mergeIn(item);
        }

        if (credentialProfile != null)
        {
            if (credentialProfile.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
            {
                credentialProfile.mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
            }

            credentialProfile.remove(ID);

            context.mergeIn(credentialProfile);
        }
    }

    /**
     * Routes a runbook execution request to the appropriate handler.
     * <p>
     * This method:
     * <ul>
     *   <li>Prepares the execution context with necessary parameters</li>
     *   <li>Sets appropriate timeouts for the execution</li>
     *   <li>Routes the request to the plugin engine for execution</li>
     *   <li>Handles notifications if required</li>
     * </ul>
     *
     * @param event  The runbook execution context
     * @param notify Whether to send notifications about the execution
     * @param manual Whether the runbook is being executed manually
     * @return A Future containing the execution result
     */
    private Future<JsonObject> route(JsonObject event, boolean notify, boolean manual)
    {
        var promise = Promise.<JsonObject>promise();

        if (event.getString(NMSConstants.URL_ENDPOINT) != null)
        {
            event.put(AIOpsObject.OBJECT_TARGET, event.getString(AIOpsObject.OBJECT_TARGET) + event.getString(NMSConstants.URL_ENDPOINT));
        }

        // if event contains timeout then use the same timout for both timeout and request timeout else set default timeout
        if (event.getValue(TIMEOUT) != null)
        {
            event.put(REQUEST_TIMEOUT, event.getLong(TIMEOUT));
        }
        else
        {
            event.put(TIMEOUT, 60L);
        }


        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("runbook context : %s", CommonUtil.removeSensitiveFields(event, true)));
        }

        vertx.eventBus().send(EventBusConstants.EVENT_ADD,
                new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID))
                        .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_PLUGIN_ENGINE));

        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("runbook %s executing on object %s", event.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME), event.getString(AIOpsObject.OBJECT_NAME)));
        }

        // for topology next-hop and availability runbook we need to reply result...for other runbook no need to wait to complete runbook result
        if (!notify)
        {
            promise.complete(event);
        }

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_ROUTER, event,
                event.containsKey(REQUEST_TIMEOUT) && event.getLong(REQUEST_TIMEOUT) > 0 ? DELIVERY_OPTIONS.setSendTimeout(TimeUnit.SECONDS.toMillis(event.getLong(REQUEST_TIMEOUT))) : DELIVERY_OPTIONS.setSendTimeout(300000L),
                reply ->
                {
                    try
                    {
                        if (reply.succeeded())
                        {
                            var response = reply.result().body();

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("runbook result : %s", response != null ? CommonUtil.removeSensitiveFields(response, true) : null));
                            }

                            // publish this event for each ncm device inorder to reflect this change in ncm explorer
                            if (event.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY) && event.getString(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY).equalsIgnoreCase(Runbook.RunbookCategory.CONFIG.getName()))
                            {
                                EventBusConstants.publish(EventBusConstants.EVENT_CONFIG_REQUEST_STATE_CHANGE, new JsonObject().put(ConfigConstants.CONFIG_OPERATION, ConfigConstants.ConfigOperation.RUNBOOK.getName()).put(ID, ConfigurationConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getObjectId(event.getLong(ID)))).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING));
                            }

                            complete(response, manual);

                            if (notify)
                            {
                                if (reply.succeeded())
                                {
                                    if (response != null && response.getString(STATUS) != null && !response.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                    {
                                        ErrorMessageConstants.extractErrorCode(response, ErrorMessageConstants.RUNBOOK_EXECUTION_FAILED, null, null, null);
                                    }

                                    promise.complete(response);
                                }

                                else
                                {
                                    ErrorMessageConstants.extractErrorCode(event.put(ERRORS, new JsonArray(new ArrayList<Throwable>(1)).add(reply.cause())), ErrorMessageConstants.RUNBOOK_EXECUTION_FAILED, null, null, null);

                                    promise.complete(event);
                                }
                            }
                        }
                        else
                        {
                            LOGGER.warn(reply.cause().getMessage());

                            event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, reply.cause().getMessage());

                            if (!promise.future().isComplete())
                            {
                                promise.complete(event);
                            }

                            complete(event, manual);
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, CommonUtil.formatStackTrace(exception.getStackTrace()));

                        if (!promise.future().isComplete())
                        {
                            promise.complete(event);
                        }

                        complete(event, manual);
                    }
                });

        return promise.future();
    }

    /**
     * Handles the completion of a runbook execution.
     * <p>
     * This method:
     * <ul>
     *   <li>Updates the count of pending executions for a runbook</li>
     *   <li>Publishes completion events when all executions are complete</li>
     *   <li>Updates the last execution time for manually run runbooks</li>
     *   <li>Processes and stores execution results and errors</li>
     *   <li>Creates and stores worklog entries for the execution</li>
     * </ul>
     *
     * @param event  The runbook execution result
     * @param manual Whether the runbook was executed manually
     */
    private void complete(JsonObject event, boolean manual)
    {
        if (event != null && !event.isEmpty() && event.containsKey(AIOpsConstants.ENTITY_ID))
        {
            var entityId = event.getLong(AIOpsConstants.ENTITY_ID, 0L);

            var count = RunbookCacheStore.getStore().getItem(entityId) - 1;

            RunbookCacheStore.getStore().updateItem(event.getLong(AIOpsConstants.ENTITY_ID), count);

            if (count <= 0) //complete runbook
            {
                if (event.containsKey(EventBusConstants.EVENT_SCHEDULER) || event.containsKey(EventBusConstants.UI_EVENT_UUID)) // for scheduler runbook and manually run runbook only send complete event to ui side
                {
                    //publish runbook completion event to UI
                    EventBusConstants.publish(EventBusConstants.EVENT_RUNBOOK_STATE_CHANGE, new JsonObject().put(ID, event.getLong(AIOpsConstants.ENTITY_ID)).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING));

                    if (event.containsKey(EventBusConstants.EVENT_SCHEDULER))
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_SCHEDULER_COMPLETE, event);
                    }
                }

                RunbookCacheStore.getStore().complete(event.getLong(AIOpsConstants.ENTITY_ID));

                if (manual)
                {
                    // update the last executed time inorder to notify ui that if the runbook is executed in past or not!
                    Bootstrap.configDBService().update(DBConstants.TBL_RUNBOOK_PLUGIN,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, entityId),
                            new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_LAST_EXECUTION_TIME, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())),
                            event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                            event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                            {
                                if (result.succeeded())
                                {
                                    RunbookPluginConfigStore.getStore().updateItem(entityId);

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("updated runbook plugin : %s", entityId));
                                    }
                                }
                                else
                                {
                                    LOGGER.warn(String.format("failed to update runbook plugin : %s ", entityId));
                                }
                            });
                }
            }

            if (!event.containsKey(EventBusConstants.EVENT) || !event.getString(EventBusConstants.EVENT).equalsIgnoreCase(EventBusConstants.UI_ACTION_RUNBOOK_PLUGIN_TEST))
            {
                builder.setLength(0);

                if (event.containsKey(RESULT) && event.getValue(RESULT) != null)
                {
                    if (event.getValue(RESULT) instanceof JsonArray)
                    {
                        builder.append(event.getJsonArray(RESULT).encodePrettily());
                    }
                    else if (event.getValue(RESULT) instanceof JsonObject)
                    {
                        builder.append(event.getJsonObject(RESULT).encodePrettily());
                    }
                    else
                    {
                        builder.append(event.getString(RESULT));
                    }
                }

                if (builder.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES)
                {
                    builder.setLength(RUNBOOK_WORKLOG_RESULT_MAX_BYTES);
                }

                var error = EMPTY_VALUE;

                if (!event.getString(STATUS, EMPTY_VALUE).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    event.put(STATUS, STATUS_FAIL);

                    error = new JsonObject()
                            .put(ERROR_CODE, event.getString(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR))
                            .put(MESSAGE, event.getString(MESSAGE, String.format(ErrorMessageConstants.RUNBOOK_PLUGIN_FAILED, UNKNOWN))).encodePrettily();
                }
                else
                {
                    error = event.getString(ERROR, EMPTY_VALUE);
                }

                if (error.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES)
                {
                    error = error.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES);
                }

                var worklog = new JsonObject().put(RUNBOOK_WORKLOG_STATUS, event.getValue(STATUS, STATUS_FAIL))
                        .put(RUNBOOK_WORKLOG_RESULT, builder.toString())
                        .put(RUNBOOK_WORKLOG_ID, entityId)
                        .put(EVENT_TIMESTAMP, event.containsKey(EVENT_TIMESTAMP) ? event.getLong(EVENT_TIMESTAMP) * 1000 : DateTimeUtil.currentMilliSeconds()) // as db sends timestamp in millisecond, need to convert it here for caching
                        .put(USER_NAME, event.containsKey(POLICY_KEY) ? SYSTEM_USER : event.getString(USER_NAME, SYSTEM_USER))
                        .put(RUNBOOK_WORKLOG_ERROR, error)
                        .put(EVENT_SOURCE, event.containsKey(AIOpsObject.OBJECT_IP) ? event.getString(AIOpsObject.OBJECT_IP) : SYSTEM_REMOTE_ADDRESS)
                        .put(AIOpsObject.OBJECT_ID, event.containsKey(ID) ? event.getLong(ID) : 0)
                        .put(INSTANCE, event.getString(INSTANCE, EMPTY_VALUE));

                // if manually run from runbook settings screen then only cache the result
                if (manual)
                {
                    updated = true;

                    worklogs.computeIfAbsent(entityId, value -> new HashMap<>())
                            .computeIfAbsent(worklog.getString(EVENT_SOURCE), value -> new JsonObject()).mergeIn(worklog);
                }

                if (event.containsKey(EventBusConstants.UI_EVENT_UUID))
                {
                    publish(event.getString(SESSION_ID), EVENT_RUNBOOK_WORKLOG_QUERY,
                            new JsonObject().put(EVENT_TYPE, EVENT_RUNBOOK_WORKLOG_QUERY)
                                    .put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID))
                                    .put(RESULT, worklog));
                }

                if (event.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS))// have to notify users....
                {
                    var fileName = event.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME, EMPTY_VALUE) + DASH_SEPARATOR + event.getString(AIOpsObject.OBJECT_IP) + DASH_SEPARATOR + DateTimeUtil.currentSeconds() + ".txt";

                    vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName, Buffer.buffer(builder.toString()));

                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EMAIL_NOTIFICATION, new JsonObject()
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("information.png").add(fileName).addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                    .put(Notification.EMAIL_NOTIFICATION_SUBJECT, event.getString(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT, "Executed Runbook"))
                                    .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, event.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS))
                                    .put(Notification.TEMPLATE_NAME, Notification.EMAIL_RUNBOOK_NOTIFICATION_HTML_TEMPLATE)
                                    .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, event.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME, EMPTY_VALUE)).put(TIME_STAMP, DateTimeUtil.timestamp(event.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, event.getString(POLICY_NAME, EMPTY_VALUE)).put(SEVERITY, event.getString(SEVERITY, EMPTY_VALUE).toLowerCase())), DELIVERY_OPTIONS,
                            reply ->
                                    vertx.fileSystem().deleteBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName));
                }

                DatastoreConstants.write(worklog.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.RUNBOOK.ordinal())
                                .put(AIOpsObject.OBJECT_ID, event.containsKey(ID) ? ObjectConfigStore.getStore().getObjectId(event.getLong(ID)) : 0)
                                .put(POLICY_ID, event.containsKey(POLICY_ID) ? event.getLong(POLICY_ID) : 0)
                                .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName())
                                .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal())
                                .put(EVENT_TIMESTAMP, event.containsKey(EVENT_TIMESTAMP) ? event.getLong(EVENT_TIMESTAMP) : DateTimeUtil.currentSeconds()), // overriding timestamp here as we need to dump timestamp in seconds in db
                        VisualizationConstants.VisualizationDataSource.RUNBOOK_WORKLOG.getName(), mappers, builder);
            }
        }
    }

    /**
     * Determines if the runbook qualifies for execution based on its category and the provided context.
     * <p>
     * This method:
     * <ul>
     *   <li>Checks if the runbook category is CONFIG and verifies the configuration management status</li>
     *   <li>Handles failure cases by publishing appropriate events and completing the promise</li>
     *   <li>Updates probe counts for devices with configuration management disabled</li>
     * </ul>
     *
     * @param context The context containing configuration details
     * @param runbook The runbook information, including its category
     * @param promise Promise to be completed with the result if qualification fails
     * @return true if the runbook qualifies for execution; false otherwise
     */
    private boolean qualify(JsonObject context, JsonObject runbook, Promise<JsonObject> promise)
    {
        if (Objects.requireNonNull(Runbook.RunbookCategory.valueOfName(runbook.getString(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY))) == Runbook.RunbookCategory.CONFIG)
        {
            if (context.getString(Configuration.CONFIG_MANAGEMENT_STATUS, EMPTY_VALUE).equalsIgnoreCase(YES))
            {
                return true;
            }
            else
            {
                // publish this event for each ncm device inorder to reflect this change in ncm explorer
                publish(EVENT_CONFIG_REQUEST_STATE_CHANGE, new JsonObject().put(ConfigConstants.CONFIG_OPERATION, ConfigConstants.ConfigOperation.RUNBOOK.getName()).put(ID, ConfigurationConfigStore.getStore().getIdByObjectId(ObjectConfigStore.getStore().getObjectId(context.getLong(ID)))).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING));

                promise.complete(new JsonObject()
                        .put(STATUS, STATUS_FAIL)
                        .put(ERRORS, new JsonArray().add(new JsonObject()
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                .put(MESSAGE, ErrorMessageConstants.OBJECT_ERROR))));

                complete(context.put(STATUS, STATUS_FAIL)
                        .put(ERROR, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                .put(MESSAGE, String.format(ErrorMessageConstants.RUNBOOK_PLUGIN_FAILED, ErrorMessageConstants.OBJECT_ERROR)).encodePrettily()), context.containsKey(MANUAL_RUN) && context.getString(MANUAL_RUN).equalsIgnoreCase(YES));

                ConfigConstants.notify(context, TOTAL_PROBES, -1); // Decrease total probe count for config.management.status off devices by 1 per device in case of runbook

                return false;
            }
        }
        return true;
    }

    /**
     * Stops the RunbookEngine and releases resources.
     * <p>
     * This method stops the event engine used for processing runbook execution requests.
     *
     * @param promise Promise to be completed when shutdown is done
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {

        eventEngine.stop(vertx, promise);
    }

    /**
     * Loads cached runbook execution results from disk.
     * <p>
     * This method:
     * <ul>
     *   <li>Reads the cached runbook worklogs from disk</li>
     *   <li>Decompresses and deserializes the data</li>
     *   <li>Populates the in-memory worklog cache</li>
     * </ul>
     * <p>
     * If the cache file doesn't exist or is empty, the method silently returns
     * without loading anything.
     */
    private void loadCache()
    {
        try
        {
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + RUNBOOK_WORKLOG_DIR);

            if (file.exists())
            {
                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                    ((Map<String, Map<String, Object>>) (context.get("items"))).forEach((key, value) ->
                            value.forEach((k, v) ->
                                    worklogs.computeIfAbsent(CommonUtil.getLong(key), val -> new HashMap<>())
                                            .computeIfAbsent(k, val -> new JsonObject())
                                            .mergeIn(JsonObject.mapFrom(v))));

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("results: %s", worklogs));
                    }

                }
            }
            else
            {
                Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
            }
        }
        catch (Exception exception)
        {
            LOGGER.warn(exception);
        }
    }
}
