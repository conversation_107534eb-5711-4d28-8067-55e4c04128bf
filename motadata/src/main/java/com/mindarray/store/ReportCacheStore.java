/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.eventbus.EventBusConstants;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.GlobalConstants.PROGRESS;

public class ReportCacheStore extends AbstractCacheStore
{
    private static final ReportCacheStore STORE = new ReportCacheStore();
    private final Map<Long, JsonObject> items = new ConcurrentHashMap<>();

    private ReportCacheStore()
    {
    }

    public static ReportCacheStore getStore()
    {
        return STORE;
    }

    public void updateItem(long id, JsonObject progress)
    {
        this.items.put(id, progress);

        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_REPORT_PROGRESS, progress.put(ID, id));

        if (progress.getLong(PROGRESS) == 100)
        {
            complete(id);
        }
    }

    public JsonObject getItem(long id)
    {
        return this.items.get(id);
    }

    public void complete(long id)
    {
        if (removeItem(id) != null)
        {
            EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_REPORT_PROGRESS, new JsonObject().put(ID, id));
        }
    }

    public JsonObject removeItem(long id)
    {
        return this.items.remove(id);
    }


    public boolean isRunning(long id)
    {
        return this.items.containsKey(id);
    }
}
