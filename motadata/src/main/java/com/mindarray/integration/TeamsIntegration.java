/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			    Notes
 *  28-Feb-2025		Bharat Chaudhari    MOTADATA-5352: promise complete added.
 */

package com.mindarray.integration;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.Integration;
import com.mindarray.api.IntegrationProfile;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.IntegrationConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.WebClientUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.ErrorMessageConstants.INTEGRATION_ATTRIBUTE_SYNC_FAILED;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Integration.*;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * Integration class for Microsoft Teams notification system.
 * <p>
 * This class provides functionality to:
 * - Synchronize Teams and channels information from Microsoft Teams
 * - Handle authentication with Microsoft Teams using OAuth tokens
 * - Provide endpoints for sending notifications to Microsoft Teams channels
 * - Support both automatic and manual synchronization of Teams data
 * <p>
 * Unlike other integration classes that focus on ticket management,
 * this integration is primarily used for sending notifications to Teams channels.
 * It uses Microsoft Graph API to communicate with Teams.
 */
public class TeamsIntegration extends AbstractVerticle
{
    public static final String TEAMS = "teams";
    public static final String CHANNELS = "channels";
    public static final String TEAM = "team";
    public static final String CHANNEL = "channel";
    public static final String MESSAGE_END_POINT = "teams/%s/channels/%s/messages";
    private static final Logger LOGGER = new Logger(TeamsIntegration.class, INTEGRATION_DIR, "Teams Integration");
    private final AtomicBoolean SYNC_FLAG = new AtomicBoolean();
    private Long timerId;
    private JsonObject integration;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            init();

            vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
            {
                if (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_INTEGRATION)
                {
                    try
                    {
                        var event = message.body();

                        if (event.containsKey(ID) && event.getValue(ID) != null
                                && event.getLong(ID).equals(IntegrationConstants.IntegrationId.MICROSOFT_TEAMS.getName()))
                        {
                            init();
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            });

            // manual sync integration attributes
            vertx.eventBus().<JsonObject>localConsumer(IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName() + DOT_SEPARATOR + EVENT_INTEGRATION_SYNC, message ->
            {
                try
                {
                    var event = message.body();

                    if (event.containsKey(ID) && event.getValue(ID) != null
                            && event.getLong(ID).equals(IntegrationConstants.IntegrationId.MICROSOFT_TEAMS.getName()))
                    {
                        sync().onComplete(reply ->
                        {
                            if (reply.succeeded())
                            {
                                message.reply(reply.result());
                            }
                            else
                            {
                                message.fail(HttpStatus.SC_BAD_REQUEST, reply.cause().getMessage());
                            }
                        });
                    }
                    else
                    {
                        message.fail(HttpStatus.SC_BAD_REQUEST, String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), ErrorMessageConstants.INTEGRATION_IS_NOT_CONFIGURED));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, exception.getMessage());

                }
            });

            promise.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

    }

    /**
     * Initializes the Microsoft Teams integration.
     * <p>
     * This method:
     * 1. Loads the Teams integration configuration from the store
     * 2. Sets up periodic synchronization if auto-sync is enabled:
     * - Cancels any existing sync timer
     * - Creates a new timer with the configured interval
     * 3. Disables periodic synchronization if auto-sync is disabled
     * <p>
     * The synchronization process keeps Teams and channels information up-to-date
     * for sending notifications to the correct destinations.
     */
    private void init()
    {
        try
        {
            integration = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.MICROSOFT_TEAMS.getName());

            if (integration != null && integration.containsKey(Integration.INTEGRATION_CONTEXT))
            {
                var context = integration.getJsonObject(INTEGRATION_CONTEXT);

                if (context.containsKey(Integration.AUTO_SYNC)
                        && context.getString(Integration.AUTO_SYNC).equalsIgnoreCase(YES)
                        && context.containsKey(Integration.SYNC_INTERVAL))
                {
                    if (timerId != null)
                    {
                        vertx.cancelTimer(timerId);
                    }

                    timerId = vertx.setPeriodic(TimeUnit.HOURS.toMillis(context.getInteger(SYNC_INTERVAL, 8)), timer -> sync());
                }
                else if (context.containsKey(AUTO_SYNC) && context.getString(AUTO_SYNC).equalsIgnoreCase(NO))
                {
                    if (timerId != null)
                    {
                        vertx.cancelTimer(timerId);
                    }
                }
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Synchronizes Teams and channels information from Microsoft Teams.
     * <p>
     * This method:
     * 1. Ensures only one synchronization job runs at a time using the SYNC_FLAG
     * 2. Retrieves the integration configuration
     * 3. Initiates the actual synchronization by calling the overloaded sync method
     * 4. Updates the integration configuration in the database when synchronization completes
     * 5. Handles various error scenarios:
     * - Integration not configured
     * - Synchronization already running
     * - Exceptions during synchronization
     *
     * @return A Future that completes with the synchronization result or fails with an error message
     */
    private Future<JsonObject> sync()
    {
        var promise = Promise.<JsonObject>promise();
        try
        {
            if (!SYNC_FLAG.get())
            {
                LOGGER.info("Sync job started!");

                SYNC_FLAG.set(true);

                if (integration != null && !integration.isEmpty()
                        && integration.containsKey(INTEGRATION_CONTEXT) && !integration.getJsonObject(INTEGRATION_CONTEXT).isEmpty())
                {
                    var context = integration.getJsonObject(INTEGRATION_CONTEXT);

                    sync(context.getString(TARGET, EMPTY_VALUE), context, integration.getJsonObject(INTEGRATION_ATTRIBUTES)).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            Bootstrap.configDBService().update(DBConstants.TBL_INTEGRATION,
                                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, integration.getLong(ID)),
                                    integration, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                                    {
                                        SYNC_FLAG.set(false);

                                        if (asyncResult.succeeded())
                                        {
                                            IntegrationConfigStore.getStore().updateItem(integration.getLong(ID));

                                            promise.complete(result.result());
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResult.cause());

                                            promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), asyncResult.cause()));
                                        }
                                    });
                        }
                        else
                        {
                            SYNC_FLAG.set(false);

                            promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), result.cause()));
                        }
                    });
                }
                else
                {
                    SYNC_FLAG.set(false);

                    promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), ErrorMessageConstants.INTEGRATION_IS_NOT_CONFIGURED));
                }
            }
            else
            {
                LOGGER.warn("Sync job is already running!");

                promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), ErrorMessageConstants.INTEGRATION_ATTRIBUTE_SYNC_JOB_ALREADY_RUNNING));
            }
        }
        catch (Exception exception)
        {
            SYNC_FLAG.set(false);

            LOGGER.error(exception);

            promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), exception));
        }

        return promise.future();
    }


    /**
     * Synchronizes Microsoft Teams and their channels using the Microsoft Graph API.
     * <p>
     * This method:
     * 1. Generates an OAuth access token for Microsoft Graph API authentication
     * 2. Fetches teams the user has joined via `GET /me/joinedTeams`
     * 3. For each team, retrieves its channels via `GET /teams/{teamId}/channels`
     * 4. Builds a hierarchical structure of teams and their channels
     * 5. Updates the integration attributes with the synchronized information
     * <p>
     * The synchronized data is used when sending notifications to ensure
     * messages are sent to the correct teams and channels.
     *
     * @param url        The base URL for Microsoft Graph API calls
     * @param context    The integration context containing configuration parameters
     * @param attributes The integration attributes object to be updated
     * @return A Future that completes with the synchronization result or fails with an error message
     */
    private Future<JsonObject> sync(String url, JsonObject context, JsonObject attributes)
    {
        var promise = Promise.<JsonObject>promise();

        var entities = new JsonArray();

        getToken(context).onComplete(reply ->
        {
            if (reply.succeeded() && reply.result() != null && !reply.result().trim().isEmpty())
            {
                WebClientUtil.getWebClient().getAbs(url + "me/joinedTeams")
                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                        .bearerTokenAuthentication(reply.result())
                        .send(result ->
                        {
                            try
                            {
                                if (result.succeeded())
                                {
                                    var response = result.result();

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("received response for group: %s", result.result().bodyAsString()));
                                    }

                                    if (response.statusCode() == HttpStatus.SC_OK)
                                    {
                                        if (CommonUtil.validJSONResponse(response))
                                        {
                                            if (response.bodyAsJsonObject().containsKey("@odata.count")
                                                    && response.bodyAsJsonObject().getLong("@odata.count") > 0)
                                            {
                                                var items = response.bodyAsJsonObject().getJsonArray(VALUE);

                                                var futures = new ArrayList<Future<JsonObject>>();

                                                for (var index = 0; index < items.size(); index++)
                                                {

                                                    var future = Promise.<JsonObject>promise();

                                                    futures.add(future.future());

                                                    var item = items.getJsonObject(index);

                                                    var entity = new JsonObject().put(ID, item.getString(ID))
                                                            .put("name", item.getString("displayName"))
                                                            .put(CHANNELS, new JsonArray());

                                                    WebClientUtil.getWebClient().getAbs(url + TEAMS + "/" + entity.getString(ID, EMPTY_VALUE) + "/" + CHANNELS)
                                                            .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                                            .bearerTokenAuthentication(reply.result())
                                                            .send(asyncResult ->
                                                            {
                                                                try
                                                                {
                                                                    if (asyncResult.succeeded())
                                                                    {
                                                                        var httpResponse = asyncResult.result();

                                                                        if (CommonUtil.traceEnabled())
                                                                        {
                                                                            LOGGER.trace(String.format("received response for channel: %s", result.result().bodyAsString()));
                                                                        }

                                                                        if (httpResponse.statusCode() == HttpStatus.SC_OK)
                                                                        {

                                                                            if (CommonUtil.validJSONResponse(httpResponse))
                                                                            {
                                                                                if (httpResponse.bodyAsJsonObject().containsKey("@odata.count")
                                                                                        && httpResponse.bodyAsJsonObject().getLong("@odata.count") > 0)
                                                                                {
                                                                                    var channels = httpResponse.bodyAsJsonObject().getJsonArray(VALUE);

                                                                                    for (var i = 0; i < channels.size(); i++)
                                                                                    {

                                                                                        var channel = channels.getJsonObject(i);

                                                                                        entity.getJsonArray(CHANNELS).add(new JsonObject()
                                                                                                .put(ID, channel.getString(ID))
                                                                                                .put("name", channel.getString("displayName"))
                                                                                                .put("membershipType", channel.getString("membershipType")));

                                                                                    }
                                                                                }
                                                                                else
                                                                                {
                                                                                    LOGGER.warn("no channels founded");
                                                                                }
                                                                            }
                                                                        }
                                                                        else
                                                                        {
                                                                            LOGGER.warn(String.format("invalid status code received in channel sync : %s", result.result().statusCode()));
                                                                        }

                                                                        entities.add(entity);

                                                                        future.complete();

                                                                    }
                                                                    else
                                                                    {
                                                                        LOGGER.error(result.cause());

                                                                        future.fail(result.cause());
                                                                    }
                                                                }
                                                                catch (Exception exception)
                                                                {
                                                                    LOGGER.error(exception);

                                                                    future.fail(exception);
                                                                }
                                                            });

                                                }

                                                Future.join(futures).onComplete(asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        if (!entities.isEmpty())
                                                        {
                                                            attributes.put(TEAMS, entities);
                                                        }

                                                        promise.complete(new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_SYNC_SUCCEEDED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName())));
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), asyncResult.cause()));

                                                        promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), asyncResult.cause()));
                                                    }
                                                });

                                            }
                                            else
                                            {
                                                LOGGER.warn(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), ErrorMessageConstants.MICROSOFT_TEAMS_NOT_FOUNT));

                                                promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), ErrorMessageConstants.MICROSOFT_TEAMS_NOT_FOUNT));

                                            }
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), String.format(ErrorMessageConstants.INTEGRATION_ATTRIBUTE_SYNC_FAILED_INVALID_STATUS_CODE, result.result().statusCode())));

                                        promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), String.format(ErrorMessageConstants.INTEGRATION_ATTRIBUTE_SYNC_FAILED_INVALID_STATUS_CODE, result.result().statusCode())));
                                    }
                                }
                                else
                                {
                                    LOGGER.warn(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), result.cause()));

                                    promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), result.cause()));
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), exception));
                            }
                        });
            }
            else
            {
                LOGGER.warn(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), ErrorMessageConstants.INTEGRATION_ATTRIBUTE_SYNC_FAILED_INVALID_TOKEN_GENERATION));

                promise.fail(String.format(INTEGRATION_ATTRIBUTE_SYNC_FAILED, IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName(), ErrorMessageConstants.INTEGRATION_ATTRIBUTE_SYNC_FAILED_INVALID_TOKEN_GENERATION));

            }
        });

        return promise.future();
    }

    /**
     * Generates an OAuth access token for Microsoft Graph API authentication.
     * <p>
     * This method:
     * 1. Validates the integration context
     * 2. Retrieves the credential profile from the store using the ID in the context
     * 3. Sends a request to the OAuth token generation event bus
     * 4. Returns the generated access token or fails with an error message
     * <p>
     * The access token is used to authenticate requests to Microsoft Graph API
     * when synchronizing Teams data or sending notifications.
     *
     * @param context The integration context containing credential profile information
     * @return A Future that completes with the access token or fails with an error message
     */
    private Future<String> getToken(JsonObject context)
    {
        var promise = Promise.<String>promise();

        try
        {
            if (context != null && !context.isEmpty())
            {
                LOGGER.info(String.format("generating token using credential profile : %s", context.getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE)));

                Bootstrap.vertx().eventBus().<String>request(EventBusConstants.EVENT_OAUTH_TOKEN_GENERATE, CredentialProfileConfigStore.getStore().getItem(context.getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE)),
                        new DeliveryOptions().setSendTimeout(15 * 1000L),
                        asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                promise.complete(asyncResult.result().body());
                            }
                            else
                            {
                                LOGGER.error(asyncResult.cause());

                                promise.fail(asyncResult.cause());
                            }
                        });
            }
            else
            {
                LOGGER.warn("invalid token context");

                promise.fail("invalid token context");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
