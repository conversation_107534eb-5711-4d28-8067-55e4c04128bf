{"************": {"result": {"kubernetes.pod": [{"kubernetes.pod": "default-cadvisor-gxmk7", "kubernetes.pod.application": "cadvisor", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Mon, Apr 14 2025, 08:11", "kubernetes.pod.ip.address": "***********", "kubernetes.pod.name": "cadvisor-gxmk7", "kubernetes.pod.namespace": "default", "kubernetes.pod.node.name": "worker1", "kubernetes.pod.restarts": 1, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "DaemonSet"}, {"kubernetes.pod": "default-cadvisor-mhmsg", "kubernetes.pod.application": "cadvisor", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Mon, Apr 14 2025, 08:11", "kubernetes.pod.ip.address": "*********", "kubernetes.pod.name": "cadvisor-mhmsg", "kubernetes.pod.namespace": "default", "kubernetes.pod.node.name": "worker2", "kubernetes.pod.restarts": 1, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "DaemonSet"}, {"kubernetes.pod": "default-my-job-8gdmh", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Mon, Feb 24 2025, 13:16", "kubernetes.pod.name": "my-job-8gdmh", "kubernetes.pod.namespace": "default", "kubernetes.pod.node.name": "worker2", "kubernetes.pod.restarts": 0, "kubernetes.pod.status": "Succeeded", "kubernetes.pod.type": "Job"}, {"kubernetes.pod": "default-my-pod", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Wed, Feb 26 2025, 05:22", "kubernetes.pod.name": "my-pod", "kubernetes.pod.namespace": "default", "kubernetes.pod.node.name": "worker2", "kubernetes.pod.restarts": 0, "kubernetes.pod.status": "Failed"}, {"kubernetes.pod": "default-my-statefulset-0", "kubernetes.pod.application": "my-app", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Mon, Feb 24 2025, 13:36", "kubernetes.pod.name": "my-statefulset-0", "kubernetes.pod.namespace": "default", "kubernetes.pod.persistent.volume": "my-volume-my-statefulset-0", "kubernetes.pod.status": "Pending", "kubernetes.pod.type": "StatefulSet"}, {"kubernetes.pod": "default-prometheus-7f6cdfb75d-h5zhz", "kubernetes.pod.application": "prometheus", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Mon, Apr 14 2025, 08:12", "kubernetes.pod.ip.address": "*********", "kubernetes.pod.name": "prometheus-7f6cdfb75d-h5zhz", "kubernetes.pod.namespace": "default", "kubernetes.pod.node.name": "worker2", "kubernetes.pod.restarts": 1, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "ReplicaSet"}, {"kubernetes.pod": "kube-system-coredns-7c65d6cfc9-r8jbl", "kubernetes.pod.containers": 1, "kubernetes.pod.cpu.request.percent": 2.5, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.pod.ip.address": "***********", "kubernetes.pod.memory.limit.percent": 4.4557248180391325, "kubernetes.pod.memory.request.percent": 1.834710219192584, "kubernetes.pod.name": "coredns-7c65d6cfc9-r8jbl", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "worker1", "kubernetes.pod.restarts": 5, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "ReplicaSet"}, {"kubernetes.pod": "kube-system-coredns-7c65d6cfc9-z7cc4", "kubernetes.pod.containers": 1, "kubernetes.pod.cpu.request.percent": 2.5, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.pod.ip.address": "***********", "kubernetes.pod.memory.limit.percent": 4.4557248180391325, "kubernetes.pod.memory.request.percent": 1.834710219192584, "kubernetes.pod.name": "coredns-7c65d6cfc9-z7cc4", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "worker1", "kubernetes.pod.restarts": 5, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "ReplicaSet"}, {"kubernetes.pod": "kube-system-etcd-master", "kubernetes.pod.containers": 1, "kubernetes.pod.cpu.request.percent": 2.5, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.pod.ip.address": "************", "kubernetes.pod.memory.request.percent": 2.6210145988465485, "kubernetes.pod.name": "etcd-master", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "master", "kubernetes.pod.restarts": 5, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "Node"}, {"kubernetes.pod": "kube-system-kube-apiserver-master", "kubernetes.pod.containers": 1, "kubernetes.pod.cpu.request.percent": 6.25, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "kube-apiserver-master", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "master", "kubernetes.pod.restarts": 8, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "Node"}, {"kubernetes.pod": "kube-system-kube-controller-manager-master", "kubernetes.pod.containers": 1, "kubernetes.pod.cpu.request.percent": 5, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "kube-controller-manager-master", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "master", "kubernetes.pod.restarts": 10, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "Node"}, {"kubernetes.pod": "kube-system-kube-proxy-bbrsg", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "kube-proxy-bbrsg", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "master", "kubernetes.pod.restarts": 5, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "DaemonSet"}, {"kubernetes.pod": "kube-system-kube-proxy-lhqdd", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:33", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "kube-proxy-lhqdd", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "worker2", "kubernetes.pod.restarts": 5, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "DaemonSet"}, {"kubernetes.pod": "kube-system-kube-proxy-ltl4w", "kubernetes.pod.containers": 1, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:33", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "kube-proxy-ltl4w", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "worker1", "kubernetes.pod.restarts": 5, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "DaemonSet"}, {"kubernetes.pod": "kube-system-kube-scheduler-master", "kubernetes.pod.containers": 1, "kubernetes.pod.cpu.request.percent": 2.5, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "kube-scheduler-master", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "master", "kubernetes.pod.restarts": 10, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "Node"}, {"kubernetes.pod": "kube-system-metrics-server-6794476995-w2sxc", "kubernetes.pod.containers": 1, "kubernetes.pod.cpu.request.percent": 2.5, "kubernetes.pod.creation.time": "Mon, Apr 14 2025, 07:10", "kubernetes.pod.ip.address": "************", "kubernetes.pod.memory.request.percent": 5.242029197693097, "kubernetes.pod.name": "metrics-server-6794476995-w2sxc", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "master", "kubernetes.pod.restarts": 1, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "ReplicaSet"}, {"kubernetes.pod": "kube-system-weave-net-6c4pk", "kubernetes.pod.containers": 2, "kubernetes.pod.cpu.request.percent": 2.5, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:47", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "weave-net-6c4pk", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "worker1", "kubernetes.pod.restarts": 13, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "DaemonSet"}, {"kubernetes.pod": "kube-system-weave-net-sx2pd", "kubernetes.pod.containers": 2, "kubernetes.pod.cpu.request.percent": 2.5, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:47", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "weave-net-sx2pd", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "master", "kubernetes.pod.restarts": 13, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "DaemonSet"}, {"kubernetes.pod": "kube-system-weave-net-wmprn", "kubernetes.pod.containers": 2, "kubernetes.pod.cpu.request.percent": 2.5, "kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:47", "kubernetes.pod.ip.address": "************", "kubernetes.pod.name": "weave-net-wmprn", "kubernetes.pod.namespace": "kube-system", "kubernetes.pod.node.name": "worker2", "kubernetes.pod.restarts": 12, "kubernetes.pod.status": "Running", "kubernetes.pod.type": "DaemonSet"}], "kubernetes.pods": 19}}}