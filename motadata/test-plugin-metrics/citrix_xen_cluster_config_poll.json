{"***********": {"result": {"citrix.xen.cluster": "New Pool", "citrix.xen.cluster.description": "", "citrix.xen.cluster.uuid": "e6451e0b-8e62-3eee-f2ee-0afc0e2f787d", "citrix.xen.cluster.ha.allow.overcommit": "No", "citrix.xen.cluster.ha.enabled": "No", "citrix.xen.cluster.ha.overcommitted": "No", "citrix.xen.cluster.ha.plan.exist.for": 0, "citrix.xen.cluster.ha.host.failures.to.tolerate": 0, "citrix.xen.cluster.redo.log.enabled": "No", "citrix.xen.cluster.wlb.enabled": "No", "citrix.xen.cluster.wlb.verify.cert": "No", "citrix.xen.cluster.wlb.username": "", "citrix.xen.cluster.wlb.url": ""}}, "************": {"result": {"citrix.xen.cluster": "XenPool", "citrix.xen.cluster.description": "XenServerCluster", "citrix.xen.cluster.uuid": "d05b66de-abe0-2f3e-5096-2bc3e8d11950", "citrix.xen.cluster.ha.allow.overcommit": "No", "citrix.xen.cluster.ha.enabled": "No", "citrix.xen.cluster.ha.overcommitted": "No", "citrix.xen.cluster.ha.plan.exist.for": 0, "citrix.xen.cluster.ha.host.failures.to.tolerate": 0, "citrix.xen.cluster.redo.log.enabled": "No", "citrix.xen.cluster.wlb.enabled": "No", "citrix.xen.cluster.wlb.verify.cert": "No", "citrix.xen.cluster.wlb.username": "", "citrix.xen.cluster.wlb.url": ""}}, "************": {"result": {"citrix.xen.cluster": "XenPool", "citrix.xen.cluster.description": "XenServerCluster", "citrix.xen.cluster.uuid": "d05b66de-abe0-2f3e-5096-2bc3e8d11950", "citrix.xen.cluster.ha.allow.overcommit": "No", "citrix.xen.cluster.ha.enabled": "No", "citrix.xen.cluster.ha.overcommitted": "No", "citrix.xen.cluster.ha.plan.exist.for": 0, "citrix.xen.cluster.ha.host.failures.to.tolerate": 0, "citrix.xen.cluster.redo.log.enabled": "No", "citrix.xen.cluster.wlb.enabled": "No", "citrix.xen.cluster.wlb.verify.cert": "No", "citrix.xen.cluster.wlb.username": "", "citrix.xen.cluster.wlb.url": ""}}, "*************": {"result": {"citrix.xen.cluster": "xen7-pool", "citrix.xen.cluster.description": "xen server pool", "citrix.xen.cluster.uuid": "551d17ac-de42-020c-31ab-e59eaf7aec61", "citrix.xen.cluster.ha.allow.overcommit": "No", "citrix.xen.cluster.ha.enabled": "No", "citrix.xen.cluster.ha.overcommitted": "No", "citrix.xen.cluster.ha.plan.exist.for": 0, "citrix.xen.cluster.ha.host.failures.to.tolerate": 0, "citrix.xen.cluster.redo.log.enabled": "No", "citrix.xen.cluster.wlb.enabled": "No", "citrix.xen.cluster.wlb.verify.cert": "No", "citrix.xen.cluster.wlb.username": "", "citrix.xen.cluster.wlb.url": ""}}, "*************": {"result": {"citrix.xen.cluster": "xen7-pool", "citrix.xen.cluster.description": "xen server pool", "citrix.xen.cluster.uuid": "551d17ac-de42-020c-31ab-e59eaf7aec61", "citrix.xen.cluster.ha.allow.overcommit": "No", "citrix.xen.cluster.ha.enabled": "No", "citrix.xen.cluster.ha.overcommitted": "No", "citrix.xen.cluster.ha.plan.exist.for": 0, "citrix.xen.cluster.ha.host.failures.to.tolerate": 0, "citrix.xen.cluster.redo.log.enabled": "No", "citrix.xen.cluster.wlb.enabled": "No", "citrix.xen.cluster.wlb.verify.cert": "No", "citrix.xen.cluster.wlb.username": "", "citrix.xen.cluster.wlb.url": ""}}}