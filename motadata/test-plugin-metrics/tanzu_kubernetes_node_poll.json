{"************": {"result": {"kubernetes.cpu.percent": 3.565553325, "kubernetes.masters": 1, "kubernetes.memory.percent": 38.33018845709266, "kubernetes.node": [{"kubernetes.node": "master", "kubernetes.node.allocatable.cpu.cores": 4000000000, "kubernetes.node.allocatable.ephemeral.storage.bytes": 14169880143, "kubernetes.node.allocatable.memory.bytes": 4000649216, "kubernetes.node.allocatable.pods": 110, "kubernetes.node.available.memory.bytes": 2467192832, "kubernetes.node.container.runtime.version": "containerd", "kubernetes.node.containers": 8, "kubernetes.node.cpu.limit.percent": 0, "kubernetes.node.cpu.request.percent": 21.246551599689166, "kubernetes.node.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.node.ip": "************", "kubernetes.node.memory.limit.percent": 0, "kubernetes.node.memory.request.percent": 7.863043796539647, "kubernetes.node.pods": 7, "kubernetes.node.status": "Ready", "kubernetes.node.type": "master", "kubernetes.node.used.cpu.cores": 142622133, "kubernetes.node.used.memory.bytes": 1533456384}, {"kubernetes.node": "worker1", "kubernetes.node.allocatable.cpu.cores": 4000000000, "kubernetes.node.allocatable.ephemeral.storage.bytes": 14169880143, "kubernetes.node.allocatable.memory.bytes": 4000649216, "kubernetes.node.allocatable.pods": 110, "kubernetes.node.available.memory.bytes": 2837106688, "kubernetes.node.container.runtime.version": "containerd", "kubernetes.node.containers": 6, "kubernetes.node.cpu.limit.percent": 0, "kubernetes.node.cpu.request.percent": 7.498782917537353, "kubernetes.node.creation.time": "Sun, Feb 09 2025, 05:33", "kubernetes.node.ip": "************", "kubernetes.node.memory.limit.percent": 8.911449636078265, "kubernetes.node.memory.request.percent": 3.669420438385168, "kubernetes.node.pods": 5, "kubernetes.node.status": "Ready", "kubernetes.node.type": "worker", "kubernetes.node.used.cpu.cores": 138340315, "kubernetes.node.used.memory.bytes": 1163542528}, {"kubernetes.node": "worker2", "kubernetes.node.allocatable.cpu.cores": 4000000000, "kubernetes.node.allocatable.ephemeral.storage.bytes": 14169880143, "kubernetes.node.allocatable.memory.bytes": 4000661504, "kubernetes.node.allocatable.pods": 110, "kubernetes.node.available.memory.bytes": 2781097984, "kubernetes.node.container.runtime.version": "containerd", "kubernetes.node.containers": 7, "kubernetes.node.cpu.limit.percent": 0, "kubernetes.node.cpu.request.percent": 2.4995866283617483, "kubernetes.node.creation.time": "Sun, Feb 09 2025, 05:33", "kubernetes.node.ip": "************", "kubernetes.node.memory.limit.percent": 0, "kubernetes.node.memory.request.percent": 0, "kubernetes.node.pods": 6, "kubernetes.node.status": "Ready", "kubernetes.node.type": "worker", "kubernetes.node.used.cpu.cores": 115772146, "kubernetes.node.used.memory.bytes": 1219563520}], "kubernetes.nodes": 3, "kubernetes.workers": 2}}}