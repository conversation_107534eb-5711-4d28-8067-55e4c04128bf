/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 */

package com.mindarray.netroute;

import com.mindarray.GlobalConstants;
import com.mindarray.api.NetRoute;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Constants and enumerations used throughout the NetRoute package.
 * <p>
 * This class provides a centralized location for all constants related to network route monitoring,
 * including string constants for configuration properties, metric names, and event columns.
 * It also contains enumerations for different types of network route monitoring, ordinal values
 * for performance optimization, and metric names for extracting values from raw results.
 * <p>
 * The constants and enumerations in this class are used by other components in the NetRoute package
 * to ensure consistency and avoid duplication of string literals throughout the codebase.
 */
public class NetRouteConstants
{
    /**
     * Key for storing network route status flap information
     */
    public static final String NETROUTE_STATUS_FLAPS = "netroute-status-flaps";

    /**
     * Key for storing durations of network route status flaps
     */
    public static final String NETROUTE_STATUS_FLAP_DURATIONS = "netroute-status-flap-durations";

    /**
     * Key for storing registry information about network route hops
     */
    public static final String NETROUTE_HOPS_REGISTRY_INFO = "netroute-hops-registry-info";

    /**
     * Key for storing information about network routes
     */
    public static final String NETROUTE_ROUTES = "netroute.routes";

    /**
     * Configuration key for maximum number of probe attempts
     */
    public static final String NETROUTE_MAX_PROBE_ATTEMPTS = "netroute.max.probe.attempts";

    /**
     * Configuration key for maximum number of probe packets
     */
    public static final String NETROUTE_MAX_PROBE_PACKETS = "netroute.max.probe.packets";

    /**
     * Configuration key for maximum number of hops to trace
     */
    public static final String NETROUTE_MAX_HOPS = "netroute.max.hops";

    /**
     * Configuration key for number of packet retries
     */
    public static final String NETROUTE_PACKET_RETRIES = "netroute.packet.retries";

    /**
     * Configuration key for network protocol to use (e.g., ICMP, UDP)
     */
    public static final String NETROUTE_PROTOCOL = "netroute.protocol";

    /**
     * Key for storing Time-To-Live (TTL) value for a hop
     */
    public static final String NETROUTE_HOP_TTL = "netroute.hop.ttl";

    /**
     * Key for storing information about connected links at a hop
     */
    public static final String NETROUTE_HOP_CONNECTED_LINK = "netroute.hop.connected.link";

    /**
     * Metric key for network latency in milliseconds
     */
    public static final String NETROUTE_LATENCY = "netroute.latency.ms";

    /**
     * Metric key for minimum network latency in milliseconds
     */
    public static final String NETROUTE_MIN_LATENCY = "netroute.min.latency.ms";

    /**
     * Metric key for maximum network latency in milliseconds
     */
    public static final String NETROUTE_MAX_LATENCY = "netroute.max.latency.ms";

    /**
     * Metric key for percentage of packets lost
     */
    public static final String NETROUTE_PACKET_LOST_PERCENT = "netroute.packet.lost.percent";

    /**
     * Metric key for probability of transit through a link
     */
    public static final String NETROUTE_LINK_TRANSIT_PROBABILITY = "netroute.link.transit.probability";

    /**
     * Key for network route availability plugin
     */
    public static final String NETROUTE_AVAILABILITY_PLUGIN = "netroute.availability";

    /**
     * Column name for network route events
     */
    public static final String NETROUTE_EVENT_COLUMN = "netroute.event";

    /**
     * Label for a hop that timed out during tracing
     */
    public static final String NETROUTE_TIMEOUT_HOP = "Timeout Hop";

    /**
     * Defines different types of network route monitoring.
     * <p>
     * This enum represents the two main approaches to network route monitoring:
     * <ul>
     *   <li>SOURCE_TO_DESTINATION: Monitors the end-to-end path from source to destination</li>
     *   <li>HOP_BY_HOP: Monitors each individual hop along the network path</li>
     * </ul>
     */
    public enum NetRouteType
    {
        /**
         * Monitors the end-to-end path from source to destination
         */
        SOURCE_TO_DESTINATION("Source-to-destination"),

        /**
         * Monitors each individual hop along the network path
         */
        HOP_BY_HOP("Hop-by-hop");

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, NetRouteType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(NetRouteType::getName, e -> e)));

        /**
         * The display name of the route type
         */
        private final String name;

        /**
         * Constructor for NetRouteType enum.
         *
         * @param name The display name of the route type
         */
        NetRouteType(String name)
        {
            this.name = name;
        }

        /**
         * Retrieves the enum value corresponding to the given name.
         *
         * @param name The display name to look up
         * @return The corresponding NetRouteType enum value, or null if not found
         */
        public static NetRouteType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the display name of this route type.
         *
         * @return The display name
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Stores duplicate keys as ordinals for performance optimization.
     * <p>
     * This enum provides a more efficient way to reference commonly used keys
     * by assigning them ordinal values, which can be used for faster lookups
     * and comparisons than string-based operations.
     */
    public enum NetRouteOrdinal
    {
        /**
         * Source of the network route
         */
        NETROUTE_SOURCE(NetRoute.NETROUTE_SOURCE),

        /**
         * Destination of the network route
         */
        NETROUTE_DESTINATION(NetRoute.NETROUTE_DESTINATION),

        /**
         * IP address of the destination
         */
        NETROUTE_DESTINATION_IP(NetRoute.NETROUTE_DESTINATION_IP),

        /**
         * Time-To-Live (TTL) value for a hop
         */
        NETROUTE_HOP_TTL(NetRouteConstants.NETROUTE_HOP_TTL),

        /**
         * Information about network routes
         */
        NETROUTE_HOPS(NetRouteConstants.NETROUTE_ROUTES),

        /**
         * Network latency in milliseconds
         */
        NETROUTE_LATENCY(NetRouteConstants.NETROUTE_LATENCY),

        /**
         * Minimum network latency in milliseconds
         */
        NETROUTE_MIN_LATENCY(NetRouteConstants.NETROUTE_MIN_LATENCY),

        /**
         * Maximum network latency in milliseconds
         */
        NETROUTE_MAX_LATENCY(NetRouteConstants.NETROUTE_MAX_LATENCY),

        /**
         * Percentage of packets lost
         */
        NETROUTE_PACKET_LOST_PERCENT(NetRouteConstants.NETROUTE_PACKET_LOST_PERCENT),

        /**
         * Probability of transit through a link
         */
        NETROUTE_LINK_TRANSIT_PROBABILITY(NetRouteConstants.NETROUTE_LINK_TRANSIT_PROBABILITY),

        /**
         * Severity level of the network route status
         */
        NETROUTE_SEVERITY(GlobalConstants.SEVERITY),

        /**
         * Status of the network route
         */
        NETROUTE_STATUS(GlobalConstants.STATUS);

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, NetRouteOrdinal> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(NetRouteOrdinal::getName, e -> e)));

        /**
         * The string key associated with this ordinal
         */
        private final String name;

        /**
         * Constructor for NetRouteOrdinal enum.
         *
         * @param name The string key associated with this ordinal
         */
        NetRouteOrdinal(String name)
        {
            this.name = name;
        }

        /**
         * Retrieves the enum value corresponding to the given name.
         *
         * @param name The string key to look up
         * @return The corresponding NetRouteOrdinal enum value, or null if not found
         */
        public static NetRouteOrdinal valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string key associated with this ordinal.
         *
         * @return The string key
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Defines metrics used for extracting values from raw network route results.
     * <p>
     * This enum provides a mapping between enum constants and the string keys used
     * in raw network route monitoring results. It helps in extracting and processing
     * specific metrics from the monitoring data.
     */
    public enum NetRouteMetrics
    {
        /**
         * Best (minimum) latency value
         */
        BEST("best"),

        /**
         * Worst (maximum) latency value
         */
        WORST("worst"),

        /**
         * Average latency value
         */
        AVG("avg"),

        /**
         * Number of packets sent
         */
        SENT("sent"),

        /**
         * Number of packets received
         */
        RECEIVED("recv"),

        /**
         * Percentage of packets lost
         */
        LOST_PACKET_PERCENT("loss_pct"),

        /**
         * Time-To-Live (TTL) value
         */
        TTL("ttl"),

        /**
         * List of hosts in the route
         */
        HOSTS("hosts"),

        /**
         * Hostname of a network device
         */
        HOSTNAME("hostname"),

        /**
         * List of hops in the route
         */
        HOPS("hops"),

        /**
         * IP address of a network device
         */
        IP("ip");

        /**
         * The string key used in raw results
         */
        private final String name;

        /**
         * Constructor for NetRouteMetrics enum.
         *
         * @param name The string key used in raw results
         */
        NetRouteMetrics(String name)
        {
            this.name = name;
        }

        /**
         * Gets the string key used in raw results.
         *
         * @return The string key
         */
        public String getName()
        {
            return name;
        }
    }
}
