/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.LogCollector;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class LogCollectorConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(LogCollectorConfigStore.class, GlobalConstants.MOTADATA_STORE, "Log Collector Config Store");

    private static final LogCollectorConfigStore STORE = new LogCollectorConfigStore();
    private final Map<Long, String> statuses = new ConcurrentHashMap<>();

    private LogCollectorConfigStore()
    {
        super(DBConstants.TBL_LOG_COLLECTOR, LOGGER, true);
    }

    public static LogCollectorConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    if (!items.isEmpty())
                    {
                        for (var item : items.entrySet())
                        {
                            statuses.put(item.getKey(), item.getValue().getString(LogCollector.LOG_COLLECTOR_STATUS));
                        }
                    }

                    promise.complete();
                }

                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init log collection config store...");

                    LOGGER.error(exception);
                }
            }

            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    public String getStatus(long id)
    {
        return statuses.getOrDefault(id, GlobalConstants.UNKNOWN);
    }

    public void setStatus(long id, String message)
    {
        statuses.put(id, message);
    }
}
