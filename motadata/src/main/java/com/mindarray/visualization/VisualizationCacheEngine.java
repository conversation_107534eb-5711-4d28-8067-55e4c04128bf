/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.visualization;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.BackupProfile;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationCacheEngine extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(VisualizationCacheEngine.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Cache Engine");
    private static final long QUERY_ABORT_TIMER_MILLIS = MotadataConfigUtil.getVisualizationQueryAbortTimerSeconds() * 1000L;
    private static final long CACHE_INVALID_TIMER_MILLIS = MotadataConfigUtil.getVisualizationCacheInvalidationTimerSeconds() * 1000L;
    private static final int REFRESH_TIMER_SECONDS = MotadataConfigUtil.getVisualizationCacheRefreshTimerSeconds();
    private final Map<String, Set<Long>> widgetsByPlugin = new HashMap<>();
    private final Map<String, Set<Long>> widgetsByColumn = new HashMap<>();
    private final Set<String> plugins = new HashSet<>();
    private final Map<String, Set<String>> indexableFields = new HashMap<>();
    private final Map<String, Set<Long>> eventAggregationFields = new HashMap<>();
    private final Map<String, Set<Long>> metricAggregationMetrics = new HashMap<>();
    private final Map<Long, Map<Long, Map<String, Long>>> sessionsByWidget = new HashMap<>();//widgetid+userid+(session+uuid) last access time
    private final Map<String, JsonObject> sessionsByContext = new HashMap<>();//sessionid+tabidoruuid combination with dashboard timeline context
    private final JsonObject metricColumns = new JsonObject();
    private final JsonObject eventColumns = new JsonObject();
    private final Map<String, Map<String, Object>> eventAggregations = new HashMap<>();
    private final Map<String, Map<String, Object>> updatedEventAggregations = new HashMap<>();
    private final Map<String, Map<String, Object>> metricAggregations = new HashMap<>();
    private final Set<String> updatedMetricAggregationPlugins = new HashSet<>();
    private final Set<String> updatedEventAggregationPlugins = new HashSet<>();
    private final Map<Long, Long> queryTickers = new HashMap<>();//query heartbeat
    private final Set<Long> realTimeWidgets = new HashSet<>();    // realtime widgets -> refresh on realtime changes of availability and policy flap
    private final Set<String> realTimePlugins = new HashSet<>();  // plugins with respect to realtime widgets

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            loadIndexableColumns();

            var refreshTimer = new AtomicInteger(REFRESH_TIMER_SECONDS);

            vertx.setPeriodic(10 * 1000L, periodicTimer ->
            {
                try
                {
                    refreshTimer.set(refreshTimer.get() - 10);

                    if (refreshTimer.get() <= 0)
                    {
                        updatedEventAggregations.clear();

                        refresh(plugins);

                        refreshTimer.set(REFRESH_TIMER_SECONDS);
                    }

                    /*
                     *   we will maintain cache of plugin which need to refresh within 10 seconds to avoid flood of events.
                     *   i.e. within 5 seconds, 50 monitors availability status changed than
                     *        we will store plugin in cache and after 5 sec we will refresh respective widgets only once rather than 50 times
                     * */

                    refresh(realTimePlugins);

                    var iterator = updatedMetricAggregationPlugins.iterator();

                    while (iterator.hasNext())
                    {
                        var plugin = iterator.next();

                        VisualizationConstants.send(DatastoreConstants.DatastoreFormat.VERTICAL.getName(), new JsonObject().put(CommonUtil.getString(plugin), metricAggregations.get(plugin)), LOGGER);

                        iterator.remove();
                    }

                    var updatedEventAggregationIterator = updatedEventAggregationPlugins.iterator();

                    while (updatedEventAggregationIterator.hasNext())
                    {
                        var key = updatedEventAggregationIterator.next();

                        updatedEventAggregations.put(key, eventAggregations.get(key));

                        updatedEventAggregationIterator.remove();
                    }

                    if (!updatedEventAggregations.isEmpty())
                    {
                        VisualizationConstants.send(DatastoreConstants.DatastoreFormat.HORIZONTAL.getName(), updatedEventAggregations, LOGGER);

                        updatedEventAggregations.clear();
                    }

                    var widgetSessionIterator = this.sessionsByWidget.entrySet().iterator();

                    while (widgetSessionIterator.hasNext())
                    {
                        var widgetSessionEntry = widgetSessionIterator.next();

                        var userSessionIterator = widgetSessionEntry.getValue().entrySet().iterator();

                        while (userSessionIterator.hasNext())
                        {
                            var userSessionEntry = userSessionIterator.next();

                            var sessionIterator = userSessionEntry.getValue().entrySet().iterator();

                            while (sessionIterator.hasNext())
                            {
                                var sessionEntry = sessionIterator.next();

                                if (System.currentTimeMillis() - sessionEntry.getValue() > CACHE_INVALID_TIMER_MILLIS)
                                {
                                    vertx.eventBus().publish(EventBusConstants.EVENT_VISUALIZATION_CACHE_INVALIDATE, new JsonObject().put(User.USER_ID, userSessionEntry.getKey()).put(UI_EVENT_UUID, sessionEntry.getKey().split(COLUMN_SEPARATOR)[1]).put(APIConstants.SESSION_ID, sessionEntry.getKey().split(COLUMN_SEPARATOR)[0]).put(ID, widgetSessionEntry.getKey()));

                                    sessionsByContext.remove(sessionEntry.getKey());

                                    sessionIterator.remove();
                                }
                            }

                            if (userSessionEntry.getValue().isEmpty())
                            {
                                userSessionIterator.remove();

                                if (widgetSessionEntry.getValue().isEmpty())
                                {
                                    widgetSessionIterator.remove();
                                }
                            }
                        }
                    }

                    for (var entry : queryTickers.entrySet())
                    {
                        if ((System.currentTimeMillis() - entry.getValue()) > QUERY_ABORT_TIMER_MILLIS)
                        {
                            vertx.eventBus().publish(EVENT_VISUALIZATION_QUERY_ABORT, entry.getKey());
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            if (MotadataConfigUtil.devMode())
            {
                vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_VISUALIZATION_CACHING_TEST, message -> message.reply(new JsonObject().put("sessions.context", JsonObject.mapFrom(sessionsByContext)).put("sessions.widget", JsonObject.mapFrom(sessionsByWidget)).put("query.tickers", JsonObject.mapFrom(queryTickers)).put("realtime.widgets", new JsonArray(new ArrayList<>(realTimeWidgets)))));
            }

            /*
            Following Local Consumer Purpose is to get acknowledgment from db and then to set the timer as per the ack in queryTicker.
             */
            vertx.eventBus().<Long>localConsumer(EVENT_DATASTORE_ACKNOWLEDGEMENT, message ->
            {
                try
                {
                    if (message.body() != null)
                    {
                        var queryId = message.body();

                        if (queryTickers.containsKey(queryId))
                        {
                            queryTickers.put(queryId, System.currentTimeMillis());
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }).exceptionHandler(LOGGER::error);


            vertx.eventBus().<JsonObject>localConsumer(EVENT_INDEXABLE_COLUMN_UPDATE, message ->
            {
                try
                {
                    if (!message.body().isEmpty())
                    {
                        for (var entry : message.body().getJsonObject(EVENT_CONTEXT).getMap().entrySet())
                        {
                            this.indexableFields.computeIfAbsent(entry.getKey(), value -> new HashSet<>());

                            for (var field : JsonObject.mapFrom(entry.getValue()).getMap().keySet())
                            {
                                //indexable column added now adding it to event aggregation if any widget created on column
                                this.indexableFields.computeIfAbsent(entry.getKey(), value -> new HashSet<>()).add(field);

                                if (eventAggregationFields.containsKey(field))
                                {
                                    eventAggregationFields.get(field).forEach(id ->
                                    {
                                        update(WidgetConfigStore.getStore().getItem(id), true);

                                        if (eventAggregations.containsKey(entry.getKey() + DASH_SEPARATOR + id))
                                        {
                                            updatedEventAggregationPlugins.add(entry.getKey() + DASH_SEPARATOR + id);
                                        }
                                    });
                                }
                            }
                        }

                        Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                .put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)
                                .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, message.body().getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                                .put(EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.ACKNOWLEDGE.getName()).getBytes()));
                    }
                }
                catch (Exception exception)
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
            {
                try
                {
                    var event = message.body();

                    var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                    if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
                    {
                        update(metricColumns, tokens, true);
                    }
                    else if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                    {
                        update(eventColumns, tokens, false);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
            {
                if (reply.succeeded())
                {
                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, "", result ->
                    {
                        if (result.succeeded())
                        {
                            metricColumns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS));

                            eventColumns.mergeIn(result.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS));

                            var items = WidgetConfigStore.getStore().getItems();

                            for (var i = 0; i < items.size(); i++)
                            {
                                update(items.getJsonObject(i), true);
                            }

                            // here publish to all DBs
                            send(RemoteEventProcessorConfigStore.getStore().getIds());

                            // here only send to those DB , who sent init request
                            vertx.eventBus().<JsonObject>localConsumer(EVENT_DATASTORE_INIT, message -> send(new JsonArray().add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, message.body().getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)).getLong(ID))));

                            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
                            {
                                var event = message.body();

                                switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)))
                                {
                                    case UPDATE_WIDGET, ADD_WIDGET ->
                                    {
                                        if (event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.UPDATE_WIDGET.name()))
                                        {
                                            refresh(event.getJsonObject(EVENT_CONTEXT).getLong(ID), new ArrayList<>(1), EMPTY_VALUE);
                                        }

                                        var aggregations = update(WidgetConfigStore.getStore().getItem(event.getJsonObject(EVENT_CONTEXT).getLong(ID)), true);

                                        if (!aggregations.isEmpty())
                                        {
                                            //send to DB as new counter available for aggregation or grouping
                                            for (var entry : aggregations.entrySet())
                                            {
                                                for (var valueEntry : entry.getValue().entrySet())
                                                {
                                                    if (entry.getKey().equals(DatastoreConstants.DatastoreFormat.HORIZONTAL.getName()))
                                                    {
                                                        updatedEventAggregationPlugins.add(valueEntry.getKey());
                                                    }
                                                    else
                                                    {
                                                        updatedMetricAggregationPlugins.add(valueEntry.getKey());
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    case DELETE_WIDGET ->
                                    {
                                        var iterator = eventAggregations.entrySet().iterator();

                                        while (iterator.hasNext())
                                        {
                                            var entry = iterator.next();

                                            if (CommonUtil.getLong(entry.getKey().split(DASH_SEPARATOR)[2]) == event.getJsonObject(EVENT_CONTEXT).getLong(ID))
                                            {
                                                var buffer = Buffer.buffer();

                                                buffer.appendByte(DatastoreConstants.OperationType.WIDGET_DELETE.getName()).appendBytes(new JsonObject().put(DatastoreConstants.DATASTORE_FORMAT, DatastoreConstants.DatastoreFormat.HORIZONTAL.getName())
                                                        .mergeIn(new JsonObject().put(entry.getKey().split(DASH_SEPARATOR)[0] + DASH_SEPARATOR + entry.getKey().split(DASH_SEPARATOR)[1], entry.getKey().split(DASH_SEPARATOR)[2])).encode().getBytes());

                                                VisualizationConstants.send(buffer);

                                                VisualizationConstants.send(DatastoreConstants.DatastoreFormat.HORIZONTAL.getName(), updatedEventAggregations, LOGGER);

                                                iterator.remove();

                                                break;
                                            }
                                        }

                                        sessionsByWidget.remove(event.getJsonObject(EVENT_CONTEXT).getLong(ID));

                                        widgetsByPlugin.values().removeIf(value -> value.contains(event.getJsonObject(EVENT_CONTEXT).getLong(ID)));

                                        realTimeWidgets.remove(event.getJsonObject(EVENT_CONTEXT).getLong(ID));
                                    }

                                    case DELETE_USER ->
                                            sessionsByWidget.values().removeIf(value -> value.containsKey(event.getJsonObject(EVENT_CONTEXT).getLong(ID)));

                                    default ->
                                    {
                                        // do nothing
                                    }
                                }
                            }).exceptionHandler(LOGGER::error);

                            vertx.eventBus().<Long>localConsumer(EVENT_VISUALIZATION_QUERY_ABORT, message -> abort(message.body()));

                            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_VISUALIZATION_SESSION_ACTIVE, message ->
                            {
                                var event = message.body();

                                if (event.containsKey(QUERY_ID) && event.getLong(QUERY_ID) != null)
                                {
                                    queryTickers.put(event.getLong(QUERY_ID), System.currentTimeMillis());
                                }
                                else if ((MotadataConfigUtil.devMode() || VISUALIZATION_CACHING_ENABLED) && event.containsKey(ID))
                                {
                                    var sessionKey = event.getString(APIConstants.SESSION_ID) + COLUMN_SEPARATOR + event.getString(UI_EVENT_UUID);

                                    var refreshWidget = false;

                                    sessionsByWidget.computeIfAbsent(event.getLong(ID), value -> new HashMap<>()).computeIfAbsent(UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME)).getLong(ID), value -> new HashMap<>()).put(sessionKey, System.currentTimeMillis());

                                    if (event.containsKey(EVENT_CONTEXT) && !event.getJsonObject(EVENT_CONTEXT).isEmpty())
                                    {
                                        var eventContext = event.getJsonObject(EVENT_CONTEXT);

                                        if (!(eventContext.containsKey("container.type") && eventContext.getString("container.type").equalsIgnoreCase("Template")) && (!sessionsByContext.containsKey(sessionKey) || !sessionsByContext.get(sessionKey).equals(eventContext)))
                                        {
                                            //reason for refreshing widget is that widget context has been changed like dashboard timeline or filter context so firstly refreshing widget with new filtered data
                                            refreshWidget = true;
                                        }

                                        sessionsByContext.put(sessionKey, eventContext);
                                    }
                                    else
                                    {
                                        // when user will select no time line at that time we need to once refresh widget.
                                        if (event.containsKey(EVENT_CONTEXT) && event.getJsonObject(EVENT_CONTEXT).isEmpty() && sessionsByContext.containsKey(sessionKey) && sessionsByContext.get(sessionKey) != null)
                                        {
                                            refreshWidget = true;
                                        }

                                        sessionsByContext.remove(sessionKey);
                                    }

                                    //TODO in terms when widget is opened at that time we receive active session event so need to ignore it as it will send an extra request to DB
                                    if (refreshWidget && (!event.containsKey("count") || event.getInteger("count") != 0))
                                    {
                                        refresh(event.getLong(ID), new ArrayList<>(1), event.getString(UI_EVENT_UUID));
                                    }
                                }
                            });

                            /*   vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_VISUALIZATION_SESSION_INACTIVE, message ->
                    {
                        var object  = message.body();

                        if (CommonUtil.devMode() || VISUALIZATION_CACHING_ENABLED)
                        {
                            var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME,object.getString(User.USER_NAME));

                            var sessionKey = object.getString(APIConstants.SESSION_ID)+COLUMN_SEPARATOR+object.getString(UI_EVENT_UUID);

                            sessionsByContext.remove(sessionKey);

                            if (sessionsByWidget.containsKey(object.getLong(ID)) && sessionsByWidget.get(object.getLong(ID)).containsKey(user.getLong(ID)))
                            {
                                sessionsByWidget.get(object.getLong(ID)).get(user.getLong(ID)).remove(sessionKey);

                                if (sessionsByWidget.get(object.getLong(ID)).get(user.getLong(ID)).isEmpty())
                                {
                                    sessionsByWidget.get(object.getLong(ID)).remove(user.getLong(ID));

                                    if (sessionsByWidget.get(object.getLong(ID)).isEmpty())
                                    {
                                        sessionsByWidget.remove(object.getLong(ID));
                                    }
                                }
                            }

                            vertx.eventBus().publish(EventBusConstants.EVENT_VISUALIZATION_CACHE_INVALIDATE,object.put(User.USER_ID,user.getLong(ID))));
                        }
                    });*/

                            if (MotadataConfigUtil.devMode() || VISUALIZATION_CACHING_ENABLED)
                            {
                                vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DATASTORE_FLUSH, message ->
                                {
                                    var context = message.body().getJsonObject(EVENT_CONTEXT);

                                    var pluginId = CommonUtil.getInteger(context.getString("plugin").split(DASH_SEPARATOR)[0]);

                                    if (context.getJsonArray(NMSConstants.OBJECT) != null)
                                    {
                                        var objects = new HashSet<String>(context.getJsonArray(NMSConstants.OBJECT).getList());

                                        if (pluginId > DatastoreConstants.PluginId.LOG_EVENT.getName())
                                        {
                                            var plugin = DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() + DASH_SEPARATOR + VisualizationDataSource.EVENT_HISTORY.getName();

                                            if (widgetsByPlugin.containsKey(plugin))
                                            {
                                                widgetsByPlugin.get(plugin).forEach(widget -> objects.forEach(object ->
                                                {
                                                    if (sessionsByWidget.containsKey(widget))
                                                    {
                                                        plugins.add(object + SEPARATOR + widget);
                                                    }
                                                }));
                                            }
                                        }

                                        if (widgetsByPlugin.containsKey(context.getString(PLUGIN)) && !realTimeWidgets.contains(context.getLong(ID)))
                                        {
                                            for (var widget : widgetsByPlugin.get(context.getString(PLUGIN)))
                                            {
                                                if (sessionsByWidget.containsKey(widget))
                                                {
                                                    objects.stream().map(object -> object + SEPARATOR + widget).forEach(plugins::add);
                                                }
                                            }
                                        }
                                    }
                                });

                                /*
                                 *   we will get event in case availability status change or policy flap to refresh respective widgets.
                                 *   based on received plugin, we will refresh respective widgets.
                                 * */

                                vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH, message ->
                                {
                                    var context = message.body().getJsonObject(EVENT_CONTEXT);

                                    if (context.getJsonArray(NMSConstants.OBJECT) != null)
                                    {
                                        var objects = new HashSet<String>(context.getJsonArray(NMSConstants.OBJECT).getList());

                                        if (widgetsByPlugin.containsKey(context.getString(PLUGIN)))
                                        {
                                            for (var widget : widgetsByPlugin.get(context.getString(PLUGIN)))
                                            {
                                                if (sessionsByWidget.containsKey(widget) && realTimeWidgets.contains(widget))
                                                {
                                                    objects.stream().map(object -> object + SEPARATOR + widget).forEach(realTimePlugins::add);
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private void abort(long queryId)
    {
        queryTickers.remove(queryId);

        vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                .put(EventBusConstants.EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.QUERY_ABORT.getName()).appendBytes(new JsonObject().put(QUERY_ID, queryId).encode().getBytes()).getBytes()));
    }


    private Map<Integer, Map<String, Object>> update(JsonObject context, boolean update)
    {
        var aggregations = new HashMap<Integer, Map<String, Object>>();

        try
        {

            var dataSources = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

            for (var i = 0; i < dataSources.size(); i++)
            {
                var dataSource = dataSources.getJsonObject(i);

                var groupType = dataSource.getString(VisualizationConstants.TYPE);

                var category = context.getString(VisualizationConstants.VISUALIZATION_CATEGORY);

                if ((category.equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) || category.equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName()) || category.equalsIgnoreCase(VisualizationCategory.GAUGE.getName()))
                        && (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName()) || groupType.equalsIgnoreCase(VisualizationDataSource.POLICY.getName()) || groupType.equalsIgnoreCase(VisualizationDataSource.POLICY_STREAM.getName())))
                {
                    realTimeWidgets.add(context.getLong(ID));
                }

                if (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC.getName()) || groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName()) || groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()) || groupType.equalsIgnoreCase(VisualizationDataSource.POLICY.getName()) || groupType.equalsIgnoreCase(VisualizationDataSource.POLICY_STREAM.getName()) || groupType.equalsIgnoreCase(VisualizationDataSource.POLICY_FLAP.getName()))
                {
                    var dataPoints = dataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                    var updateIndexColumns = true;

                    for (var j = 0; j < dataPoints.size(); j++)
                    {
                        var column = dataPoints.getJsonObject(j).getString(VisualizationConstants.DATA_POINT);

                        if (CommonUtil.isNotNullOrEmpty(column))
                        {
                            if ((groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC.getName()) || groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName())))
                            {
                                if (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName()))
                                {
                                    if (category.equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) || category.equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName()))
                                    {
                                        if (column.equalsIgnoreCase(GROUP_VM))
                                        {
                                            updateWidgetPlugins(VM_DATA_POINTS, context);
                                        }
                                        else if (column.equalsIgnoreCase(GROUP_ACCESS_POINT))
                                        {
                                            updateWidgetPlugins(ACCESS_POINT_DATA_POINTS, context);
                                        }
                                        else if (column.equalsIgnoreCase(GROUP_APP))
                                        {
                                            updateWidgetPlugins(APPLICATION_DATA_POINTS, context);
                                        }
                                        else
                                        {
                                            if (column.equalsIgnoreCase(VisualizationGrouping.MONITOR.getName()))
                                            {
                                                widgetsByPlugin.computeIfAbsent(NMSConstants.AVAILABILITY_PLUGIN_ID + DASH_SEPARATOR + VisualizationDataSource.OBJECT_AVAILABILITY.getName(), value -> new HashSet<>()).add(context.getLong(ID));
                                            }
                                            else
                                            {
                                                if (metricColumns.containsKey(column))
                                                {
                                                    var mapper = metricColumns.getJsonObject(column);

                                                    mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).forEach(id ->
                                                    {
                                                        var pluginId = CommonUtil.getInteger(id);

                                                        widgetsByPlugin.computeIfAbsent(pluginId + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginId), value -> new HashSet<>()).add(context.getLong(ID));
                                                    });
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (column.contains(INSTANCE_SEPARATOR))
                                        {
                                            if (metricColumns.containsKey(column.split(INSTANCE_SEPARATOR)[0]))
                                            {
                                                var mapper = metricColumns.getJsonObject(column.split(INSTANCE_SEPARATOR)[0]);

                                                mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).forEach(id ->
                                                {
                                                    var pluginId = CommonUtil.getInteger(id);

                                                    widgetsByPlugin.computeIfAbsent(pluginId + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginId), value -> new HashSet<>()).add(context.getLong(ID));
                                                });
                                            }
                                        }
                                        else
                                        {
                                            widgetsByPlugin.computeIfAbsent(NMSConstants.AVAILABILITY_PLUGIN_ID + DASH_SEPARATOR + VisualizationDataSource.OBJECT_AVAILABILITY.getName(), value -> new HashSet<>()).add(context.getLong(ID));
                                        }
                                    }
                                }
                                else
                                {
                                    if (context.containsKey(CONTAINER_TYPE))
                                    {
                                        metricAggregationMetrics.computeIfAbsent(column, value -> new HashSet<>()).add(context.getLong(ID));
                                    }

                                    if (metricColumns.containsKey(column))
                                    {
                                        var mapper = metricColumns.getJsonObject(column);

                                        var aggregationPlugins = new HashMap<String, Object>();

                                        mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).forEach(id ->
                                        {
                                            var pluginId = CommonUtil.getInteger(id);

                                            var metricPlugin = pluginId + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginId);

                                            widgetsByPlugin.computeIfAbsent(metricPlugin, value -> new HashSet<>()).add(context.getLong(ID));

                                            if (update && context.containsKey(CONTAINER_TYPE))
                                            {
                                                metricAggregations.computeIfAbsent(metricPlugin, value -> new HashMap<>());

                                                if (!metricAggregations.get(metricPlugin).containsKey(column))
                                                {
                                                    metricAggregations.get(metricPlugin).computeIfAbsent(column, value -> mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES).contains(DatastoreConstants.DataCategory.NUMERIC.ordinal()) || mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES).contains(DatastoreConstants.DataCategory.FLOAT.ordinal()) ? DatastoreConstants.DataCategory.NUMERIC.ordinal() : DatastoreConstants.DataCategory.STRING.ordinal());

                                                    metricAggregations.get(metricPlugin).put(TYPE, groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC.getName()) ? DatastoreConstants.DatastoreType.PERFORMANCE_METRIC.ordinal() : DatastoreConstants.DatastoreType.OBJECT_STATUS_METRIC.ordinal());

                                                    aggregationPlugins.put(metricPlugin, metricAggregations.get(metricPlugin));
                                                }
                                            }
                                        });

                                        if (!aggregationPlugins.isEmpty())
                                        {
                                            aggregations.put(DatastoreConstants.DatastoreFormat.VERTICAL.getName(), aggregationPlugins);
                                        }
                                    }
                                }
                            }
                            else if (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()))
                            {
                                if (eventColumns.containsKey(column))
                                {
                                    var mapper = eventColumns.getJsonObject(column);

                                    mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).forEach(pluginId -> widgetsByPlugin.computeIfAbsent(pluginId + DASH_SEPARATOR + (CommonUtil.getInteger(pluginId) == DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() ? VisualizationDataSource.EVENT_HISTORY.getName() : mapper.getString(DatastoreConstants.MAPPER_EVENT_CATEGORY)), value -> new HashSet<>()).add(context.getLong(ID)));

                                    if (update && context.containsKey(CONTAINER_TYPE) && !drillDownRequest(context) && update(context.getLong(ID), eventColumns.getJsonObject(column), groupType, column, aggregations, dataSource.getJsonArray(VISUALIZATION_RESULT_BY), dataSource.getJsonObject(FILTERS), updateIndexColumns))
                                    {
                                        eventAggregationFields.computeIfAbsent(column, value -> new HashSet<>()).add(context.getLong(ID));
                                    }
                                }
                                else if (context.containsKey(CONTAINER_TYPE) && (!column.equalsIgnoreCase(MESSAGE) && !column.equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY) && !column.equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE) && !column.equalsIgnoreCase(EVENT_SOURCE)))
                                {
                                    eventAggregationFields.computeIfAbsent(column, value -> new HashSet<>()).add(context.getLong(ID));
                                }
                            }
                            else if (groupType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))
                            {
                                widgetsByPlugin.computeIfAbsent(DatastoreConstants.PluginId.FLOW_EVENT.getName() + DASH_SEPARATOR + VisualizationDataSource.FLOW.getName(), value -> new HashSet<>()).add(context.getLong(ID));

                                if (eventColumns.containsKey(column))
                                {
                                    if (update && context.containsKey(CONTAINER_TYPE)
                                            && update(context.getLong(ID), eventColumns.getJsonObject(column), groupType, column, aggregations, dataSource.getJsonArray(VISUALIZATION_RESULT_BY), dataSource.getJsonObject(FILTERS), updateIndexColumns))
                                    {
                                        eventAggregationFields.computeIfAbsent(column, value -> new HashSet<>()).add(context.getLong(ID));
                                    }
                                }
                                else if (context.containsKey(CONTAINER_TYPE))
                                {
                                    eventAggregationFields.computeIfAbsent(column, value -> new HashSet<>()).add(context.getLong(ID));
                                }
                            }
                            else if (groupType.equalsIgnoreCase(VisualizationDataSource.POLICY.getName()) || groupType.equalsIgnoreCase(VisualizationDataSource.POLICY_STREAM.getName()))
                            {
                                if (dataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(METRIC))
                                {
                                    widgetsByPlugin.computeIfAbsent(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName(), value -> new HashSet<>()).add(context.getLong(ID));
                                }
                                else if (dataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(EVENT_LOG))
                                {
                                    widgetsByPlugin.computeIfAbsent(DatastoreConstants.PluginId.POLICY_EVENT.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY.getName(), value -> new HashSet<>()).add(context.getLong(ID));
                                }
                                else if (dataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(EVENT_FLOW))
                                {
                                    widgetsByPlugin.computeIfAbsent(DatastoreConstants.PluginId.POLICY_FLOW.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY.getName(), value -> new HashSet<>()).add(context.getLong(ID));
                                }
                            }
                            else if (groupType.equalsIgnoreCase(VisualizationDataSource.POLICY_FLAP.getName()))
                            {
                                widgetsByPlugin.computeIfAbsent(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName(), value -> new HashSet<>()).add(context.getLong(ID));
                            }

                            widgetsByColumn.computeIfAbsent(column, value -> new HashSet<>()).add(context.getLong(ID));
                        }

                        updateIndexColumns = false;
                    }


                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return aggregations;
    }

    //as in case of drilldown request we are not going to send widget request to DB as they cannot create view for same as its raw data as in like Event History..
    private boolean drillDownRequest(JsonObject context)
    {
        var drillDown = false;

        try
        {
            var dataSources = context.getJsonArray(VISUALIZATION_DATA_SOURCES, new JsonArray());

            if (!dataSources.isEmpty())
            {
                for (var i = 0; i < dataSources.size(); i++)
                {
                    var dataPoints = dataSources.getJsonObject(i).getJsonArray(VisualizationConstants.DATA_POINTS);

                    for (var j = 0; j < dataPoints.size(); j++)
                    {
                        if (dataPoints.getJsonObject(i).getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(EMPTY_VALUE))
                        {
                            drillDown = true;

                            break;
                        }
                    }
                }
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return drillDown;
    }

    private void updateWidgetPlugins(JsonArray dataPoints, JsonObject context)
    {
        for (var dataPoint : dataPoints)
        {
            if (metricColumns.containsKey(CommonUtil.getString(dataPoint).split(INSTANCE_SEPARATOR)[0]))
            {
                var mapper = metricColumns.getJsonObject(CommonUtil.getString(dataPoint).split(INSTANCE_SEPARATOR)[0]);

                mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).forEach(id ->
                {
                    var pluginId = CommonUtil.getInteger(id);

                    widgetsByPlugin.computeIfAbsent(pluginId + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginId), value -> new HashSet<>()).add(context.getLong(ID));
                });
            }
        }
    }

    private void refresh(long widgetId, List<String> ids, String uuid)
    {
        if (sessionsByWidget.containsKey(widgetId))
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace("Refreshing widget:" + WidgetConfigStore.getStore().getItem(widgetId).getString(VISUALIZATION_NAME));
            }

            sessionsByWidget.get(widgetId).forEach((userId, widgetSessions) ->
            {
                var userSessions = new HashSet<>();

                widgetSessions.keySet().forEach(session ->
                {
                    var widgetUUID = session.split(COLUMN_SEPARATOR)[1];

                    var validUUID = uuid.isEmpty() || uuid.equalsIgnoreCase(widgetUUID);

                    if (sessionsByContext.containsKey(session))
                    {
                        var context = sessionsByContext.get(session);

                        var valid = false;

                        if (!ids.isEmpty() && context.containsKey(ENTITY_TYPE) && context.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()))//for template
                        {
                            var items = ObjectConfigStore.getStore().getItemsByObjectIds(ids);

                            if (context.getJsonArray(ENTITIES).stream().anyMatch(items::contains))
                            {
                                valid = true;
                            }
                        }

                        else
                        {
                            if (validUUID)
                            {
                                valid = true;
                            }
                        }

                        if (valid)
                        {
                            vertx.eventBus().send(EventBusConstants.EVENT_VISUALIZATION, new JsonObject().mergeIn(sessionsByContext.get(session)).put(UI_EVENT_UUID, widgetUUID).put(APIConstants.SESSION_ID, session.split(COLUMN_SEPARATOR)[0]).put(VisualizationConstants.VISUALIZATION_STREAMING, true).put(ID, widgetId).put(USER_NAME, UserConfigStore.getStore().getItem(userId).getString(USER_NAME)));
                        }
                    }

                    else
                    {
                        if (validUUID)
                        {
                            userSessions.add(session);
                        }
                    }

                    if (MotadataConfigUtil.devMode())
                    {
                        if (sessionsByContext.containsKey(session))
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace("sending session visualization event:" + WidgetConfigStore.getStore().getItem(widgetId).getString(VISUALIZATION_NAME));
                            }

                            vertx.eventBus().publish(EventBusConstants.EVENT_VISUALIZATION_TEST, new JsonObject().put(VisualizationConstants.VISUALIZATION_TIMELINE, sessionsByContext.get(session)).put(UI_EVENT_UUID, session.split(COLUMN_SEPARATOR)[1]).put(APIConstants.SESSION_ID, session.split(COLUMN_SEPARATOR)[0]).put(VisualizationConstants.VISUALIZATION_STREAMING, true).put(ID, widgetId).put(USER_NAME, UserConfigStore.getStore().getItem(userId).getString(USER_NAME)));
                        }

                        else
                        {

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace("sending visualization event:" + WidgetConfigStore.getStore().getItem(widgetId).getString(VISUALIZATION_NAME));
                            }

                            vertx.eventBus().publish(EventBusConstants.EVENT_VISUALIZATION_TEST, new JsonObject().put(UI_EVENT_UUID, session.split(COLUMN_SEPARATOR)[1]).put(APIConstants.SESSION_ID, session.split(COLUMN_SEPARATOR)[0]).put(VisualizationConstants.VISUALIZATION_STREAMING, true).put(ID, widgetId).put(USER_NAME, UserConfigStore.getStore().getItem(userId).getString(USER_NAME)));
                        }
                    }
                });

                if (!userSessions.isEmpty())
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_VISUALIZATION, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(VisualizationConstants.VISUALIZATION_SESSIONS, new ArrayList<>(userSessions)).put(VisualizationConstants.VISUALIZATION_STREAMING, true).put(ID, widgetId).put(USER_NAME, UserConfigStore.getStore().getItem(userId).getString(USER_NAME)));
                }
            });
        }
    }

    private void update(JsonObject columns, String[] tokens, boolean metric)
    {
        var column = tokens[DatastoreConstants.MetricWriterOrdinal.COLUMN.ordinal()];

        if (!columns.containsKey(column))
        {
            columns.put(column, new JsonObject());
        }

        var mapper = columns.getJsonObject(column);

        var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

        var pluginId = CommonUtil.getInteger(tokens[1]);

        var dataType = CommonUtil.getInteger(tokens[0]);

        var updateAggregation = false;

        if (plugins == null)
        {
            plugins = new JsonArray(new ArrayList<>(1));
        }

        var plugin = EMPTY_VALUE;

        if (metric)
        {
            plugin = pluginId + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginId);
        }

        var eventCategory = EMPTY_VALUE;

        if (!metric && tokens.length > 4)
        {
            eventCategory = tokens[4].replace(" ", ".").toLowerCase().trim();

            mapper.put(DatastoreConstants.MAPPER_EVENT_CATEGORY, eventCategory);
        }

        if (!plugins.contains(pluginId))
        {
            mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(pluginId));

            if ((metric && ((metricAggregationMetrics.containsKey(column) && !metricAggregations.containsKey(plugin)) || (metricAggregationMetrics.containsKey(column) && metricAggregations.containsKey(plugin) && !metricAggregations.get(plugin).containsKey(column)))) || (!metric && !eventAggregations.containsKey(pluginId + DASH_SEPARATOR + eventCategory) && eventAggregationFields.containsKey(column)))
            {
                updateAggregation = true;
            }
        }

        var categories = mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);

        if (categories == null)
        {
            categories = new JsonArray(new ArrayList<>(1));
        }

        if (!categories.contains(dataType))
        {
            mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(dataType));

            if (metric && metricAggregations.containsKey(plugin) && metricAggregations.get(plugin).containsKey(column) && (dataType == DatastoreConstants.DataCategory.NUMERIC.ordinal() || dataType == DatastoreConstants.DataCategory.FLOAT.ordinal()) && metricAggregations.get(plugin).get(column).equals(DatastoreConstants.DataCategory.STRING.ordinal()))
            {
                metricAggregations.get(plugin).put(column, DatastoreConstants.DataCategory.NUMERIC.ordinal());

                updatedMetricAggregationPlugins.add(plugin);
            }

            else if (!metric && eventAggregationFields.containsKey(column) && (dataType == DatastoreConstants.DataCategory.NUMERIC.ordinal() || dataType == DatastoreConstants.DataCategory.FLOAT.ordinal()))
            {
                updateAggregation = true;
            }
        }

        mapper.put(DatastoreConstants.MAPPER_STATUS, tokens[3]);

        if (column.contains(GlobalConstants.INSTANCE_SEPARATOR))
        {
            mapper.put(DatastoreConstants.MAPPER_INSTANCE, column.split(GlobalConstants.INSTANCE_SEPARATOR)[0]);
        }

        if (widgetsByColumn.containsKey(column))
        {
            widgetsByColumn.get(column).forEach(widget -> update(WidgetConfigStore.getStore().getItem(widget), false));
        }

        if (updateAggregation)
        {
            if (metric)
            {
                metricAggregationMetrics.get(column).forEach(id -> update(WidgetConfigStore.getStore().getItem(id), true));

                if (metricAggregations.containsKey(pluginId + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginId)))
                {
                    updatedMetricAggregationPlugins.add(pluginId + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(pluginId));
                }
            }

            else
            {
                eventAggregationFields.get(column).forEach(id ->
                {
                    update(WidgetConfigStore.getStore().getItem(id), true);

                    if (eventAggregations.containsKey(pluginId + DASH_SEPARATOR + mapper.getString(DatastoreConstants.MAPPER_EVENT_CATEGORY, EMPTY_VALUE) + DASH_SEPARATOR + id))
                    {
                        updatedEventAggregationPlugins.add(pluginId + DASH_SEPARATOR + mapper.getString(DatastoreConstants.MAPPER_EVENT_CATEGORY, EMPTY_VALUE) + DASH_SEPARATOR + id);
                    }
                });
            }
        }
    }

    private boolean update(long widgetId, JsonObject mapper, String groupType, String dataPoint, Map<Integer, Map<String, Object>> aggregations, JsonArray groupingColumns, JsonObject filters, boolean updateIndexColumns)
    {
        var exist = false;

        try
        {
            var pluginIds = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

            var eventCategory = groupType.equalsIgnoreCase(VisualizationDataSource.FLOW.getName()) ? VisualizationDataSource.FLOW.getName() : mapper.getString(DatastoreConstants.MAPPER_EVENT_CATEGORY, EMPTY_VALUE);

            var update = false;

            var aggregationPlugins = new HashMap<String, Object>();

            for (var k = 0; k < pluginIds.size(); k++)
            {
                var plugin = pluginIds.getInteger(k) + DASH_SEPARATOR + eventCategory.replace(" ", ".").toLowerCase().trim();

                if (this.indexableFields.containsKey(plugin))
                {
                    exist = true;

                    eventAggregations.computeIfAbsent(plugin + DASH_SEPARATOR + widgetId, value -> new HashMap<>());

                    var eventAggregation = eventAggregations.get(plugin + DASH_SEPARATOR + widgetId);

                    eventAggregation.computeIfAbsent(TYPE, value -> groupType.equalsIgnoreCase(VisualizationDataSource.LOG.getName()) ? DatastoreConstants.DatastoreType.LOG.ordinal() : DatastoreConstants.DatastoreType.FLOW.ordinal());

                    eventAggregation.computeIfAbsent(INDEXABLE_COLUMNS, value -> new ArrayList<>());

                    var indexableColumns = ((ArrayList<String>) eventAggregation.get(INDEXABLE_COLUMNS));

                    if (!eventAggregation.containsKey(dataPoint))
                    {
                        eventAggregation.put(dataPoint, mapper.containsKey(DatastoreConstants.MAPPER_DATA_CATEGORIES) && (mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES).contains(DatastoreConstants.DataCategory.NUMERIC.ordinal()) || mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES).contains(DatastoreConstants.DataCategory.FLOAT.ordinal())) ? DatastoreConstants.DataCategory.NUMERIC.ordinal() : DatastoreConstants.DataCategory.STRING.ordinal());

                        aggregationPlugins.put(plugin + DASH_SEPARATOR + widgetId, eventAggregation);

                        update = true;
                    }

                    if (updateIndexColumns)
                    {
                        updateIndexColumns = false;

                        if (groupingColumns != null && !groupingColumns.isEmpty())
                        {
                            for (var index = 0; index < groupingColumns.size(); index++)
                            {
                                var column = groupingColumns.getString(index);

                                if (!indexableColumns.contains(column))
                                {
                                    updateIndexColumns = true;

                                    indexableColumns.add(column);
                                }
                            }
                        }

                        if (filters != null && !filters.isEmpty())
                        {
                            eventAggregation.put(FILTERS, filters);

                            if (filters.getJsonObject(DATA_FILTER, null) != null && filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS) != null)
                            {
                                var conditionGroups = filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS);

                                for (var i = 0; i < conditionGroups.size(); i++)
                                {
                                    var conditionGroup = conditionGroups.getJsonObject(i);

                                    var conditions = conditionGroup.getJsonArray(CONDITIONS);

                                    for (var j = 0; j < conditions.size(); j++)
                                    {
                                        var condition = conditions.getJsonObject(j);

                                        if (!indexableColumns.contains(condition.getString(OPERAND)))
                                        {
                                            updateIndexColumns = true;

                                            indexableColumns.add(condition.getString(OPERAND));
                                        }
                                    }
                                }
                            }
                        }

                        if (!update && updateIndexColumns)//no column update its groupby or filter changes or update from widget
                        {
                            aggregationPlugins.put(plugin + DASH_SEPARATOR + widgetId, eventAggregation);
                        }
                    }
                }

            }

            if (!aggregationPlugins.isEmpty())
            {
                aggregations.put(DatastoreConstants.DatastoreFormat.HORIZONTAL.getName(), aggregationPlugins);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return !exist;
    }

    private void refresh(Set<String> plugins)
    {
        var iterator = plugins.iterator();

        var widgetRequests = new HashMap<Long, Set<String>>();

        while (iterator.hasNext())
        {
            var tokens = iterator.next().split(SEPARATOR_WITH_ESCAPE);

            widgetRequests.computeIfAbsent(CommonUtil.getLong(tokens[1]), value -> new HashSet<>()).add(!tokens[0].equalsIgnoreCase(EMPTY_VALUE) ? tokens[0] : EMPTY_VALUE);

            iterator.remove();
        }

        widgetRequests.forEach((key, values) -> refresh(key, new ArrayList<>(values), EMPTY_VALUE));
    }

    private void loadIndexableColumns()
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + DatastoreConstants.INDEXABLE_COLUMNS);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap().forEach((key, value) -> ((List) value).forEach(column -> this.indexableFields.computeIfAbsent(key, val -> new HashSet<>()).add(CommonUtil.getString(column))));

                    LOGGER.info(String.format("%s loaded from the backup file...", DatastoreConstants.INDEXABLE_COLUMNS));
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void send(JsonArray ids)
    {
        try
        {
            if (!metricAggregations.isEmpty())
            {
                VisualizationConstants.send(DatastoreConstants.DatastoreFormat.VERTICAL.getName(), metricAggregations, LOGGER);
            }

            if (!eventAggregations.isEmpty())
            {
                VisualizationConstants.send(DatastoreConstants.DatastoreFormat.HORIZONTAL.getName(), eventAggregations, LOGGER);
            }

            VisualizationConstants.send(LOGGER, ids);//data retention settings update..

            VisualizationConstants.sendCycleStartNotification(EMPTY_VALUE, NOT_AVAILABLE, NOT_AVAILABLE, LOGGER);

            // send latest report db backup profile context when datastore.init event is received
            DatastoreConstants.notify(new JsonObject().put(DatastoreConstants.DATASTORE_TYPES, BackupProfileConfigStore.getStore().getItemByValue(BackupProfile.BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.REPORT_DB.getName()).getJsonObject(BackupProfile.BACKUP_PROFILE_CONTEXT).getJsonArray(DatastoreConstants.DATASTORE_TYPES)),
                    DatastoreConstants.OperationType.MODIFY_BACKUP_PROFILE.ordinal(), ids);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        promise.complete();
    }
}
