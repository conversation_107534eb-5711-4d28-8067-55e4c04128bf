/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*   Change Logs:
 *   Date          Author              Notes
 *   2025-02-11    Deven Chopra      MOTADATA-4979  Changed logger level of most frequent info logs to trace logs
 *   17-Mar-2025   Chandresh         MOTADATA-5379: Assign ping and trace route runbook by default to all monitors except cloud
 *   24-Jun-2025   Yash T<PERSON>wari       MOTADATA-6528: Removing explorer views on deletion of object
 *   24-Jul-2025   <PERSON><PERSON>       MOTADATA-6864: sending change notification for dependency mapper deletion if object is deleted
 */

package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.api.MetricPlugin;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.util.WorkerUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.LogParser.LOG_PARSER_ENTITIES;
import static com.mindarray.api.LogParserPlugin.*;
import static com.mindarray.api.User.*;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.*;
import static com.mindarray.flow.FlowEngineConstants.SOURCE_IP;
import static com.mindarray.log.LogEngineConstants.SOURCE_GROUPS;
import static com.mindarray.nms.NMSConstants.*;

/**
 * The LocalEventChangeNotificationHandler class processes local change notifications and performs appropriate actions.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Handling configuration change notifications within the local system</li>
 *   <li>Provisioning and configuring system components based on change events</li>
 *   <li>Assigning and managing object groups, metrics, log parsers, and runbooks</li>
 *   <li>Registering and configuring event sources</li>
 *   <li>Managing flow sampling rates for network monitoring</li>
 *   <li>Coordinating configuration changes across the system</li>
 * </ul>
 * <p>
 * The handler uses an EventEngine to process events in a controlled manner, ensuring that
 * configuration changes are applied consistently and in the correct order. It responds to
 * various types of change notifications including:
 * <ul>
 *   <li>Object additions, updates, and deletions</li>
 *   <li>Metric additions, updates, and deletions</li>
 *   <li>Group structure changes</li>
 *   <li>Log parser assignments</li>
 *   <li>Runbook assignments</li>
 *   <li>Event source registrations</li>
 * </ul>
 * <p>
 * For each type of change notification, the handler performs the necessary actions to
 * ensure that the system configuration remains consistent and that all dependent components
 * are updated appropriately.
 * <p>
 * Example usage:
 * <pre>
 * // Deploy the LocalEventChangeNotificationHandler
 * vertx.deployVerticle(new LocalEventChangeNotificationHandler());
 *
 * // Send a change notification event
 * JsonObject event = new JsonObject()
 *     .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_OBJECT.name())
 *     .put(ID, objectId);
 *
 * vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, event);
 * </pre>
 */
public class LocalEventChangeNotificationHandler extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(LocalEventChangeNotificationHandler.class, GlobalConstants.MOTADATA_EVENT_BUS, "Local Event Change Notification Handler");

    private EventEngine eventEngine;

    private String replyTopic;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        replyTopic = EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION + EventBusConstants.EVENT_REPLY;

        eventEngine = new EventEngine().setEventType(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION).setPersistEventOffset(true)
                .setBlockingEvent(true).setEventQueueSize(1).setLogger(LOGGER).setEventHandler(event ->
                {
                    try
                    {
                        switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                        {
                            case ADD_METRIC ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    if (event.containsKey(AIOpsObject.OBJECT_AGENT)) //for agent monitor we don't have object type metric so need to pass flag to assign log parsers on agent monitor
                                    {
                                        var object = ObjectConfigStore.getStore().getItemByAgentId(event.getLong(AIOpsObject.OBJECT_AGENT));

                                        if (object != null)
                                        {
                                            assignLogParsers(LogParserConfigStore.getStore().getItemsByValue(LogParser.LOG_PARSER_SOURCE_TYPE, object.getString(AIOpsObject.OBJECT_TYPE)), object, ADD_METRIC, event);
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(replyTopic, event);
                                        }
                                    }
                                    else
                                    {
                                        var metric = MetricConfigStore.getStore().getItem(event.getLong(ID));

                                        if (metric != null)
                                        {
                                            if (Category.NETWORK.getName().equalsIgnoreCase(metric.getString(Metric.METRIC_CATEGORY)))
                                            {
                                                assignLogParsers(LogParserConfigStore.getStore().flatItemsByValue(LogParser.LOG_PARSER_SOURCE_TYPE, metric.getString(Metric.METRIC_TYPE), LogParser.LOG_PARSER_SOURCE_VENDOR, ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_VENDOR)),
                                                        ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT)), ADD_METRIC, event);
                                            }
                                            else
                                            {
                                                assignLogParsers(LogParserConfigStore.getStore().getItemsByValue(LogParser.LOG_PARSER_SOURCE_TYPE, metric.getString(Metric.METRIC_TYPE)),
                                                        ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT)), ADD_METRIC, event);
                                            }
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(replyTopic, event);
                                        }
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case ADD_OBJECT ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var item = ObjectConfigStore.getStore().getItem(event.getLong(ID));

                                    if (item != null)
                                    {
                                        if (item.containsKey(AIOpsObject.OBJECT_CONTEXT))
                                        {
                                            item.mergeIn(item.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                                            item.remove(AIOpsObject.OBJECT_CONTEXT);
                                        }

                                        if (Category.NETWORK.getName().equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_CATEGORY)))
                                        {
                                            assignLogParsers(LogParserConfigStore.getStore().flatItemsByValue(LogParser.LOG_PARSER_SOURCE_TYPE, item.getString(AIOpsObject.OBJECT_TYPE), LogParser.LOG_PARSER_SOURCE_VENDOR, item.getString(AIOpsObject.OBJECT_VENDOR)), item, ADD_OBJECT, null);
                                        }
                                        else
                                        {
                                            assignLogParsers(LogParserConfigStore.getStore().getItemsByValue(LogParser.LOG_PARSER_SOURCE_TYPE, item.getString(AIOpsObject.OBJECT_TYPE)), item, ADD_OBJECT, null);
                                        }

                                        if (CommonUtil.isNotNullOrEmpty(item.getString(PING_CHECK_STATUS)) && YES.equalsIgnoreCase(item.getString(PING_CHECK_STATUS)))
                                        {
                                            assignRunbooks(RunbookPluginConfigStore.getStore().getItems(Runbook.DEFAULT_RUNBOOKS), item);
                                        }
                                    }

                                    assignQualifiedGroups(item).onComplete(result -> assignMetricPlugin(item, event.put(AIOpsObject.OBJECT_GROUPS, result.succeeded() ? result.result() : null)));
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case UPDATE_OBJECT ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var item = ObjectConfigStore.getStore().getItem(event.getLong(ID));

                                    if (item != null && item.containsKey(AIOpsObject.OBJECT_CONTEXT))
                                    {
                                        item.mergeIn(item.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                                        item.remove(AIOpsObject.OBJECT_CONTEXT);
                                    }

                                    assignMetricPlugin(item, event);
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case UPDATE_AGENT ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var context = event.getJsonObject(EVENT_CONTEXT);

                                    var item = ObjectConfigStore.getStore().getItemByAgentId(context.getLong(ID));

                                    LOGGER.info(String.format("updating agent object : %s", item.encodePrettily()));

                                    Bootstrap.configDBService().update(TBL_OBJECT,
                                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                            item.put(AIOpsObject.OBJECT_IP, context.getString(AIOpsObject.OBJECT_IP))
                                                    .put(AIOpsObject.OBJECT_HOST, context.getString(AIOpsObject.OBJECT_HOST))
                                                    .put(AIOpsObject.OBJECT_TARGET, context.getString(AIOpsObject.OBJECT_NAME)),
                                            event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                                            event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    ObjectConfigStore.getStore().updateItem(item.getLong(ID));

                                                    LOGGER.info(String.format("updated agent : %s", item.getLong(ID)));

                                                    vertx.eventBus().send(replyTopic, event);
                                                }
                                                else
                                                {
                                                    LOGGER.warn(String.format("failed to update agent : %s ", item.getLong(ID)));

                                                    vertx.eventBus().send(replyTopic, event);
                                                }
                                            });
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case DELETE_OBJECT ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var object = event.getJsonObject(OBJECT);

                                    NetworkServiceCacheStore.getStore().deleteItem(event.getLong(ID));

                                    var views = ExplorerConfigStore.getStore().getItemsByValue(APIConstants.ENTITY_ID, event.getLong(ID));

                                    for (var index = 0; index < views.size(); index++)
                                    {
                                        ExplorerConfigStore.getStore().deleteItem(views.getJsonObject(index).getLong(GlobalConstants.ID));
                                    }

                                    var items = DependencyMapperConfigStore.getStore().getItemsByObject(object.getString(AIOpsObject.OBJECT_IP));

                                    var ids = new JsonArray();

                                    items.get(DependencyMapper.PARENTS).forEach(item -> ids.add(item.getLong(ID)));

                                    for (var item : items.get(DependencyMapper.CHILDREN))
                                    {
                                        ids.add(item.getLong(ID));

                                        var context = item.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT);

                                        if (context != null)
                                        {
                                            for (var dependencyType : AIOpsConstants.DependencyType.values())
                                            {
                                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType.getName(),
                                                        new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT))
                                                                .put(AIOpsConstants.DEPENDENCY_TYPE, dependencyType.getName())
                                                                .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE_MULTIPLES.getName())
                                                                .put(AIOpsConstants.DEPENDENCY_FILTER, context.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                                                .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD))
                                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, context.getString(DependencyMapper.DEPENDENCY_MAPPER_LINK_LAYER).equalsIgnoreCase(DependencyMapper.DependencyMapperLinkLayer.L2.getName())
                                                                        ? AIOpsConstants.DependencyLevel.SIX.getName() : AIOpsConstants.DependencyLevel.FIVE.getName()));
                                            }
                                        }
                                    }

                                    if (!ids.isEmpty())
                                    {
                                        Bootstrap.configDBService().deleteAll(DBConstants.TBL_DEPENDENCY_MAPPER,
                                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, ids),
                                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                result ->
                                                {
                                                    DependencyMapperConfigStore.getStore().deleteItems(ids);

                                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_DEPENDENCY_MAPPER.name()).put(ID, DUMMY_ID));

                                                    vertx.eventBus().send(replyTopic, event);
                                                });

                                    }
                                    else
                                    {
                                        vertx.eventBus().send(replyTopic, event);
                                    }

                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case ADD_APPLICATION, DELETE_APPLICATION ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var item = ObjectConfigStore.getStore().getItem(event.getLong(ID));

                                    if (item != null)
                                    {
                                        var objectGroups = item.getJsonArray(AIOpsObject.OBJECT_GROUPS);

                                        var groups = GroupConfigStore.getStore().getItemsByValue(Group.FIELD_GROUP_NAME, event.getString(Metric.METRIC_TYPE));

                                        var valid = false;

                                        for (var index = 0; index < groups.size(); index++)
                                        {
                                            var group = groups.getJsonObject(index);

                                            if (group.getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_SYSTEM))
                                            {
                                                valid = true;

                                                if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.DELETE_APPLICATION.name()))
                                                {
                                                    LOGGER.info(String.format("application group %s removed from object %s", group.getString(Group.FIELD_GROUP_NAME), item.getString(AIOpsObject.OBJECT_NAME)));

                                                    objectGroups.remove(group.getLong(ID));
                                                }

                                                else if (!objectGroups.contains(group.getLong(ID)))
                                                {
                                                    LOGGER.info(String.format("application group %s added into object %s", group.getString(Group.FIELD_GROUP_NAME), item.getString(AIOpsObject.OBJECT_NAME)));

                                                    objectGroups.add(group.getLong(ID));
                                                }

                                                Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                                                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                        new JsonObject().put(AIOpsObject.OBJECT_GROUPS, objectGroups),
                                                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                        result -> ObjectConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult -> vertx.eventBus().send(replyTopic, event)));

                                                break;
                                            }
                                        }

                                        if (!valid)
                                        {
                                            vertx.eventBus().send(replyTopic, event);
                                        }
                                    }
                                    else
                                    {
                                        vertx.eventBus().send(replyTopic, event);
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case ADD_METADATA_FIELDS ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var metadataFields = event.getJsonObject(METADATA_FIELDS);

                                    event.remove(METADATA_FIELDS);

                                    var fieldNames = CustomMonitoringFieldConfigStore.getStore().getFieldNames();

                                    var iterator = metadataFields.iterator();

                                    var updatedItems = new JsonArray();

                                    while (iterator.hasNext())
                                    {
                                        var metadataField = iterator.next();

                                        if (CommonUtil.isNotNullOrEmpty(metadataField.getKey()) && CommonUtil.isNotNullOrEmpty(CommonUtil.getString(metadataField.getValue())))
                                        {
                                            if (!fieldNames.contains(metadataField.getKey()))
                                            {
                                                updatedItems.add(new JsonObject().put(CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME, metadataField.getKey()).put(ID, CommonUtil.newId()).put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM));
                                            }
                                        }

                                        else
                                        {
                                            iterator.remove();
                                        }
                                    }

                                    var asyncFuture = Promise.<Void>promise();

                                    // means new tag adding need to save in custom fields
                                    if (!updatedItems.isEmpty())
                                    {
                                        Bootstrap.configDBService().saveAll(DBConstants.TBL_CUSTOM_MONITORING_FIELD,
                                                updatedItems,
                                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        CustomMonitoringFieldConfigStore.getStore().updateItems(result.result()).onComplete(response -> asyncFuture.complete());
                                                    }
                                                    else
                                                    {
                                                        LOGGER.error(result.cause());

                                                        asyncFuture.fail(result.cause());
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        asyncFuture.complete();
                                    }

                                    asyncFuture.future().onComplete(result ->
                                    {
                                        LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) + " promise completed...");

                                        var fields = event.getJsonObject(AIOpsObject.OBJECT_CUSTOM_FIELDS);

                                        if (fields == null)
                                        {
                                            fields = new JsonObject();
                                        }

                                        for (var field : metadataFields)
                                        {
                                            var item = CustomMonitoringFieldConfigStore.getStore().getItemByValue(CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME, field.getKey());

                                            if (item != null)
                                            {
                                                fields.put(CommonUtil.getString(item.getLong(ID)), field.getValue());
                                            }
                                        }

                                        Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(Metric.METRIC_OBJECT)),
                                                new JsonObject().put(AIOpsObject.OBJECT_CUSTOM_FIELDS, fields),
                                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        ObjectConfigStore.getStore().updateItem(event.getLong(Metric.METRIC_OBJECT));
                                                    }
                                                    else
                                                    {
                                                        LOGGER.error(result.cause());
                                                    }

                                                    vertx.eventBus().send(replyTopic, event);
                                                });
                                    });
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case ADD_DEPENDENCY_MAPPER ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var dependencyMapper = event.getJsonObject(EventBusConstants.EVENT_CONTEXT);

                                    // means auto discovered topology request for dependency mapping
                                    if (dependencyMapper != null)
                                    {
                                        // list down existing dependencies where parent is requested dependencies children
                                        /* ex :- mapper already contains entry 10.47(19) -> 10.43(10119) & request came to add 10.43(10119) -> 10.47(19),
                                         then we will check if requested dependency parent i.e 10.43 is child in already existing child in mapper entry.*/
                                        var items = DependencyMapperConfigStore.getStore().getItemsByValue(DependencyMapper.DEPENDENCY_MAPPER_PARENT, dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD));

                                        var valid = true;

                                        var dependencyMapperContext = dependencyMapper.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT);

                                        if (!items.isEmpty())
                                        {
                                            var level = AIOpsConstants.getDependencyLevel(new JsonObject().mergeIn(dependencyMapper), true);

                                            for (var index = 0; index < items.size(); index++)
                                            {
                                                var item = items.getJsonObject(index);

                                                // check that if requested children is parent for some link so in that case it's children is not requested dependencies parent
                                                if (AIOpsConstants.getDependencyLevel(item, false).equals(level) &&
                                                        CommonUtil.isNotNullOrEmpty(dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                                        && item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD).equalsIgnoreCase(dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT)))

                                                {
                                                    var context = item.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT);

                                                    var objectIP = event.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE);

                                                    // update existing dependencies child interface if child interface exist
                                                    if (CommonUtil.isNullOrEmpty(context.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)) &&
                                                            CommonUtil.isNotNullOrEmpty(dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)) &&
                                                            context.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE).equalsIgnoreCase(dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)))
                                                    {
                                                        valid = false;

                                                        context.put(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE, dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE));

                                                        context.fieldNames().forEach(item::remove);

                                                        Bootstrap.configDBService().update(DBConstants.TBL_DEPENDENCY_MAPPER,
                                                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                                item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result -> DependencyMapperConfigStore.getStore().updateItem(item.getLong(ID)));

                                                        if (CommonUtil.debugEnabled())
                                                        {
                                                            LOGGER.debug(String.format("seed ip : %s and current dependency parent : %s ", objectIP, dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT)));
                                                        }

                                                        // if object ip means seed ip is equal to current link's parent then delete previous link and add current link
                                                        if (!objectIP.equalsIgnoreCase(EMPTY_VALUE) && dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT).equalsIgnoreCase(objectIP))
                                                        {
                                                            valid = true;

                                                            if (CommonUtil.traceEnabled())
                                                            {
                                                                LOGGER.trace(String.format("item is deleted : %s ", item.encode()));

                                                                LOGGER.trace(String.format("dependency is added : parent : %s , child : %s ", dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT), dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD)));
                                                            }

                                                            Bootstrap.configDBService().delete(DBConstants.TBL_DEPENDENCY_MAPPER,
                                                                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                                    result -> DependencyMapperConfigStore.getStore().deleteItem(item.getLong(ID)));
                                                        }
                                                    }
                                                    // ignore if existing dependencies parent interface is same as requested dependencies child (means same dependencies)
                                                    else if (CommonUtil.isNotNullOrEmpty(dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)) &&
                                                            context.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE).equalsIgnoreCase(dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)))
                                                    {
                                                        valid = false;

                                                        if (CommonUtil.debugEnabled())
                                                        {
                                                            LOGGER.debug(String.format("seed ip : %s and current dependency parent : %s ", objectIP, dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT)));
                                                        }

                                                        // if object ip means seed ip is equal to current link's parent then delete previous link and add current link
                                                        if (!objectIP.equalsIgnoreCase(EMPTY_VALUE) && dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT).equalsIgnoreCase(objectIP))
                                                        {
                                                            valid = true;

                                                            if (CommonUtil.traceEnabled())
                                                            {
                                                                LOGGER.trace(String.format("item is deleted : %s ", item.encode()));

                                                                LOGGER.trace(String.format("dependency is added : parent : %s , child : %s ", dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT), dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD)));
                                                            }

                                                            Bootstrap.configDBService().delete(DBConstants.TBL_DEPENDENCY_MAPPER,
                                                                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                                    result -> DependencyMapperConfigStore.getStore().deleteItem(item.getLong(ID)));
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        // list down existing dependencies where parent is requested dependencies children
                                        // check that in that case parent is not same and if it's same then do not update new dependencies
                                        items = DependencyMapperConfigStore.getStore().getItemsByValue(DependencyMapper.DEPENDENCY_MAPPER_CHILD, dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD));

                                        if (!items.isEmpty())
                                        {
                                            var level = AIOpsConstants.getDependencyLevel(new JsonObject().mergeIn(dependencyMapper), true);

                                            for (var index = 0; index < items.size(); index++)
                                            {
                                                var item = items.getJsonObject(index);

                                                var context = item.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT);    //means same dependency exist

                                                if (AIOpsConstants.getDependencyLevel(item, false).equals(level) &&
                                                        CommonUtil.isNotNullOrEmpty(context.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                                        && dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE).equalsIgnoreCase(context.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                                        && item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT).equalsIgnoreCase(dependencyMapper.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT)))
                                                {
                                                    valid = false;

                                                    // if previously child interface not found and currently we found it then update it
                                                    // if any device support CDP/LLDP both protocol at that time CDP return connected neighbor's interface but LLDP don't so in that case if first CDP result came and then LLDP came then child interface not updated so need this condition to update it
                                                    if (CommonUtil.isNullOrEmpty(context.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)) && CommonUtil.isNotNullOrEmpty(dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)))
                                                    {
                                                        context.put(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE, dependencyMapperContext.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE));

                                                        context.fieldNames().forEach(item::remove);

                                                        Bootstrap.configDBService().update(DBConstants.TBL_DEPENDENCY_MAPPER,
                                                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                                item,
                                                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result -> DependencyMapperConfigStore.getStore().updateItem(item.getLong(ID)));

                                                    }
                                                }
                                            }
                                        }

                                        if (valid)
                                        {
                                            dependencyMapper.remove(AIOpsConstants.DEPENDENCY_SOURCE);

                                            Bootstrap.configDBService().save(DBConstants.TBL_DEPENDENCY_MAPPER,
                                                    dependencyMapper,
                                                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                    result ->
                                                    {
                                                        if (result.succeeded())
                                                        {
                                                            DependencyMapperConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                                                            {
                                                                if (asyncResult.failed())
                                                                {
                                                                    LOGGER.error(asyncResult.cause());
                                                                }

                                                                vertx.eventBus().send(replyTopic, event);
                                                            });
                                                        }

                                                        else
                                                        {
                                                            LOGGER.error(result.cause());

                                                            vertx.eventBus().send(replyTopic, event);
                                                        }
                                                    });
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(replyTopic, event);
                                        }
                                    }

                                    else // manual created dependency mapper
                                    {
                                        provisionInstance(event);
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case UPDATE_DEPENDENCY_MAPPER -> provisionInstance(event);

                            case ADD_EVENT_SOURCE -> registerEventSource(event);

                            case ADD_FLOW_SAMPLING_RATE -> addFlowSamplingRate(event);

                            case DELETE_EVENT_SOURCE ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var item = EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, event.getString(EventBusConstants.EVENT_SOURCE));

                                    Bootstrap.configDBService().delete(DBConstants.TBL_EVENT_SOURCE,
                                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    EventSourceConfigStore.getStore().deleteItem(item.getLong(ID));
                                                }

                                                vertx.eventBus().send(replyTopic, event);
                                            });
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case UPDATE_EVENT_SOURCE ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var futures = new ArrayList<Future<Void>>();

                                    var sources = event.getJsonArray(ENTITIES);

                                    for (var index = 0; index < sources.size(); index++)
                                    {
                                        var future = Promise.<Void>promise();

                                        futures.add(future.future());

                                        var item = EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, sources.getString(index));

                                        if (event.containsKey(PLUGIN_ID) && item.containsKey(PLUGIN_ID) && item.getJsonArray(PLUGIN_ID).contains(event.getInteger(PLUGIN_ID)))
                                        {
                                            item.getJsonArray(PLUGIN_ID).remove(event.getInteger(PLUGIN_ID));

                                            Bootstrap.configDBService().update(DBConstants.TBL_EVENT_SOURCE,
                                                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                    item,
                                                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                    result -> EventSourceConfigStore.getStore().updateItem(item.getLong(ID))
                                                            .onComplete(reply -> future.complete()));
                                        }

                                        else
                                        {
                                            future.complete();
                                        }
                                    }

                                    Future.join(futures).onComplete(result ->
                                    {

                                        LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) + " all promises completed..");

                                        vertx.eventBus().send(replyTopic, event);
                                    });
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case ADD_ARTIFACT ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var artifact = event.getJsonObject(EVENT_CONTEXT);

                                    Bootstrap.configDBService().save(TBL_ARTIFACT, artifact, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            ArtifactConfigStore.getStore().addItem(result.result());

                                            LOGGER.info(String.format("added artifact of type %s and id %s ", artifact.getString(MotadataApp.ARTIFACT_TYPE), artifact.getLong(ID)));

                                            vertx.eventBus().send(replyTopic, event);
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("failed to add id %s for type %s ", artifact.getLong(ID), artifact.getString(MotadataApp.ARTIFACT_TYPE)));

                                            vertx.eventBus().send(replyTopic, event);
                                        }
                                    });
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case UPDATE_ARTIFACT ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var artifact = event.getJsonObject(EVENT_CONTEXT);

                                    if (CommonUtil.traceEnabled())
                                    {

                                        LOGGER.trace(String.format("updating artifact : %s", artifact.encode()));
                                    }

                                    Bootstrap.configDBService().update(TBL_ARTIFACT,
                                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, artifact.getLong(ID)),
                                            artifact,
                                            event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                                            event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    ArtifactConfigStore.getStore().updateItem(artifact.getLong(ID));

                                                    LOGGER.info(String.format("updated artifact of type %s and id %s ", artifact.getString(MotadataApp.ARTIFACT_TYPE), artifact.getLong(ID)));

                                                    vertx.eventBus().send(replyTopic, event);
                                                }
                                                else
                                                {
                                                    LOGGER.warn(String.format("failed to update id %s for type %s ", artifact.getLong(ID), artifact.getString(MotadataApp.ARTIFACT_TYPE)));

                                                    vertx.eventBus().send(replyTopic, event);
                                                }
                                            });
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }

                            }

                            case DELETE_ARTIFACT ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var id = event.getLong(ID);

                                    if (id != null && id > DUMMY_ID)
                                    {
                                        Bootstrap.configDBService().delete(TBL_ARTIFACT, new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id), event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                                                event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        ArtifactConfigStore.getStore().deleteItem(id);

                                                        LOGGER.info(String.format("id %s deleted for type %s ", id, event.getString(MotadataApp.ARTIFACT_TYPE)));

                                                        vertx.eventBus().send(replyTopic, event);
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("failed to delete id %s for type %s ", id, event.getString(MotadataApp.ARTIFACT_TYPE)));

                                                        vertx.eventBus().send(replyTopic, event);
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format("Invalid id value %s ", id));

                                        vertx.eventBus().send(replyTopic, event);
                                    }
                                }

                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            case ASSIGN_LOG_PARSER ->
                                    assignLogParsers(LogParserConfigStore.getStore().getItems(new JsonArray(event.getJsonArray(LogEngineConstants.ASSIGNED_LOG_PARSERS).stream().filter(Objects::nonNull).collect(Collectors.toList()))), event, ASSIGN_LOG_PARSER, event);

                            case UNASSIGN_LOG_PARSER ->
                                    unassignLogParsers(LogParserConfigStore.getStore().getItems(new JsonArray(event.getJsonArray(LogEngineConstants.UNASSIGNED_LOG_PARSERS).stream().filter(Objects::nonNull).collect(Collectors.toList()))), event);

                            case ADD_LOG_PARSER_PLUGIN ->
                            {
                                var error = ErrorMessageConstants.TIMED_OUT;

                                var fileName = event.getString(APIConstants.FILENAME);

                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var classFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + PLUGIN_SCRIPT_DIR + GlobalConstants.PATH_SEPARATOR + PLUGIN_PARSER_DIR + PATH_SEPARATOR + fileName + ".java";

                                    Bootstrap.vertx().fileSystem().writeFileBlocking(classFile, Buffer.buffer(event.getJsonObject(LOG_PARSER_PLUGIN_CONTEXT).getString(LOG_PARSER_PLUGIN_SCRIPT)));

                                    var processBuilder = new ProcessBuilder(MotadataConfigUtil.devMode() ? "javac" : GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "jdk" + GlobalConstants.PATH_SEPARATOR + "bin" + GlobalConstants.PATH_SEPARATOR + "javac", "-classpath", GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "lib" + GlobalConstants.PATH_SEPARATOR + "*", classFile);

                                    var errors = new StringBuilder();

                                    var process = processBuilder.start();

                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("custom log parser plugin %s process %s started", event.getString(LOG_PARSER_PLUGIN_NAME), process.pid()));
                                    }

                                    WorkerUtil.setupCleanupTimer(MotadataConfigUtil.getLogParserPluginTimeoutSeconds(), process, processBuilder, NOT_AVAILABLE, null, null);

                                    var exited = process.waitFor(MotadataConfigUtil.getLogParserPluginTimeoutSeconds() + 1, TimeUnit.SECONDS);

                                    if (exited)
                                    {
                                        if (CommonUtil.debugEnabled())
                                        {
                                            LOGGER.debug(String.format("custom log parser plugin process having PID %s exited with status code %s", process.pid(), process.exitValue()));
                                        }

                                        try (var reader = new BufferedReader(new InputStreamReader(process.getErrorStream())))
                                        {
                                            errors.append(reader.lines().collect(Collectors.joining(System.lineSeparator())));

                                            if (CommonUtil.traceEnabled())
                                            {
                                                LOGGER.trace(String.format("custom log parser plugin %s errors : %s", event.getString(LOG_PARSER_PLUGIN_NAME), errors));
                                            }
                                        }

                                        if (errors.indexOf("error") == -1 && errors.indexOf("ERROR") == -1 && errors.indexOf("Error") == -1)
                                        {
                                            try
                                            {
                                                //validate class and method name [has to be 'parse']
                                                Class.forName(GlobalConstants.PLUGIN_PARSER_DIR + "." + fileName, true, LogEngineConstants.LOG_PLUGIN_CLASS_LOADER).getDeclaredMethod(GlobalConstants.PARSE_METHOD, JsonObject.class);

                                                vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_SUCCEED));
                                            }

                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                FileUtils.deleteQuietly(new File(GlobalConstants.PLUGIN_PARSER_DIR + "." + fileName));

                                                vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_FAIL).put(ERROR, ErrorMessageConstants.LOG_PARSER_INVALID_PLUGIN_METHOD));

                                            }
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_FAIL).put(ERROR, error));
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.trace(String.format("custom log parser plugin process having PID %s has not exited within %s seconds", process.pid(), MotadataConfigUtil.getLogParserPluginTimeoutSeconds()));

                                        vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_FAIL));
                                    }

                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event.put(STATUS, STATUS_FAIL));
                                }
                            }
                            case UPDATE_AGENT_TAGS ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    if (event.getJsonArray(AIOpsObject.OBJECT_TAGS) != null)
                                    {
                                        var tags = event.getJsonArray(AIOpsObject.OBJECT_TAGS);

                                        var item = ObjectConfigStore.getStore().getItemByAgentId(event.getLong(ID));

                                        if (tags != null && item != null)
                                        {
                                            Bootstrap.configDBService().update(TBL_OBJECT,
                                                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                    item.put(AIOpsObject.OBJECT_TAGS, tags),
                                                    event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                                                    event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                                                    {
                                                        if (result.succeeded())
                                                        {
                                                            ObjectConfigStore.getStore().updateItem(item.getLong(ID));

                                                            LOGGER.info(String.format("updated agent : %s", item.getLong(ID)));

                                                            vertx.eventBus().send(replyTopic, event);
                                                        }
                                                        else
                                                        {
                                                            LOGGER.warn(String.format("failed to update agent : %s ", item.getLong(ID)));

                                                            vertx.eventBus().send(replyTopic, event);
                                                        }
                                                    });
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(replyTopic, event);
                                        }
                                    }
                                    else
                                    {
                                        vertx.eventBus().send(replyTopic, event);
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }
                            case UPDATE_AGENT_GROUPS ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : UPDATE_AGENT_GROUPS");

                                    var groups = event.getJsonArray(AIOpsObject.OBJECT_GROUPS);

                                    var item = ObjectConfigStore.getStore().getItemByAgentId(event.getLong(ID));

                                    if (groups != null && item != null)
                                    {
                                        Bootstrap.configDBService().update(TBL_OBJECT,
                                                new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                item.put(AIOpsObject.OBJECT_GROUPS, groups),
                                                event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                                                event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS, result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        ObjectConfigStore.getStore().updateItem(item.getLong(ID));

                                                        LOGGER.info(String.format("updated agent : %s", item.getLong(ID)));

                                                        vertx.eventBus().send(replyTopic, event);
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("failed to update agent : %s", item.getLong(ID)));

                                                        vertx.eventBus().send(replyTopic, event);
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        vertx.eventBus().send(replyTopic, event);
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }

                            }
                            case DELETE_LDAP_USER ->
                            {
                                try
                                {
                                    LOGGER.info("Event Received for Change Notification : " + EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)));

                                    var items = UserConfigStore.getStore().flatItemsByValue(USER_TYPE, USER_TYPE_LDAP, USER_LDAP_SERVER, event.getLong(ID));

                                    if (items != null && !items.isEmpty())
                                    {
                                        var users = new JsonArray();

                                        for (var index = 0; index < items.size(); index++)
                                        {
                                            users.add(items.getJsonObject(index).getLong(ID));
                                        }

                                        Bootstrap.configDBService().deleteAll(DBConstants.TBL_USER, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, users),
                                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        LOGGER.info(String.format("deleted LDAP users : %s", result.result()));

                                                        UserConfigStore.getStore().deleteItems(result.result());

                                                        vertx.eventBus().send(replyTopic, event);
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("failed to delete LDAP users : %s", result.cause()));

                                                        vertx.eventBus().send(replyTopic, event);
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        vertx.eventBus().send(replyTopic, event);
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }
                            case DELETE_ACCESS_TOKEN ->
                            {
                                try
                                {
                                    if (Objects.nonNull(AuthTokenConfigStore.getStore()
                                            .getItemByValue(USER_ID, event.getLong(ID))))
                                    {
                                        Bootstrap.configDBService().delete(TBL_TOKEN,
                                                new JsonObject().put(FIELD_NAME, USER_ID).put(VALUE, event.getLong(ID)),
                                                DEFAULT_USER,
                                                SYSTEM_REMOTE_ADDRESS,
                                                result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        AuthTokenConfigStore.getStore().deleteItems(result.result());

                                                        vertx.eventBus().send(replyTopic, event);
                                                    }
                                                    else
                                                    {
                                                        LOGGER.fatal("Error deleting access token of user: " + event.getLong(ID));

                                                        LOGGER.error(result.cause());

                                                        vertx.eventBus().send(replyTopic, event);
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        vertx.eventBus().send(replyTopic, event);
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    vertx.eventBus().send(replyTopic, event);
                                }
                            }

                            default -> vertx.eventBus().send(replyTopic, event);
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        vertx.eventBus().send(replyTopic, event);
                    }
                }).start(vertx, promise);
    }

    private void provisionInstance(JsonObject event)
    {
        try
        {
            LOGGER.info("Event Received for Change Notification :  provisionInstance(JsonObject event)");

            var item = DependencyMapperConfigStore.getStore().getItem(event.getLong(ID));

            var level = AIOpsConstants.getDependencyLevel(item, true);

            if (CommonUtil.isNotNullOrEmpty(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE)))
            {
                provisionInstance(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT), item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE), item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD), level, item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT), item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE));
            }

            if (CommonUtil.isNotNullOrEmpty(item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)))
            {
                provisionInstance(item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD), item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE), item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT), level, item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT), item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        vertx.eventBus().send(replyTopic, event);
    }

    private void provisionInstance(String ip, String objectName, String destination, Byte level, String parent, String link)
    {
        var id = ObjectConfigStore.getStore().getItemByIP(ip);

        if (id != null)
        {
            var instances = MetricConfigStore.getStore().getMetricInstancesByInstanceType(id, INTERFACES);

            if (!instances.contains(objectName))
            {
                var metric = MetricConfigStore.getStore().getItemsByObject(id).stream().filter(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
                        .map(JsonObject::mapFrom).findFirst().orElse(null);

                if (metric != null)
                {
                    var discoveredObjects = metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(DISCOVERED_OBJECTS);

                    if (discoveredObjects != null)
                    {
                        var iterator = discoveredObjects.iterator();

                        while (iterator.hasNext())
                        {
                            var item = JsonObject.mapFrom(iterator.next());

                            if (!item.getString(AIOpsObject.OBJECT_NAME).equalsIgnoreCase(objectName))
                            {
                                iterator.remove();
                            }
                        }

                        if (!discoveredObjects.isEmpty())
                        {
                            metric.put(OBJECTS, discoveredObjects).put(REDISCOVER_JOB, RediscoverJob.NETWORK_INTERFACE.getName())
                                    .put(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT, new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, ip)
                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, destination)
                                            .put(AIOpsConstants.DEPENDENCY_PARENT, level.equals(AIOpsConstants.DependencyLevel.SIX.getName()) ? parent : null)
                                            .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, objectName)
                                            .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, objectName).put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, link)
                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, level));

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("sending objects %s for provision ", discoveredObjects));
                            }

                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, metric);
                        }
                    }
                }
            }

            else
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                        new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, ip)
                                .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD.getName())
                                .put(AIOpsConstants.DEPENDENCY_DESTINATION, destination)
                                .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, objectName).put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, objectName).put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, link)
                                .put(AIOpsConstants.DEPENDENCY_PARENT, level.equals(AIOpsConstants.DependencyLevel.SIX.getName()) ? parent : null)
                                .put(AIOpsConstants.DEPENDENCY_LEVEL, level));

                if (CommonUtil.debugEnabled())
                {

                    LOGGER.debug(String.format("adding source : %s  and destination : %s of object %s", ip + VALUE_SEPARATOR + objectName, destination + VALUE_SEPARATOR + link, ip));
                }

            }
        }
    }

    // Assign groups to provisioned objects if any match found
    private Future<JsonArray> assignQualifiedGroups(JsonObject object)
    {
        var promise = Promise.<JsonArray>promise();

        try
        {
            var qualifiedGroups = new JsonArray();

            if (object != null)
            {
                var groups = GroupConfigStore.getStore().getItems();

                var type = object.getString(AIOpsObject.OBJECT_TYPE);

                for (var index = 0; index < groups.size(); index++)
                {
                    var group = groups.getJsonObject(index);

                    group.mergeIn(group.getJsonObject(Group.GROUP_CONTEXT));

                    if (group.getString(Group.GROUP_AUTO_ASSIGN).equalsIgnoreCase(YES))
                    {
                        var conditionOperator = group.getString(Group.GROUP_OPERATOR, "and");

                        if (conditionOperator.equalsIgnoreCase("and"))// AND Operator
                        {
                            var qualified = true;

                            var rules = group.getJsonArray(Group.AutoAssignmentRule.IP_ADDRESS_RANGE.getName());

                            if (rules != null && !rules.isEmpty())
                            {
                                for (var rule : rules)
                                {
                                    var start = CommonUtil.getString(rule).split("-")[0].trim();

                                    var end = start.substring(0, start.lastIndexOf('.')) + "." + CommonUtil.getString(rule).split("-")[1].trim();

                                    qualified = APIUtil.validateRange(start, end) && CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).contains(object.getString(AIOpsObject.OBJECT_IP));

                                    if (qualified)
                                    {
                                        break;
                                    }
                                }
                            }

                            rules = group.getJsonArray(Group.AutoAssignmentRule.OBJECT_TYPE.getName());

                            if (qualified && rules != null && !rules.isEmpty())
                            {
                                qualified = rules.contains(type);

                                if (qualified && object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(Type.PING.getName()))
                                {
                                    var parentGroup = GroupConfigStore.getStore().getItem(group.getLong(Group.FIELD_PARENT_GROUP));

                                    if (parentGroup != null)
                                    {
                                        qualified = parentGroup.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_CATEGORY));
                                    }
                                    else if ((group.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(Category.SERVICE_CHECK.getName()) || (group.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(Category.OTHER.getName()))))
                                    {
                                        qualified = object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(group.getString(Group.FIELD_GROUP_NAME));
                                    }
                                }
                            }

                            rules = group.getJsonArray(Group.AutoAssignmentRule.OBJECT_VENDOR.getName());

                            if (qualified && object.containsKey(AIOpsObject.OBJECT_VENDOR) && rules != null && !rules.isEmpty())
                            {
                                qualified = rules.contains(object.getString(AIOpsObject.OBJECT_VENDOR));
                            }

                            rules = group.getJsonArray(Group.AutoAssignmentRule.OBJECT_NAME.getName());

                            if (qualified && rules != null && !rules.isEmpty())
                            {
                                for (var ruleIndex = 0; ruleIndex < rules.size(); ruleIndex++)
                                {
                                    qualified = object.getString(AIOpsObject.OBJECT_NAME).toLowerCase().contains(rules.getString(ruleIndex).toLowerCase());

                                    if (qualified)
                                    {
                                        break;
                                    }
                                }
                            }

                            if (qualified)
                            {
                                qualifiedGroups.add(group.getLong(ID));
                            }
                        }
                        else //Default OR
                        {
                            var rules = group.getJsonArray(Group.AutoAssignmentRule.IP_ADDRESS_RANGE.getName());

                            if (rules != null && !rules.isEmpty())
                            {
                                for (var rule : rules)
                                {
                                    if (!qualifiedGroups.contains(group.getLong(ID)))
                                    {
                                        var start = CommonUtil.getString(rule).split("-")[0].trim();

                                        var end = start.substring(0, start.lastIndexOf('.')) + "." + CommonUtil.getString(rule).split("-")[1].trim();

                                        if (APIUtil.validateRange(start, end) && CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).contains(object.getString(AIOpsObject.OBJECT_IP)))
                                        {
                                            qualifiedGroups.add(group.getLong(ID));
                                        }
                                    }
                                }
                            }

                            rules = group.getJsonArray(Group.AutoAssignmentRule.OBJECT_TYPE.getName());

                            if (!qualifiedGroups.contains(group.getLong(ID)) && rules != null && rules.contains(type) && !rules.isEmpty())
                            {
                                if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(Type.PING.getName()))
                                {
                                    var parentGroup = GroupConfigStore.getStore().getItem(group.getLong(Group.FIELD_PARENT_GROUP));

                                    if ((parentGroup != null && parentGroup.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_CATEGORY))) || group.getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_USER))
                                    {
                                        qualifiedGroups.add(group.getLong(ID));
                                    }
                                }
                                else
                                {
                                    qualifiedGroups.add(group.getLong(ID));
                                }
                            }

                            rules = group.getJsonArray(Group.AutoAssignmentRule.OBJECT_VENDOR.getName());

                            if (!qualifiedGroups.contains(group.getLong(ID)) && object.containsKey(AIOpsObject.OBJECT_VENDOR) && rules != null && !rules.isEmpty() && rules.contains(object.getString(AIOpsObject.OBJECT_VENDOR)))
                            {
                                qualifiedGroups.add(group.getLong(ID));
                            }

                            rules = group.getJsonArray(Group.AutoAssignmentRule.OBJECT_NAME.getName());

                            if (!qualifiedGroups.contains(group.getLong(ID)) && rules != null && !rules.isEmpty())
                            {
                                for (var ruleIndex = 0; ruleIndex < rules.size(); ruleIndex++)
                                {
                                    if (!qualifiedGroups.contains(group.getLong(ID)) && object.getString(AIOpsObject.OBJECT_NAME).toLowerCase().contains(rules.getString(ruleIndex).toLowerCase()))
                                    {
                                        qualifiedGroups.add(group.getLong(ID));
                                    }
                                }
                            }
                        }
                    }
                }

                var future = Promise.<Void>promise();

                if (!CLOUD_TYPES.contains(type) && CommonUtil.isNotNullOrEmpty(object.getString(AIOpsObject.OBJECT_VENDOR))
                        && (object.getString(AIOpsObject.OBJECT_VENDOR).equalsIgnoreCase(Type.AWS_CLOUD.getName()) || object.getString(AIOpsObject.OBJECT_VENDOR).equalsIgnoreCase(Type.AZURE_CLOUD.getName())))
                {
                    // cloud group logic
                    groups = GroupConfigStore.getStore().getItemsByValue(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM);

                    var filter = object.getString(AIOpsObject.OBJECT_VENDOR).equalsIgnoreCase(Type.AWS_CLOUD.getName()) ? AIOpsObject.OBJECT_REGION : AIOpsObject.OBJECT_RESOURCE_GROUP;

                    var thirdLevelGroup = object.getString(filter);

                    var thirdLevelGroupId = DUMMY_ID;

                    for (var index = 0; index < groups.size(); index++)
                    {
                        var group = groups.getJsonObject(index);

                        if (group.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(thirdLevelGroup))
                        {
                            var parentGroup = GroupConfigStore.getStore().getItem(group.getLong(Group.FIELD_PARENT_GROUP));

                            if (parentGroup != null && parentGroup.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_VENDOR)))
                            {
                                thirdLevelGroupId = group.getLong(ID);

                                break;
                            }
                        }
                    }

                    var newGroups = new JsonArray();

                    if (thirdLevelGroupId > DUMMY_ID)
                    {
                        if (!qualifiedGroups.contains(thirdLevelGroupId))
                        {
                            qualifiedGroups.add(thirdLevelGroupId);
                        }

                        var fourthLevelGroupId = DUMMY_ID;

                        // means parent group found...now check that child group present or not
                        for (var index = 0; index < groups.size(); index++)
                        {
                            var group = groups.getJsonObject(index);

                            if (group.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(type)
                                    && group.getLong(Group.FIELD_PARENT_GROUP).equals(thirdLevelGroupId))
                            {
                                fourthLevelGroupId = group.getLong(ID);

                                break;
                            }
                        }

                        if (fourthLevelGroupId > DUMMY_ID)
                        {
                            // means child group found with same parent
                            if (!qualifiedGroups.contains(fourthLevelGroupId))
                            {
                                qualifiedGroups.add(fourthLevelGroupId);
                            }

                            if (type.equalsIgnoreCase(Type.AWS_ELB.getName()) && CommonUtil.isNotNullOrEmpty(object.getString(AIOpsObject.OBJECT_MAKE_MODEL)))
                            {
                                var model = object.getString(AIOpsObject.OBJECT_MAKE_MODEL);

                                var fifthLevelGroupId = DUMMY_ID;

                                // means parent group found...now check that child group present or not
                                for (var index = 0; index < groups.size(); index++)
                                {
                                    var group = groups.getJsonObject(index);

                                    if (group.getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(model)
                                            && group.getLong(Group.FIELD_PARENT_GROUP).equals(fourthLevelGroupId))
                                    {
                                        fifthLevelGroupId = group.getLong(ID);

                                        break;
                                    }
                                }

                                if (fifthLevelGroupId > DUMMY_ID)
                                {
                                    if (!qualifiedGroups.contains(fifthLevelGroupId))
                                    {
                                        qualifiedGroups.add(fifthLevelGroupId);
                                    }
                                }
                                else
                                {
                                    createChildGroups(model, fourthLevelGroupId, newGroups, qualifiedGroups);
                                }
                            }
                        }

                        else
                        {
                            // create child group and inner child (tags) groups
                            createChildGroups(object, thirdLevelGroupId, newGroups, qualifiedGroups);
                        }
                    }

                    else
                    {
                        // create all levels groups (Region, type)
                        var secondLevelGroupId = groups.stream().filter(item -> JsonObject.mapFrom(item).getString(Group.FIELD_GROUP_NAME).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_VENDOR))).map(item -> JsonObject.mapFrom(item).getLong(ID)).findFirst().orElse(DUMMY_ID);

                        if (secondLevelGroupId > DUMMY_ID)
                        {
                            thirdLevelGroupId = CommonUtil.newId();

                            qualifiedGroups.add(thirdLevelGroupId);

                            newGroups.add(new JsonObject().put(Group.FIELD_GROUP_NAME, thirdLevelGroup).put(Group.FIELD_PARENT_GROUP, secondLevelGroupId)
                                    .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM).put(ID, thirdLevelGroupId)
                                    .put(Group.GROUP_CONTEXT, new JsonObject().put(Group.GROUP_AUTO_ASSIGN, NO)));

                            createChildGroups(object, thirdLevelGroupId, newGroups, qualifiedGroups);
                        }
                    }

                    if (!newGroups.isEmpty())
                    {
                        Bootstrap.configDBService().saveAll(DBConstants.TBL_GROUP,
                                newGroups,
                                DEFAULT_USER,
                                SYSTEM_REMOTE_ADDRESS,
                                result ->
                                {
                                    if (result.succeeded())
                                    {
                                        GroupConfigStore.getStore().updateItems(result.result()).onComplete(asyncResult -> future.complete());
                                    }
                                    else
                                    {
                                        future.fail(result.cause());
                                    }
                                });
                    }

                    else
                    {
                        future.complete();
                    }
                }

                else
                {
                    future.complete();
                }

                future.future().onComplete(result ->
                {
                    // for filter already assign groups to particular object
                    for (var group : object.getJsonArray(AIOpsObject.OBJECT_GROUPS))
                    {
                        if (!qualifiedGroups.contains(group))
                        {
                            qualifiedGroups.add(group);
                        }
                    }

                    Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                            new JsonObject().put(AIOpsObject.OBJECT_GROUPS, qualifiedGroups),
                            DEFAULT_USER,
                            SYSTEM_REMOTE_ADDRESS,
                            asyncResult ->
                            {
                                promise.complete(qualifiedGroups);

                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info(String.format("monitor %s groups updated successfully....", object.getString(AIOpsObject.OBJECT_NAME)));

                                    ObjectConfigStore.getStore().updateItem(object.getLong(ID));
                                }
                                else
                                {
                                    LOGGER.warn(String.format("failed to update groups for monitor %s , reason : %s", object.getString(AIOpsObject.OBJECT_NAME), asyncResult.cause()));
                                }
                            });
                });
            }

            else
            {
                promise.complete(qualifiedGroups);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private void createChildGroups(JsonObject object, long thirdLevelGroupId, JsonArray newGroups, JsonArray qualifiedGroups)
    {
        LOGGER.info("Creating Child Groups using object");

        var fourthLevelGroupId = CommonUtil.newId();

        qualifiedGroups.add(fourthLevelGroupId);

        newGroups.add(new JsonObject().put(Group.FIELD_GROUP_NAME, object.getString(AIOpsObject.OBJECT_TYPE)).put(Group.FIELD_PARENT_GROUP, thirdLevelGroupId)
                .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM).put(ID, fourthLevelGroupId)
                .put(Group.GROUP_CONTEXT, new JsonObject().put(Group.GROUP_AUTO_ASSIGN, NO)));

        // for elb instance we have to create groups like custom/network/application
        if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(Type.AWS_ELB.getName()) && CommonUtil.isNotNullOrEmpty(object.getString(AIOpsObject.OBJECT_MAKE_MODEL)))
        {
            createChildGroups(object.getString(AIOpsObject.OBJECT_MAKE_MODEL), fourthLevelGroupId, newGroups, qualifiedGroups);
        }
    }

    private void createChildGroups(String groupName, long fourthLevelGroupId, JsonArray newGroups, JsonArray qualifiedGroups)
    {
        LOGGER.info("Creating Child Groups using group");

        var fifthLevelGroupId = CommonUtil.newId();

        qualifiedGroups.add(fifthLevelGroupId);

        newGroups.add(new JsonObject().put(Group.FIELD_GROUP_NAME, groupName).put(Group.FIELD_PARENT_GROUP, fourthLevelGroupId)
                .put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM).put(ID, fifthLevelGroupId)
                .put(Group.GROUP_CONTEXT, new JsonObject().put(Group.GROUP_AUTO_ASSIGN, NO)));
    }

    private void assignMetricPlugin(JsonObject object, JsonObject event)
    {
        var groups = event.getJsonArray(AIOpsObject.OBJECT_GROUPS);

        if (object != null && groups != null && !groups.isEmpty())
        {
            var items = MetricPluginConfigStore.getStore().getItemsByMultiValueFieldAny(MetricPlugin.METRIC_PLUGIN_ENTITIES, groups);

            var pluginIds = new HashSet<Long>();

            var metrics = new ArrayList<JsonObject>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.containsKey(MetricPlugin.METRIC_PLUGIN_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT));

                    item.remove(MetricPlugin.METRIC_PLUGIN_CONTEXT);
                }

                if (!exist(object, item, pluginIds))
                {
                    setMetricPluginContext(item.put(APIConstants.ENTITY_ID, item.getLong(ID)), object, metrics);
                }
            }

            updateMetrics(metrics, EventBusConstants.EVENT_METRIC_PROVISION).onComplete(result -> vertx.eventBus().send(replyTopic, event));
        }
        else
        {
            vertx.eventBus().send(replyTopic, event);
        }
    }

    private boolean exist(JsonObject object, JsonObject item, Set<Long> pluginIds)
    {
        var exist = false;

        for (var metric : MetricConfigStore.getStore().getItemsByObject(object.getLong(ID)))
        {
            var context = metric.getJsonObject(Metric.METRIC_CONTEXT);

            if (pluginIds.contains(item.getLong(ID)))
            {
                exist = true;
            }
            else if (context != null && context.containsKey(APIConstants.ENTITY_ID) && context.getLong(APIConstants.ENTITY_ID).equals(item.getLong(ID)))
            {
                pluginIds.add(item.getLong(ID));

                exist = true;
            }

            if (exist)
            {
                break;
            }
        }

        return exist;
    }

    private void assignLogParsers(JsonArray items, JsonObject object, EventBusConstants.ChangeNotificationType notificationType, JsonObject event)
    {
        try
        {
            LOGGER.info("Assigning Log Parsers for event : " + notificationType);

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                var item = items.getJsonObject(index);

                var entities = item.getJsonArray(LOG_PARSER_ENTITIES, new JsonArray());

                if (!entities.contains(object.getString(AIOpsObject.OBJECT_IP)))
                {
                    entities.add(object.getString(AIOpsObject.OBJECT_IP));

                    if (ADD_OBJECT.equals(notificationType) || ASSIGN_LOG_PARSER.equals(notificationType))
                    {
                        Bootstrap.configDBService().update(DBConstants.TBL_LOG_PARSER,
                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                item.put(LOG_PARSER_ENTITIES, entities),
                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                result ->
                                {
                                    if (result.succeeded())
                                    {
                                        LogParserConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult -> Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, item.getLong(ID)).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_LOG_PARSER.name())));
                                    }

                                    vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, object.getString(AIOpsObject.OBJECT_IP))
                                            .put(EventBusConstants.EVENT, EVENT_LOG)
                                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE)
                                            .put(LogEngineConstants.SOURCE_GROUPS, GroupConfigStore.getStore().flatItemsByValueField(Group.FIELD_GROUP_NAME, item.getString(LogParser.LOG_PARSER_SOURCE_TYPE), GlobalConstants.ID))
                                            .put(PLUGIN_ID, item.getInteger(PLUGIN_ID, DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName())));

                                    promise.complete();
                                });
                    }
                    else
                    {
                        Bootstrap.configDBService().update(DBConstants.TBL_LOG_PARSER,
                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                item.put(LOG_PARSER_ENTITIES, entities),
                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                result ->
                                {
                                    if (result.succeeded())
                                    {
                                        LogParserConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult -> Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, item.getLong(ID)).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_LOG_PARSER.name())));
                                    }

                                    promise.complete();
                                });
                    }
                }
                else
                {
                    promise.complete();
                }
            }

            Future.join(futures).onComplete(result ->
            {
                LOGGER.info("Log Parsers assigned successfully for event : " + notificationType);

                if (event != null)
                {
                    vertx.eventBus().send(replyTopic, event);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            vertx.eventBus().send(replyTopic, event);
        }
    }

    private void unassignLogParsers(JsonArray items, JsonObject event)
    {
        try
        {
            LOGGER.info("Event Received for Change Notification : unassignLogParsers(JsonArray items, JsonObject event)");

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                item.getJsonArray(LOG_PARSER_ENTITIES).remove(event.getString(AIOpsObject.OBJECT_IP));

                Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                        new JsonObject().put(PLUGIN_ID, item.getInteger(PLUGIN_ID))
                                .put(GlobalConstants.ENTITIES, new JsonArray().add(event.getString(AIOpsObject.OBJECT_IP)))
                                .put(CHANGE_NOTIFICATION_TYPE, UPDATE_EVENT_SOURCE));

                Bootstrap.configDBService().update(DBConstants.TBL_LOG_PARSER,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                        new JsonObject().put(LOG_PARSER_ENTITIES, item.getJsonArray(LOG_PARSER_ENTITIES)),
                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                        result ->
                        {
                            if (result.succeeded())
                            {
                                LogParserConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                {
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, item.getLong(ID))
                                            .put(ENTITIES, new JsonArray().add(event.getString(AIOpsObject.OBJECT_IP)))
                                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_LOG_PARSER.name()));

                                    vertx.eventBus().send(replyTopic, event);
                                });
                            }

                            else
                            {
                                vertx.eventBus().send(replyTopic, event);
                            }
                        });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            vertx.eventBus().send(replyTopic, event);
        }
    }

    private void assignRunbooks(JsonArray items, JsonObject object)
    {
        try
        {
            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var entities = item.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES) ? item.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES) : new JsonArray(new ArrayList<>(1));

                if (!entities.contains(object.getLong(ID)))
                {
                    entities.add(object.getLong(ID));
                }

                Bootstrap.configDBService().update(DBConstants.TBL_RUNBOOK_PLUGIN,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                        new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, entities),
                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                        result -> RunbookPluginConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                        {
                            if (asyncResult.failed())
                            {
                                LOGGER.warn("Error assigning runbook plugin: " + item.getLong(ID));

                                LOGGER.error(asyncResult.cause());
                            }
                        }));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void addFlowSamplingRate(JsonObject event)
    {
        LOGGER.info("Event Received for Change Notification : addFlowSamplingRate(JsonObject event)");

        event.remove(CHANGE_NOTIFICATION_TYPE);

        Bootstrap.configDBService().save(DBConstants.TBL_FLOW_SAMPLING_RATE, event,
                event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        FlowSamplingRateConfigStore.getStore().addItem(asyncResult.result()).onComplete(result -> vertx.eventBus().send(replyTopic, event));
                    }
                    else
                    {
                        LOGGER.warn(String.format("failed to add id flow sampling rate for source.ip %s and interface.index %s", event.getString(SOURCE_IP), event.getString("interface.id")));

                        vertx.eventBus().send(replyTopic, event);
                    }
                });
    }

    private void registerEventSource(JsonObject event)
    {
        try
        {
            LOGGER.info("Event Received for Change Notification : registerEventSource(JsonObject event)");

            var eventType = event.getString(EventBusConstants.EVENT);

            var source = event.getString(EventBusConstants.EVENT_SOURCE);

            if (CommonUtil.traceEnabled())
            {

                LOGGER.trace(String.format("register event source %s from event %s", source, eventType));
            }

            if (CommonUtil.isNotNullOrEmpty(source))
            {
                var item = EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, source);

                if (item == null)    // save
                {
                    event.put(EventBusConstants.EVENT_TYPE, new JsonArray().add(event.remove(EventBusConstants.EVENT)));

                    // source group
                    if (event.getJsonArray(SOURCE_GROUPS) != null)
                    {
                        event.put(SOURCE_GROUPS, getEventSourceGroups(source, eventType)    // sync with ObjectConfigStore  [make sure is never null]
                                .addAll(event.getJsonArray(SOURCE_GROUPS)) // new groups
                                .stream().distinct().toList()); // distinct
                    }

                    else
                    {
                        event.put(SOURCE_GROUPS, getEventSourceGroups(source, eventType)    // sync with ObjectConfigStore  [make sure is never null]
                                .stream().distinct().toList()); // distinct
                    }

                    // plugin id
                    if (event.getInteger(PLUGIN_ID) != null)
                    {
                        event.put(PLUGIN_ID, new JsonArray().add(event.getInteger(PLUGIN_ID)));
                    }

                    else
                    {
                        event.put(PLUGIN_ID, new JsonArray());
                    }

                    // category
                    if (CommonUtil.isNotNullOrEmpty(event.getString(LogEngineConstants.EVENT_CATEGORY)))
                    {
                        event.put(LogEngineConstants.EVENT_CATEGORY, new JsonArray().add(event.getString(LogEngineConstants.EVENT_CATEGORY)));
                    }

                    else
                    {
                        event.put(LogEngineConstants.EVENT_CATEGORY, new JsonArray());
                    }

                    // remove garbage fields
                    event.remove(CHANGE_NOTIFICATION_TYPE);

                    event.remove(EVENT_ID);

                    Bootstrap.configDBService().save(DBConstants.TBL_EVENT_SOURCE, event.put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM),
                            event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                            event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                            asyncResult -> EventSourceConfigStore.getStore().addItem(asyncResult.result())
                                    .onComplete(result -> vertx.eventBus().send(replyTopic, event)));
                }

                else    // update
                {
                    // event type
                    item.put(EventBusConstants.EVENT_TYPE, item.getJsonArray(EventBusConstants.EVENT_TYPE).add(eventType).stream().distinct().toList());

                    // source group
                    item.put(SOURCE_GROUPS, getEventSourceGroups(source, eventType)         // sync with ObjectConfigStore  [make sure is never null]
                            .addAll(event.getJsonArray(SOURCE_GROUPS, new JsonArray()))     // new groups
                            .addAll(item.getJsonArray(SOURCE_GROUPS, new JsonArray()))      // existing
                            .stream().distinct().toList());

                    if (event.getInteger(PLUGIN_ID) != null)   // plugin
                    {
                        item.put(PLUGIN_ID, item.getJsonArray(PLUGIN_ID, new JsonArray()).add(event.getInteger(PLUGIN_ID)).stream().distinct().toList());
                    }

                    if (CommonUtil.isNotNullOrEmpty(event.getString(LogEngineConstants.EVENT_CATEGORY)))  // category
                    {
                        item.put(LogEngineConstants.EVENT_CATEGORY, item.getJsonArray(LogEngineConstants.EVENT_CATEGORY, new JsonArray()).add(event.getString(LogEngineConstants.EVENT_CATEGORY)).stream().distinct().toList());
                    }

                    Bootstrap.configDBService().update(DBConstants.TBL_EVENT_SOURCE, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                            item,
                            GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS,
                            asyncResult -> EventSourceConfigStore.getStore().updateItem(item.getLong(ID))
                                    .onComplete(result -> vertx.eventBus().send(replyTopic, event)));
                }
            }

            else
            {
                LOGGER.warn("null or blank Event source found, " + event.encode());

                vertx.eventBus().send(replyTopic, event);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            vertx.eventBus().send(replyTopic, event);
        }
    }

    private JsonArray getEventSourceGroups(String source, String eventType)
    {
        var groups = new JsonArray();

        var items = ObjectConfigStore.getStore().flatItemsByValueField(AIOpsObject.OBJECT_IP, source, AIOpsObject.OBJECT_GROUPS);

        if (items != null && !items.isEmpty())
        {
            groups.addAll(items.getJsonArray(0));
        }

        else if (EVENT_FLOW.equalsIgnoreCase(eventType))
        {
            groups.add(GroupConfigStore.getStore().getItemByValue(Group.FIELD_GROUP_NAME, LogEngineConstants.Category.NETWORK.getName()).getLong(GlobalConstants.ID));
        }

        // if eventType is Log, User has to manually assign the monitor/group to source.

        return groups;
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
